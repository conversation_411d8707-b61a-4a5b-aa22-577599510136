name: Setup Terraform environment
description: |
  Setup Terraform environment including AWS credentials, terraform binary, and tftools.
  This action exports the TFTOOLS environment variable for use in subsequent steps.
  
  Example usage:
    - uses: ./.github/actions/setup-tf-env
      with:
        tftools: terraform_abc123  # Optional, defaults to terraform_abe817d45b
    
    - name: Use tftools
      run: $TFTOOLS verify ecs-only  # TFTOOLS env var is now available

inputs:
  tftools:
    description: |
      TFTOOLS to use. Example: `terraform_<tag>`
      The tftools binary is our custom wrapper around terraform that we
      use to add additional functionality. See `//nirvana/cmd/terraform` for
      the implementation.
    required: false
    default: "terraform_abe817d45b"
  account_id:
    description: AWS account ID to use. Defaults to production account (************).
    default: "************"

outputs:
  tftools:
    description: The tftools binary name that was set up and exported as TFTOOLS environment variable
    value: ${{ inputs.tftools }}

runs:
  using: "composite"
  steps:
    - name: Configure AWS Credentials
      uses: ./.github/actions/setup-aws-credentials
      with:
        aws-account-id: ${{ inputs.account_id }}

    - name: Setup terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.7.5
        terraform_wrapper: false # ! Removing this will break our scripts which run jq on terraform output

    - name: Run terraform init
      shell: bash
      working-directory: src/deployment/app
      run: |
        terraform init

    - name: Setup tftools
      shell: bash
      run: |
        aws s3 cp s3://cloud.nirvanatech.com/tools/"${{ inputs.tftools }}" $HOME/.local/bin/"${{ inputs.tftools }}"
        chmod +x $HOME/.local/bin/"${{ inputs.tftools }}"
        
        # Export TFTOOLS to GITHUB_ENV so it's available as $TFTOOLS environment variable in all subsequent steps
        echo "TFTOOLS=${{ inputs.tftools }}" >> $GITHUB_ENV
