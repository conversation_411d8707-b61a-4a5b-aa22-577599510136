name: Scala CI

permissions: read-all

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

defaults:
  run:
    shell: bash
    working-directory: src/spark

jobs:
  scala_changes:
    runs-on: ubuntu-latest
    outputs:
      src: ${{ steps.filter.outputs.src }}
    steps:
      - uses: actions/checkout@v5
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            src:
              - 'src/spark/**'

  scala_build_and_test:
    needs: [scala_changes]
    if: ${{ needs.scala_changes.outputs.src == 'true'}}

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v5

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "17"
          cache: "sbt"

      - name: Set up SBT launcher
        uses: sbt/setup-sbt@v1

      - name: Run lint
        run: sbt scalafmtCheckAll

      - name: Run compile
        run: sbt compile

      - name: Run tests
        run: sbt test

      - name: Run assembly
        run: sbt assembly
