set -euo pipefail
echo "Installing brew"

/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"


echo "Installing Ansible"

brew install ansible@9
brew link --force ansible@9

while true; do
    read -p "Do you wish to copy the default configuration to your home directory: " yn
    case $yn in
        [Yy]* ) cp $(git rev-parse --show-toplevel)/ansible-setup/.nirvana.config.yaml ~/.nirvana.config.yaml;
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

ansible-playbook "$(git rev-parse --show-toplevel)/ansible-setup/all.yaml"

while true; do
    read -p "Do you wish to install data science dependencies: " yn
    case $yn in
        [Yy]* ) cd $(git rev-parse --show-toplevel)/src/ds;
                # Build psycopg2 in a virtual environment requires the following flags
                # if they aren't included in the shell profile
                # https://stackoverflow.com/questions/26288042/error-installing-psycopg2-library-not-found-for-lssl
                eval "$(pyenv init -)";
                export LDFLAGS="-L/opt/homebrew/opt/openssl@3/lib";
                export CFLAGS="-I/opt/homebrew/opt/openssl@3/include";
                export GRPC_PYTHON_BUILD_SYSTEM_OPENSSL=1;
                export GRPC_PYTHON_BUILD_SYSTEM_ZLIB=1;
                task install;
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

while true; do
    read -p "Do you wish to install frontend dependencies: " yn
    case $yn in
        [Yy]* ) cd $(git rev-parse --show-toplevel)/src/nirvana/client;
                yarn install;
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

while true; do
    read -p "Do you wish to setup aws? This is necessary to run Data Science modules and migrations: " yn
    case $yn in
        [Yy]* ) echo "Configuring aws, enter region us-east-2 when prompted";
                aws configure sso;
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

while true; do
    read -p "Do you wish to run migrations? This is necessary to start go-backend: " yn
    case $yn in
        [Yy]* ) cd $(git rev-parse --show-toplevel)/src/nirvana/;
                bazel run db -- migrate up nirvana;
                bazel run db -- migrate up fmcsa;
                echo "You can now run api-server with 'bazel run //nirvana/api-server/cmd/api_server_local'";
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

while true; do
    read -p "Do you wish to create a user? This is necessary to login on locally run apps: " yn
    case $yn in
        [Yy]* ) cd $(git rev-parse --show-toplevel)/src/nirvana/;
                bazel run //nirvana/db-api/cmd/auth_management_tool -- createSuperuser --user_email=<EMAIL> --user_password=NotNirvana123 --user_first_name=Super --user_last_name=Test;
                echo "\n";
                echo "If the command failed because of duplicate key error. The user is already created";
                echo "Created <NAME_EMAIL> with password NotNirvana123";
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done

while true; do
    read -p "Test the installation by building and testing scripts? Please run all.yaml before runnning this step: " yn
    case $yn in
        [Yy]* ) cd $(git rev-parse --show-toplevel)/src/nirvana/;
                task bazel:build-all;
                break;;
        [Nn]* ) break;;
        * ) echo "Please answer y or n.";;
    esac
done
