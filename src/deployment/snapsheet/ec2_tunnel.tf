# Private key stored in 1password:
# https://start.1password.com/open/i?a=UKPM4OB4VRFATCUENU3FVXEY44&v=raa63enqonl4uytvxtdvgiwnlm&i=g4kjatvptydxb6e2xa5gqzdrnu&h=nirvanavault.1password.com
data "aws_key_pair" "claims_bastion_main_keypair" {
  key_name = "neosync-bastion-key"
}

import {
  to = aws_instance.claims_bastion
  id = "i-06578fee89ce84dfa"
}

// Tunnel host for Airbyte Cloud and DBT Cloud access to Production DB
resource "aws_instance" "claims_bastion" {
  ami           = "ami-050cd642fd83388e4"
  instance_type = "t2.micro"

  key_name = data.aws_key_pair.claims_bastion_main_keypair.key_name

  user_data = <<EOF
  #!/bin/bash

  echo "Adding public keys"

  rm -rf /home/<USER>/.ssh
  mkdir -p /home/<USER>/.ssh/

  {
    # Main key
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQCitRWEfMo9AWtTdCd7vRbA/h+xAUKBZQJ1JCzjChYzOrmUEzmrakdx8z77hPxSqw7HNTxfE7+ragrF2r9e4N+4L32aADjgbtOTxcr3+2XfWZZQQKcCHIvr/tNBaePtcujM7qd/y3P10r3GubjAkpHH6lUm+NNF4hIGGFt2AEU0Co8mKaOee1hnq6m0yguimZbDdHp0AgrNru75I3/Wki4adeH/mNVXPPBaARLUEyt/MJPMclXdu7NnOnV/XsMFJM6OEGUS4a3LhcPHBrku9fk/2mHYzGDw/U1ZGjS8NJq+jFbxLV1ATMp4AvTaHm++CyTDmp/a1k9Y6pCx+csXCGLP main-bastion-key"

    # Airbyte Cloud access
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDAKR/bgbIBuHT4geOwlbXbwKiMUUFa+oqZ92u7kzShE9aCUAUHrAScXZWyZjj2yv03iY8wlUBI/wYDY34WUyb/+21BMYYi74O8VRv7pC/bEv+DNM2z2CZ7JcoAkJaxRJYoeCn+KpPLhzpOfeiSjt+6w6zJg80n2W/Fc/6qrj6euF3AJ7HZDxPX1lZp9d0PbhoCUR/VEev2Wj3k+k9w52dwlS5iqLyHabyAaOJY9LpinVl1y568OakRLWWOD3iq2St5ImdP6jYjwvTeyvBMXf/Inw6MC2G9vadhJMsZq7vUsKJmaesW1trwCbtQCThq51kJhMyIx5DIJTc9MkWXJIQzZBSPps8LhhYpaOHHv9MQoDT44i1bmGYORaR2BlFz+rKXj5xdtUNNJ2TspN7nBTNPxmBvynaUb50t0ToUXeK9rpj7HJs1W53TsvPPH0F4srWZmWZYGNa75KPR5JvcpksTvAgnYn1b+EuJPXISfLxWB0++cNjHr4GOwoYtCViYI1YA2DwdiZqAFNCYqZPfcaLWzf5vzL/hUuwNGBMrG0xurL6wg/8PFBnY8Q71X1/0EbDiO8BgxpXj7u0j9QkiTY4fyuie2z4qGjom2gT76EYRkNKclpD/0ryzbh4X5P1UcDhA+K6LjnJsVRHPLDTAM+dXOnG0+fQe+cIpruYgGQ0pvQ== airbyte-cloud"

    # DBT Cloud access
    echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDI5VXTWRWrIpx+CPkq0NRawmjAASoWraIWzobpxxwG5CvvRStBgubC6nyMbOLz+1a9SQcoTXMsj+2oVnVhsbNZ5xjeZXvqhobFEs0qRvkaf6jPeNhfrfIb3yhob9IEEIkc9SKFqm7lBwpVzr8wwzfEKuon2WlStvujRFgOF4oEiVP258Izl6+yWuFxhvqV/YlcHg5FA/5XuOuQM2iDAN4UdreVNC/j0xZfxb/4Q+9MPmvrIZo4ZBPWxN6NN2KlQwT+jBKp2X1dIL62qEWy5okFumexoe8F3CiZ/yftDXgNFGOrKa+kXXxKWFU2L/9IMv62p1rcxYg/BzW2cF0/VprZUiZMpf6y7Ycm/STy+eKnbAAUDA+nyujh1zJWqGOOgYbdvrFBDOXYBBtc4fmFaULenQBqQULzxu6+DJyF6sDot6+wZZAUWE0KCW767UMdq6sEzIdIprp4DAjRUxOMtRdSyJkOvwMEYCNxBDR2/wNAV4d8g2SKebEXlM+9h8NhmfDb9eSRkh0lnGm3kxUmdi/obXHnhBlgfc3pPKxvmr3kipEfVjGwtddREtSnCb0FZZYjNtRsF1Z7nObh3zC7OeC8tXJ27bAwG2M5P3E1HLt+ZElIxkQS+NbJtymlgLUkdxifZX2b56WkMjRB5S7AI1nLNR4jl0gS2rpUlVZt2PH32w== dbt-cloud"
    } >> /home/<USER>/.ssh/authorized_keys
  chown ec2-user /home/<USER>/.ssh/authorized_keys
  chmod 400 /home/<USER>/.ssh/authorized_keys
  EOF

  tags = {
    Name = "claims-bastion"
    ManagedBy = "InsuredEng"
  }
}

import {
  to = aws_eip.claims_bastion_eip
  id = "eipalloc-0ecf856a29b5d25d3"
}

resource "aws_eip" "claims_bastion_eip" {
  domain   = "vpc"

  tags = {
    Name = "claims-bastion-eip"
    ManagedBy = "InsuredEng"
  }
}

resource "aws_eip_association" "claims_bastion_eip_assoc" {
  instance_id   = aws_instance.claims_bastion.id
  allocation_id = aws_eip.claims_bastion_eip.id
}

resource "aws_route53_record" "claims_bastion_cname" {
  zone_id = "Z04984401BYE18UCI6HZ1"
  name    = "claims-bastion.nirvanatech.com"
  type    = "A"
  ttl     = 300
  records = [aws_eip.claims_bastion_eip.public_ip]
}
