import streamlit as st
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from boards.renewals.constants import SECTIONS_MAPPING, TAB_DISPLAY_NAMES
from boards.renewals.data_formatter.main import DataFormatters
from boards.renewals.utils import show_data_in_expander


@dataclass
class DataSection:
    """Represents a section of data to be displayed in the UI."""

    title: Optional[str]
    show_title: bool
    data: Dict[str, Any]
    formatter: Callable
    section: Optional[str]


def _create_data_sections(data: Dict[str, Any]) -> List[DataSection]:
    """
    Create a list of DataSection objects from the input data.

    Parameters
    ----------
    data : dict
        Dictionary containing all the data to be displayed

    Returns
    -------
    list
        List of DataSection objects
    """
    return [
        DataSection(
            title=None,
            show_title=False,
            data=data["overview"],
            formatter=DataFormatters.top_level_information,
            section=None,
        ),
        DataSection(
            title="Recommendation",
            show_title=False,
            data={
                "first_failing_rule_message": data["first_failing_rule_message"],
                "rules": data["rules"],
            },
            formatter=DataFormatters.summary,
            section=SECTIONS_MAPPING["RECOMMENDATION_AND_SUMMARY"],
        ),
        DataSection(
            title="General Info",
            show_title=True,
            data=data["overview"],
            formatter=DataFormatters.general_information,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Expiring Policy Details",
            show_title=True,
            data={
                "claims_data": data["claims_data"],
                "earned_premium": data["earned_premium"],
                "application_id": data["overview"]["APPLICATION_ID"].iloc[0],
                "overview": data["overview"],
            },
            formatter=DataFormatters.expiring_policy_stats,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Policy Stats",
            show_title=True,
            data=data["overview"],
            formatter=DataFormatters.aggregate_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Auto Liability Stats",
            show_title=True,
            data={
                "overview": data["overview"],
                "earned_premium": data["earned_premium"],
                "claims_data": data["claims_data"],
                "insurance_metrics": data["insurance_metrics"],
            },
            formatter=DataFormatters.auto_liability_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Auto Physical Damage Stats",
            show_title=True,
            data={
                "overview": data["overview"],
                "earned_premium": data["earned_premium"],
                "claims_data": data["claims_data"],
                "insurance_metrics": data["insurance_metrics"],
            },
            formatter=DataFormatters.auto_physical_damage_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Motor Truck Cargo Stats",
            show_title=True,
            data={
                "overview": data["overview"],
                "earned_premium": data["earned_premium"],
                "claims_data": data["claims_data"],
                "insurance_metrics": data["insurance_metrics"],
            },
            formatter=DataFormatters.motor_truck_cargo_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="TRS Data",
            show_title=True,
            data={"trs_data": data["trs_data"], "score_to_segment": data["score_to_segment"]},
            formatter=DataFormatters.trs_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Claims",
            show_title=True,
            data=data["claims_data"],
            formatter=DataFormatters.claims_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
        DataSection(
            title="Earned Premium",
            show_title=True,
            data=data["earned_premium"],
            formatter=DataFormatters.earned_premium_data,
            section=SECTIONS_MAPPING["DETAIL"],
        ),
    ]


def _show_section_content(section: DataSection) -> None:
    """
    Display the content of a single section.

    Parameters
    ----------
    section : DataSection
        DataSection object containing the data and formatter
    """
    if section.show_title:
        st.subheader(section.title)
    formatted_data = section.formatter(section.data)
    if formatted_data is not None:
        st.write(formatted_data)


def _group_sections_by_tab(sections: List[DataSection]) -> Dict[str, List[DataSection]]:
    """
    Group sections by their tab name.

    Parameters
    ----------
    sections : list
        List of DataSection objects

    Returns
    -------
    dict
        Dictionary mapping tab names to lists of sections
    """
    section_groups = {key: [] for key in SECTIONS_MAPPING.values()}
    for section in sections:
        if section.section is None:
            show_data_in_expander(section.title, section.data, section.formatter, section.show_title)
        elif section.section in section_groups:
            section_groups[section.section].append(section)
    return section_groups


def _render_tab_content(sections: List[DataSection]) -> None:
    """
    Render the content for a specific tab.

    Parameters
    ----------
    sections : list
        List of DataSection objects to display in the tab
    """
    if not sections:
        return

    # Define sections that should be in tabs
    TABBED_SECTIONS = {"Policy Stats", "Auto Liability Stats", "Auto Physical Damage Stats", "Motor Truck Cargo Stats"}

    # Split sections into tabbed and non-tabbed
    sections_for_tabs = [s for s in sections if s.title in TABBED_SECTIONS]
    other_sections = [s for s in sections if s not in sections_for_tabs]

    # Render initial sections
    for section in other_sections:
        if section.title in ["General Info", "Expiring Policy Details"]:
            show_data_in_expander(section.title, section.data, section.formatter, section.show_title)

    # Create expander for tabbed sections
    with st.expander("Historical Policy Details"):
        if sections_for_tabs:
            subtabs = st.tabs([TAB_DISPLAY_NAMES[section.title] for section in sections_for_tabs])
            for section, subtab in zip(sections_for_tabs, subtabs):
                with subtab:
                    _show_section_content(section)

    # Render remaining sections
    for section in other_sections:
        if section.title not in ["General Info", "Expiring Policy Details"]:
            show_data_in_expander(section.title, section.data, section.formatter, section.show_title)


def display_all_data(data: Dict[str, Any]) -> None:
    """
    Main function to display all data in the UI.

    Parameters
    ----------
    data : dict
        Dictionary containing all the data to be displayed
    """
    # Create and organize data sections
    data_sections = _create_data_sections(data)
    section_groups = _group_sections_by_tab(data_sections)

    # Create main tabs
    main_tabs = st.tabs(list(SECTIONS_MAPPING.values()))

    # Render content for each tab
    for main_tab_name, main_tab in zip(SECTIONS_MAPPING.values(), main_tabs):
        with main_tab:
            _render_tab_content(section_groups[main_tab_name])
