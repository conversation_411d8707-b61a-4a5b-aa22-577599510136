from typing import Union, Any, Optional
from boards.renewals.constants import CLAIMS_THRESHOLDS, SEGMENT_COLORS
from boards.renewals.utils import convert_to_readable, format_numeric, format_text
import streamlit as st
import pandas as pd


class ClaimsDataFormatter:
    """Handles formatting and display of claims data."""

    @staticmethod
    def format_data(data: Optional[pd.DataFrame]) -> None:
        """
        Main entry point for formatting claims data.

        Parameters
        ----------
        data : pd.DataFrame or None
            Input DataFrame containing claims data
        """
        with st.container():
            if not ClaimsDataFormatter._validate_data(data):
                st.write("No claims data available.")
                return

            processed_df = ClaimsDataFormatter._process_data(data)
            formatted_df = ClaimsDataFormatter._apply_styling(processed_df)
            ClaimsDataFormatter._display_data(formatted_df)

    @staticmethod
    def _validate_data(data: Optional[pd.DataFrame]) -> bool:
        """
        Validate input data for processing.

        Parameters
        ----------
        data : pd.DataFrame or None
            Input DataFrame to validate

        Returns
        -------
        bool
            True if data is valid, False otherwise
        """
        return not (data is None or data.empty)

    @staticmethod
    def _process_data(data: pd.DataFrame) -> pd.DataFrame:
        """
        Process the claims data.

        Parameters
        ----------
        data : pd.DataFrame
            Input DataFrame to process

        Returns
        -------
        pd.DataFrame
            Processed DataFrame
        """
        # Convert column names to readable format
        readable_columns = {col: convert_to_readable(col) for col in data.columns}
        return data.rename(columns=readable_columns)

    @staticmethod
    def _apply_styling(df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply formatting to the DataFrame columns.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame to format

        Returns
        -------
        pd.DataFrame
            Formatted DataFrame
        """
        formatted_df = df.copy()

        # Define column-specific formatting
        formatters = {
            "Net Incurred": lambda x: format_numeric(x, 2),
            "Claim Count": lambda x: format_numeric(x, 0),
            "Claim Type": format_text,
            "Coverage Type": format_text,
        }

        # Apply formatting to each column
        for col, formatter in formatters.items():
            if col in formatted_df.columns:
                formatted_df[col] = formatted_df[col].apply(formatter)

        return formatted_df

    @staticmethod
    def _display_data(df: pd.DataFrame) -> None:
        """
        Display the formatted data in a Streamlit dataframe with conditional coloring.

        Parameters
        ----------
        df : pd.DataFrame
            Formatted DataFrame to display
        """
        if "Net Incurred" in df.columns:
            styled_df = df.style.applymap(ClaimsDataFormatter._color_by_amount, subset=["Net Incurred"])
            st.dataframe(styled_df, use_container_width=True, hide_index=True)
        else:
            st.dataframe(df, use_container_width=True, hide_index=True)

    @staticmethod
    def _color_by_amount(val: Any) -> str:
        """
        Apply conditional coloring based on claim amount thresholds.

        Parameters
        ----------
        val : Any
            Value to evaluate for coloring

        Returns
        -------
        str
            CSS style string for background color
        """
        try:
            val = float(str(val).replace(",", ""))
            if val >= CLAIMS_THRESHOLDS["HIGH"]:
                return f"background-color: {SEGMENT_COLORS['Decline']}"
            elif val >= CLAIMS_THRESHOLDS["MEDIUM"]:
                return f"background-color: {SEGMENT_COLORS['Extended']}"
        except (ValueError, TypeError):
            pass
        return "background-color: #FFFFFF"
