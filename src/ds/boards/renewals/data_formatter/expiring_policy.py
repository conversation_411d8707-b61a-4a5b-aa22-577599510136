import streamlit as st


class ExpiringPolicyFormatter:
    """Handles the formatting and calculation of loss ratio statistics."""

    @staticmethod
    def format_data(data):
        """Main entry point for loss ratio data formatting."""
        # Add top padding

        claims_data = data.get("claims_data")
        earned_premium = data.get("earned_premium")
        application_id = data.get("application_id")
        overview = data.get("overview")

        # Create container with padding
        container = st.container()
        with container:

            left_col, _, right_col = st.columns([10, 1, 10])  # Add spacing column between main columns

            with left_col:
                st.markdown("### Policy Overview")
                st.write("")  # Add space after heading

                matching_overview = overview[overview["APPLICATION_ID"] == application_id]

                if not matching_overview.empty:
                    row = matching_overview.iloc[0]
                    policy_details = {
                        "Projected Mileage": (
                            f"{int(float(row.get('PROJECTED_MILEAGE', 0))):,} miles"
                            if row.get("PROJECTED_MILEAGE") != "N/A"
                            else "N/A"
                        ),
                        "Billed Mileage": (
                            f"{int(float(row.fillna(0).get('ACTUAL_MILEAGE', 0))):,} miles"
                            if row.get("ACTUAL_MILEAGE") != "N/A"
                            else "N/A"
                        ),
                        "Miles % to Exp": (
                            f"{float(row.get('ACTUAL_VS_PROJECTED_MILEAGE_PCT', 0)):.1f}%"
                            if row.get("ACTUAL_VS_PROJECTED_MILEAGE_PCT") != "N/A"
                            else "N/A"
                        ),
                        "AL Premium": (
                            f"${float(row.get('LIAB_PREMIUM') or 0):,.2f}"
                            if row.get("LIAB_PREMIUM") != "N/A"
                            else "N/A"
                        ),
                        "APD Premium": (
                            f"${float(row.get('PHYS_PREMIUM') or 0):,.2f}"
                            if row.get("PHYS_PREMIUM") != "N/A"
                            else "N/A"
                        ),
                        "APD TIV": f"${float(row.get('TIV') or 0):,.2f}" if row.get("TIV") != "N/A" else "N/A",
                        "MTC Premium": (
                            f"${float(row.get('MTC_PREMIUM') or 0):,.2f}"
                            if row.get("MTC_PREMIUM") not in ["N/A", None]
                            else "N/A"
                        ),
                        "PU Count": f"{int(float(row.get('PU_COUNT', 0)))}" if row.get("PU_COUNT") != "N/A" else "N/A",
                    }
                else:
                    policy_details = {
                        "Projected Mileage": "N/A",
                        "Billed Mileage": "N/A",
                        "Miles % to Exp": "N/A",
                        "AL Rate": "N/A",
                        "APD Rate": "N/A",
                        "Expiring TIV APD": "N/A",
                        "Power Unit Count": "N/A",
                        "APD TIV": "N/A",
                    }

                for label, value in policy_details.items():
                    cols = st.columns([3, 1])
                    with cols[0]:
                        st.write(label)
                    with cols[1]:
                        st.markdown(f"<div style='text-align: right'>{value}</div>", unsafe_allow_html=True)

            with right_col:
                if not ExpiringPolicyFormatter._validate_input_data(claims_data, earned_premium):
                    st.write("No earned premium data available.")
                    return

                filtered_claims = claims_data[claims_data["APPLICATION_ID"] == application_id]
                filtered_premium = earned_premium[earned_premium["APPLICATION_ID"] == application_id]
                metrics = ExpiringPolicyFormatter._calculate_metrics(filtered_claims, filtered_premium)
                ExpiringPolicyFormatter._display_metrics(metrics)

        # Add bottom padding
        st.write("")

    @staticmethod
    def _validate_input_data(claims_data, earned_premium):
        """Validates that required data is present and not empty."""
        return not (claims_data is None or earned_premium is None or claims_data.empty or earned_premium.empty)

    @staticmethod
    def _calculate_metrics(claims_data, earned_premium):
        """Calculates loss ratios and claims frequency for all coverages."""
        metrics = {"Total": ExpiringPolicyFormatter._calculate_total_metrics(claims_data, earned_premium)}

        # Calculate metrics for each coverage type
        for coverage in ["AL", "APD", "MTC"]:
            metrics[coverage] = ExpiringPolicyFormatter._calculate_coverage_metrics(
                claims_data, earned_premium, coverage
            )

        return metrics

    @staticmethod
    def _calculate_total_metrics(claims_data, earned_premium):
        """Calculates total metrics across all coverages."""
        total_incurred = claims_data["NET_INCURRED"].sum()
        total_premium = earned_premium["AMOUNT_IN_NUMBERS"].sum()

        return {
            "loss_ratio": (total_incurred, total_premium),
            "claims_frequency": None,  # Total frequency not applicable
        }

    @staticmethod
    def _calculate_coverage_metrics(claims_data, earned_premium, coverage):
        """Calculates metrics for a specific coverage type."""
        # Filter claims for the coverage
        coverage_claims = claims_data[claims_data[f"LOSS_{coverage}_COUNT"] == 1]

        # Calculate incurred losses
        coverage_incurred = coverage_claims["NET_INCURRED"].sum()

        # Get premium for the coverage
        coverage_premium = earned_premium[earned_premium["COVERAGE"] == coverage]["AMOUNT_IN_NUMBERS"].sum()

        # Calculate at-fault claims
        at_fault_claims = ExpiringPolicyFormatter._calculate_at_fault_claims(claims_data, coverage)

        # Determine premium divisor based on coverage
        premium_divisor = 10000 if coverage == "AL" else 1000

        return {
            "loss_ratio": (coverage_incurred, coverage_premium),
            "claims_frequency": (at_fault_claims, coverage_premium / premium_divisor),
        }

    @staticmethod
    def _calculate_at_fault_claims(claims_data, coverage):
        """Calculates the number of at-fault claims for a coverage."""
        return claims_data[(claims_data[f"LOSS_{coverage}_COUNT"] == 1) & (claims_data["NIRVANA_CLAIM_TYPE"] != "NAF")][
            f"LOSS_{coverage}_COUNT"
        ].sum()

    @staticmethod
    def _display_metrics(metrics):
        """Displays the calculated metrics in the Streamlit interface."""
        # Display Total metrics
        ExpiringPolicyFormatter._display_total_section(metrics["Total"])

        # Display coverage-specific metrics
        ExpiringPolicyFormatter._display_coverage_section(metrics)

    @staticmethod
    def _display_total_section(total_metrics):
        """Displays the total metrics section."""
        st.markdown("### Loss Ratio")

        total_ratio = ExpiringPolicyFormatter._calculate_ratio(*total_metrics["loss_ratio"])
        _, col2, _ = st.columns([1, 2, 1])  # Middle column is twice as wide
        with col2:
            st.metric(label="Total Loss Ratio", value=f"{total_ratio:.2%}")

    @staticmethod
    def _display_coverage_section(metrics):
        """Displays the coverage-specific metrics section."""
        st.markdown("##### Coverage-wise Performance")

        for coverage in ["AL", "APD", "MTC"]:
            ExpiringPolicyFormatter._display_coverage_metrics(coverage, metrics[coverage])

    @staticmethod
    def _display_coverage_metrics(coverage, metrics):
        """Displays metrics for a specific coverage."""
        cols = st.columns(2)

        # Display Loss Ratio
        loss_ratio = ExpiringPolicyFormatter._calculate_ratio(*metrics["loss_ratio"])
        cols[0].metric(label=f"{coverage} Loss Ratio", value=f"{loss_ratio:.2%}")

        # Display Claims Frequency
        if metrics["claims_frequency"]:
            claims_freq = ExpiringPolicyFormatter._calculate_ratio(*metrics["claims_frequency"])
            cols[1].metric(label=f"{coverage} Claims Frequency", value=f"{claims_freq:.2%}")

    @staticmethod
    def _calculate_ratio(numerator, denominator):
        """Safely calculates a ratio, handling division by zero."""
        try:
            return numerator / denominator if denominator != 0 else 0
        except (TypeError, ValueError):
            return 0
