import pandas as pd
import streamlit as st


class InsuranceDataFormatter:
    """Handles formatting and processing of insurance-specific data."""

    # Class-level mappings to avoid re-creation on every call
    COVERAGE_MAP = {
        "AL": "auto_liability",
        "APD": "auto_physical_damage",
        "MTC": "motor_truck_cargo",
    }

    COMMON_COLUMNS = {
        "EFFECTIVE_YEAR": "Policy Effective Year",
        "TOTAL_CLAIMS": "Total Claims",
        "AT_FAULT_CLAIMS": "At-Fault Claims",
        "LARGE_CLAIMS": "Large Claims",
        "OPEN_CLAIMS": "Open Claims",
        "NET_INCURRED_LR": "Net Incurred Loss Ratio",
        "AT_FAULT_FREQ": "At-Fault Frequency",
    }

    COVERAGE_SPECIFIC_COLUMNS = {
        "auto_liability": {
            "LIAB_RATE_PER_100_MILES": "Rate / 100 miles",
            "LIAB_PREMIUM": "Earned Premium",
            "LIAB_PRICE_PER_PU": "Price per PU",
            "LIAB_DEDUCTIBLE": "Deductible",
            "LIAB_LIMIT": "Limit",
        },
        "auto_physical_damage": {
            "PHY_RATE_PER_TIV": "Rate per TIV",
            "PHYS_PREMIUM": "Earned Premium",
            "PHYS_PRICE_PER_PU": "Price per PU",
            "PHYS_DEDUCTIBLE": "Deductible",
            "TIV": "TIV",
        },
        "motor_truck_cargo": {
            "MTC_RATE_PER_100_MILES": "Rate / 100 miles",
            "MTC_PREMIUM": "Earned Premium",
            "MTC_PRICE_PER_PU": "Price per PU",
            "MTC_DEDUCTIBLE": "Deductible",
            "MTC_COVERAGE_LIMIT": "Limit",
        },
    }

    @staticmethod
    def format_data(data, coverage_type, columns, coverage_code):
        """
        Main entry point for insurance data formatting.
        Displays the formatted DataFrame in a Streamlit container.
        """
        with st.container():
            df = InsuranceDataFormatter._prepare_base_data(data, columns)
            if df is None:
                st.write(f"No {coverage_type} data available.")
                return

            df = InsuranceDataFormatter._process_data(df, data, coverage_code)
            df = InsuranceDataFormatter._format_final_data(df, coverage_type)
            st.dataframe(df, use_container_width=True, hide_index=True)

    @staticmethod
    def _prepare_base_data(data, columns):
        """
        Prepares the base DataFrame from overview data.
        Returns None if overview is missing or empty.
        """
        overview = data.get("overview")
        if overview is None or overview.empty:
            return None

        # Always include APPLICATION_ID for merging, in addition to requested columns
        columns_to_select = list(columns) if isinstance(columns, (list, tuple)) else [columns]
        if "APPLICATION_ID" not in columns_to_select:
            columns_to_select.append("APPLICATION_ID")

        return pd.DataFrame(overview[columns_to_select])

    @staticmethod
    def _process_data(df, data, coverage_code):
        """
        Processes claims and premium data to calculate metrics.
        Merges precomputed metrics if available, else adds empty metrics.
        """
        metrics_dict = data.get("insurance_metrics", {})
        coverage_key = InsuranceDataFormatter.COVERAGE_MAP.get(coverage_code, "")
        metrics_df = metrics_dict.get(coverage_key)

        if metrics_df is None or metrics_df.empty:
            return InsuranceDataFormatter._add_empty_metrics(df)

        # Merge metrics with df on APPLICATION_ID
        df = df.merge(metrics_df, on="APPLICATION_ID", how="left")
        return InsuranceDataFormatter._fill_missing_values(df)

    @staticmethod
    def _add_empty_metrics(df):
        """
        Adds empty metrics columns to the DataFrame.
        """
        df["TOTAL_CLAIMS"] = 0
        df["AT_FAULT_CLAIMS"] = 0
        df["LARGE_CLAIMS"] = 0
        df["OPEN_CLAIMS"] = 0
        df["NET_INCURRED_LR"] = "0%"
        df["AT_FAULT_FREQ"] = "0%"
        return df

    @staticmethod
    def _fill_missing_values(df):
        """
        Fills missing values in metrics columns with zeros.
        """
        columns_to_fill = [
            "TOTAL_CLAIMS",
            "AT_FAULT_CLAIMS",
            "LARGE_CLAIMS",
            "OPEN_CLAIMS",
            "NET_INCURRED_LR",
            "AT_FAULT_FREQ",
        ]
        # Only fill NaN values for columns that exist in the dataframe
        existing_columns = [col for col in columns_to_fill if col in df.columns]
        if existing_columns:
            df[existing_columns] = df[existing_columns].fillna(0)
        return df

    @staticmethod
    def _format_final_data(df, coverage_type):
        """
        Formats the final DataFrame with proper column names and drops APPLICATION_ID.
        """
        df = df.drop("APPLICATION_ID", axis=1)
        return df.rename(columns=InsuranceDataFormatter._get_column_mapping(coverage_type))

    @staticmethod
    def _get_column_mapping(coverage_type):
        """
        Returns the appropriate column mapping for the coverage type.
        """
        return {
            **InsuranceDataFormatter.COMMON_COLUMNS,
            **InsuranceDataFormatter.COVERAGE_SPECIFIC_COLUMNS[coverage_type],
        }
