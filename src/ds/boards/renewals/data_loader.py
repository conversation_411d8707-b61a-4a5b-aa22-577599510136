import json
from typing import Dict, List, Any, Optional
import streamlit as st
import pandas as pd
import numpy as np
from boards.renewals.queries import (
    GET_OVERVIEW_DATA_QUERY,
    EARNED_PREMIUM_QUERY,
    FLEET_INTERVENTION_QUERY,
    GET_CLAIMS_QUERY,
    PCT_HAZARD_DISTANCE_QUERY,
    PCT_HAZARD_DURATION_QUERY,
    RENEWAL_GET_DOT_DETAILS_QUERY,
    RENEWAL_GET_OPPORTUNITY_ID_QUERY,
    GET_RUBRIC_QUERY,
    TRS_OVERTIME_QUERY,
)
from boards.renewals.snowflake_connector import fetch_from_snowflake


def compute_insurance_metrics(
    claims_data: Optional[pd.DataFrame], earned_premium: Optional[pd.DataFrame]
) -> Dict[str, pd.DataFrame]:
    """
    Compute insurance metrics for each coverage type and application.

    Parameters
    ----------
    claims_data : pd.DataFrame or None
        DataFrame containing claims information
    earned_premium : pd.DataFrame or None
        DataFrame containing earned premium data

    Returns
    -------
    dict
        Dictionary containing insurance metrics for different coverage types
    """

    def metrics_for_coverage(coverage_code: str) -> pd.DataFrame:
        if claims_data is None or claims_data.empty or earned_premium is None or earned_premium.empty:
            return pd.DataFrame()

        loss_col = f"LOSS_{coverage_code}_COUNT"
        if loss_col not in claims_data.columns:
            return pd.DataFrame()

        coverage_claims = claims_data[claims_data[loss_col] == 1]
        coverage_premium = (
            earned_premium[earned_premium["COVERAGE"] == coverage_code]
            .groupby("APPLICATION_ID")["AMOUNT_IN_NUMBERS"]
            .sum()
        )

        net_incurred_sum = coverage_claims.groupby("APPLICATION_ID")["NET_INCURRED"].sum()
        # Handle division by zero for loss ratio calculation
        safe_coverage_premium = coverage_premium.where(coverage_premium > 0, pd.NA)
        loss_ratio = ((net_incurred_sum / safe_coverage_premium) * 100).fillna(0).round(2)

        at_fault_claims = (
            coverage_claims[coverage_claims["NIRVANA_CLAIM_TYPE"] != "NAF"].groupby("APPLICATION_ID")[loss_col].sum()
        )

        premium_divisor = 10000 if coverage_code == "AL" else 1000
        # Handle division by zero for at-fault frequency calculation
        safe_premium_denominator = (coverage_premium / premium_divisor).where(
            (coverage_premium / premium_divisor) > 1e-10, pd.NA
        )
        # Calculate at-fault frequency, replacing any infinite values with NaN before filling with 0
        at_fault_freq = (
            ((at_fault_claims / safe_premium_denominator) * 100).replace([np.inf, -np.inf], pd.NA).fillna(0).round(2)
        )

        metrics = {
            "TOTAL_CLAIMS": coverage_claims.groupby("APPLICATION_ID")[loss_col]
            .sum()
            .reindex(coverage_premium.index, fill_value=0),
            "AT_FAULT_CLAIMS": at_fault_claims.reindex(coverage_premium.index, fill_value=0),
            "LARGE_CLAIMS": coverage_claims[coverage_claims["NET_INCURRED"] > 100000]
            .groupby("APPLICATION_ID")
            .size()
            .reindex(coverage_premium.index, fill_value=0),
            "OPEN_CLAIMS": coverage_claims[coverage_claims["CLAIM_STATUS"] == "OPEN"]
            .groupby("APPLICATION_ID")
            .size()
            .reindex(coverage_premium.index, fill_value=0),
            "NET_INCURRED_LR": (loss_ratio.astype(str) + "%").reindex(coverage_premium.index, fill_value="0%"),
            "AT_FAULT_FREQ": (at_fault_freq.astype(str) + "%").reindex(coverage_premium.index, fill_value="0%"),
        }
        metrics_df = pd.DataFrame(metrics)
        metrics_df = metrics_df.reset_index()
        return metrics_df

    metrics = {
        "auto_liability": metrics_for_coverage("AL"),
        "auto_physical_damage": metrics_for_coverage("APD"),
        "motor_truck_cargo": metrics_for_coverage("MTC"),
    }
    return metrics


def load_all_data(dot_number: str) -> Optional[Dict[str, Any]]:
    """
    Load all required data for the renewals dashboard.

    Parameters
    ----------
    dot_number : str
        DOT number to load data for

    Returns
    -------
    dict or None
        Dictionary containing all loaded data, or None if loading failed
    """
    # Load Overview Data
    overview = load_overview_data(dot_number)
    if overview is None or (isinstance(overview, pd.DataFrame) and overview.empty):
        return None

    # Get the latest application ID
    max_date_filter = overview["EFFECTIVE_DATE"] == overview["EFFECTIVE_DATE"].max()
    filtered_overview = overview[max_date_filter]

    if filtered_overview.empty:
        st.error("No records found with valid effective date.")
        return None

    application_id = filtered_overview["APPLICATION_ID"].iloc[0]

    application_ids = overview.get("APPLICATION_ID").tolist()

    # Add safety platform link
    overview["SAFETY_PLATFORM_LINK"] = f"https://safety.nirvanatech.com/{dot_number}/overview"

    # Add opportunity link
    overview["OPPORTUNITY_LINK"] = load_opportunity_link(application_id)

    handle_id = overview["TSP_CONNECTION_HANDLE_ID"].iloc[0]

    # Check if handle_id is valid before using it in queries
    if pd.isna(handle_id):
        hazard_distance_data = None
        hazard_duration_data = None
    else:
        hazard_distance_data = load_pct_hazard_distance(handle_id)
        hazard_duration_data = load_pct_hazard_duration(handle_id)

    # Load all required data
    data = {
        "overview": overview,
        "fleet_intervention": load_fleet_intervention(dot_number, overview),
        "trs_data": load_trs_data(dot_number),
        "score_to_segment": None,  # Will be updated after loading TRS data
        "claims_data": load_claims_data(application_ids),
        "earned_premium": load_earned_premium(application_ids),
        "dot_details": load_dot_details(dot_number),
        "hazard_distance": hazard_distance_data,
        "hazard_duration": hazard_duration_data,
    }

    # Update score_to_segment if TRS data is available
    if data["trs_data"] is not None and not data["trs_data"].empty:
        score_version = data["trs_data"]["SCORE_VERSION"].iloc[0]
        data["score_to_segment"] = load_score_to_segment(score_version)

    # Compute insurance metrics and add to data
    data["insurance_metrics"] = compute_insurance_metrics(data["claims_data"], data["earned_premium"])

    return data


def load_overview_data(dot_number: str) -> Optional[pd.DataFrame]:
    """
    Load overview data for the given DOT number.

    Parameters
    ----------
    dot_number : str
        DOT number to load overview data for

    Returns
    -------
    pd.DataFrame or None
        Overview data DataFrame, or None if loading failed
    """
    try:
        overview = fetch_from_snowflake(GET_OVERVIEW_DATA_QUERY.format(dot_number))
        if overview is None:
            st.error(
                f"Failed to load overview data for DOT number {dot_number}. Please check if the DOT number is correct and try again."
            )
            return None
        if isinstance(overview, pd.DataFrame) and overview.empty:
            st.error(f"No data found for DOT number {dot_number}. Please verify the DOT number and try again.")
            return None
        return overview
    except Exception as e:
        st.error(f"An unexpected error occurred while loading overview data: {str(e)}")
        return None


def load_fleet_intervention(dot_number: str, overview: pd.DataFrame) -> Optional[pd.DataFrame]:
    """
    Load fleet intervention data and update overview with fleet grade.

    Parameters
    ----------
    dot_number : str
        DOT number to load fleet intervention data for
    overview : pd.DataFrame
        Overview DataFrame to update with fleet grade

    Returns
    -------
    pd.DataFrame or None
        Fleet intervention data DataFrame
    """
    fleet_intervention_data = fetch_from_snowflake(FLEET_INTERVENTION_QUERY.format(dot_number))
    if fleet_intervention_data is not None and not fleet_intervention_data.empty:
        overview["FLEET_GRADE"] = fleet_intervention_data.get("GRADE").iloc[0]
    return fleet_intervention_data


def load_opportunity_link(application_id: str) -> Optional[str]:
    """
    Load external IDs and update overview with opportunity link.

    Parameters
    ----------
    application_id : str
        Application ID to load opportunity link for

    Returns
    -------
    str or None
        Opportunity link URL or None if not found
    """
    external_ids = fetch_from_snowflake(
        RENEWAL_GET_OPPORTUNITY_ID_QUERY.format(application_id), "ANALYTICS_DB", "AIRBYTE_PG_PUBLIC"
    )

    if external_ids is not None and not external_ids.empty:
        try:
            external_data = external_ids.get("EXTERNAL_DATA").iloc[0]
            if external_data is not None:
                parsed_data = json.loads(external_data)
                opp_id = parsed_data.get("Id")
                if opp_id:
                    return f"https://nirvanatech.lightning.force.com/lightning/r/Opportunity/{opp_id}/view"
        except (json.JSONDecodeError, TypeError, IndexError, AttributeError) as e:
            # Log the error if needed, but continue gracefully
            st.warning(f"Failed to parse opportunity link data: {str(e)}")
            return None

    return None


def load_trs_data(dot_number: str) -> Optional[pd.DataFrame]:
    """
    Load TRS (Total Risk Score) data over time.

    Parameters
    ----------
    dot_number : str
        DOT number to load TRS data for

    Returns
    -------
    pd.DataFrame or None
        TRS data DataFrame with processed dates, or None if loading failed
    """
    trs_over_time_data = fetch_from_snowflake(TRS_OVERTIME_QUERY.format(dot_number))
    if trs_over_time_data is None or trs_over_time_data.empty:
        st.error("Failed to load TRS data. Stopping further data loading.")
        return None
    trs_over_time_data["START_DATE"] = pd.to_datetime(trs_over_time_data["START_DATE"])
    return trs_over_time_data


def load_score_to_segment(score_version: str) -> Optional[pd.DataFrame]:
    """
    Load score to segment mapping based on score version.

    Parameters
    ----------
    score_version : str
        Score version to load mapping for

    Returns
    -------
    pd.DataFrame or None
        Score to segment mapping DataFrame
    """
    return fetch_from_snowflake(GET_RUBRIC_QUERY.format(score_version))


def load_claims_data(application_ids: List[str]) -> Optional[pd.DataFrame]:
    """
    Load claims data for the given application IDs.

    Parameters
    ----------
    application_ids : list
        List of application IDs to load claims data for

    Returns
    -------
    pd.DataFrame or None
        Claims data DataFrame
    """
    if not application_ids:
        st.error("Failed to load application IDs. Cannot fetch claims data.")
        return None

    formatted_ids = [f"'{id}'" for id in application_ids]
    ids_string = ", ".join(formatted_ids)
    return fetch_from_snowflake(GET_CLAIMS_QUERY.format(ids_string))


def load_earned_premium(application_ids: List[str]) -> Optional[pd.DataFrame]:
    """
    Load and process earned premium data for the given application IDs.

    Parameters
    ----------
    application_ids : list
        List of application IDs to load earned premium data for

    Returns
    -------
    pd.DataFrame or None
        Processed earned premium data DataFrame
    """
    if not application_ids:
        st.error("Failed to load application IDs. Cannot fetch earned premium data.")
        return None

    formatted_ids = [f"'{id}'" for id in application_ids]
    ids_string = ", ".join(formatted_ids)

    # Load raw data from Snowflake
    raw_data = fetch_from_snowflake(EARNED_PREMIUM_QUERY.format(ids_string))

    if raw_data is None or raw_data.empty:
        return None

    # Process the data
    processed_data = raw_data.copy()

    # Format dates
    if "BILLED_MONTH" in processed_data.columns:
        processed_data["BILLED_MONTH"] = pd.to_datetime(processed_data["BILLED_MONTH"]).dt.strftime("%Y-%m")

    if "EVALUATED_AT" in processed_data.columns:
        processed_data["EVALUATED_AT"] = pd.to_datetime(processed_data["EVALUATED_AT"]).dt.strftime("%Y-%m-%d")

    processed_data["AMOUNT_IN_NUMBERS"] = processed_data["AMOUNT"]
    # Format currency
    if "AMOUNT" in processed_data.columns:
        processed_data["AMOUNT"] = processed_data["AMOUNT"].apply(lambda x: f"${x:,.2f}" if pd.notnull(x) else "")

    return processed_data


def load_dot_details(dot_number: str) -> Optional[pd.DataFrame]:
    """
    Load DOT details for the given DOT number.

    Parameters
    ----------
    dot_number : str
        DOT number to load details for

    Returns
    -------
    pd.DataFrame or None
        DOT details DataFrame
    """
    return fetch_from_snowflake(RENEWAL_GET_DOT_DETAILS_QUERY.format(dot_number))


def load_pct_hazard_distance(handle_id: str) -> Optional[pd.DataFrame]:
    """
    Load PCT Hazard Distance data.

    Parameters
    ----------
    handle_id : str
        Handle ID to load hazard distance data for

    Returns
    -------
    pd.DataFrame or None
        PCT Hazard Distance data DataFrame
    """
    return fetch_from_snowflake(PCT_HAZARD_DISTANCE_QUERY.format(handle_id), "PUBLIC")


def load_pct_hazard_duration(handle_id: str) -> Optional[pd.DataFrame]:
    """
    Load PCT Hazard Duration data.

    Parameters
    ----------
    handle_id : str
        Handle ID to load hazard duration data for

    Returns
    -------
    pd.DataFrame or None
        PCT Hazard Duration data DataFrame
    """
    d = fetch_from_snowflake(PCT_HAZARD_DURATION_QUERY.format(handle_id), "PUBLIC")
    return d
