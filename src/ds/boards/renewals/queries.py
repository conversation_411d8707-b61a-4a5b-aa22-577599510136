TRS_OVERTIME_QUERY = """
WITH latest_connection AS (
    -- Only select needed columns and apply filters early
    SELECT 
        APPLICATION_ID,
        DOT_NUMBER,
        TSP_CONNECTION_HANDLE_ID
    FROM opportunities_funnel.gold_standard_applications
    WHERE DOT_NUMBER = {}
        AND STATE = 'PolicyCreated'
        AND TSP_CONNECTION_HANDLE_ID IS NOT NULL
    QUALIFY ROW_NUMBER() OVER (PARTITION BY DOT_NUMBER ORDER BY EFFECTIVE_DATE DESC) = 1
),
scored_data AS (
    -- Explicitly list needed columns instead of using frs.*
    SELECT 
        lc.APPLICATION_ID,
        lc.DOT_NUMBER,
        frs.last_modified_at,
        frs.start_date,
        frs.end_date,
        frs.score_type,
        frs.version,
        frs.score,
        frs.vin_count,
        drv.DEFAULT_RUBRIC_VERSION,
        CASE 
            WHEN frs.score_type = 'TRS' AND frs.version = '4.0' THEN 1
            WHEN frs.score_type = 'GPS Only 10-sec TRS' AND frs.version = '1.0' THEN 2
            WHEN frs.score_type = 'GPS Only 1-min TRS' AND frs.version = '3.1' THEN 3
            ELSE 4
        END as score_priority
    FROM latest_connection lc
    INNER JOIN ds.fleet_risk_scores_unique frs  -- Using INNER JOIN instead of JOIN for clarity
        ON lc.TSP_CONNECTION_HANDLE_ID = frs.connection_id
    LEFT JOIN ds.Default_Rubric_Per_Score_Type_Version drv
        ON frs.score_type = drv.SCORE_TYPE
        AND TRIM(frs.version, '.0') = CAST(drv.VERSION AS VARCHAR)
        AND drv.APPLICATION_SOURCE = 'Fleet'
)
SELECT 
    sd.application_id,
    sd.dot_number,
    sd.last_modified_at,
    sd.start_date,
    sd.end_date,
    sd.score_type,
    sd.version,
    sd.score,
    sr.SEGMENT,
    sr.version as score_version,
    sd.vin_count,
FROM scored_data sd
LEFT JOIN ds.score_rubrics sr
    ON sr.VERSION = sd.DEFAULT_RUBRIC_VERSION
    AND sd.score >= sr.LO 
    AND sd.score < sr.HI
QUALIFY ROW_NUMBER() OVER (
    PARTITION BY sd.start_date 
    ORDER BY sd.score_priority
) = 1
ORDER BY sd.start_date DESC;
"""

FLEET_INTERVENTION_QUERY = """
select dot_number, opportunity_name, grade from public.fleet_intervention where dot_number = {}
"""

GET_RUBRIC_QUERY = """
select * from analytics_prod.ds.score_rubrics where version = '{}'
"""

EARNED_PREMIUM_QUERY = """
select policy_number, coverage, amount, billed_month, evaluated_at, application_id
from analytics_prod.risk_metrics.earned_premium 
where application_id IN ({})
"""

GET_CLAIMS_QUERY = """
WITH claims_data AS (
    SELECT 
        claim_number,
        coverage_type,
        loss_at AS accident_date,
        YEAR(TRY_TO_DATE(policy_effective_date)) AS policy_year,
        nirvana_claim_type,
        net_incurred,
        claim_status,
        policy_number,
        LEFT(policy_number, 5) AS policy_prefix,
        accident_code,
        total_indemnity_paid,
        total_expense_paid,
        total_indemnity_reserve,
        total_expense_reserve,
        total_recovery,
        loss_description,
        loss_al_count,
        loss_apd_count,
        loss_mtc_count,
        application_id,
    FROM analytics_prod.nars.claims_master_tracker_v2 
    WHERE application_id IN ({})
)

SELECT 
    ROW_NUMBER() OVER (ORDER BY accident_date) AS row_num,
    *
FROM claims_data
ORDER BY accident_date;
"""

GET_OVERVIEW_DATA_QUERY = """
WITH actual_mileage AS (
    SELECT 
        application_id,
        SUM(mileage) as total_actual_mileage
    FROM analytics_prod.risk_metrics.application_exposures
    GROUP BY application_id
)

SELECT 
    g.APPLICATION_ID,
    g.ACCOUNT_NAME,
    g.DOT_NUMBER,
    g.EFFECTIVE_DATE,
    DATEADD(year, 1, g.EFFECTIVE_DATE) as EXPIRATION_DATE,
    YEAR(g.EFFECTIVE_DATE) as Effective_Year,
    g.US_STATE,
    g.STATE,
    g.APPETITE_SCORE,
    g.RISK_SCORE_AT_REVIEW,
    g.RISK_SCORE_BUCKET_AT_REVIEW,
    g.POWER_UNITS as PU_Count,
    g.VIN_VISIBILITY_PERCENTAGE,
    g.PROJECTED_MILEAGE,
    a.total_actual_mileage as Actual_Mileage,
    CASE 
        WHEN g.PROJECTED_MILEAGE = 0 THEN NULL 
        ELSE (a.total_actual_mileage / g.PROJECTED_MILEAGE) * 100 
    END as Actual_vs_Projected_Mileage_Pct,
    g.LIAB_RATE * 100 AS liab_rate_per_100_miles,
    g.liab_premium,
    g.liab_premium / (g.POWER_UNITS) as liab_price_per_pu,
    g.liab_deductible,
    1000000 as liab_limit,
    b.tiv as TIV,
    g.phys_premium,
    (g.phys_premium / b.tiv) * 100 as phy_rate_per_tiv,
    g.phys_premium / (g.POWER_UNITS) as phys_price_per_pu,
    b.phys_deductible,
    (b.mtc_premium/b.projected_mileage) * 100 as mtc_rate_per_100_miles,
    g.mtc_premium / (g.POWER_UNITS) as mtc_price_per_pu,
    g.mtc_premium,
    b.mtc_deductible,
    b.mtc_coverage_limit,
    g.TSP_CONNECTION_HANDLE_ID,
FROM opportunities_funnel.gold_standard_applications g
LEFT JOIN actual_mileage a 
    ON g.application_id = a.application_id
LEFT JOIN analytics_prod.opportunities_funnel.bindable_submission_indications b
    ON g.bindable_submission_id = b.bindable_submission_id
WHERE g.DOT_NUMBER = {}
    AND g.STATE = 'PolicyCreated'
ORDER BY g.EFFECTIVE_DATE DESC;
"""

# TODO - Add renewal or new application flag
RENEWAL_GET_OPPORTUNITY_ID_QUERY = """
select * from pg_external_ids where request_id = '{}' and EXTERNAL_ID_TYPE = 'Salesforce' AND PROGRAM_TYPE = 'ProgramType_Fleet' LIMIT 1;
"""

RENEWAL_GET_DOT_DETAILS_QUERY = """
select census_rating, census_rating_date, * from analytics_prod.core.fmcsa_dot_details where dot_number = {}
"""

PCT_HAZARD_DISTANCE_QUERY = """
SELECT * FROM analytics_prod.public.pct_hazard_distance where HANDLE_ID = '{}';
"""

PCT_HAZARD_DURATION_QUERY = """
SELECT * FROM analytics_prod.public.pct_hazard_duration where HANDLE_ID = '{}';
"""
