from enum import Enum


class SupportedTSPs(str, Enum):

    # Supported TSPs in Nirvana Risk Score models
    SAMSARA = "Samsara"
    KEEPTRUCKIN = "KeepTruckin"
    GEOTAB = "Geotab"
    VERIZONCONNECTFLEET = "VerizonConnectFleet"
    VERIZONCONNECT = "VerizonConnect"
    ZONAR = "Zonar"
    FLEETCOMPLETE = "FleetComplete"
    BIGROAD = "BigRoad"
    VERIZONCONNECTREVEAL = "VerizonConnectReveal"
    MASTERELD = "MasterELD"
    GPSTAB = "GPSTab"
    KSKELD = "KSKELD"
    MOUNTAINELD = "MountainELD"
    RIGHTTRUCKINGELD = "RightTruckingELD"
    ROADSTARELD = "RoadStarELD"
    TFMELD = "TFMELD"
    FMELD = "FMELD"
    EROAD = "EROAD"
    OMNITRACSXRS = "OmnitracsXRS"
    OMNITRACSES = "OmnitracsES"
    OPTIMAELD = "OptimaELD"
    XELD = "XELD"
    PRORIDEELD = "ProRideELD"
    REALELD = "RealELD"
    TTELD = "TTELD"
    TRANSFLO = "Transflo"
    GOFLEET = "GoFleet"
    GOGPS = "GoGPS"
    AGILISISLINXUP = "AgilisLinxup"
    ATANDTFLEET = "ATAndTFleet"
    ONTIMEELD = "OntimeELD"
    ATANDTFLEETCOMPLETE = "ATAndTFleetComplete"
    EVOELD = "EVOELD"
    IDELD = "IDELD"
    EAGLEWIRELESS = "EagleWireless"
    ONESTEPGPS = "OneStepGPS"
    SMARTELDS = "Smartelds"
    MAXELD = "MaxELD"
    ZIPPYELD = "ZippyELD"
    APEXULTIMA = "ApexUltima"
    BLUEHORSEELD = "BlueHorseELD"
    LIGHTANDTRAVELELD = "LightAndTravelELD"
    ORIENTELD = "OrientELD"
    ELDBOOKS = "ELDBooks"

    # Additional TSPs encountered at Nirvana
    TELETRACNAVMAN = "TeletracNavman"

    # Additional TSPs supported by Milliman but not supported by Nirvana
    AZUGA = "Azuga"
    WEBFLEET = "Webfleet"
    ZUBIE = "Zubie"
    TESLA = "Tesla"
    PLATFORMSCIENCE = "PlatformScience"
    LYTX = "Lytx"
    EQUIMENTSHARE = "EquimentShare"
    INTOUCHGPS = "InTouchGPS"
    PEOPLENET = "PeopleNet"
    MOJIO = "Mojio"

    def __str__(self) -> str:
        """
        When called, it returns a string representation of the object by converting the self.value attribute to a string.

        Returns
        -------
        str
            A string representation of the object.
        """
        return str(self.value)


class MillimanShadowTSPMap:
    SHADOW_TSP_MAP = {
        "FleetComplete": "Geotab",
        "Zonar": "Geotab",
        "BigRoad": "Geotab",
        "VerizonConnectFleet": "VerizonConnect",
        "VerizonConnectReveal": "VerizonConnect",
        "MasterELD": "Geotab",
        "MountainELD": "Geotab",
        "GPSTab": "Geotab",
        "KSKELD": "Geotab",
        "RightTruckingELD": "Geotab",
        "RoadStarELD": "Geotab",
        "TFMELD": "Geotab",
        "FMELD": "Geotab",
        "EROAD": "Geotab",
        "OmnitracsXRS": "Geotab",
        "OmnitracsES": "Geotab",
        "OptimaELD": "Geotab",
        "XELD": "Geotab",
        "ProRideELD": "Geotab",
        "RealELD": "Geotab",
        "TTELD": "Geotab",
        "Transflo": "Geotab",
        "GoFleet": "Geotab",
        "GoGPS": "Geotab",
        "AgilisLinxup": "Geotab",
        "ATAndTFleet": "Geotab",
        "OntimeELD": "Geotab",
        "ATAndTFleetComplete": "Geotab",
        "EVOELD": "Geotab",
        "IDELD": "Geotab",
        "EAGLEWIRELESS": "Geotab",
        "OneStepGPS": "OneStepGPS",
        "Smartelds": "Geotab",
        "MaxELD": "Geotab",
        "ZippyELD": "Geotab",
        "ApexUltima": "Geotab",
        "BlueHorseELD": "Geotab",
        "LightAndTravelELD": "Geotab",
        "OrientELD": "Geotab",
        "ELDBooks": "Geotab"
    }


class MillimanShadowTSP(str, Enum):
    FLEETCOMPLETE = "FleetComplete"
    ZONAR = "Zonar"
    BIGROAD= "BigRoad"
    VERIZONCONNECTFLEET= "VerizonConnectFleet"
    VERIZONCONNECTREVEAL= "VerizonConnectReveal"
    MASTERELD = "MasterELD"
    GPSTAB = "GPSTab"
    KSKELD = "KSKELD"
    MOUNTAINELD = "MountainELD"
    RIGHTTRUCKINGELD = "RightTruckingELD"
    ROADSTARELD = "RoadStarELD"
    TFMELD = "TFMELD"
    FMELD = "FMELD"
    EROAD = "EROAD"
    OMNITRACSXRS = "OmnitracsXRS"
    OMNITRACSES = "OmnitracsES"
    OPTIMAELD = "OptimaELD"
    XELD = "XELD"
    PRORIDEELD = "ProRideELD"
    REALELD = "RealELD"
    TTELD = "TTELD"
    TRANSFLO = "Transflo"
    GOFLEET = "GoFleet"
    GOGPS = "GoGPS"
    AGILISISLINXUP = "AgilisLinxup"
    ATANDTFLEET = "ATAndTFleet"
    ATANDTFLEETCOMPLETE = "ATAndTFleetComplete"
    EVOELD = "EVOELD"
    ONTIMEELD = "OntimeELD"
    IDELD = "IDELD"
    EAGLEWIRELESS = "EagleWireless"
    ONESTEPGPS = "OneStepGPS"
    SMARTELDS = "Smartelds"
    MAXELD = "MaxELD"
    ZIPPYELD = "ZippyELD"
    APEXULTIMA = "ApexUltima"
    BLUEHORSEELD = "BlueHorseELD"
    LIGHTANDTRAVELELD = "LightAndTravelELD"
    ORIENTELD = "OrientELD"
    ELDBOOKS = "ELDBooks"

    def __str__(self) -> str:
        return str(self.value)

    def get_shadow_tsp(self) -> str:
        return MillimanShadowTSPMap.SHADOW_TSP_MAP[str(self.value)]

class TRSSupportedTSPs(str, Enum):
    # Supported TSPs in Nirvana Risk Score models
    ORIENTELD = "OrientELD"
    SAMSARA = "Samsara"
    KEEPTRUCKIN = "KeepTruckin"
    GEOTAB = "Geotab"
    VERIZONCONNECTFLEET = "VerizonConnectFleet"
    VERIZONCONNECT = "VerizonConnect"
    ZONAR = "Zonar"
    FLEETCOMPLETE = "FleetComplete"
    BIGROAD = "BigRoad"
    VERIZONCONNECTREVEAL = "VerizonConnectReveal"
    MASTERELD = "MasterELD"
    GPSTAB = "GPSTab"
    KSKELD = "KSKELD"
    MOUNTAINELD = "MountainELD"
    RIGHTTRUCKINGELD = "RightTruckingELD"
    ROADSTARELD = "RoadStarELD"
    TFMELD = "TFMELD"
    FMELD = "FMELD"
    EROAD = "EROAD"
    OMNITRACSXRS = "OmnitracsXRS"
    OMNITRACSES = "OmnitracsES"
    OPTIMAELD = "OptimaELD"
    XELD = "XELD"
    PRORIDEELD = "ProRideELD"
    REALELD = "RealELD"
    TTELD = "TTELD"
    TRANSFLO = "Transflo"
    GOFLEET = "GoFleet"
    GOGPS = "GoGPS"
    AGILISISLINXUP = "AgilisLinxup"
    ATANDTFLEET = "ATAndTFleet"
    ONTIMEELD = "OntimeELD"
    ATANDTFLEETCOMPLETE = "ATAndTFleetComplete"
    EVOELD = "EVOELD"
    IDELD = "IDELD"
    EAGLEWIRELESS = "EagleWireless"
    ONESTEPGPS = "OneStepGPS"
    SMARTELDS = "Smartelds"
    MAXELD = "MaxELD"
    ZIPPYELD = "ZippyELD"
    APEXULTIMA = "ApexUltima"
    BLUEHORSEELD = "BlueHorseELD"
    LIGHTANDTRAVELELD = "LightAndTravelELD"
    ELDBOOKS = "ELDBooks"
