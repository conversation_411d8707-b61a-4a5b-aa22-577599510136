{"//": {"metadata": {"backend": "s3", "overrides": {"stack": ["terraform"]}, "stackName": "default-production-coreinfra-bastion-us-east-2", "version": "0.20.10"}, "outputs": {}}, "data": {"aws_ami": {"ami": {"//": {"metadata": {"path": "default-production-coreinfra-bastion-us-east-2/ami", "uniqueId": "ami"}}, "filter": [{"name": "name", "values": ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-********"]}, {"name": "virtualization-type", "values": ["hvm"]}], "most_recent": true, "owners": ["************"]}}, "terraform_remote_state": {"cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2": {"backend": "s3", "config": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/production/default-production-coreinfra-legacy-network-us-east-2.json", "region": "us-east-2"}, "workspace": "${terraform.workspace}"}}}, "provider": {"aws": [{"allowed_account_ids": ["************"], "default_tags": [{"tags": {"environment": "production", "group": "coreinfra", "infraWorkspace": "default", "region": "us-east-2", "stackName": "bastion"}}], "region": "us-east-2"}]}, "resource": {"aws_ebs_volume": {"home_volume": {"//": {"metadata": {"path": "default-production-coreinfra-bastion-us-east-2/home_volume", "uniqueId": "home_volume"}}, "availability_zone": "${element([data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-dataaws_availability_zonesavailability_zonesnames[\"0\"], data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-dataaws_availability_zonesavailability_zonesnames[\"1\"], data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-dataaws_availability_zonesavailability_zonesnames[\"2\"]], 0)}", "size": 200, "tags": {"Name": "prod-production-bastion-home-volume"}}}, "aws_eip": {"elastic_ip": {"//": {"metadata": {"path": "default-production-coreinfra-bastion-us-east-2/elastic_ip", "uniqueId": "elastic_ip"}}, "instance": "${aws_instance.instance.id}", "tags": {"Name": "prod-production-bastion"}}}, "aws_instance": {"instance": {"//": {"metadata": {"path": "default-production-coreinfra-bastion-us-east-2/instance", "uniqueId": "instance"}}, "ami": "${data.aws_ami.ami.id}", "hibernation": false, "instance_type": "t3.2xlarge", "key_name": "${aws_key_pair.ssh_key_pair.key_name}", "root_block_device": {"volume_size": 50}, "subnet_id": "${element([data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_aid, data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_bid, data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_subnetpublic_subnet_cid], 0)}", "tags": {"Name": "prod-production-bastion"}, "user_data": "#! /bin/bash\n# Fail fast\nset -euo pipefail\n\n####### Setup home mount #########\n##################################\n\n# This is potentially fragile. If SSH fails, logon to EC2 console and connect to the instance,\n# then look at /var/log/syslog (cloud-init logs) to debug\n\n# Wait for a minute for the home volume to be mounted\nsleep 60\n\nDEVICE=/dev/nvme1n1\nFS_TYPE=$(file -s $DEVICE | awk '{print $2}')\nMOUNT_POINT=/home\n\n# If no FS, then this output contains \"data\"\nif [ \"$FS_TYPE\" = \"data\" ]\nthen\n    echo \"Creating file system on $DEVICE\"\n    mkfs -t ext4 $DEVICE\nfi\n\nmkdir -p $MOUNT_POINT\nmount $DEVICE $MOUNT_POINT\nchown -R ubuntu:ubuntu $MOUNT_POINT\n\nSSH_CONFIG_FILE=/etc/ssh/sshd_config.d/nirvana_sshd.conf\ntest -f $SSH_CONFIG_FILE && sudo rm -f $SSH_CONFIG_FILE\ncat <<EOT | sudo tee $SSH_CONFIG_FILE\nAcceptEnv NIRVANA_EMAIL\nEOT\n\n##### CUSTOM CRON #########\n###########################\nCUSTOM_CRON_PATH=/home/<USER>/cronschedule\n\n# create custom config file if it doesn't exist\nif [[ ! -e $CUSTOM_CRON_PATH ]]; then\n    echo \"*/5 * * * * crontab $CUSTOM_CRON_PATH\" >> $CUSTOM_CRON_PATH\n    echo \"# Add custom cron configurations below this line\" >> $CUSTOM_CRON_PATH\nfi\n\n# register\ncrontab $CUSTOM_CRON_PATH\n\n\n####### Add public keys ##########\n##################################\necho \"Adding public keys\"\n# Always recreate the ssh config directory to have access control driven thru cloud-init\nrm -rf /home/<USER>/.ssh\nmkdir -p /home/<USER>/.ssh/\n{\n    # Abhay\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIKgYy5u/dQjJz8o6bnvtRkiL0fm13YKL13oHkccLDRaY <EMAIL>\"\n    # Sanket\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDIoF0vPrvnV1KpzpVO4dt4B+kFrHpZW0fynNucnhYtJ+SQ83K4V09mnZHtgWos0rx2IHyeak34hhXz2KaBiH5J71t2IFA18kmyyueN8in5oGD4PayFMHXWv5YTyV/p10Y2k2Gx2WHTjttHpjGrqhS+MlzwFnLf6U/CvLtQgU5If9aO7PX5X9pREamwrlbuEC0iJFP1FA1ouBKMJ+uQx0bwmAnL+oyAoBm60ZI1Pv5vS3daK9EEsm6Q1fv5JZQVMveSvAbP+4puCuGLJHxhfPQi4WrHkfpN6KXuIV1hXdJnDK6I7okUh8wcBTTNzM9In6nTlq+QRzj+lY0gWoCMZTp/ <EMAIL>\"\n    # Francisco\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCsKCgcxp9l+dk0WTanaFTjvYG3yfutPDZQe8Rh8AXKxjBHVsBPS+MSIynnZH9xBj310SLc9D3dmfJL2+9BxsSXnYYZRUfVolr7olAklJQ75YkL3Y6C4xFJCC+9ZYTxKzQLLbw4CRVsbknQnCvZpckL6PPMuwxpZKLyhX8BwZu8JKicncaJnV8AoJ+BB+awogeHsK1hZ1JY++wNqVbPDCClevhBMZQTObDXb+4Ro3u4aYsgYZM85Xx2HkUCi+F/iP+c3De5URxqb7mWkFMvmq4mTd2Zu4LVPf92Orgidb4RBpyKETHv7adhnEbDo85ifk6lRhRT2S69ktFuJ9ekZJPjY7yOcFNw4JnqhRldFwbVc7b4RP6fkN2UQkucpP4gJIP2sru8/KqAge8VybKlJFA/Y/E6QM0nHGOHFBUIJfU/pM1ZYA9gkogARlgzq5JUJbn7vMvxCEllJ9aSgs3neHRO1r8ujnQsyMQiGKmC9FdMq3icwToKgYjnTf8cxT8RJyU= francisco\"\n    # Nishant\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDQ/S6OkJExsJup+PScWz/r2ObpZV4OAt2XJAlkXYOOsr7jiJ0fOn6dtXKN31kTrtCt4nFwNF8pBWJnXMpLnAIHo7A94BE+dAH6mzaTomWGl30/U/jo4KlFmFvFWEEYKBRaD2LaAfanH0Ih8OOlNx+N00kmVvZWo6kJLwlg2QYCDbpxj9WQLw/ygQDG0JP1QtJkbtnJcXJp/cZgpIx+NKXgtKjzjqqgElZOws+WXbJ8Wp3tsahdGcuThjNLDqXVgXeMWMNgWTP8DFYePTc2s7Y4zmkBFYqlMvb+sBII9Qj5w/YEy75COIHbKpCUdGURuRIEV41FF1je3cYE3Ol5rBfb9uiCfLljAa5WbCWDIJkzno8B50zEAdB+0kB+5EX7NbmiqJO/XUQD4SGAHqHRDNCvItEk49qb1/XOp3kEp40zzZFHs6LEsiHDo4jZvGv/G20LfuWY/lrYMr9/MB/9PcVmpwokxetUgZ6ZSUeYoNQN7CBYHAJZpNkDD1fVglRqv4Os2hLXEoCsgg3a5ADLK1xu43N+j2hgnadu4adGEHmV+bbAM/L153ofL/SeuUwIFOjR11KcScB/lqhzqCJXMSdXNoYlPDCMU2ywasz0mh+3VOBOifWwQzRzqo9pDMZ/eegqROphXRxG6jmiYC8JCeHThn0f48r80MKAxNoBKu2SkQ== <EMAIL>\"\n    # Shashank\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCr/fteybYzxgNii6q9gucaNUp4SsxK6YdM/CTe1GBx1z5QhvID+VCgOcQPVC+pkpWiWH5hWDsLa0LDi9bSKQoopm4KeZlWo6nbr2pIdGNOce3+1xiuNodfr1bGwEI+s6gFlUY8Ztx/Z6/2KY7VBj2WjtKaU+h8ERmthXvsz0QQb0+IrJBpMCyiczbnxK/ltnxbTHBbumAiepDKG3CK4WSlGXPR75eouRLjucSM765fyWzMy7D3VOKZnSQYqqv6t/LlgB5/YQ6693E3G4DvFsIgBU45KPOPKVFk7qN9190PW1xsCHpb27a1kXtjecurRJ6IuUkar2idLIXr0QtzHK6s6HMpYK+1rOMhw1aZMNB6LgPUzqrvRtTrWbZwbZrNyS2VoSbCSoTPFse0eryU5Yd3uopdadtsBelfGq6QR/67MThIwE9scKkrHcR+Wg1VS0qPRgf8ZMsujyfprgHUOCXKbnnZN7BSPIJj4HgXv7LmaRCKWQ//6p/pOSdSXeHN6FeVXry/a69yvf6Nk8PmryI+6ottiE+mRDtLanADvIQL4hWt26DwO2UksvcOugBMpEMHOhrhM/S8gTxQywQ7vEIppB0GnnebHAbdaH4XqYiw2X9avpnJ+7HRN5ncSY8BN3z9HWx+9SFO/Luh5ALS/jgYv6Dd4jux2Jt3+f2HII3bHQ== <EMAIL>\"\n    # Greg\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINXgZFnoWOazWMJTB3NfQOsumd0wISC6eJLn0eAR3Hiu <EMAIL>\"\n    # Sarthak\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDDH8iQKC25cCpaishXrNVQLO7TGUY9IXqFYK1osOiGNwUv0tDGsV5DmeogK7UhizzD5z/5TnesdqH28YYJpZaZ6+zkND2golVEUgBb+RelrIwtnT4IQBaV7l+wi/m5pKlXC45SeaWNStiBOgs258B9hvcaQksV6wq7CBOPZKWDZteo13ehBOLXL+W5z0PdPOc6xvLZV8p3XszgUP+H/pRWlx1O9RTC2zS9ENXJ+d0Vzz0DholK0JDrhtNM3gqFAm4VYA0PfIpnXB0PQM62p01UCIBaWsZo51zOAZVcVECnNZkg5wPbPzkM7qOgtZw7Ma3iOsessFTENAjyjNYneVgE+9uX5XxL4R1FGoxXHzhWzkoS3tje44dZY2MyIyej3JjhhYyTSrcvVZPQ43KSy5/8xf4mKa5v3HupSJTkp34ewn24bFBmfP8kNz0eGEsX27ai2aGEi9WGl7IVs0zxs6YWDEvrjkkyBVfLRSLLtU+dim9/FMbf0Fp7K62+zneYr+GLzyrsI2sLUq2a3NGyHjBvft5CHhPIDBW1hmCppvYNu19ULgSjhoFzS1rHhhnm4W9ySdb/aofcbsYjVjBBizhGtLdzIaDpww+hMIXRVqN5tHntN+wOzui10d2Mm8X4AdlBo2Yx3TLUqdsZSVFa1uBjX+7nmyc6cX5h/pN2wKSpLQ== <EMAIL>\"\n    # Anand\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQClnPJ8qbqO8PuGvicsI4sLN0fh11rzsdH6qcy1gYg8ru4qYhgHDTb/09mz61MlsjgssNSCGqFxhSVU2lnYzKTeRDbfVPqBWFazBXC5Iur0J8I9xiOpr2AWqlygnYcGO5muuScO3Lw/wAPp1aEYi9ky6o9pQs5+ZLOXSZvg/97DFwjAZ1DKNUfqLP4IzNLAgAZdXUwqdwif8NfgA8maCA27+AutdktZsIiG2E6NmJBzfvfHt3RHro8HbNG23F3Fu7pohv0TnxoEVmSQR/kkuuoTUzNWhnBLmQ4DekFYLOvn9hP1gAtrDa8KHl64CdS261WS6qRbUMTsyyImvBaCupiSCreCIgxWmwCmAYacGp11BM7YFECtqdY9LIevJO0XarwdQELKjV5ghYEroPztojeGEtefEnzB5Lfu7rf4hIHgigZxC7I1QxEEIqaU3apIoYVYon2Li1xymH4Ydc3NVIcAJcj3+xD3Fruw57fIPMwilKlh+xu4Fr2KZDsCSdWfWT0= <EMAIL>\"\n    # Jigar\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCietee+kpEczbaHn5QsSpgJ786PzmZ9TAo4tfrkKN+nbCJDEvB21gMfeeph3yRTYgORIGxPiyrzBFjG+ot0nzz0NGiIR2oli7ytgl+5LY+w8BCkypfj+vvngkjwDs/xCLxcP9j8aTlo3A+z8Z0e0U4T+RGDYPauEadLCQQhFugjEqCLk1Hw+XX3lP505X+uzkAOg21ANErqpQ3oGNcmm24iDNMLIs/gEDiYDqjlo6R5ctOMtTWw8LB1OFpZuSuTkjrC/Ic0vM9vZeHcedNGzvscrp7J5tznJ3PwvRlttuwbWUjgeK5mmuswzssAG3NykXgsGtiSrm7o16OxjA9tBya1MkdFOkNsX08kp7XZjVjhZmlc/ZGQNaoQ4msgEB1gH1V1/6pTToaGBy72cQ0E8AhhAOcPTqmQvAx2DfX4UZHTCqzVrw0xfJ7LLEH77BuGQmYADOopCS7V/i2BFRWuFmemGbOykbWJa6uIljg+SYV8A3DwNas6g2CDfU26gE+9Z0= <EMAIL>\"\n    # Nicolas\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBW0hFjb+k4miDICaHRYnUnpClaxgBNI2L3HNUCLFEyU <EMAIL>\"\n    # Vedant\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMM6RyMM4SdTK08THzm3zIicy1ypYAJyceLGO73o5Qjb <EMAIL>\"\n    # Josh\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC5uBroVBaGhvPRG87pOTNlaMw2qDNwUiy6cZ8GpcY75rhIiygbwTyc9ob+biMIk+0zcd1Oo0SC7btSOqFKjcPr/t+1cuUe/gC7pfQJ4gIGTKC7uz7cZ0iVBj4/XMptjm8/RE18+KX4ZvD/dWwFBdYIGmZnw+59ilUgUigkJx2Oanqc8ojsLvVlsAJe9Z2KolNSBLsHZktO2wkFGE6EoP9Htm2EfkE7sk3szC3TkYA2asg1PiYCJWJD9AkL9MdILeMuUf+h6/7MFyi2Wl4Yqvc/wWTXuO1zPna36DWvQX/KxzAnszIooknBkiY448sq7tdc8VsWTLDO800iaP19B398JvJJb1ANs7AeqWwC748WGrFcptOAw+oYsWz1/EcskSP7uXU+yZ+OGZVqe5jitRTRoS7PTYDO9rxxBI+zJjCfRhVHnvdPLUYkIdKIMqca7QETOc22GvYvSbAqxl9aLG8jcocaeJu+GdtXmVeHmdaZnuNikhlVi1fEn37G/IU2VO+XcL/+k+Hb7uYzJRvYUdbSaguJ/IF/pTRXlhtgo03IXMJNrg4p7a7rsYDm3rxNFvwf9CT/uKrX6C1C9bTJVB1XmN/d9o90F3jgX0suBwudHW4gYdSY0Ds5MD0tlm6PJ2ZvC2UfOR1e9SyPYDVchj9TpNfrSyDdnxSXO11vQpeWPw== <EMAIL>\"\n    # Himanshu\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC/dQx6ZRys52wyMYRBrpP8PfWqBr1Tt3F8mvT47s08MtUji4wbP+yxgGyFlC1ljk+T7pvNoAnE/QzSS+1dnxMmljTLmMA5jWK6jB60Cl/JLGIhqqdO874fC2m5aDjlOeZWvHV4wfNckNdmr5ZSPiDJZ32PbotrKUvJerZ+QSxgNkJEd7xhdwXbVoOv/AV+CjSq6zLGdWiCVgEWWNEmOrvonB5XxOY/xD5bVy43NZsdmhWGkbLXBbyNBz017HE9OXJzeEigmg68ygxlCeQrnD7RBmpVou4B4E5g0EG/NhikiEZjKk2YnYl2YAzvy1Q3G7njUaZvBDN4UVMp76UQ3/sn11G7kgzZoV+lHW2RlLDeAS6XAhE1u/gn41RsBWwzmIDfvIygeD3X9YSZz4HOs47flT8WheZ+9lfOQN82zKdAG+tyKGEEhUnJz3jPyXV0FBq1WaR0ixcxTgE6c2rxr+mj4hNVdpTn3zeszpKBykMzblPMH2ninC7QmX7TyLqAoh8= <EMAIL>\",\n    # Rodrigo\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAII6g0L4pmo/uNpPXZy3i1K1QPQaV/ehkOpCrw35Xs/31 <EMAIL>\"\n    # Alex\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIL/RPIRT9MQknN7kKGGpEj8oXupJXJdOhLFsDNFBCALK <EMAIL>\"\n    # Ayush\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIFcvNrY5l/5+U9vtyFbosRuBsCyx99Hs/7uwSqHVNM6x <EMAIL>\"\n    # Tushar\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOYdxIs7WrU1M9VAZaGgWewOJwHJFwZZMbRJzFAFuYKk <EMAIL>\"\n    # Abhi\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDOoYlG6d3Egxg5iCv7VWdFjZiu3F61nZzkmpgYrZJu2fYQi1KF3jFfyUMhn+av+0FyTOik2oBeDXR6XgtedWBxmJhUfL9h7CdOo9ewysHp57GLPdI/Q1tzHv3QZX/lfbDNazlqUn8ewXyc3CdLUWVBWhAPKy08mJSbc8c3XCKlUt9dSqhsr5aAoPQTIakS6M29nTWyZLml5TAqFsVa0yKrWEMRCsIDbk6A/d85+QYKKx+TVvOXmOTnCIiCnGTQ+VOGEL2Q99FVjdSeNuNVubqqiJbbSrq/VEhD9bJ3uACpZKbAzD+YcpE8cauLTahkqnCjDiibdJa9C92BQfezPKsPzZtiPpuBZEfQRfemOE6hOfeT/NlCaFTCF6Yn0yarVV6QBMgnhn8F6xZf3BWpr8YK7iwOW7M/N60oNMwR1DBHAmvvajON13UmWfq/F/B1YqPVgBZO2rpc+VA76qva2cnbdGHoIvD3ejrC1TUG9AGlPFYRLZwQpt3vc/wl6c2wtMLxIz8B7UJ2ERN/QGyC3a+1KRGSmWaGdwBdBlMtT7nB6zh4NEpCagvZEL0QEEtBsqCh1v4RpNNZDFANgbVio+pyiTJF9BvCCNiqh9+MrN5eB64N24LHGtbnNsHLaM8Glx5hrcve2QpmSoOQUFPVXN9a0UUMsNXNpgAHxrBEQiiikw== <EMAIL>\"\n    # Sebastian\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPRBUeLQzGC7ReJvOCfsobbKEmIQ1fF63ErERAIISeaC <EMAIL>\"\n    # Ayush Agarwal\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBHJJOSWTip2FV5AvFHoK8sRRdJ4ofo4klMD2JcJ4AhA <EMAIL>\"\n    # Saundarya\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIKdXchKY7H8KWPLIkXxmnBIpUYM2nJSAGAIeOVo8nJJW <EMAIL>\"\n    # Nicolas\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQD1hK4ivKCC6XQpojSGQlFVVqlO/IE0nB46H4O/9I4TENU0jB1+gxEO6PGkKJ19ISsgFhtTaUf35UrUEOBMMHnij8v+fuiaDclN6JQ06a+pFq4Uyp8RfADfXfcIQ6kGLvPYa+5U8S3NNqbNIoR1SKDM+2Zzr0jPqzUJgT88jototuNFR5ypiHfiz1ee7Q/aE5J1O1RsFmiN3wtsCUW6kKz12n1sGB8KUNaMjWM3s0tU+us1Db9Hv2BKwsW9QEv12U5Ts9f7dbjo+iNqiGaZ7gNznEj++9nU8Kq0vhwDYn8MF9BTuhbh7pJBCqlfKlVZSo5gJA60QslDDvcWCplI7wvQERqKJhc5DnBMidFHf/Fs4uIQN1G7qnLhWqd9Oke793Zdgy/6XpJJ7xX0dMw22NyyDttHtvvG1I3ozXcbwh/YIXw93MLrf2k8qeTpx2Qcrd447gtxRUaw8HDZT5CnHE77k1Lh9hAH0b3kWDoUZJMenVpmuPEyUQnADcXgB+E/tKE= <EMAIL>\"\n    # Sahar\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQCyqShzLNcxnP6e2Vuv6yGhKxQUXv7r4GzmxQNyGAXoatinQD1Q3a1W94R7k+pfQvlIxSd79d2HVAOd7ki6vyX3ZC/7lj0OvrV+s5Z3/4wTsYOS/vY2QnUkKEDsUzq3DmFi2C4UNCvRHeAkXlqCgH7upJoxaczgx1j2l8CL1+EyXnzLyqNSsirNHWWEDkUeUyadVRKQkncE5PpXvnV8FjUCKZW0fe/KTO/vZ+sIHowowFcspykCrvf6855qAEYuNRUrsdpNO8nhmZxEL08jbGfG98ZO4yuRyCkRJPJxv21az0nBfmy++dBtDD8jVcPhjgWagKV8i/iriTdw/8AzzQpBsPYWzF35LLHB7GcbdyDXzAKuS57u5rXaq7OWSxHsf2TDDgH9VaJl1+avAHxVUbZeiiI/ctJBVM8Sz2dOHH7lnMf/R0VuCyL/LWbZxcMk2XVzDfjnuiqTJBKns9Z+k2/fR1yN6DK2esdJzz+miF8oZ0TFv04esk1s42hhwzSU3tc= saharvacnich@Sahars-Air\"\n    # Jitesh\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPMX3ZTJfM6AXnXh0dxjLZ75gII46ZQxhegZOpeGHU/2 <EMAIL>\"\n    # Sebastian Salata\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMEeHqcFWoI+73LWNNZ7bgrCeswXFB8TorqEYDSvYp6M <EMAIL>\"\n    # Aman Gupta\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIHqgSFjSuw18mrSB8SsuQgheJKhazYJXCwvAfDR/ic92 <EMAIL>\"\n    # Abhay Kumar\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIKzwAj1ZDyQGjFZCqKfmsw6HJ2LNoKKDkw8i5+F8XIKs <EMAIL>\"\n    # Cristobal Rey\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIHg/iX3trRkMx5fJQbVZTrg7Oif9tYahtFsg661OOGrq <EMAIL>\"\n    # Kunal Sachdeva\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBcP1pBPZgp/YJSK3J13hcHE+WL6x+X+id50irxybsIq <EMAIL>\"\n    # Andres Bannura\n    echo \"ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBAGQ/RoApd3Xjuqxcun8nUx5isv+zyDADzCCUq7uIon2qgQHVULyUr1FeAUjjyqYZw6WiJnUZpmYAz2Zd7+idd903gA2aPd42NHgSeO2zU6ivqBgmIcjBcn3npllVFHkSnqJdKjmOa5zK61Ad+jKrWlHWevrrOTwFypjz8fPM86cue4DoQ== <EMAIL>\"\n    # Andres Bannura 2\n    echo \"ecdsa-sha2-nistp521 AAAAE2VjZHNhLXNoYTItbmlzdHA1MjEAAAAIbmlzdHA1MjEAAACFBABxQJHRSLTkliB67AXtWJO+uV/eO3f78/GdVVHsfFJrW2bH+6TnF+7/iKTh98h9kBEwnTg3ZUL0qXC25jNt7wDTNABNuArt5/kPRYLpVyKdIZXKt1GoC0Fi4oTZo9mHGYa6anlrDNkv/4qnhvQ5kY48lGDNte9mpBb3zrUcR25RfL0fWA== <EMAIL>\"\n    # Kushagra\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPzhmddO6OH4WA0H2Fq7IZRG9rU0a19MioayPKlPoAIf <EMAIL>\"\n    # Tanzeem Ahmed\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPHRfnILaMy0YRHp6P0Sz4LP61H+vUAm2rJXFfnlu1Sz <EMAIL>\"\n    # Garvit Gupta\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAILNXEICvqjGSLtc8QM3WuNmT3xdhBdNozr2N53l/92qV <EMAIL>\"\n    #David Halman\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEt++GGhtKZtpxu2FLbssOWLcnW0R10jk1GzAZZ/CJqo <EMAIL>\"\n    # Nicolas Leyton\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAILCcSfzLw6ojB+uOp4wr7BpMgLTR1Kd/mxolfwk/TigC <EMAIL>\"\n    # Abhinav Sharma\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIMWvFrbn5+vfpoxE8OYxPAA+cIvINHiHhmZU5RE6kP5C <EMAIL>\"\n    # Kushagra Gupta\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIFbog5m0TZBDpFQob8laeYkVlyNQnKi6UczBxOMgQcID <EMAIL>\"\n    # Cristian Navarrete\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAICSqYZ19fsS2q72X1LbWiANBkXEiXnY0TRjqhs9/+5Nt <EMAIL>\"\n    # Vedang Bhardwaj\n    echo \"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQC3NCaN6Y815jErivMbZAk0sqJr/J6+Afk8GOymj/6hbNkZsm+lOzbqaRi4IxRcDrYXtddgRczMiaQK5v15zko7C4i2m8yG/mh8Qjhsa3A67IXkHJjmgiHLGlgeC9ApBpgwknJyCH52mDm6Ile8Q4dS6Bc3p8nslHQOhZ/mBWjC3TxMlrPRiOLlNCapPjxoRn8xTgb97PwvCC2WCyfQqHnvP40mtf/bs0DmEt6TCTVXa6aTk7Z6ICYno2sdZWmjXnnarr/YGzmrBcyF7BMb5X5K+lL3JBzRTjlMCw3e/8xCrU1vAcPAbH+8OW/CjDUhQBV2gSM5VmKrZee48NrrbdKkQKd9rfSXSXq0MT1+nipbzbYVJSr+7xFQQQB6toeZ55ZkOLAOcreASUe/9pA2WDt1GNQ4AkfAltcNLKd8NU7htLowhF9lErEleuDjSbHfksGUfxbmV+3lUwfGvBNIRkySZHU6+FMYQkQSp7+5K2zeB3nJDFdFOzXInh6W4MfMrUYKX8sd6pjMANZqFAEWFUSF5LuTnARNo7bMdJSMJMylAdbAXZCgiZDvIBes0Rhk/O7hyk/pkgHIxFmczmiFyUD+BmHyTpPjgBdW3rgoG0loSFEzbKR0udRJdedvD/jGmZK95JQlSu2/2Id5cYZPaUEwOsYipctn2XPry4Z9MJcSZQ== <EMAIL>\"\n    # Jitesh #2\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIDFl7NAw2LCLMdEuA/xpT4PSwTeL/ivIdHrdfxLUA5ej <EMAIL>\"\n    # Stephanie Chau\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIO23fR6yiiVeX6M8gGmPPg7mtQpwsoNacKFpUwkcpRPj <EMAIL>\"\n    # Prabhnoor Saluja\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIP0hPKm//JsQ/enDSnYDgdwl+cAznCN3CNcyq0SBfERO <EMAIL>\"\n    # Shashank\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIFQ2yDQl83xQu7vgs5Ed1aVWL59+o0AaO9OxsefVIpRm <EMAIL>\"\n    # Rafaela\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOq4hVQwkurimg9oOteim9/Cx7wCg+8hHok8/mmcbQZQ <EMAIL>\"\n    # Harsh\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIHDdqK44eC9pP12/BqpbHTShSwhSzGkEx8denS4NRaKj <EMAIL>\"\n    # Shabs\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEH+5VRlP8gbVtjyBIJvtcncdm5exl/Q+66pBFfvgmk7 <EMAIL>\"\n    # Pratish\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIE/+FgPfba5c2+rDoIpTqIaUW/gRPO8LqCPxtMHKsVYq <EMAIL>\"\n    # Suchhanda\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIPhbGt2ZPK4zQ8ifsKCV5wk+lIJ0cxs9vJLDVXcA/KDQ <EMAIL>\"\n    # Raman \n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIC7zgCs5JPuexSxNaFHQVO0T4M9bZtEYRPrGNAjUMVbN <EMAIL>\"\n    # Mohit Solanki\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAID7dDc0qMe9A4451hisn4cxpyT9I0OeIXakHjNo+dMVf <EMAIL>\"\n    # Dileep\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJncFpnxBTa6YQw9pstbpRhiSeeCGvonZcTxgX02y1eP <EMAIL>\"\n    # Komal\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIIfSjeeWpRPkSvdhQ6Y6DAxBcWJ2b9Q4A+Ae4IPSkMXH <EMAIL>\"\n    # chaudhary shivam\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIN1wawjpkrWSOR8RGBg6eeStzrw9yt8ZQygsjWyjMFfT <EMAIL>\"\n    # om krishna\n    echo \"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINdPtWRJPwmT7FlavUiy5iInCm/qMkddV+jnDW4dHt+U <EMAIL>\"\n} >> /home/<USER>/.ssh/authorized_keys\nchown ubuntu /home/<USER>/.ssh/authorized_keys\nchmod 400 /home/<USER>/.ssh/authorized_keys\n\n######### Install deps #############\n####################################\nsudo apt -y update\nsudo apt install -y net-tools\nsudo apt install -y postgresql-client-common postgresql-client-12\n# this is only needed to install bazelisk\nsudo apt install -y golang-go\n# needed to parse terraform output tags\nsudo apt -y install jq\n\n# needed to install aws cli\nsudo apt -y install unzip\n\nsudo apt -y install atop\n\n# needed to fetch postgres password\ncurl \"https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip\" -o /home/<USER>/awscliv2.zip\nrm -rf /home/<USER>/aws\nunzip /home/<USER>/awscliv2.zip -d /home/<USER>/\nsudo /home/<USER>/aws/install\n", "vpc_security_group_ids": ["${data.terraform_remote_state.cross-stack-reference-input-default-production-coreinfra-legacy-network-us-east-2.outputs.cross-stack-output-aws_security_groupbastion_sgid}"]}}, "aws_key_pair": {"ssh_key_pair": {"//": {"metadata": {"path": "default-production-coreinfra-bastion-us-east-2/ssh_key_pair", "uniqueId": "ssh_key_pair"}}, "key_name": "bastion-default-prod-key-pair", "public_key": "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDPBaKbqcFjA9OEAocGIZUSv5+4KWf5Q2DezIwGfSTO4bbnGCTclPTIhu1MrE5X+mMKVDMFAhtseffW5t1Eb/A031sOZo7W1eg61j9+koBlLPrnz5MFvAwOXMGwCUutWyq/diWf/eW2yHWMjUkeFQoPKiOLT4Jo9pZc3uIObCWvNo6QgK7kxYW4Qqg0d/qqqmbT4nDe7KAOGrmgydwaYsaWT3BUk1NBTLCbUIQNbOCVL+0irAObHzb0ohS4lolmO56Kfhdv4vA3uSTt7fdK985hs4yEqpwivC7n7xMwO6MTsqvyRPJ6Kpcj81R/L799QSgz3VhM9gN8J6Ulko02uiwM43jhJms3Ns/vmJFUozWE8UjGpv/ZntRMAmUKk7daruRJ1q3WwhswgFkKdQbxVHcsFC1u4mvsFO0Q4FjSEyehoCsO+Vvz49+zNkkb5Dyhvg8LLamhO2fq6ganyq1lRkngRGLypalp25Zk50xfyceVlV0mDTMtOKvHRmjj+T8AWsjdtVz9l8GnfKYa+nJ5iuj5aw51FaRw3VWdu+/oYgOCxhHlBsyZ/hSsPn+/EDm72u93cdvLSgunE/xBq69fBIAodpqc4oxO84zP5bYua6JltT/p9j2qBGNUKLYTZuUtwJi9T4sHSKmqrAwS+k9awUyjhwkcXhsHSyI5NIbIqXZ3pQ== <EMAIL>"}}, "aws_volume_attachment": {"attachment_home_volume": {"//": {"metadata": {"path": "default-production-coreinfra-bastion-us-east-2/attachment_home_volume", "uniqueId": "attachment_home_volume"}}, "device_name": "/dev/sdf", "instance_id": "${aws_instance.instance.id}", "volume_id": "${aws_ebs_volume.home_volume.id}"}}}, "terraform": {"backend": {"s3": {"bucket": "cloud.nirvanatech.com", "key": "private/deployment/cdktf/default/production/default-production-coreinfra-bastion-us-east-2.json", "region": "us-east-2"}}, "required_providers": {"aws": {"source": "aws", "version": "5.88.0"}}, "required_version": "1.7.5"}}