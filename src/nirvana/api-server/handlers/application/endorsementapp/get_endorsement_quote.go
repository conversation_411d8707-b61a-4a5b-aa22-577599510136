package endorsementapp

import (
	"context"

	"nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request/enums"

	"nirvanatech.com/nirvana/api-server/handlers/common/endorsement"
	endorsement_request "nirvanatech.com/nirvana/db-api/db_wrappers/endorsementapp/endorsement-request"
	oapi "nirvanatech.com/nirvana/openapi-specs/components/common"

	endorsementapp_intake_oapi "nirvanatech.com/nirvana/openapi-specs/components/endorsementapp/intake"

	"github.com/google/uuid"
	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/application/deps"
	"nirvanatech.com/nirvana/infra/authz"
)

func HandleGetEndorsementQuoteAuthz(
	ctx context.Context,
	deps deps.Deps,
	endorsementRequestID uuid.UUID,
) common.HandlerAuthzResponse {
	return hasPermissionOverEndorsementRequest(
		ctx,
		deps,
		authz.UserFromContext(ctx),
		authz.ReadAction,
		endorsementRequestID,
	)
}

func HandleGetEndorsementQuote(
	ctx context.Context,
	deps deps.Deps,
	endorsementRequestID uuid.UUID,
) (*endorsementapp_intake_oapi.GetEndorsementQuoteResponse, error) {
	endReqObj, err := endorsement.GetEndorsementRequest(ctx, deps.EndorsementRequestManager, endorsementRequestID)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, ErrGetRequest.Error())
	}
	endorsementPrice := endReqObj.WrittenPremium.ToOAPIEndorsementPrice()

	response := &endorsementapp_intake_oapi.GetEndorsementQuoteResponse{
		State: toOAPIQuoteGenerationState(endReqObj),
		Price: endorsementPrice,
	}
	return response, nil
}

func toOAPIQuoteGenerationState(endReq *endorsement_request.Request) endorsementapp_intake_oapi.EndorsementQuoteState {
	quoteGenerationInfo := endReq.QuoteGenerationInfo
	endReqState := endReq.State

	mvrStatus := oapi.MVRPullStatusNotRequested
	pricingJobStatus := enums.PricingJobStatusInvalid
	if quoteGenerationInfo != nil {
		if quoteGenerationInfo.MVRPullDetails != nil {
			mvrStatus = quoteGenerationInfo.MVRPullDetails.Status
		}
		if quoteGenerationInfo.PricingJobInfo != nil {
			pricingJobStatus = quoteGenerationInfo.PricingJobInfo.Status
		}
	}
	switch {
	case pricingJobStatus == enums.PricingJobStatusRunning:
		return endorsementapp_intake_oapi.EndorsementQuoteRefreshing
	case pricingJobStatus == enums.PricingJobStatusCompleted,
		endReqState == enums.EndorsementRequestStateApproved,
		endReqState == enums.EndorsementRequestStateBound:
		return endorsementapp_intake_oapi.EndorsementQuoteGenerated
	case mvrStatus == oapi.MVRPullStatusError, pricingJobStatus == enums.PricingJobStatusFailed:
		return endorsementapp_intake_oapi.EndorsementQuotePanic
	default:
		return endorsementapp_intake_oapi.EndorsementQuoteNotAvailable
	}
}
