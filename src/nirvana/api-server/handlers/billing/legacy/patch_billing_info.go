package legacy

import (
	"context"
	"net/http"
	"time"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/api-server/common"
	billing_interceptor "nirvanatech.com/nirvana/api-server/interceptors/billing/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	sfdc_wrapper "nirvanatech.com/nirvana/external_client/salesforce/wrapper"
	"nirvanatech.com/nirvana/openapi-specs/api_server_billing"
	oapi_billing "nirvanatech.com/nirvana/openapi-specs/components/billing"
)

const (
	nonDeferredDepositInstallments = 1
	deferredDepositInstallments    = 3
	deferredDepositOption          = "Deferred Deposit"
)

func HandlePatchBillingInfo(
	ctx context.Context,
	deps billing_interceptor.Deps,
	params api_server_billing.PatchBillingInfoParams,
	req oapi_billing.PatchBillingInfoRequest,
	now time.Time,
) (err error) {
	ctx = log.ContextWithFields(
		ctx,
		log.HandlerName("HandlePatchBillingInfo"),
		log.Any("params", params),
		log.Any("req", req),
	)

	defer func() {
		if err != nil {
			log.Error(ctx, "failed to update billing info", log.Err(err))
		} else {
			log.Info(ctx, "billing info updated successfully")
		}
	}()

	var policyFilter []policy.Filter

	// Validate mutually exclusive parameters
	if (params.PolicyIdentifier != nil || params.IssuanceYear != nil) && params.ApplicationID != nil {
		err := errors.New("cannot provide both policy identifier + issuance year and application ID")
		return common.NewNirvanaBadRequestErrorWithReason(err, err.Error())
	}

	// Set filters based on valid input combinations
	switch {
	case params.PolicyIdentifier != nil && params.IssuanceYear != nil:
		policyFilter = []policy.Filter{
			policy.PolicyIdentifierIs(*params.PolicyIdentifier),
			policy.PolicyIssuanceYearIs(*params.IssuanceYear),
		}
	case params.ApplicationID != nil:
		policyFilter = []policy.Filter{
			policy.ApplicationIdIs(*params.ApplicationID),
		}
	default:
		err = errors.New("at least one of policy identifier + issuance year or application ID must be provided")
		return common.NewNirvanaBadRequestErrorWithReason(err, err.Error())
	}

	policies, err := deps.PolicyWrapper.GetAllPolicies(ctx, &policy.GetRequest{
		Filters: policyFilter,
	})
	if err != nil {
		return err
	}

	if len(policies) == 0 {
		err = errors.New("Policies not found")

		return common.NewNirvanaErrorWithCodeAndReason(http.StatusNotFound, err, err.Error())
	}

	// Assumption: All policies with the same identifier and issuance year have the same effective date.
	maxAllowedDate := nextBillingPipelineDate(policies[0].EffectiveDate)

	// Assumption: All policies with the same identifier and issuance year have the same application ID.
	applicationID := policies[0].ApplicationId

	if !now.Before(maxAllowedDate) {
		err = errors.Newf(
			"Can only update billing info before the billing pipeline date (%s)",
			maxAllowedDate.Format(time_utils.ISOLayout),
		)
		return common.NewNirvanaBadRequestErrorWithReason(err, err.Error())
	}

	sfdcArgs := sfdc_wrapper.SalesforceEventUpdateBillingInfoArgs{
		ApplicationID: applicationID.String(),
	}

	oapiPaymentMethod := req.PaymentMethod
	if oapiPaymentMethod != nil {
		paymentMethod, err := deserializePaymentMethod(*oapiPaymentMethod)
		if err != nil {
			return common.NewNirvanaBadRequestErrorWithReason(err, "Invalid payment method")
		}

		sfdcArgs.PaymentMethod = pointer_utils.String(
			string(*oapiPaymentMethod),
		)

		// Update on database
		for _, p := range policies {
			err = deps.PolicyWrapper.UpdatePolicy(ctx, &policy.UpdatePolicyRequest{
				PolicyID: p.Id,
				UpdateFn: func(ctx context.Context, policy *policy.Policy) (*policy.Policy, error) {
					policy.BillingInfo.PaymentMethod = paymentMethod
					return policy, nil
				},
			})
			if err != nil {
				return errors.Wrapf(err, "failed to update policy %s", p.Id)
			}
		}
	}

	deferredDeposit := req.DeferredDeposit
	if deferredDeposit != nil {
		currentOpportunity, err := deps.SalesforceWrapper.GetOpportunityFieldsForApplicationId(
			ctx,
			sfdcArgs.ApplicationID,
			sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitOffered,
			sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitBound,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to get current opportunity for application %s", applicationID)
		}

		err = buildSalesforceToolkitArgs(
			&sfdcArgs,
			currentOpportunity,
			*deferredDeposit,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to build salesforce toolkit args for application %s", applicationID)
		}

		if *deferredDeposit {
			sfdcArgs.CountOfDepositInstallments = pointer_utils.Int(deferredDepositInstallments)
		} else {
			sfdcArgs.CountOfDepositInstallments = pointer_utils.Int(nonDeferredDepositInstallments)
		}
	}

	// Update on Salesforce
	err = deps.SalesforceWrapper.UpdateOpportunityForBillingInfo(
		ctx,
		sfdcArgs,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to update Salesforce opportunity for application %s", applicationID)
	}

	return nil
}

func nextBillingPipelineDate(t time.Time) time.Time {
	nextMonth := t.AddDate(0, 1, 0) // Add 1 month
	return time.Date(
		nextMonth.Year(),
		nextMonth.Month(),
		4,          // Day = 4th
		0, 0, 0, 0, // Time = 00:00:00
		time.UTC,
	)
}

// buildSalesforceToolkitArgs builds the toolkit fields for the Salesforce opportunity update.
// It parses the toolkit query response from salesforce and modifies the toolkit fields based on the deferred deposit option.
func buildSalesforceToolkitArgs(
	originalArgs *sfdc_wrapper.SalesforceEventUpdateBillingInfoArgs,
	queryResponse map[string]any,
	deferredDeposit bool,
) error {
	toolkits := make(map[string]sfdc_wrapper.MultiPicklist)
	shouldUpdateToolkitField := make(map[string]bool)

	fields := []string{
		sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitOffered,
		sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitBound,
	}

	for _, field := range fields {
		var toolkit sfdc_wrapper.MultiPicklist

		toolkitResponse, ok := queryResponse[field]
		if !ok {
			toolkit = sfdc_wrapper.MultiPicklist{} // Salesforce doesn't return the field if it is empty
		} else {
			toolkitString, ok := toolkitResponse.(string)
			if !ok {
				return errors.Errorf("toolkit field %s is not a string", field)
			}

			toolkit = sfdc_wrapper.MultiPicklistFromString(toolkitString)
		}

		if deferredDeposit {
			shouldUpdateToolkitField[field] = toolkit.AppendIfNotExists(deferredDepositOption)
		} else {
			shouldUpdateToolkitField[field] = toolkit.DeleteIfExists(deferredDepositOption)
		}

		toolkits[field] = toolkit
	}

	// Only define toolkit fields if they actually change (to avoid unnecessary updates)
	if shouldUpdateToolkitField[sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitOffered] {
		originalArgs.ToolkitOffered = toolkits[sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitOffered]
	}

	if shouldUpdateToolkitField[sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitBound] {
		originalArgs.ToolkitBound = toolkits[sfdc_wrapper.OpportunitySalesforceObjectFieldToolkitBound]
	}

	return nil
}
