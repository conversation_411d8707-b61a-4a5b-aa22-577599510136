package underwriting

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/infra/authz"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

type GetAuthorityRequestRequest struct {
	ApplicationReviewID string
}

func HandleGetAuthorityRequest(
	ctx context.Context,
	deps deps.Deps,
	req GetAuthorityRequestRequest,
) (*oapi_uw.AuthorityRequestDetails, error) {
	// Parse and validate application review ID
	applicationReviewID, err := uuid.Parse(req.ApplicationReviewID)
	if err != nil {
		return nil, common.NewNirvanaBadRequestErrorWithReason(err, "Invalid application review ID format")
	}

	// Get the latest authority request for this application review
	request, err := deps.AuthorityManager.GetLatestRequestForApplication(ctx, applicationReviewID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, common.NewNirvanaNotFoundErrorf(nil, "authority request for application review", applicationReviewID.String())
		}
		return nil, common.NewNirvanaInternalServerWithReason(err, "Failed to get latest authority request")
	}

	// Convert internal response to OpenAPI response
	oapiResponse, err := convertToAuthorityRequestDetails(request)
	if err != nil {
		return nil, common.NewNirvanaInternalServerWithReason(err, "Failed to convert authority request details")
	}

	// Check if the current user has underwriting manager role
	user := authz.UserFromContext(ctx)
	isUnderwritingManager := user.IsUnderwriterManager()
	oapiResponse.IsUnderwritingManager = &isUnderwritingManager

	// Fetch requester name
	if request.RequesterID != uuid.Nil {
		requesterInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, request.RequesterID)
		if err == nil && requesterInfo != nil {
			requesterName := requesterInfo.FullName()
			oapiResponse.RequesterName = &requesterName
		}
		// If error fetching user, we don't fail the request, just omit the name
	}

	// Fetch reviewer name if last_reviewed_by is set
	if oapiResponse.LastReviewedBy != nil && *oapiResponse.LastReviewedBy != uuid.Nil {
		reviewerInfo, err := deps.AuthWrapper.FetchUserInfo(ctx, *oapiResponse.LastReviewedBy)
		if err == nil && reviewerInfo != nil {
			reviewerName := reviewerInfo.FullName()
			oapiResponse.ReviewerName = &reviewerName
		}
		// If error fetching user, we don't fail the request, just omit the name
	}

	return &oapiResponse, nil
}
