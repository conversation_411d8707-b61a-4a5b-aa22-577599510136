package underwriting

import (
	"context"
	"net/http"

	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/underwriting/app_review/actions"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/helpers"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	"nirvanatech.com/nirvana/quoting/quoting_logic"
	"nirvanatech.com/nirvana/underwriting/app_review/actions/reasons"
)

type CloseApplicationReviewRequest struct {
	ApplicationReviewID               oapi_uw.ApplicationReviewID
	CloseApplicationReviewReasonsForm oapi_uw.ApplicationReviewCloseReasonsForm
}

func NewCloseApplicationReviewRequest(
	applicationReviewID oapi_uw.ApplicationReviewID,
	closeApplicationReviewReasonsForm oapi_uw.ApplicationReviewCloseReasonsForm,
) CloseApplicationReviewRequest {
	return CloseApplicationReviewRequest{
		ApplicationReviewID:               applicationReviewID,
		CloseApplicationReviewReasonsForm: closeApplicationReviewReasonsForm,
	}
}

type CloseApplicationReviewResponse struct {
	ValidationError *oapi_common.ErrorMessage
	ServerError     *oapi_common.ErrorMessage
	Success         *struct{}
}

func HandleCloseApplicationReview(
	ctx context.Context,
	deps deps.Deps,
	req CloseApplicationReviewRequest,
) CloseApplicationReviewResponse {
	err := validateEndpointState(ctx, deps, req.ApplicationReviewID, CloseAppReviewEndpoint)
	if err != nil {
		log.Error(ctx, "HandleCloseApplicationReview: validateEndpointState failed",
			log.Err(err),
			log.String("ApplicationReviewID", req.ApplicationReviewID))
		errMessage := helpers.WrapErrorMessage(err, "UpdateCoverages: validateEndpointState failed")
		return CloseApplicationReviewResponse{ValidationError: &errMessage}
	}
	appReviewID, err := uuid.Parse(req.ApplicationReviewID)
	if err != nil {
		log.Error(ctx, "CloseApplicationReview: Failed to parse ApplicationReviewID",
			log.Err(err),
			log.String("ApplicationReviewID", req.ApplicationReviewID))
		errMessage := helpers.WrapErrorMessage(err, "CloseApplicationReview: Failed to parse ApplicationReviewID")
		return CloseApplicationReviewResponse{ValidationError: &errMessage}
	}
	appReviewObject, err := deps.ApplicationReviewWrapper.GetReview(ctx, appReviewID.String())
	if err != nil {
		log.Error(ctx, "CloseApplicationReview: Failed to fetch Application Review",
			log.Err(err),
			log.String("ApplicationReviewID", req.ApplicationReviewID))
		errMessage := helpers.WrapErrorMessage(err, "CloseApplicationReview: Failed to fetch Application Review")
		return CloseApplicationReviewResponse{ServerError: &errMessage}
	}
	validatedReasons, err := getValidatedReasons(req.CloseApplicationReviewReasonsForm)
	if err != nil {
		log.Error(ctx, "CloseApplicationReview: Failed to validate reasons",
			log.Err(err),
			log.String("ApplicationReviewID", req.ApplicationReviewID))
		errMessage := helpers.WrapErrorMessage(err, "CloseApplicationReview: Failed to validate reasons")
		return CloseApplicationReviewResponse{ValidationError: &errMessage}
	}

	carrierName, err := getCarrierName(req.CloseApplicationReviewReasonsForm.WinCarrier)
	if err != nil {
		log.Error(ctx, "CloseApplicationReview: Invalid CarrierID given",
			log.Err(err),
			log.String("ApplicationReviewID", req.ApplicationReviewID))
		errMessage := helpers.WrapErrorMessage(err, "CloseApplicationReview: Invalid CarrierID given")
		return CloseApplicationReviewResponse{ValidationError: &errMessage}
	}

	winCarrierPricing, err := validateAndConvertWinCarrierPricing(req.CloseApplicationReviewReasonsForm.WinCarrierPricing)
	if err != nil {
		log.Error(ctx, "CloseApplicationReview: Invalid WinCarrierPricing given",
			log.Err(err),
			log.String("ApplicationReviewID", req.ApplicationReviewID))
		errMessage := helpers.WrapErrorMessage(err, "CloseApplicationReview: Invalid WinCarrierPricing given")
		return CloseApplicationReviewResponse{ValidationError: &errMessage}
	}

	closeReasons := uw.CloseReasonsStruct{
		Reasons:           *validatedReasons,
		Comments:          req.CloseApplicationReviewReasonsForm.Comments,
		WinCarrier:        carrierName,
		IntentionToQuote:  req.CloseApplicationReviewReasonsForm.IntentionToQuote,
		WinCarrierPricing: winCarrierPricing,
	}
	supplementArgs := actions.CloseActionSupplementArgs{
		SupplementArgs: actions.SupplementArgs{
			Feedback: req.CloseApplicationReviewReasonsForm.RecommendedActionFeedback,
		},
	}
	err = quoting_logic.CloseApplication(
		ctx,
		deps.ApplicationWrapper,
		deps.ApplicationReviewWrapper,
		deps.AppReviewWrapper,
		deps.AuthWrapper,
		deps.AgencyWrapper,
		deps.EventsHandler,
		deps.Jobber,
		deps.FeatureFlagClient,
		deps.MetricsClient,
		&appReviewObject.Id,
		appReviewObject.ApplicationID,
		closeReasons,
		supplementArgs)
	if err != nil {
		log.Error(ctx, "CloseApplicationReview: Failed to close application",
			log.Err(err),
			log.String("ApplicationId", appReviewObject.ApplicationID))
		errMessage := helpers.WrapErrorMessage(err, "CloseApplicationReview: Failed to close application review")
		return CloseApplicationReviewResponse{ServerError: &errMessage}
	}
	return CloseApplicationReviewResponse{Success: &emptyStruct}
}

func getValidatedReasons(reasonsForm oapi_uw.ApplicationReviewCloseReasonsForm) (*[]reasons.ReasonStruct, error) {
	if reasonsForm.ReasonsArray == nil || len(reasonsForm.ReasonsArray) == 0 {
		return nil, errors.New("No reasons provided")
	}
	validatedReasons := make([]reasons.ReasonStruct, 0)
	for _, reason := range reasonsForm.ReasonsArray {
		found := false
		for _, validReason := range reasons.ApplicationCloseReasons {
			if reason.ReasonId == validReason.ReasonCode && reason.SubReasonId == validReason.SubReasonCode {
				if !validReason.IsActive {
					return nil, errors.Newf("Inactive reason %v in the reasonsForm", reason)
				}
				validatedReasons = append(validatedReasons, validReason.ReasonStruct)
				found = true
				break
			}
		}
		if !found {
			return nil, errors.Newf("Invalid reason %v in the reasonsForm", reason)
		}
	}
	return &validatedReasons, nil
}

func getCarrierName(carrier *int32) (*string, error) {
	if carrier == nil {
		return nil, nil //nolint:nilnil
	}

	for _, winCarrier := range reasons.WinCarrierReason {
		if *carrier == winCarrier.WinCarrierCode {
			return &winCarrier.WinCarrierName, nil
		}
	}

	return nil, errors.Newf("No Carrier with the given id found", *carrier)
}

func validateAndConvertWinCarrierPricing(apiPricing *oapi_uw.WinCarrierPricing) (*uw.WinCarrierPricing, error) {
	if apiPricing == nil {
		return &uw.WinCarrierPricing{}, nil
	}

	if apiPricing.AutoLiability != nil && *apiPricing.AutoLiability < 0 {
		return nil, errors.Newf("AutoLiability cannot be negative, got: %d", *apiPricing.AutoLiability)
	}

	if apiPricing.FullPrice != nil && *apiPricing.FullPrice < 0 {
		return nil, errors.Newf("FullPrice cannot be negative, got: %d", *apiPricing.FullPrice)
	}

	return &uw.WinCarrierPricing{
		AutoLiability: apiPricing.AutoLiability,
		FullPrice:     apiPricing.FullPrice,
	}, nil
}

func (p *CloseApplicationReviewResponse) Body() interface{} {
	switch {
	case p.ValidationError != nil:
		return *p.ValidationError
	case p.ServerError != nil:
		return *p.ServerError
	case p.Success != nil:
		return *p.Success
	default:
		return nil
	}
}

func (p *CloseApplicationReviewResponse) StatusCode() int {
	switch {
	case p.ValidationError != nil:
		return http.StatusBadRequest
	case p.ServerError != nil:
		return http.StatusUnprocessableEntity
	case p.Success != nil:
		return http.StatusOK
	default:
		return http.StatusInternalServerError
	}
}

var _ common.HandlerResponse = (*CloseApplicationReviewResponse)(nil)
