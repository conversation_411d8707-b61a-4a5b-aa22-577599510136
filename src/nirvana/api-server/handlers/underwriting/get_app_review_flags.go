package underwriting

import (
	"context"

	"nirvanatech.com/nirvana/common-go/pointer_utils"

	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

type GetAppReviewFlagsResponse struct {
	Success *oapi_uw.ApplicationReviewFlags
	Error   error
}

func HandleGetAppReviewFlags(ctx context.Context, deps deps.Deps, applicationReviewID string) GetAppReviewFlagsResponse {
	ctx = log.ContextWithFields(ctx, log.HandlerName("GetAppReviewFlags"),
		log.AppReviewID(applicationReviewID))

	// For now, default to V1. TODO wire up to experiments service once those changes are merged
	lossSummaryVersion := oapi_uw.V1
	log.Info(ctx, "Returning V1 loss summary version")

	return GetAppReviewFlagsResponse{
		Success: &oapi_uw.ApplicationReviewFlags{
			LossSummaryVersion: pointer_utils.ToPointer(lossSummaryVersion),
		},
	}
}
