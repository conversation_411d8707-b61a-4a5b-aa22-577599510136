package underwriting

import (
	"context"

	"github.com/cockroachdb/errors"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/api-server/handlers/underwriting/utils"
	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/underwriting"
	app_review_widgets "nirvanatech.com/nirvana/underwriting/app_review/widgets"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/lossesv2"
)

type GetLossSummaryResponseV2 struct {
	Success *oapi_uw.ApplicationReviewLossSummaryV2
	Error   error
}

func HandleGetLossSummaryV2(
	ctx context.Context,
	deps deps.Deps,
	reviewId string,
	req oapi_uw.ApplicationReviewLossSummaryV2Request,
) GetLossSummaryResponseV2 {
	// build request
	request, err := createGetSummaryRequest(reviewId, req)
	if err != nil {
		return GetLossSummaryResponseV2{
			Error: common.NewNirvanaBadRequestErrorWithReason(err, "unable to build request"),
		}
	}

	// get summary
	summary, err := deps.ApplicationReviewManager.Losses.LossSummaryV2.Get(ctx, *request)
	if err != nil {
		return GetLossSummaryResponseV2{
			Error: common.NewNirvanaInternalServerWithReason(err, "unable to fetch loss summary v2"),
		}
	}

	// map to response
	resp, err := mapToOAPIResponse(*summary)
	if err != nil {
		return GetLossSummaryResponseV2{
			Error: common.NewNirvanaInternalServerWithReason(err, "unable to map to OAPI response"),
		}
	}

	return GetLossSummaryResponseV2{
		Success: resp,
	}
}

func createGetSummaryRequest(reviewId string, params oapi_uw.ApplicationReviewLossSummaryV2Request) (*lossesv2.GetLossSummaryRequestV2, error) {
	coverageParams, err := getCoverageParams(params)
	if err != nil {
		return nil, errors.Wrap(err, "unable to get coverage params")
	}
	return &lossesv2.GetLossSummaryRequestV2{
		ApplicationReviewId: reviewId,
		CalculationInputs:   coverageParams,
	}, nil
}

func getCoverageParams(params oapi_uw.ApplicationReviewLossSummaryV2Request) ([]lossesv2.CalculationInputs, error) {
	var coverageParams []lossesv2.CalculationInputs
	for _, param := range params.CoverageParams {
		mappedCoverage, err := utils.MapCoverageFromOAPI(param.CoverageType)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to map coverage %s", param.CoverageType)
		}
		mappedParam := lossesv2.CalculationInputs{
			CoverageType:          *mappedCoverage,
			RequestedPremiumPerPU: float64(param.RequestedPremiumPerPU),
			PreClaimDeductible:    float64(param.PreClaimDeductible),
		}
		coverageParams = append(coverageParams, mappedParam)
	}
	return coverageParams, nil
}

func mapCoverageToOAPI(coverage app_enums.Coverage) (*externalRef0.CoverageType, error) {
	switch coverage {
	case app_enums.CoverageAutoLiability:
		return pointer_utils.ToPointer(externalRef0.CoverageAutoLiability), nil
	case app_enums.CoverageAutoPhysicalDamage:
		return pointer_utils.ToPointer(externalRef0.CoverageAutoPhysicalDamage), nil
	case app_enums.CoverageMotorTruckCargo:
		return pointer_utils.ToPointer(externalRef0.CoverageMotorTruckCargo), nil
	default:
		return nil, errors.Newf("coverage %s is not supported", coverage)
	}
}

func mapToOAPIResponse(summary lossesv2.LossSummaryResponse) (*oapi_uw.ApplicationReviewLossSummaryV2, error) {
	mappedSummaries, err := mapLatestCoverageSummaries(summary.LatestCoverageSummaries)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to map latest coverage summaries")
	}
	mappedParsingStatus, err := mapToOAPIParsingStatus(summary.ParsingStatus)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to map parsing status")
	}
	return &oapi_uw.ApplicationReviewLossSummaryV2{
		LatestCoverageSummaries: mappedSummaries,
		ParsingStatus:           *mappedParsingStatus,
		Meta:                    mapToOAPIMeta(summary.Meta),
	}, nil
}

func mapToOAPIParsingStatus(status lossesv2.ParsingStatus) (*oapi_uw.ParsedLossStateV2, error) {
	switch status {
	case lossesv2.ParsingStatusProcessing:
		return pointer_utils.ToPointer(oapi_uw.Processing), nil
	case lossesv2.ParsingStatusValidated:
		return pointer_utils.ToPointer(oapi_uw.Validated), nil
	default:
		return nil, errors.Newf("unable to map parsing status %s", status)
	}
}

func mapLatestCoverageSummaries(coverageSummaries []lossesv2.CoverageSummary) ([]oapi_uw.LatestCoverageSummary, error) {
	var mappedCoverageSummaries []oapi_uw.LatestCoverageSummary
	for _, summary := range coverageSummaries {
		mappedCoverage, err := mapCoverageToOAPI(summary.CoverageType)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to map coverage %s", summary.CoverageType)
		}
		mappedSummaryRecords, err := mapSummaryItemRecords(summary.Summary)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to map summary records")
		}
		mappedCoverageSummary := oapi_uw.LatestCoverageSummary{
			Averages:            mapLossRunAverages(summary.Averages),
			CoverageType:        *mappedCoverage,
			LossRatioAggregates: mapLossRatioAggregates(summary.LossRatioAggregates),
			Summary:             mappedSummaryRecords,
		}
		mappedCoverageSummaries = append(mappedCoverageSummaries, mappedCoverageSummary)
	}
	return mappedCoverageSummaries, nil
}

func mapLossRunAverages(averages lossesv2.LossRunAverages) oapi_uw.LossRunAverages {
	return oapi_uw.LossRunAverages{
		AverageBurnRate:  averages.AverageBurnRate,
		AverageClaimSize: averages.AverageClaimSize,
		LossFrequency:    mapLossFrequency(averages.LossFrequency),
	}
}

func mapLossFrequency(lossFrequency lossesv2.LossFrequency) oapi_uw.LossFrequency {
	return oapi_uw.LossFrequency{
		PerMillionMiles: lossFrequency.PerMillionMiles,
		PerUnit:         lossFrequency.PerUnit,
	}
}

func mapLossRatioAggregates(aggregates []lossesv2.LossRatioAggregate) oapi_uw.LossRatioAggregates {
	result := make(oapi_uw.LossRatioAggregates, len(aggregates))
	for i, agg := range aggregates {
		result[i].PeriodLabel = agg.PeriodLabel
		result[i].ValuePercent = agg.ValuePercent
	}
	return result
}

func mapSummaryItemRecords(records []lossesv2.LossSummaryRecord) ([]oapi_uw.ApplicationReviewLossSummaryItemRecordV2, error) {
	var lossSummaryRecords []oapi_uw.ApplicationReviewLossSummaryItemRecordV2
	for _, record := range records {
		mappedRecord, err := mapSummaryItemRecord(record)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to map summary item record %s", record.PeriodStartDate)
		}
		lossSummaryRecords = append(lossSummaryRecords, *mappedRecord)
	}
	return lossSummaryRecords, nil
}

func mapSummaryItemRecord(record lossesv2.LossSummaryRecord) (*oapi_uw.ApplicationReviewLossSummaryItemRecordV2, error) {
	grossLoss, err := mapLossValue(record.GrossLoss)
	if err != nil {
		return nil, errors.Wrap(err, "unable to map gross loss value")
	}
	claimCount, err := mapLossValue(record.NumberOfClaims)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to map claim count")
	}
	puCount, err := mapLossValue(record.NumberOfPowerUnits)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to map power unit count")
	}

	var tags []oapi_uw.ApplicationReviewLossSummaryItemRecordV2Tags
	for _, tag := range record.Tags {
		mappedTag, err := mapTag(tag)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to map tag %s", tag)
		}
		tags = append(tags, *mappedTag)
	}

	// todo make interface loss ratio nullable
	lossRatioPercent := float32(0)
	if record.LossRatioPercent != nil {
		lossRatioPercent = *record.LossRatioPercent
	}

	return &oapi_uw.ApplicationReviewLossSummaryItemRecordV2{
		GrossLoss:          *grossLoss,
		NumberOfClaims:     *claimCount,
		NumberOfPowerUnits: *puCount,
		LossRatio:          lossRatioPercent,
		PeriodStartDate:    openapi_types.Date{Time: record.PeriodStartDate},
		PeriodEndDate:      openapi_types.Date{Time: record.PeriodEndDate},
		Tags:               pointer_utils.ToPointer(tags),
	}, nil
}

func mapLossValue(value lossesv2.LossValue) (*oapi_uw.LossValueWithOverride, error) {
	source, err := mapValueSource(value.ValueSource)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to map value source")
	}
	return &oapi_uw.LossValueWithOverride{
		Override:    value.Override,
		Value:       value.Value,
		ValueSource: *source,
	}, nil
}

func mapValueSource(source application.LossRunValueSource) (*oapi_uw.LossRunValueSource, error) {
	switch source {
	case application.LossRunValueSourceAgent:
		return pointer_utils.ToPointer(oapi_uw.Agent), nil
	case application.LossRunValueSourceParsed:
		return pointer_utils.ToPointer(oapi_uw.Parsed), nil

	default:
		return nil, errors.Newf("value source %s is not supported", source)
	}
}

func mapTag(tag lossesv2.LossSummaryTag) (*oapi_uw.ApplicationReviewLossSummaryItemRecordV2Tags, error) {
	switch tag {
	case lossesv2.LossSummaryTagFileMissing:
		return pointer_utils.ToPointer(oapi_uw.FileMissing), nil
	case lossesv2.LossSummaryTagFileOutOfDate:
		return pointer_utils.ToPointer(oapi_uw.FileOutOfDate), nil
	default:
		return nil, errors.Newf("unknown tag %s", tag)
	}
}

// mapToOAPIMeta converts the internal widget metadata model to the public API model.
func mapToOAPIMeta(meta *app_review_widgets.InternalWidgetMeta) *oapi_uw.ApplicationReviewWidgetMeta {
	// 1. Handle nil input for the entire object.
	if meta == nil {
		return nil
	}

	// 2. Map all fields directly in the return statement.
	//    The helper functions are responsible for handling nil inputs for sub-fields.
	return &oapi_uw.ApplicationReviewWidgetMeta{
		Credit:             meta.Credit,
		Merit:              meta.Merit,
		AutoLiability:      mapToOAPICoverageMeta(meta.AutoLiability),
		AutoPhysicalDamage: mapToOAPICoverageMeta(meta.AutoPhysicalDamage),
		GeneralLiability:   mapToOAPICoverageMeta(meta.GeneralLiability),
		MotorTruckCargo:    mapToOAPICoverageMeta(meta.MotorTruckCargo),
		Flags:              mapToOAPIMetaFlags(meta.Flags),
	}
}

// mapToOAPICoverageMeta maps the nested coverage metadata struct.
func mapToOAPICoverageMeta(internal *app_review_widgets.InternalWidgetCoverageMeta) *oapi_uw.ApplicationReviewWidgetCoverageMeta {
	if internal == nil {
		return nil
	}
	return &oapi_uw.ApplicationReviewWidgetCoverageMeta{
		Credit: internal.Credit,
		Merit:  internal.Merit,
	}
}

// mapToOAPIMetaFlag maps the nested flag struct.
func mapToOAPIMetaFlag(internal app_review_widgets.InternalWidgetMetaFlag) oapi_uw.ApplicationReviewWidgetMetaFlag {
	return oapi_uw.ApplicationReviewWidgetMetaFlag{
		Description: internal.Description,
		Title:       internal.Title,
		Weight:      internal.Weight,
	}
}

// mapToOAPIMetaFlags maps the slice of flag structs.
func mapToOAPIMetaFlags(internalFlags *[]app_review_widgets.InternalWidgetMetaFlag) *[]oapi_uw.ApplicationReviewWidgetMetaFlag {
	if internalFlags == nil {
		return nil
	}

	apiFlags := make([]oapi_uw.ApplicationReviewWidgetMetaFlag, 0, len(*internalFlags))
	for _, internalFlag := range *internalFlags {
		apiFlags = append(apiFlags, mapToOAPIMetaFlag(internalFlag))
	}
	return &apiFlags
}
