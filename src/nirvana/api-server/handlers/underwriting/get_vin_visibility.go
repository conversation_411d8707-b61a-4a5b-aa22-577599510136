package underwriting

import (
	"context"

	"nirvanatech.com/nirvana/underwriting/app_review/vin_visibility"

	"nirvanatech.com/nirvana/api-server/interceptors/underwriting/deps"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

type GetVinVisibilityResponse struct {
	Error  error
	Result *underwriting.ApplicationReviewVinVisibility
}

// TODO: add a status field to handle telematics not connected errors
func HandleGetVinVisibility(
	ctx context.Context,
	deps deps.Deps,
	reviewId string,
) GetVinVisibilityResponse {
	ctx = log.ContextWithFields(ctx, log.String("reviewId", reviewId))
	data, err := vin_visibility.GenerateVinVisibility(
		ctx,
		reviewId,
		deps.ApplicationReviewWrapper,
		deps.ApplicationReviewManager,
		deps.FeatureStore,
		vin_visibility.EquipmentsInput{IsPresent: false},
	)
	if err != nil {
		// TODO: this function should return network errors
		return GetVinVisibilityResponse{
			Error: err,
		}
	}
	log.Info(ctx, "Vin visibility fetched", log.Any("data", data))
	return GetVinVisibilityResponse{
		Result: data,
	}
}
