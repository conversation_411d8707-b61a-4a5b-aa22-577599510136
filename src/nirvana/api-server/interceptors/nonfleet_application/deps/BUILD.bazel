load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "deps",
    srcs = ["deps.go"],
    importpath = "nirvanatech.com/nirvana/api-server/interceptors/nonfleet_application/deps",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/file_upload_lib",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/agency_bd_mapping",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/prog_type_identifier",
        "//nirvana/db-api/db_wrappers/sharing",
        "//nirvana/events",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/authz/checker",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/monitoring",
        "//nirvana/jobber/event",
        "//nirvana/nonfleet/rule_engine",
        "//nirvana/nonfleet/state-machine",
        "//nirvana/servers/quote_scraper",
        "//nirvana/telematics/connections",
        "//nirvana/underwriting/express_lane",
        "//nirvana/underwriting/scheduler",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@org_uber_go_fx//:fx",
    ],
)
