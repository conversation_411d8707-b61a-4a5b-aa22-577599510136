package deps

import (
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	non_fleet_experiment "nirvanatech.com/nirvana/application/experiments/non_fleet"
	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency_bd_mapping"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"
	"nirvanatech.com/nirvana/jobber/event"
	"nirvanatech.com/nirvana/underwriting/express_lane"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/file_upload_lib"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/db-api/db_wrappers/prog_type_identifier"
	"nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/events"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/infra/authz/checker"
	"nirvanatech.com/nirvana/nonfleet/rule_engine"
	statemachine "nirvanatech.com/nirvana/nonfleet/state-machine"
	"nirvanatech.com/nirvana/servers/quote_scraper"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/underwriting/scheduler"
)

type Deps struct {
	fx.In

	ProgramTypeIdentifier    prog_type_identifier.ProgramTypeIdentifier
	AdmittedAppWrapper       nf_app.Wrapper[*admitted_app.AdmittedApp]
	EventsHandler            events.EventsHandler
	TSPConnManager           *tsp_connections.TSPConnManager
	ApplicationWrapper       application.DataWrapper
	NFAppStateMachineWrapper *statemachine.NFAppStateMachineImpl
	Jobber                   quoting_jobber.Client
	AuthWrapper              auth.DataWrapper
	FormsWrapper             forms.FormWrapper
	AgencyWrapper            agency.DataWrapper
	AdmittedRuleEngine       *rule_engine.NFRuleEngine[*admitted_app.AdmittedApp]
	FMCSAWrapper             fmcsa.DataWrapper
	AuthzChecker             *checker.Checker
	UWScheduler              scheduler.UwScheduler
	FileUploadManager        file_upload_lib.FileUploadManager[file_upload_lib.DefaultS3Keygen]
	FFClient                 feature_flag_lib.Client
	QuoteScraperClient       quote_scraper.QuoteScraperClient
	MetricsClient            statsd.Statter
	FetcherClientFactory     data_fetching.FetcherClientFactory
	ProcessorClientFactory   data_processing.ProcessorClientFactory
	SharingWrapper           sharing.DataWrapper
	StoreManager             store_management.StoreManager
	AgencyBDWrapper          agency_bd_mapping.Wrapper
	NFAppReviewWrapper       nf_app_review.Wrapper
	InsuranceEngPDClient     insurance_eng.PagerDutyClient
	IBClient                 service.InsuranceBundleManagerClient
	CryptoClient             *crypto_utils.Client
	ExperimentManager        *non_fleet_experiment.Manager
	EventClient              event.Client
	ExpressLaneManager       *express_lane.ExpressLaneManager
}
