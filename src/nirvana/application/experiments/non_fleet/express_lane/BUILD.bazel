load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "express_lane",
    srcs = [
        "experiment.go",
        "fact_retriver.go",
        "facts_keys.go",
        "fx.go",
        "models.go",
        "violation_codes.go",
    ],
    importpath = "nirvanatech.com/nirvana/application/experiments/non_fleet/express_lane",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/experiments",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/nonfleet/underwriting_panels/base_panel",
        "//nirvana/nonfleet/underwriting_panels/driver",
        "//nirvana/nonfleet/underwriting_panels/operations",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/rules/evaluation_service",
        "//nirvana/rules/facts",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)
