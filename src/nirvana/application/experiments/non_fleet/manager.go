package non_fleet

import (
	"context"

	"github.com/benbjohnson/clock"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/express_lane"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/pre_telematics_quote"
	"nirvanatech.com/nirvana/experiments/models"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/experiments"
	"nirvanatech.com/nirvana/experiments/client"
	"nirvanatech.com/nirvana/experiments/enums"
)

type ApplicationExperimentManagerDeps struct {
	fx.In

	ExperimentsClient client.ExperimentsClient
	Registry          *Registry
	Clock             clock.Clock
}

type Manager struct {
	deps ApplicationExperimentManagerDeps
}

func newAppExperimentManager(deps ApplicationExperimentManagerDeps) *Manager {
	return &Manager{
		deps: deps,
	}
}

func (m Manager) getAssignedExperiments(ctx context.Context, appId uuid.UUID) ([]*experiments.Assignment, error) {
	assignedExperiments, err := m.deps.ExperimentsClient.GetLatestExperimentsAssignations(
		ctx,
		enums.DomainAgents,
		appId.String(),
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get assigned experiments for appId %s", appId.String())
	}

	return assignedExperiments, nil
}

// AssignExperimentsToApplication assigns a list of experiments to the given applicationId
//
// Note: This is intended to run once per application lifecycle. While review creation can occur multiple times,
// assignment is strictly guarded to ensure consistency and avoid unnecessary reassignments.
func (m Manager) AssignExperimentsToApplication(ctx context.Context, applicationId uuid.UUID) error {
	experimentAssignments, err := m.getAssignedExperiments(
		ctx,
		applicationId,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to get assigned experiments for appId %s", applicationId.String())
	}
	if len(experimentAssignments) > 0 {
		return nil
	}

	registeredIDs := m.deps.Registry.GetRegisteredExperimentsIds()

	activeExperiments, err := m.deps.ExperimentsClient.GetActiveExperimentsByIds(
		ctx,
		registeredIDs,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to get active experiments for IDs: %v", registeredIDs)
	}

	assignableExperiments := make([]*experiments.Experiment, 0)
	for _, experiment := range activeExperiments {
		// get from the registry and assign the experiment
		experimentFromRegistry, err := m.deps.Registry.GetExperiment(experiment.Id)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to get experiment %s with id %s from registry",
				experiment.Name,
				experiment.Id.String(),
			)
		}

		isApplicable, err := experimentFromRegistry.IsApplicable(ctx, applicationId)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to check if experiment %s is applicable for appId %s",
				experiment.Id.String(),
				applicationId.String(),
			)
		}

		if isApplicable {
			assignableExperiments = append(assignableExperiments, experiment)
		}
	}

	if len(assignableExperiments) == 0 {
		return nil
	}

	err = m.deps.ExperimentsClient.AssignExperiments(
		ctx,
		applicationId.String(),
		true,
		assignableExperiments,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to assign experiments for appId %s", applicationId.String())
	}

	return nil
}

// IsExperimentApplicableToApp - This would be used in a Pr on top, currently should not be used anywhere else.
// Not fit for production use
// nolint:unused
func (m Manager) IsExperimentApplicableToApp(ctx context.Context, applicationId, experimentId uuid.UUID) (*bool, error) {
	// Get all experiments applied for that application for Agents Domain
	assignedExperiments, err := m.deps.ExperimentsClient.GetLatestExperimentsAssignations(ctx, enums.DomainAgents, applicationId.String())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest experiments")
	}
	// If no experiments are assigned, return nil
	if len(assignedExperiments) == 0 {
		// nolint:nilnil
		return nil, nil
	}

	// Check if the given experiment is part of the assignedExperiments
	found := false
	for _, assignation := range assignedExperiments {
		if experimentId == assignation.ExperimentId {
			found = true
			break
		}
	}
	return pointer_utils.ToPointer(found), nil
}

func (m Manager) GetAndApplyExperiments(
	ctx context.Context,
	applicationId uuid.UUID,
) error {
	assignedExperiments, err := m.getAssignedExperiments(
		ctx,
		applicationId,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to get assigned application experiments for appId %s", applicationId)
	}

	// We only apply those that are enabled
	assignedExperiments = slice_utils.Filter(assignedExperiments,
		func(assignment *experiments.Assignment) bool {
			return assignment.Enabled
		},
	)

	if len(assignedExperiments) == 0 {
		return nil
	}

	err = m.applyExperiments(
		ctx,
		applicationId,
		assignedExperiments,
	)
	if err != nil {
		return errors.Wrapf(err, "failed to apply application creation experiments for appId %s", applicationId)
	}

	return nil
}

func (m Manager) applyExperiments(
	ctx context.Context,
	applicationId uuid.UUID,
	assignments []*experiments.Assignment,
) error {
	for _, assignment := range assignments {
		experimentFromRegistry, err := m.deps.Registry.GetExperiment(assignment.ExperimentId)
		if err != nil {
			return errors.Wrapf(err, "failed to get experiment %s from registry", assignment.ExperimentId.String())
		}

		if !assignment.Enabled {
			return errors.Newf("experiment %s is not enabled", assignment.ExperimentId.String())
		}

		applicability, err := experimentFromRegistry.Apply(ctx, applicationId)
		if err != nil {
			return errors.Wrapf(
				err,
				"failed to apply experiment %s to app review %s",
				assignment.ExperimentId.String(),
				applicationId,
			)
		}

		if applicability != nil {
			// Apply post-applicability business rules
			m.applyBusinessRulesForApplicability(assignments, assignment, applicability)

			err = m.deps.ExperimentsClient.RecordExperimentApplicability(ctx, *applicability)
			if err != nil {
				return errors.Wrapf(err,
					"failed to insert experiment applicability for experiment %s for app review %s",
					assignment.ExperimentId.String(),
					applicationId,
				)
			}
		}
	}

	return nil
}

func (m Manager) RecordExperimentStateChangeEvent(ctx context.Context, experimentId, appId uuid.UUID, payload interface{}) error {
	event := models.ExperimentEvent{
		Id:           uuid.New(),
		ExperimentId: experimentId,
		SubjectId:    appId.String(),
		Name:         "ApplicationStateChange",
		Payload: models.EventPayload{
			Data: payload,
		},
		CreatedAt: m.deps.Clock.Now(),
	}
	err := m.deps.ExperimentsClient.RecordExperimentEvent(ctx, event)
	if err != nil {
		return errors.Wrapf(err, "failed to record experiment event")
	}
	return nil
}

func (m Manager) GetLatestExperimentApplicabilities(ctx context.Context, experimentId uuid.UUID, subjectIds []uuid.UUID) ([]models.ExperimentApplicability, error) {
	subjectIdsStr := slice_utils.Map(subjectIds, func(id uuid.UUID) string {
		return id.String()
	})
	applicabilties, err := m.deps.ExperimentsClient.GetLatestExperimentApplicabilities(ctx, experimentId, subjectIdsStr)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest experiment applicabilities")
	}
	return applicabilties, nil
}

// applyBusinessRulesForApplicability centralizes post-apply business logic overrides.
// Add new rules here to keep the main flow lean.
func (m Manager) applyBusinessRulesForApplicability(
	assignments []*experiments.Assignment,
	currentAssignment *experiments.Assignment,
	applicability *experiments.Applicability,
) {
	if applicability == nil || currentAssignment == nil {
		return
	}

	// Rule: If Express Lanes is applied and Pre-Telematics Quote is also assigned, block Express Lanes.
	// Currently version of Express Lanes is v1, so we are not checking for version.
	// We can add version check in future if needed. (e.g. if we have multiple versions of Pre Telematics Quote)
	if currentAssignment.ExperimentId == express_lane.ExpressLaneV1ExperimentId {
		preTelematicsQuoteExperimentId := pre_telematics_quote.PreQuoteTelematicsV1ExperimentId
		preTelematicsQuoteExperimentAssigned := slice_utils.Find(assignments, func(a *experiments.Assignment) bool {
			return a.ExperimentId == preTelematicsQuoteExperimentId && a.Enabled
		}) != nil

		if preTelematicsQuoteExperimentAssigned {
			applicability.Applied = false
			if applicability.Metadata == nil {
				applicability.Metadata = &experiments.ApplicabilityMetadata{}
			}
			applicability.Metadata.ExpressLanesBlockedDueToFlexQuote = pointer_utils.ToPointer(true)
		}
	}
}

func (m Manager) IsExperimentAppliedToSubject(ctx context.Context, experimentId, subjectId uuid.UUID) (*bool, error) {
	applicabilties, err := m.deps.ExperimentsClient.GetLatestExperimentApplicabilities(ctx, experimentId, []string{subjectId.String()})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get latest experiment applicabilities")
	}
	if len(applicabilties) == 0 {
		return pointer_utils.Bool(false), nil
	}
	return pointer_utils.ToPointer(applicabilties[0].Applied), nil
}
