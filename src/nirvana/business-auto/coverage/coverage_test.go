package coverage

import (
	"testing"
	"time"

	"github.com/stretchr/testify/suite"

	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

type CoverageTestSuite struct {
	suite.Suite
}

func TestCoverageTestSuite(t *testing.T) {
	suite.Run(t, new(CoverageTestSuite))
}

// Helper function to convert VehicleInfo to vehicleDeductibles format for APD coverage
func (s *CoverageTestSuite) buildAPDVehicleDeductiblesFromVehicleInfo(selectedCoverages []app_enums.Coverage, vehiclesInfo *[]model.VehicleInfo) map[app_enums.Coverage]map[string]float64 {
	vehicleDeductibles := make(map[app_enums.Coverage]map[string]float64)

	if vehiclesInfo == nil {
		return vehicleDeductibles
	}

	// Initialize maps for APD subcoverages if APD is selected
	for _, coverage := range selectedCoverages {
		if coverage == app_enums.CoverageAutoPhysicalDamage {
			vehicleDeductibles[app_enums.CoverageComprehensive] = make(map[string]float64)
			vehicleDeductibles[app_enums.CoverageCollision] = make(map[string]float64)
			break
		}
	}

	// Build vehicle deductibles from vehicle info
	for _, vehicleInfo := range *vehiclesInfo {
		if vehicleInfo.APDDeductible != nil {
			deductibleAmount := float64(*vehicleInfo.APDDeductible)
			if vehicleDeductibles[app_enums.CoverageComprehensive] != nil {
				vehicleDeductibles[app_enums.CoverageComprehensive][vehicleInfo.VIN] = deductibleAmount
			}
			if vehicleDeductibles[app_enums.CoverageCollision] != nil {
				vehicleDeductibles[app_enums.CoverageCollision][vehicleInfo.VIN] = deductibleAmount
			}
		}
	}

	return vehicleDeductibles
}

// Helper functions using simple, idiomatic Go for loops
func (s *CoverageTestSuite) findPrimaryCoverage(coverages []model.PrimaryCoverage, coverageID app_enums.Coverage) *model.PrimaryCoverage {
	for i := range coverages {
		if coverages[i].ID == coverageID {
			return &coverages[i]
		}
	}
	return nil
}

func (s *CoverageTestSuite) findCombinedLimit(limits []model.Limit, subcoverages ...app_enums.Coverage) *model.Limit {
	for i := range limits {
		limit := &limits[i]
		if limit.Grouping != model.LimitGroupingCombined {
			continue
		}
		if len(limit.SubCoverageIDs) != len(subcoverages) {
			continue
		}

		// Check if all subcoverages are present
		matches := 0
		for _, sub := range subcoverages {
			for _, limitSub := range limit.SubCoverageIDs {
				if sub == limitSub {
					matches++
					break
				}
			}
		}
		if matches == len(subcoverages) {
			return limit
		}
	}
	return nil
}

func (s *CoverageTestSuite) findSingleLimit(limits []model.Limit, subcoverage app_enums.Coverage) *model.Limit {
	for i := range limits {
		limit := &limits[i]
		if limit.Grouping == model.LimitGroupingSingle &&
			len(limit.SubCoverageIDs) == 1 &&
			limit.SubCoverageIDs[0] == subcoverage {
			return limit
		}
	}
	return nil
}

func (s *CoverageTestSuite) findCombinedDeductible(deductibles []model.Deductible, subcoverages ...app_enums.Coverage) *model.Deductible {
	for i := range deductibles {
		deductible := &deductibles[i]
		if len(deductible.SubCoverageIDs) != len(subcoverages) {
			continue
		}

		// Check if all subcoverages are present
		matches := 0
		for _, sub := range subcoverages {
			for _, dedSub := range deductible.SubCoverageIDs {
				if sub == dedSub {
					matches++
					break
				}
			}
		}
		if matches == len(subcoverages) {
			return deductible
		}
	}
	return nil
}

func (s *CoverageTestSuite) findSingleDeductible(deductibles []model.Deductible, subcoverage app_enums.Coverage) *model.Deductible {
	for i := range deductibles {
		deductible := &deductibles[i]
		if len(deductible.SubCoverageIDs) == 1 && deductible.SubCoverageIDs[0] == subcoverage {
			return deductible
		}
	}
	return nil
}

func (s *CoverageTestSuite) findVehicleDeductibles(deductibles []model.Deductible, subcoverage app_enums.Coverage) []string {
	var vehicleDeductibles []string
	for i := range deductibles {
		deductible := &deductibles[i]
		if len(deductible.SubCoverageIDs) == 1 && deductible.SubCoverageIDs[0] == subcoverage && deductible.VIN != nil {
			vehicleDeductibles = append(vehicleDeductibles, *deductible.VIN)
		}
	}
	return vehicleDeductibles
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_PrimaryCoverage() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoLiability}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 1000000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 0,
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		[]app_enums.Coverage{},
		nil, // No vehicle limits for AL coverage test
		nil, // No vehicles for AL coverage test
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 1)
	primaryCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoLiability)
	s.NotNil(primaryCoverage, "AL coverage should be present")
	s.Equal(app_enums.CoverageAutoLiability, primaryCoverage.ID)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageBodilyInjury)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoveragePropertyDamage)

	alLimit := s.findCombinedLimit(result.Limits, app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage)
	s.NotNil(alLimit, "AL limit should be present")
	s.Equal(float64(1000000), alLimit.Amount)
	s.Equal(model.LimitGroupingCombined, alLimit.Grouping)

	// AL deductibles are now separate for BI and PD
	s.Len(result.Deductibles, 2)

	biDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageBodilyInjury)
	s.NotNil(biDeductible, "BI deductible should be present")
	s.Equal(float64(0), biDeductible.Amount)

	pdDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoveragePropertyDamage)
	s.NotNil(pdDeductible, "PD deductible should be present")
	s.Equal(float64(0), pdDeductible.Amount)
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_APDCoverage() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoPhysicalDamage}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage: 50000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage: 1000,
	}

	// Create test vehicles with APD deductibles
	vehiclesInfo := []model.VehicleInfo{
		{
			VIN:           "1HGCM82633A123456",
			APDDeductible: pointer_utils.ToPointer(int64(1000)),
		},
		{
			VIN:           "1HGCM82633A123457",
			APDDeductible: pointer_utils.ToPointer(int64(2500)),
		},
	}

	vehicleDeductibles := s.buildAPDVehicleDeductiblesFromVehicleInfo(selectedCovs, &vehiclesInfo)

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		[]app_enums.Coverage{},
		nil, // No vehicle limits for APD coverage test
		vehicleDeductibles,
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 1)
	primaryCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoPhysicalDamage)
	s.NotNil(primaryCoverage, "APD coverage should be present")
	s.Equal(app_enums.CoverageAutoPhysicalDamage, primaryCoverage.ID)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageComprehensive)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageCollision)

	// Should have 2 limits (one for each subcoverage)
	s.Len(result.Limits, 2)

	// Find and verify Comprehensive limit
	comprLimit := s.findSingleLimit(result.Limits, app_enums.CoverageComprehensive)
	s.NotNil(comprLimit, "Comprehensive limit should be present")
	s.Equal(float64(50000), comprLimit.Amount)
	s.Equal(model.LimitGroupingSingle, comprLimit.Grouping)

	// Find and verify Collision limit
	collLimit := s.findSingleLimit(result.Limits, app_enums.CoverageCollision)
	s.NotNil(collLimit, "Collision limit should be present")
	s.Equal(float64(50000), collLimit.Amount)
	s.Equal(model.LimitGroupingSingle, collLimit.Grouping)

	// Should have 4 deductibles (2 vehicles x 2 subcoverages each)
	s.Len(result.Deductibles, 4)

	// Find and verify vehicle-specific Comprehensive deductibles
	comprDeductibles := s.findVehicleDeductibles(result.Deductibles, app_enums.CoverageComprehensive)
	s.Len(comprDeductibles, 2)
	s.Contains(comprDeductibles, "1HGCM82633A123456")
	s.Contains(comprDeductibles, "1HGCM82633A123457")

	// Find and verify vehicle-specific Collision deductibles
	collDeductibles := s.findVehicleDeductibles(result.Deductibles, app_enums.CoverageCollision)
	s.Len(collDeductibles, 2)
	s.Contains(collDeductibles, "1HGCM82633A123456")
	s.Contains(collDeductibles, "1HGCM82633A123457")
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_MixedCoverages() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoLiability, app_enums.CoverageAutoPhysicalDamage}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability:      1000000,
		app_enums.CoverageAutoPhysicalDamage: 50000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability:      0,
		app_enums.CoverageAutoPhysicalDamage: 1000,
	}

	// Create test vehicles with APD deductibles
	vehiclesInfo := []model.VehicleInfo{
		{
			VIN:           "1HGCM82633A123456",
			APDDeductible: pointer_utils.ToPointer(int64(1000)),
		},
	}

	vehicleDeductibles := s.buildAPDVehicleDeductiblesFromVehicleInfo(selectedCovs, &vehiclesInfo)

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		[]app_enums.Coverage{},
		nil, // No vehicle limits for mixed coverage test
		vehicleDeductibles,
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 2)

	// Find and verify AL coverage
	alCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoLiability)
	s.NotNil(alCoverage, "AL coverage should be present")
	s.Equal(app_enums.CoverageAutoLiability, alCoverage.ID)
	s.Contains(alCoverage.SubCoverageIDs, app_enums.CoverageBodilyInjury)
	s.Contains(alCoverage.SubCoverageIDs, app_enums.CoveragePropertyDamage)

	// Find and verify APD coverage
	apdCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoPhysicalDamage)
	s.NotNil(apdCoverage, "APD coverage should be present")
	s.Equal(app_enums.CoverageAutoPhysicalDamage, apdCoverage.ID)
	s.Contains(apdCoverage.SubCoverageIDs, app_enums.CoverageComprehensive)
	s.Contains(apdCoverage.SubCoverageIDs, app_enums.CoverageCollision)

	// Find and verify AL limit (combined for BI, PD)
	alLimit := s.findCombinedLimit(result.Limits, app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage)
	s.NotNil(alLimit, "AL combined limit should be present")
	s.Equal(float64(1000000), alLimit.Amount)
	s.Equal(model.LimitGroupingCombined, alLimit.Grouping)

	// Find and verify APD limits (individual for Comprehensive and Collision)
	comprLimit := s.findSingleLimit(result.Limits, app_enums.CoverageComprehensive)
	s.NotNil(comprLimit, "Comprehensive limit should be present")
	s.Equal(float64(50000), comprLimit.Amount)
	s.Equal(model.LimitGroupingSingle, comprLimit.Grouping)

	collLimit := s.findSingleLimit(result.Limits, app_enums.CoverageCollision)
	s.NotNil(collLimit, "Collision limit should be present")
	s.Equal(float64(50000), collLimit.Amount)
	s.Equal(model.LimitGroupingSingle, collLimit.Grouping)

	// Find and verify AL deductible (now separate for BI and PD)
	biDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageBodilyInjury)
	s.NotNil(biDeductible, "BI deductible should be present")
	s.Equal(float64(0), biDeductible.Amount)

	pdDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoveragePropertyDamage)
	s.NotNil(pdDeductible, "PD deductible should be present")
	s.Equal(float64(0), pdDeductible.Amount)

	// Find and verify APD deductibles (vehicle-specific for Comprehensive and Collision)
	comprDeductibles := s.findVehicleDeductibles(result.Deductibles, app_enums.CoverageComprehensive)
	s.Len(comprDeductibles, 1)
	s.Contains(comprDeductibles, "1HGCM82633A123456")

	collDeductibles := s.findVehicleDeductibles(result.Deductibles, app_enums.CoverageCollision)
	s.Len(collDeductibles, 1)
	s.Contains(collDeductibles, "1HGCM82633A123456")
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_InvalidLimitAmount() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoLiability}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 400000, // Invalid limit amount
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 0,
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		[]app_enums.Coverage{},
		nil, // No vehicle limits for invalid limit test
		nil, // No vehicles for invalid limit test
	)
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "invalid limit amount 400000 for coverage CoverageAutoLiability")
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_InvalidDeductibleAmount() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoPhysicalDamage}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage: 50000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage: 750, // Invalid deductible
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		[]app_enums.Coverage{},
		nil, // No vehicle limits for invalid deductible test
		nil, // No vehicles for invalid deductible test
	)
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "invalid deductible amount 750 for coverage CoverageAutoPhysicalDamage")
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_WithAncillaryCoverages() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoLiability}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 1000000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 0,
	}

	// Add ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoverageUninsuredMotoristBodilyInjury,
		app_enums.CoveragePersonalInjuryProtection,
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		ancillaryCoverages,
		nil, // No vehicle limits for ancillary coverage test
		nil, // No vehicles for ancillary coverage test
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 1)
	primaryCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoLiability)
	s.NotNil(primaryCoverage, "AL coverage should be present")
	s.Equal(app_enums.CoverageAutoLiability, primaryCoverage.ID)

	// Should have mandatory subcoverages (BI, PD) plus ancillary coverages
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageBodilyInjury)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoveragePropertyDamage)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageUninsuredMotoristBodilyInjury)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoveragePersonalInjuryProtection)
	s.Len(primaryCoverage.SubCoverageIDs, 4)

	// Should have limits: 1 for primary coverage (BI, PD) only
	// Ancillary coverages don't have their own specific limits, so they don't get limits
	s.Len(result.Limits, 1)

	// Find and verify primary coverage limit (combined for BI, PD only)
	primaryLimit := s.findCombinedLimit(result.Limits, app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage)
	s.NotNil(primaryLimit, "Primary AL limit should be present")
	s.Equal(float64(1000000), primaryLimit.Amount)
	s.Equal(model.LimitGroupingCombined, primaryLimit.Grouping)

	// Ancillary coverages should not have individual limits since they don't have specific values
	umLimit := s.findSingleLimit(result.Limits, app_enums.CoverageUninsuredMotoristBodilyInjury)
	s.Nil(umLimit, "UM limit should not be present since no specific limit was provided")

	pipLimit := s.findSingleLimit(result.Limits, app_enums.CoveragePersonalInjuryProtection)
	s.Nil(pipLimit, "PIP limit should not be present since no specific limit was provided")

	// Should have deductibles: 2 for primary coverage (BI and PD separately) only
	// Ancillary coverages don't have their own specific deductibles, so they don't get deductibles
	s.Len(result.Deductibles, 2)

	// Find and verify primary coverage deductible (now separate for BI and PD)
	biDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageBodilyInjury)
	s.NotNil(biDeductible, "BI deductible should be present")
	s.Equal(float64(0), biDeductible.Amount)
	s.Len(biDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoverageBodilyInjury, biDeductible.SubCoverageIDs[0])

	pdDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoveragePropertyDamage)
	s.NotNil(pdDeductible, "PD deductible should be present")
	s.Equal(float64(0), pdDeductible.Amount)
	s.Len(pdDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoveragePropertyDamage, pdDeductible.SubCoverageIDs[0])

	// Ancillary coverages should not have individual deductibles since they don't have specific values
	umDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageUninsuredMotoristBodilyInjury)
	s.Nil(umDeductible, "UM deductible should not be present since no specific deductible was provided")

	pipDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoveragePersonalInjuryProtection)
	s.Nil(pipDeductible, "PIP deductible should not be present since no specific deductible was provided")
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_WithAncillaryCoveragesWithSpecificLimits() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoLiability}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability:                 1000000,
		app_enums.CoverageUninsuredMotoristBodilyInjury: 500000, // Specific limit for UM
		app_enums.CoveragePersonalInjuryProtection:      25000,  // Specific limit for PIP
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability:                 0,
		app_enums.CoverageUninsuredMotoristBodilyInjury: 1000, // Specific deductible for UM
		app_enums.CoveragePersonalInjuryProtection:      500,  // Specific deductible for PIP
	}

	// Add ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoverageUninsuredMotoristBodilyInjury,
		app_enums.CoveragePersonalInjuryProtection,
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		ancillaryCoverages,
		nil, // No vehicle limits for ancillary coverage with limits test
		nil, // No vehicles for ancillary coverage with limits test
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 1)
	primaryCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoLiability)
	s.NotNil(primaryCoverage, "AL coverage should be present")
	s.Equal(app_enums.CoverageAutoLiability, primaryCoverage.ID)

	// Should have mandatory subcoverages (BI, PD) plus ancillary coverages
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageBodilyInjury)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoveragePropertyDamage)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageUninsuredMotoristBodilyInjury)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoveragePersonalInjuryProtection)
	s.Len(primaryCoverage.SubCoverageIDs, 4)

	// Should have limits: 1 for primary coverage (BI, PD) + 2 for ancillary coverages with specific limits
	s.Len(result.Limits, 3)

	// Find and verify primary coverage limit (combined for BI, PD only)
	primaryLimit := s.findCombinedLimit(result.Limits, app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage)
	s.NotNil(primaryLimit, "Primary AL limit should be present")
	s.Equal(float64(1000000), primaryLimit.Amount)
	s.Equal(model.LimitGroupingCombined, primaryLimit.Grouping)

	// Find and verify ancillary coverage limits (individual) with their specific values
	umLimit := s.findSingleLimit(result.Limits, app_enums.CoverageUninsuredMotoristBodilyInjury)
	s.NotNil(umLimit, "UM limit should be present with specific value")
	s.Equal(float64(500000), umLimit.Amount)
	s.Equal(model.LimitGroupingSingle, umLimit.Grouping)

	pipLimit := s.findSingleLimit(result.Limits, app_enums.CoveragePersonalInjuryProtection)
	s.NotNil(pipLimit, "PIP limit should be present with specific value")
	s.Equal(float64(25000), pipLimit.Amount)
	s.Equal(model.LimitGroupingSingle, pipLimit.Grouping)

	// Should have deductibles: 2 for primary coverage (BI and PD separately) + 2 for ancillary coverages with specific deductibles
	s.Len(result.Deductibles, 4)

	// Find and verify primary coverage deductible (now separate for BI and PD)
	biDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageBodilyInjury)
	s.NotNil(biDeductible, "BI deductible should be present")
	s.Equal(float64(0), biDeductible.Amount)
	s.Len(biDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoverageBodilyInjury, biDeductible.SubCoverageIDs[0])

	pdDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoveragePropertyDamage)
	s.NotNil(pdDeductible, "PD deductible should be present")
	s.Equal(float64(0), pdDeductible.Amount)
	s.Len(pdDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoveragePropertyDamage, pdDeductible.SubCoverageIDs[0])

	// Find and verify ancillary coverage deductibles (individual) with their specific values
	umDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageUninsuredMotoristBodilyInjury)
	s.NotNil(umDeductible, "UM deductible should be present with specific value")
	s.Equal(float64(1000), umDeductible.Amount)
	s.Len(umDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoverageUninsuredMotoristBodilyInjury, umDeductible.SubCoverageIDs[0])

	pipDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoveragePersonalInjuryProtection)
	s.NotNil(pipDeductible, "PIP deductible should be present with specific value")
	s.Equal(float64(500), pipDeductible.Amount)
	s.Len(pipDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoveragePersonalInjuryProtection, pipDeductible.SubCoverageIDs[0])
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_AncillaryCoverageWithoutPrimaryCoverage() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoPhysicalDamage} // Only APD selected
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage: 50000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage: 1000,
	}

	// Add ancillary coverage that requires AL (which is not selected)
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoverageUninsuredMotoristBodilyInjury, // Requires AL
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		ancillaryCoverages,
		nil, // No vehicle limits for ancillary coverage error test
		nil, // No vehicles for ancillary coverage error test
	)
	s.Error(err)
	s.Nil(result)
	s.Contains(err.Error(), "ancillary coverage CoverageUninsuredMotoristBodilyInjury requires primary coverage CoverageAutoLiability to be selected")
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_EmptyAncillaryCoverages() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoLiability}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 1000000,
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoLiability: 0,
	}

	// Empty ancillary coverages
	ancillaryCoverages := []app_enums.Coverage{}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		ancillaryCoverages,
		nil, // No vehicle limits for empty ancillary coverage test
		nil, // No vehicles for empty ancillary coverage test
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 1)
	primaryCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoLiability)
	s.NotNil(primaryCoverage, "AL coverage should be present")
	s.Equal(app_enums.CoverageAutoLiability, primaryCoverage.ID)

	// Should only have mandatory subcoverages (BI, PD)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageBodilyInjury)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoveragePropertyDamage)
	s.Len(primaryCoverage.SubCoverageIDs, 2)

	// Should only have limits/deductibles for primary coverage
	s.Len(result.Limits, 1)
	s.Len(result.Deductibles, 2) // Separate deductibles for BI and PD
}

func (s *CoverageTestSuite) TestBuildCoverageInfo_APDWithAncillaryCoverages() {
	selectedCovs := []app_enums.Coverage{app_enums.CoverageAutoPhysicalDamage}
	limits := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage:         50000,
		app_enums.CoverageRentalReimbursement:        1000, // Specific limit for RentalReimbursement
		app_enums.CoverageEnhancedPackageTowingLimit: 500,  // Specific limit for EnhancedPackageTowingLimit
	}
	deductibles := map[app_enums.Coverage]float64{
		app_enums.CoverageAutoPhysicalDamage:         1000,
		app_enums.CoverageRentalReimbursement:        250, // Specific deductible for RentalReimbursement
		app_enums.CoverageEnhancedPackageTowingLimit: 100, // Specific deductible for EnhancedPackageTowingLimit
	}

	// Create test vehicles with APD deductibles
	vehiclesInfo := []model.VehicleInfo{
		{
			VIN:           "1HGCM82633A123456",
			APDDeductible: pointer_utils.ToPointer(int64(1000)),
		},
		{
			VIN:           "1HGCM82633A123457",
			APDDeductible: pointer_utils.ToPointer(int64(2500)),
		},
	}

	vehicleDeductibles := s.buildAPDVehicleDeductiblesFromVehicleInfo(selectedCovs, &vehiclesInfo)

	// Add ancillary coverages that belong to APD
	ancillaryCoverages := []app_enums.Coverage{
		app_enums.CoverageRentalReimbursement,        // Example ancillary coverage for APD
		app_enums.CoverageEnhancedPackageTowingLimit, // Another example ancillary coverage for APD
	}

	result, err := BuildCoverageInfo(
		time.Now(),
		us_states.OH,
		selectedCovs,
		limits,
		deductibles,
		ancillaryCoverages,
		nil, // No vehicle limits for APD with ancillary coverage test
		vehicleDeductibles,
	)
	s.NoError(err)
	s.NotNil(result)

	s.Len(result.PrimaryCoverages, 1)
	primaryCoverage := s.findPrimaryCoverage(result.PrimaryCoverages, app_enums.CoverageAutoPhysicalDamage)
	s.NotNil(primaryCoverage, "APD coverage should be present")
	s.Equal(app_enums.CoverageAutoPhysicalDamage, primaryCoverage.ID)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageComprehensive)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageCollision)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageRentalReimbursement)
	s.Contains(primaryCoverage.SubCoverageIDs, app_enums.CoverageEnhancedPackageTowingLimit)
	s.Len(primaryCoverage.SubCoverageIDs, 4)

	// Should have limits: 2 for primary coverage (Comprehensive, Collision) + 2 for ancillary coverages with specific limits
	s.Len(result.Limits, 4)

	// Find and verify APD limits (individual for Comprehensive and Collision)
	comprLimit := s.findSingleLimit(result.Limits, app_enums.CoverageComprehensive)
	s.NotNil(comprLimit, "Comprehensive limit should be present")
	s.Equal(float64(50000), comprLimit.Amount)
	s.Equal(model.LimitGroupingSingle, comprLimit.Grouping)

	collLimit := s.findSingleLimit(result.Limits, app_enums.CoverageCollision)
	s.NotNil(collLimit, "Collision limit should be present")
	s.Equal(float64(50000), collLimit.Amount)
	s.Equal(model.LimitGroupingSingle, collLimit.Grouping)

	// Find and verify ancillary coverage limits (individual) with their specific values
	rentalLimit := s.findSingleLimit(result.Limits, app_enums.CoverageRentalReimbursement)
	s.NotNil(rentalLimit, "RentalReimbursement limit should be present with specific value")
	s.Equal(float64(1000), rentalLimit.Amount)
	s.Equal(model.LimitGroupingSingle, rentalLimit.Grouping)

	towingLimit := s.findSingleLimit(result.Limits, app_enums.CoverageEnhancedPackageTowingLimit)
	s.NotNil(towingLimit, "EnhancedPackageTowingLimit limit should be present with specific value")
	s.Equal(float64(500), towingLimit.Amount)
	s.Equal(model.LimitGroupingSingle, towingLimit.Grouping)

	// Should have deductibles: 4 for primary coverage (2 vehicles x 2 subcoverages) + 2 for ancillary coverages with specific deductibles
	s.Len(result.Deductibles, 6)

	// Find and verify vehicle-specific APD deductibles
	comprDeductibles := s.findVehicleDeductibles(result.Deductibles, app_enums.CoverageComprehensive)
	s.Len(comprDeductibles, 2)
	s.Contains(comprDeductibles, "1HGCM82633A123456")
	s.Contains(comprDeductibles, "1HGCM82633A123457")

	collDeductibles := s.findVehicleDeductibles(result.Deductibles, app_enums.CoverageCollision)
	s.Len(collDeductibles, 2)
	s.Contains(collDeductibles, "1HGCM82633A123456")
	s.Contains(collDeductibles, "1HGCM82633A123457")

	// Find and verify ancillary coverage deductibles (individual) with their specific values
	rentalDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageRentalReimbursement)
	s.NotNil(rentalDeductible, "RentalReimbursement deductible should be present with specific value")
	s.Equal(float64(250), rentalDeductible.Amount)
	s.Len(rentalDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoverageRentalReimbursement, rentalDeductible.SubCoverageIDs[0])

	towingDeductible := s.findSingleDeductible(result.Deductibles, app_enums.CoverageEnhancedPackageTowingLimit)
	s.NotNil(towingDeductible, "EnhancedPackageTowingLimit deductible should be present with specific value")
	s.Equal(float64(100), towingDeductible.Amount)
	s.Len(towingDeductible.SubCoverageIDs, 1)
	s.Equal(app_enums.CoverageEnhancedPackageTowingLimit, towingDeductible.SubCoverageIDs[0])
}
