package coverage

import (
	"errors"
	"sort"

	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

// Sentinel errors for specific cases
var (
	ErrEmptySubCoverages = errors.New("no sub-coverages provided")
	ErrUnsupportedState  = errors.New("state has no coverage expansion mappings")
	ErrNoMatchingParent  = errors.New("no matching parent coverage found for provided sub-coverage group")
)

// ExpansionMapping defines the expansion details for a parent coverage
type ExpansionMapping struct {
	SubCoverages []app_enums.Coverage
	Grouping     model.LimitGrouping
}

// expansionMappings is the global map of state -> parent coverage -> expansion details
var expansionMappings = map[us_states.USState]map[app_enums.Coverage]ExpansionMapping{
	us_states.TX: {
		app_enums.CoveragePersonalInjuryProtection: {
			SubCoverages: []app_enums.Coverage{
				app_enums.CoverageMedicalExpenseBenefits,
				app_enums.CoverageFuneralExpenseBenefits,
				app_enums.CoverageWorkLossBenefits,
				app_enums.CoverageEssentialServiceExpenses,
			},
			Grouping: model.LimitGroupingCombined,
		},
		app_enums.CoverageUMUIM: {
			SubCoverages: []app_enums.Coverage{
				app_enums.CoverageUninsuredMotoristBodilyInjury,
				app_enums.CoverageUnderinsuredMotoristBodilyInjury,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
				app_enums.CoverageUnderinsuredMotoristPropertyDamage,
			},
			Grouping: model.LimitGroupingCombined,
		},
	},
	us_states.GA: {
		app_enums.CoverageUMUIMAddedTo: {
			SubCoverages: []app_enums.Coverage{
				app_enums.CoverageUninsuredMotoristBodilyInjury,
				app_enums.CoverageUnderinsuredMotoristBodilyInjury,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
				app_enums.CoverageUnderinsuredMotoristPropertyDamage,
			},
			Grouping: model.LimitGroupingCombined,
		},
		app_enums.CoverageUMUIMReducedBy: {
			SubCoverages: []app_enums.Coverage{
				app_enums.CoverageUninsuredMotoristBodilyInjury,
				app_enums.CoverageUnderinsuredMotoristBodilyInjury,
				app_enums.CoverageUninsuredMotoristPropertyDamage,
				app_enums.CoverageUnderinsuredMotoristPropertyDamage,
			},
			Grouping: model.LimitGroupingCombined,
		},
	},
}

// GetExpansionMapping returns the expected sub-coverages and grouping for a parent coverage in a given state.
// ok=false means no expansion defined for this state+parent combination.
func GetExpansionMapping(state us_states.USState, parent app_enums.Coverage) ([]app_enums.Coverage, model.LimitGrouping, bool) {
	stateMap, stateExists := expansionMappings[state]
	if !stateExists {
		return nil, model.LimitGroupingSingle, false
	}

	mapping, parentExists := stateMap[parent]
	if !parentExists {
		return nil, model.LimitGroupingSingle, false
	}

	return mapping.SubCoverages, mapping.Grouping, true
}

// GetParentCoverageFromSubCoverage performs reverse mapping: given a state and a slice of sub-coverages,
// returns the first parent coverage if there's an exact match.
// Returns specific errors for different failure cases:
//   - ErrEmptySubCoverages: when no sub-coverages are provided
//   - ErrUnsupportedState: when the state has no expansion mappings
//   - ErrNoMatchingParent: when sub-coverages don't match any valid parent in supported state
//
// The sub-coverages slice order doesn't matter - it will be sorted for comparison.
func GetParentCoverageFromSubCoverage(state us_states.USState, subCoverages []app_enums.Coverage) (*app_enums.Coverage, error) {
	if len(subCoverages) == 0 {
		return nil, ErrEmptySubCoverages
	}

	// Sort the input for consistent comparison
	inputSorted := make([]app_enums.Coverage, len(subCoverages))
	copy(inputSorted, subCoverages)
	sort.Slice(inputSorted, func(i, j int) bool {
		return inputSorted[i] < inputSorted[j]
	})

	// Check all parent coverages for this state using the global map
	stateMap, stateExists := expansionMappings[state]
	if !stateExists {
		return nil, ErrUnsupportedState
	}

	for parentCoverage, mapping := range stateMap {
		// Sort the expected sub-coverages for comparison
		expectedSorted := make([]app_enums.Coverage, len(mapping.SubCoverages))
		copy(expectedSorted, mapping.SubCoverages)
		sort.Slice(expectedSorted, func(i, j int) bool {
			return expectedSorted[i] < expectedSorted[j]
		})

		if coveragesEqual(inputSorted, expectedSorted) {
			result := parentCoverage
			return &result, nil
		}
	}

	return nil, ErrNoMatchingParent
}

// coveragesEqual compares two sorted slices of coverages for equality
func coveragesEqual(a, b []app_enums.Coverage) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}
