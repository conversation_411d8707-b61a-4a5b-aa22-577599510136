package client_test

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/google/uuid"
	"go.uber.org/mock/gomock"

	claims_enums "nirvanatech.com/nirvana/claims/enums"
	"nirvanatech.com/nirvana/claims/fnols/db"
	fnol_enums "nirvanatech.com/nirvana/claims/fnols/enums"
	"nirvanatech.com/nirvana/claims/snapsheet/internal"
	oapi "nirvanatech.com/nirvana/claims/snapsheet/internal/openapi/components"
	file_enums "nirvanatech.com/nirvana/common-go/file_upload_lib/enums"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/file_upload"
	policy_constants "nirvanatech.com/nirvana/policy_common/constants"
)

// TODO(IE-1064): add tests for endorsement attachments
func (s *snapsheetClientTestSuite) TestSubmitFnol() {
	ctx := context.Background()

	fnolId := uuid.New()
	mockClaimReferenceNumber := "12345"
	now := time.Now().UTC().Round(time.Minute)

	prodAgency := s.seedAgency(ctx, agencyOptions{isTest: false})
	prodPolicyMstSpecialty := s.seedPolicy(ctx, prodAgency.ID, "1234567", policy_constants.InsuranceCarrierMSTSpeciality)
	prodPolicyMst := s.seedPolicy(ctx, prodAgency.ID, "7231563", policy_constants.InsuranceCarrierMSTransverse)

	testAgency := s.seedAgency(ctx, agencyOptions{isTest: true})
	testPolicy := s.seedPolicy(ctx, testAgency.ID, "7654321", policy_constants.InsuranceCarrierFalseLake)

	validReporter, err := db.NewFnolContactBuilder().
		WithDefaultMockData().
		WithFnolId(fnolId).
		WithContactType(fnol_enums.FnolContactTypeReporter).
		WithFirstName("John").
		WithLastName("Doe").
		WithEmail(pointer_utils.String("<EMAIL>")).
		WithPhone("************").
		Build()
	s.Require().NoError(err)

	insuredVehicle, err := db.NewFnolVehicleBuilder().
		WithDefaultMockData().
		WithFnolId(fnolId).
		WithIsInsuredVehicle(true).
		WithVIN(pointer_utils.String("1HGBH41JXMN109186")).
		WithRegistrationNumber(pointer_utils.String("ABC123")).
		Build()
	s.Require().NoError(err)

	nonInsuredVehicle, err := db.NewFnolVehicleBuilder().
		WithDefaultMockData().
		WithFnolId(fnolId).
		WithIsInsuredVehicle(false).
		WithVIN(pointer_utils.String("2HGBH41JXMN109187")).
		WithRegistrationNumber(pointer_utils.String("XYZ789")).
		Build()
	s.Require().NoError(err)

	validSendableProdSnapsheetFnol, err := db.NewClaimFnolBuilder(claims_enums.ClaimsProviderSnapsheet).
		WithDefaultMockData().
		WithID(fnolId).
		WithNoticeType(pointer_utils.ToPointer(fnol_enums.FnolNoticeTypeClaim)).
		WithPolicyNumber(pointer_utils.String(prodPolicyMstSpecialty.PolicyNumber.String())).
		WithClientClaimNumber(pointer_utils.String("ccn:1234567-25")).
		WithLossDatetime(pointer_utils.Time(now)).
		WithLossLocation(pointer_utils.String("123 Main St, Anytown, USA")).
		WithLossState(pointer_utils.String("CA")).
		WithIncidentDescription(pointer_utils.String("Car accident on the 101 freeway")).
		WithInjuriesInvolved(pointer_utils.Bool(true)).
		WithPoliceAgencyName(pointer_utils.String("LAPD")).
		WithPoliceReportNumber(pointer_utils.String("PR-2024-001")).
		WithContacts([]db.FnolContact{*validReporter}).
		WithVehicles([]db.FnolVehicle{*insuredVehicle, *nonInsuredVehicle}).
		WithAttachments([]db.FnolAttachment{
			{
				Id:       uuid.New(),
				FnolId:   fnolId,
				HandleId: uuid.New(),
			},
			{
				Id:       uuid.New(),
				FnolId:   fnolId,
				HandleId: uuid.New(),
			},
		}).
		WithStatus(fnol_enums.FnolStatusSendable).
		Build()
	s.Require().NoError(err)
	s.Require().NoError(validSendableProdSnapsheetFnol.Validate())

	insertClaimFile := func(attachment db.FnolAttachment, key string) {
		s.Require().NoError(s.fileUploadWrapper.InsertFile(ctx, file_upload.NewFile(
			attachment.HandleId,
			attachment.FnolId,
			key,
			file_enums.FileDestinationGroupClaims,
		)))
	}
	insertClaimFile(validSendableProdSnapsheetFnol.Attachments[0], "key/fnol-attachment-1.txt")
	insertClaimFile(validSendableProdSnapsheetFnol.Attachments[1], "key/fnol-attachment-2.txt")

	insertPolicyFile := func(handleId uuid.UUID, key string) {
		s.Require().NoError(s.fileUploadWrapper.InsertFile(ctx, file_upload.NewFile(
			handleId,
			uuid.New(),
			key,
			file_enums.FileDestinationGroupPDFGen,
		)))
	}
	insertPolicyFile(prodPolicyMstSpecialty.DocumentHandleId, "key/prod-policy-document.pdf")
	insertPolicyFile(prodPolicyMst.DocumentHandleId, "key/prod-policy-document.pdf")
	insertPolicyFile(testPolicy.DocumentHandleId, "key/test-policy-document.pdf")

	wantProdClaimRequest := oapi.CreateClaimRequest{
		PolicyNumber:   prodPolicyMstSpecialty.PolicyNumber.String(),
		DatetimeOfLoss: now,
		AccountCode:    oapi.InsuranceCarrierMsTransverseSpecialty,
		LossType:       oapi.AutoClaimCollisionWithMotorVehicle,
		ClaimType:      oapi.Auto,
		ClaimNumber:    "ccn:1234567-25",
		ClaimParties: []oapi.ClaimParty{{
			PartyType: oapi.PERSON,
			Id:        "<EMAIL>",
			FirstName: "John",
			LastName:  "Doe",
			ContactMethods: &[]oapi.ClaimPartyContactMethod{
				{
					Type:  oapi.ClaimPartyContactMethodTypeEmail,
					Value: "<EMAIL>",
				},
				{
					Type:        oapi.ClaimPartyContactMethodTypePhone,
					Value:       "************",
					Country:     pointer_utils.String("us"),
					CountryCode: pointer_utils.String("1"),
				},
			},
		}},
		ClaimIncidentDetails: &oapi.ClaimIncidentDetails{
			IncidentLocationType:        pointer_utils.String("other"),
			IncidentLocationDescription: pointer_utils.String("123 Main St, Anytown, USA"),
			IncidentLocationAddress: &oapi.ClaimIncidentLocationAddress{
				Region:  pointer_utils.String("CA"),
				Country: pointer_utils.String("US"),
			},
			AutoClaimCollisionWithMotorVehicleIncidentDetail: &oapi.ClaimIncidentDetail{
				FactsOfLoss: "Car accident on the 101 freeway",
			},
		},
		EmergencyServicesDetail: &oapi.ClaimEmergencyServicesDetail{
			PoliceAttended:     pointer_utils.Bool(true),
			PoliceStation:      pointer_utils.String("LAPD"),
			PoliceReport:       pointer_utils.Bool(true),
			PoliceReportNumber: pointer_utils.String("PR-2024-001"),
		},
		Vehicles: &[]oapi.ClaimVehicle{
			{
				Id:                 insuredVehicle.Id.String(),
				Make:               "1HGBH41JXMN109186 (VIN)",
				Model:              "unknown",
				VinNumber:          pointer_utils.String("1HGBH41JXMN109186"),
				RegistrationNumber: pointer_utils.String("ABC123"),
			},
			{
				Id:                 nonInsuredVehicle.Id.String(),
				Make:               "2HGBH41JXMN109187 (VIN)",
				Model:              "unknown",
				VinNumber:          pointer_utils.String("2HGBH41JXMN109187"),
				RegistrationNumber: pointer_utils.String("XYZ789"),
			},
		},
		Exposures: &[]oapi.ClaimExposure{
			{
				Id:           "exposure_" + insuredVehicle.Id.String(),
				ExposureType: oapi.Vehicle,
				LossParty:    oapi.Insured,
				Vehicle: &oapi.ClaimExposureVehicle{
					VehicleId: insuredVehicle.Id.String(),
				},
			},
			{
				Id:           "exposure_" + nonInsuredVehicle.Id.String(),
				ExposureType: oapi.Vehicle,
				LossParty:    oapi.ThirdParty,
				Vehicle: &oapi.ClaimExposureVehicle{
					VehicleId: nonInsuredVehicle.Id.String(),
				},
			},
		},
		NotificationMethod: oapi.ONLINE,
		Notifier: &oapi.ClaimNotifier{
			ClaimPartyId: "<EMAIL>",
		},
	}

	wantProdUpdateClaimRequest := oapi.UpdateClaimRequest{
		Data: oapi.UpdateClaimRequestData{
			Id:   mockClaimReferenceNumber,
			Type: "claim",
			Attributes: oapi.UpdateClaimRequestDataAttributes{
				CfFrontingCarrierUat7b39:   "MST Specialty",
				CfInjuriesInvolvedUatB662:  true,
				CfInsuredNameUatCbbb:       pointer_utils.String("Nirvana Trucking"),
				CfReportOnlyUat915a:        pointer_utils.Bool(false),
				CfInsuredDotUatF4a0:        "123456",
				CfFrontingCarrierProdE809:  "MST Specialty",
				CfInjuriesInvolvedProd6a0b: true,
				CfInsuredNameProd2ee5:      pointer_utils.String("Nirvana Trucking"),
				CfReportOnlyProd7595:       pointer_utils.Bool(false),
				CfInsuredDotProdCa0d:       "123456",
			},
		},
	}

	wantProdAttachmentRequests := []oapi.CreateAttachmentRequest{
		{
			Data: &oapi.CreateAttachmentData{
				Type: "attachment",
				Attributes: oapi.ClaimAttachmentAttribute{
					Name: "prod-policy-document.pdf",
					Url:  "https://s3.amazonaws.com/nirvana-pdfgen/key/prod-policy-document.pdf",
				},
				Relationships: oapi.ClaimAttachmentRelationship{
					AttachmentTarget: &oapi.ClaimAttachmentTarget{
						Data: oapi.ClaimAttachmentRelationshipData{
							Type: "claim",
							Id:   mockClaimReferenceNumber,
						},
					},
				},
			},
		},
		{
			Data: &oapi.CreateAttachmentData{
				Type: "attachment",
				Attributes: oapi.ClaimAttachmentAttribute{
					Name: "fnol-attachment-1.txt",
					Url:  "https://s3.amazonaws.com/nirvana-claims/key/fnol-attachment-1.txt",
				},
				Relationships: oapi.ClaimAttachmentRelationship{
					AttachmentTarget: &oapi.ClaimAttachmentTarget{
						Data: oapi.ClaimAttachmentRelationshipData{
							Type: "claim",
							Id:   mockClaimReferenceNumber,
						},
					},
				},
			},
		},
		{
			Data: &oapi.CreateAttachmentData{
				Type: "attachment",
				Attributes: oapi.ClaimAttachmentAttribute{
					Name: "fnol-attachment-2.txt",
					Url:  "https://s3.amazonaws.com/nirvana-claims/key/fnol-attachment-2.txt",
				},
				Relationships: oapi.ClaimAttachmentRelationship{
					AttachmentTarget: &oapi.ClaimAttachmentTarget{
						Data: oapi.ClaimAttachmentRelationshipData{
							Type: "claim",
							Id:   mockClaimReferenceNumber,
						},
					},
				},
			},
		},
	}

	var gotCreateClaimRequest *oapi.CreateClaimRequest
	var gotCreateClaimRequestOpts *internal.ReqOptions
	var gotUpdateClaimRequest *oapi.UpdateClaimRequest
	var gotUpdateClaimRequestOpts *internal.ReqOptions
	var gotCreateAttachmentRequests []oapi.CreateAttachmentRequest
	var gotCreateAttachmentOpts []internal.ReqOptions

	_ = s.snapsheetOapiClient.EXPECT().
		CreateClaim(gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes().
		DoAndReturn(func(
			ctx context.Context,
			req oapi.CreateClaimRequest,
			opts *internal.ReqOptions,
		) (*oapi.CreateClaim200Response, error) {
			gotCreateClaimRequest = &req
			gotCreateClaimRequestOpts = opts

			claimReferenceNumber, err := strconv.Atoi(mockClaimReferenceNumber)
			s.Require().NoError(err)

			return &oapi.CreateClaim200Response{
				ClaimReferenceNumber: claimReferenceNumber,
			}, nil
		})

	_ = s.snapsheetOapiClient.EXPECT().
		UpdateClaim(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes().
		DoAndReturn(func(
			ctx context.Context,
			snapsheetId string,
			req oapi.UpdateClaimRequest,
			opts *internal.ReqOptions,
		) error {
			gotUpdateClaimRequest = &req
			gotUpdateClaimRequestOpts = opts
			return nil
		})

	_ = s.snapsheetOapiClient.EXPECT().
		CreateAttachment(gomock.Any(), gomock.Any(), gomock.Any()).
		AnyTimes().
		DoAndReturn(func(
			ctx context.Context,
			req oapi.CreateAttachmentRequest,
			opts *internal.ReqOptions,
		) (*oapi.CreateAttachment201Response, error) {
			gotCreateAttachmentRequests = append(gotCreateAttachmentRequests, req)
			gotCreateAttachmentOpts = append(gotCreateAttachmentOpts, *opts)
			return &oapi.CreateAttachment201Response{}, nil
		})

	testCases := []struct {
		name                          string
		fnolFactory                   func() db.ClaimFnol
		wantExternalId                string
		wantClaimRequestFactory       func() oapi.CreateClaimRequest
		wantUpdateClaimRequestFactory func() oapi.UpdateClaimRequest
		wantAttachmentRequestsFactory func() []oapi.CreateAttachmentRequest
		wantSandboxed                 bool
		wantErr                       bool
	}{
		{
			name: "valid sendable FNOL (Snapsheet, Prod Agency)",
			fnolFactory: func() db.ClaimFnol {
				return *validSendableProdSnapsheetFnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				return wantProdClaimRequest
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				return wantProdUpdateClaimRequest
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return wantProdAttachmentRequests
			},
			wantSandboxed: false,
			wantErr:       false,
		},
		{
			name: "valid sendable FNOL with no police info (Snapsheet, Prod Agency)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.PoliceAgencyName = nil
				fnol.PoliceReportNumber = nil
				return fnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				req := wantProdClaimRequest
				req.EmergencyServicesDetail = &oapi.ClaimEmergencyServicesDetail{
					PoliceAttended:     pointer_utils.Bool(false),
					PoliceStation:      nil,
					PoliceReport:       pointer_utils.Bool(false),
					PoliceReportNumber: nil,
				}
				return req
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				return wantProdUpdateClaimRequest
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return wantProdAttachmentRequests
			},
			wantSandboxed: false,
			wantErr:       false,
		},
		{
			name: "valid sendable FNOL (Snapsheet, Test Agency)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.PolicyNumber = pointer_utils.String(testPolicy.PolicyNumber.String())
				return fnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				req := wantProdClaimRequest
				req.AccountCode = oapi.FinancialAccountCode("")
				req.PolicyNumber = testPolicy.PolicyNumber.String()
				return req
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				req := wantProdUpdateClaimRequest
				req.Data.Attributes.CfFrontingCarrierUat7b39 = "FLI"
				return req
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return []oapi.CreateAttachmentRequest{
					{
						Data: &oapi.CreateAttachmentData{
							Type: "attachment",
							Attributes: oapi.ClaimAttachmentAttribute{
								Name: "test-policy-document.pdf",
								Url:  "https://s3.amazonaws.com/nirvana-pdfgen/key/test-policy-document.pdf",
							},
							Relationships: oapi.ClaimAttachmentRelationship{
								AttachmentTarget: &oapi.ClaimAttachmentTarget{
									Data: oapi.ClaimAttachmentRelationshipData{
										Type: "claim",
										Id:   mockClaimReferenceNumber,
									},
								},
							},
						},
					},
					{
						Data: &oapi.CreateAttachmentData{
							Type: "attachment",
							Attributes: oapi.ClaimAttachmentAttribute{
								Name: "fnol-attachment-1.txt",
								Url:  "https://s3.amazonaws.com/nirvana-claims/key/fnol-attachment-1.txt",
							},
							Relationships: oapi.ClaimAttachmentRelationship{
								AttachmentTarget: &oapi.ClaimAttachmentTarget{
									Data: oapi.ClaimAttachmentRelationshipData{
										Type: "claim",
										Id:   mockClaimReferenceNumber,
									},
								},
							},
						},
					},
					{
						Data: &oapi.CreateAttachmentData{
							Type: "attachment",
							Attributes: oapi.ClaimAttachmentAttribute{
								Name: "fnol-attachment-2.txt",
								Url:  "https://s3.amazonaws.com/nirvana-claims/key/fnol-attachment-2.txt",
							},
							Relationships: oapi.ClaimAttachmentRelationship{
								AttachmentTarget: &oapi.ClaimAttachmentTarget{
									Data: oapi.ClaimAttachmentRelationshipData{
										Type: "claim",
										Id:   mockClaimReferenceNumber,
									},
								},
							},
						},
					},
				}
			},
			wantSandboxed: true,
			wantErr:       false,
		},
		{
			name: "valid draft FNOL (Snapsheet)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.Status = fnol_enums.FnolStatusDraft
				return fnol
			},
			wantErr: true,
		},
		{
			name: "valid sendable FNOL (NARS)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.Source = claims_enums.ClaimsProviderNars
				return fnol
			},
			wantErr: true,
		},
		{
			name: "valid sendable FNOL with no insured vehicles (defaults to unknown vehicle)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.Vehicles = []db.FnolVehicle{}
				return fnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				req := wantProdClaimRequest

				wantDefaultVehicleId := uuid_utils.StableUUID(fmt.Sprintf("default_vehicle_%s", fnolId)).String()
				req.Vehicles = &[]oapi.ClaimVehicle{
					{
						Id:    wantDefaultVehicleId,
						Make:  "unknown",
						Model: "unknown",
					},
				}

				defaultVehicleExposureId := fmt.Sprintf("exposure_%s", wantDefaultVehicleId)
				req.Exposures = &[]oapi.ClaimExposure{
					{
						Id:           defaultVehicleExposureId,
						ExposureType: oapi.Vehicle,
						LossParty:    oapi.Insured,
						Vehicle: &oapi.ClaimExposureVehicle{
							VehicleId: wantDefaultVehicleId,
						},
					},
				}
				return req
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				return wantProdUpdateClaimRequest
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return wantProdAttachmentRequests
			},
			wantSandboxed: false,
			wantErr:       false,
		},
		{
			name: "valid sendable FNOL with no attachments (only policy document)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.Attachments = []db.FnolAttachment{}
				return fnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				return wantProdClaimRequest
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				return wantProdUpdateClaimRequest
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return []oapi.CreateAttachmentRequest{
					{
						Data: &oapi.CreateAttachmentData{
							Type: "attachment",
							Attributes: oapi.ClaimAttachmentAttribute{
								Name: "prod-policy-document.pdf",
								Url:  "https://s3.amazonaws.com/nirvana-pdfgen/key/prod-policy-document.pdf",
							},
							Relationships: oapi.ClaimAttachmentRelationship{
								AttachmentTarget: &oapi.ClaimAttachmentTarget{
									Data: oapi.ClaimAttachmentRelationshipData{
										Type: "claim",
										Id:   mockClaimReferenceNumber,
									},
								},
							},
						},
					},
				}
			},
			wantSandboxed: false,
			wantErr:       false,
		},
		{
			name: "valid sendable FNOL with reporter phone as N/A (phone not included in contact methods)",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				reporterWithNAPhone := validReporter
				reporterWithNAPhone.Phone = "N/A"
				fnol.Contacts = []db.FnolContact{*reporterWithNAPhone}
				return fnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				req := wantProdClaimRequest
				// Contact methods should only include email, not phone
				req.ClaimParties = []oapi.ClaimParty{{
					PartyType: oapi.PERSON,
					Id:        "<EMAIL>",
					FirstName: "John",
					LastName:  "Doe",
					ContactMethods: &[]oapi.ClaimPartyContactMethod{
						{
							Type:  oapi.ClaimPartyContactMethodTypeEmail,
							Value: "<EMAIL>",
						},
					},
				}}
				return req
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				return wantProdUpdateClaimRequest
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return wantProdAttachmentRequests
			},
			wantSandboxed: false,
			wantErr:       false,
		},
		{
			name: "valid sendable FNOL reporter phone as N/A (phone not included in contact methods) for MST (traditional) policy",
			fnolFactory: func() db.ClaimFnol {
				fnol := *validSendableProdSnapsheetFnol
				fnol.PolicyNumber = pointer_utils.String(prodPolicyMst.PolicyNumber.String())
				reporterWithNAPhone := validReporter
				reporterWithNAPhone.Phone = "N/A"
				fnol.Contacts = []db.FnolContact{*reporterWithNAPhone}
				return fnol
			},
			wantExternalId: mockClaimReferenceNumber,
			wantClaimRequestFactory: func() oapi.CreateClaimRequest {
				req := wantProdClaimRequest
				req.PolicyNumber = prodPolicyMst.PolicyNumber.String()
				// Contact methods should only include email, not phone
				req.ClaimParties = []oapi.ClaimParty{{
					PartyType: oapi.PERSON,
					Id:        "<EMAIL>",
					FirstName: "John",
					LastName:  "Doe",
					ContactMethods: &[]oapi.ClaimPartyContactMethod{
						{
							Type:  oapi.ClaimPartyContactMethodTypeEmail,
							Value: "<EMAIL>",
						},
					},
				}}
				req.AccountCode = oapi.InsuranceCarrierMsTransverse
				return req
			},
			wantUpdateClaimRequestFactory: func() oapi.UpdateClaimRequest {
				req := wantProdUpdateClaimRequest
				req.Data.Attributes.CfFrontingCarrierUat7b39 = "MST"
				req.Data.Attributes.CfFrontingCarrierProdE809 = "MST"
				return req
			},
			wantAttachmentRequestsFactory: func() []oapi.CreateAttachmentRequest {
				return wantProdAttachmentRequests
			},
			wantSandboxed: false,
			wantErr:       false,
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			gotCreateClaimRequest = nil
			gotCreateClaimRequestOpts = nil
			gotCreateAttachmentRequests = nil
			gotCreateAttachmentOpts = nil

			fnol := tc.fnolFactory()
			gotExternalId, err := s.snapsheetClient.SubmitFnol(ctx, fnol)
			if tc.wantErr {
				s.Require().Error(err)
				s.Nil(gotCreateClaimRequest)
				s.Nil(gotCreateClaimRequestOpts)
				s.Empty(gotExternalId)
				return
			}

			s.Require().NoError(err)
			s.NotNil(gotCreateClaimRequest)
			s.NotNil(gotCreateClaimRequestOpts)
			s.Equal(tc.wantExternalId, gotExternalId)

			s.Equal(tc.wantSandboxed, gotCreateClaimRequestOpts.Sandboxed)

			wantRequest := tc.wantClaimRequestFactory()

			s.Equal(wantRequest.AccountCode, gotCreateClaimRequest.AccountCode)
			s.Equal(wantRequest.PolicyNumber, gotCreateClaimRequest.PolicyNumber)
			s.Equal(wantRequest.DatetimeOfLoss, gotCreateClaimRequest.DatetimeOfLoss)
			s.Equal(wantRequest.LossType, gotCreateClaimRequest.LossType)
			s.Equal(wantRequest.ClaimType, gotCreateClaimRequest.ClaimType)
			s.Equal(wantRequest.ClaimNumber, gotCreateClaimRequest.ClaimNumber)
			s.Equal(wantRequest.ClaimParties, gotCreateClaimRequest.ClaimParties)
			s.Equal(wantRequest.ClaimParties[0].Id, gotCreateClaimRequest.Notifier.ClaimPartyId)
			s.Equal(oapi.ONLINE, gotCreateClaimRequest.NotificationMethod)

			wantClaimIncidentDetails := wantRequest.ClaimIncidentDetails
			gotClaimIncidentDetails := gotCreateClaimRequest.ClaimIncidentDetails
			s.Require().NotNil(gotClaimIncidentDetails)
			s.Equal(
				wantClaimIncidentDetails.IncidentLocationType,
				gotClaimIncidentDetails.IncidentLocationType,
			)
			s.Equal(
				wantClaimIncidentDetails.IncidentLocationDescription,
				gotClaimIncidentDetails.IncidentLocationDescription,
			)
			s.Equal(
				wantClaimIncidentDetails.IncidentLocationAddress.Region,
				gotClaimIncidentDetails.IncidentLocationAddress.Region,
			)
			s.Equal(
				wantClaimIncidentDetails.IncidentLocationAddress.Country,
				gotClaimIncidentDetails.IncidentLocationAddress.Country,
			)
			s.Equal(
				wantClaimIncidentDetails.AutoClaimCollisionWithMotorVehicleIncidentDetail.FactsOfLoss,
				gotClaimIncidentDetails.AutoClaimCollisionWithMotorVehicleIncidentDetail.FactsOfLoss,
			)

			wantEmergencyServicesDetail := wantRequest.EmergencyServicesDetail
			gotEmergencyServicesDetail := gotCreateClaimRequest.EmergencyServicesDetail
			s.Require().NotNil(gotEmergencyServicesDetail)
			s.Equal(
				wantEmergencyServicesDetail.PoliceAttended,
				gotEmergencyServicesDetail.PoliceAttended,
			)
			s.Equal(
				wantEmergencyServicesDetail.PoliceStation,
				gotEmergencyServicesDetail.PoliceStation,
			)
			s.Equal(
				wantEmergencyServicesDetail.PoliceReport,
				gotEmergencyServicesDetail.PoliceReport,
			)
			s.Equal(
				wantEmergencyServicesDetail.PoliceReportNumber,
				gotEmergencyServicesDetail.PoliceReportNumber,
			)

			wantVehicles := *wantRequest.Vehicles
			gotVehicles := *gotCreateClaimRequest.Vehicles
			vehiclesSorter := func(vehicles []oapi.ClaimVehicle) {
				sort.Slice(vehicles, func(i, j int) bool {
					return vehicles[i].Id < vehicles[j].Id
				})
			}
			vehiclesSorter(wantVehicles)
			vehiclesSorter(gotVehicles)
			s.Require().NotNil(gotVehicles)
			s.Require().NotNil(wantVehicles)
			s.Require().Equal(len(wantVehicles), len(gotVehicles))
			for i, wantVehicle := range wantVehicles {
				gotVehicle := gotVehicles[i]
				s.Equal(wantVehicle.Id, gotVehicle.Id)
				s.Equal(wantVehicle.Make, gotVehicle.Make)
				s.Equal(wantVehicle.Model, gotVehicle.Model)
				s.Equal(wantVehicle.VinNumber, gotVehicle.VinNumber)
				s.Equal(wantVehicle.RegistrationNumber, gotVehicle.RegistrationNumber)
			}

			wantExposures := *wantRequest.Exposures
			gotExposures := *gotCreateClaimRequest.Exposures
			exposuresSorter := func(exposures []oapi.ClaimExposure) {
				sort.Slice(exposures, func(i, j int) bool {
					return exposures[i].Id < exposures[j].Id
				})
			}
			exposuresSorter(wantExposures)
			exposuresSorter(gotExposures)
			s.Require().NotNil(gotExposures)
			s.Require().NotNil(wantExposures)
			s.Require().Equal(len(wantExposures), len(gotExposures))
			for i, wantExposure := range wantExposures {
				gotExposure := gotExposures[i]
				s.Equal(wantExposure.Id, gotExposure.Id)
				s.Equal(wantExposure.ExposureType, gotExposure.ExposureType)
				s.Equal(wantExposure.LossParty, gotExposure.LossParty)
				s.Equal(wantExposure.Vehicle.VehicleId, gotExposure.Vehicle.VehicleId)
			}

			wantUpdateClaimRequest := tc.wantUpdateClaimRequestFactory()
			s.Require().NotNil(gotUpdateClaimRequest)
			s.Require().NotNil(gotUpdateClaimRequestOpts)
			s.Equal(tc.wantSandboxed, gotUpdateClaimRequestOpts.Sandboxed)
			wantUpdateData := wantUpdateClaimRequest.Data
			gotUpdateData := gotUpdateClaimRequest.Data
			s.Equal(wantUpdateData.Id, gotUpdateData.Id)
			s.Equal(wantUpdateData.Type, gotUpdateData.Type)
			s.Equal(wantUpdateData.Attributes.CfFrontingCarrierUat7b39, gotUpdateData.Attributes.CfFrontingCarrierUat7b39)
			s.Equal(wantUpdateData.Attributes.CfInjuriesInvolvedUatB662, gotUpdateData.Attributes.CfInjuriesInvolvedUatB662)
			s.Equal(*wantUpdateData.Attributes.CfInsuredNameUatCbbb, *gotUpdateData.Attributes.CfInsuredNameUatCbbb)
			s.Equal(wantUpdateData.Attributes.CfReportOnlyUat915a, gotUpdateData.Attributes.CfReportOnlyUat915a)
			s.Equal(wantUpdateData.Attributes.CfInsuredDotUatF4a0, gotUpdateData.Attributes.CfInsuredDotUatF4a0)
			s.Equal(wantUpdateData.Attributes.CfInsuredDotProdCa0d, gotUpdateData.Attributes.CfInsuredDotProdCa0d)

			wantAttachmentsRequests := tc.wantAttachmentRequestsFactory()
			attachmentsSorter := func(attachments []oapi.CreateAttachmentRequest) {
				sort.Slice(attachments, func(i, j int) bool {
					return attachments[i].Data.Attributes.Name < attachments[j].Data.Attributes.Name
				})
			}
			attachmentsSorter(wantAttachmentsRequests)
			attachmentsSorter(gotCreateAttachmentRequests)
			s.Require().Equal(len(wantAttachmentsRequests), len(gotCreateAttachmentRequests))
			s.Require().Equal(len(wantAttachmentsRequests), len(gotCreateAttachmentOpts))
			for i, gotReq := range gotCreateAttachmentRequests {
				wantReq := wantAttachmentsRequests[i]
				s.Equal(wantReq.Data.Type, gotReq.Data.Type)
				s.Equal(wantReq.Data.Attributes.Name, gotReq.Data.Attributes.Name)
				s.Equal(wantReq.Data.Attributes.Url, gotReq.Data.Attributes.Url)
				s.Equal(
					wantReq.Data.Relationships.AttachmentTarget.Data.Type,
					gotReq.Data.Relationships.AttachmentTarget.Data.Type,
				)
				s.Equal(
					wantReq.Data.Relationships.AttachmentTarget.Data.Id,
					gotReq.Data.Relationships.AttachmentTarget.Data.Id,
				)
				s.Equal(tc.wantSandboxed, gotCreateAttachmentOpts[i].Sandboxed)
			}
		})
	}
}
