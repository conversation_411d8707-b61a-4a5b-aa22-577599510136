import dotenv from "dotenv";
import { main } from "./index.mjs";
import { s3Keys, sharedAttributes } from "./shared.mjs";

dotenv.config();

const { INSURED_CONFIRMATION_EMAIL, INSURED_SENDGRID_TEMPLATE } = process.env;

const dataForInsuredMail = {
  ...sharedAttributes,
  subject: `Nirvana Insurance - FNOL - Policy Number: ${sharedAttributes.policy_number}, DOL: ${sharedAttributes.loss_date_formatted}`,
  insured_vehicles_vins: [],
  other_vehicles_registration_numbers: [],
};

main({
  to: INSURED_CONFIRMATION_EMAIL,
  templateId: INSURED_SENDGRID_TEMPLATE,
  body: dataForInsuredMail,
  attachmentKeys: s3Keys,
});
