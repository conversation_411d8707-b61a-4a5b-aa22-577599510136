import dotenv from "dotenv";
import { main } from "./index.mjs";
import { dataForNARS, s3Keys } from "./shared.mjs";

dotenv.config();

const { NIRVANA_SENDGRID_TEMPLATE, NIRVANA_CONFIRMATION_EMAIL } = process.env;

const dataForNirvana = dataForNARS;
dataForNirvana.recipient_is_nars = false;
main({
  to: NIRVANA_CONFIRMATION_EMAIL,
  templateId: NIRVANA_SENDGRID_TEMPLATE,
  body: dataForNirvana,
  attachmentKeys: s3Keys,
});
