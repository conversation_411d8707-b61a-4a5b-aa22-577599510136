// select distinct f."key" from nars.fnol_attachments as a, public.file_upload_handle as f where a.fnol_id='<FNOL_ID>' and a.handle_id=f.handle;
export const s3Keys = [];

// It can be retrieved from https://metabase.dev.nirvanatech.com/question/4171-resending-fnol-email-data-gathering?fnol_id=9019f539-362f-4f7f-acd5-810baed8b78f
export const sharedAttributes = {
  company_name: "",
  reported_person_contact_phone: "",
  reported_person_email: "",
  reported_person_first_name: "",
  reported_person_last_name: "",
  notice_type: "", // Initiate Claim / Report Only
  loss_date: "", // Eg 2025-01-30T14:00:00Z
  loss_date_formatted: "",
  loss_state: "",
  loss_location: "",
  policy_number: "",
  client_lob: "",
  police_on_the_scene: false,
  police_agency_name: "",
  police_report_number: "",
  loss_description: "", // Prepend [Only reporting, DO NOT initiate a claim] if is_report_only is true
  is_report_only: false,
  injureds_involved: false,
};


export const dataForNARS = {
  ...sharedAttributes,
  recipient_is_nars: true,
  client_contact_email: "<EMAIL>",
  client_id: "NIT", // No need to change this (src/nirvana/claims/nars/client.go)
  loss_country: "US",
  subject: `Nirvana Insurance - FNOL - Policy Number: ${sharedAttributes.policy_number}, Insured Name: ${sharedAttributes.company_name}, DOL: ${sharedAttributes.loss_date_formatted}`,
  client_claim_number: "", // You can look for this on the logs
  client_name: "NIRVANA TRANSPORTATION", // No need to change this (src/nirvana/claims/nars/client.go)
  policy_effective_date: "",
  policy_expiration_date: "",
  policy_insured_name: sharedAttributes.company_name,
  properties: [
    // Remove if not applicable
    {
      vehicle_plate_number: null,
      vehicle_vin: "",
    },
  ],
  injury_description: sharedAttributes.injureds_involved ? "Injuries involved" : "",
};
