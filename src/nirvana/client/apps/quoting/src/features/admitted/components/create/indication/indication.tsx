import { Box, Divider, FormHelperText, Grid } from '@material-ui/core';
import {
  AdmittedApp,
  ApplicationDetails,
  CoverageType,
  CreditReportStatus,
  GetAdmittedAppIndicationResponse,
  PageState,
} from '@nirvana/api/non-fleet';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import { Show } from '@nirvana/ui';
import {
  NF_CONNECT_TELEMATICS_START,
  NF_QUOTE_PAGE_VIEW,
} from 'src/constants/analytics';
import { useApplicationDetailsContext } from 'src/features/admitted/hooks/useApplicationDetails';
import GreenTick from 'src/assets/icons/green-tick.svg?react';
import {
  fetchCreditStatus,
  fetchIndication,
  submitQuote,
  updateApplicationDetails,
} from 'src/features/admitted/queries/application';
import Loader from 'src/features/application/components/create/indicationOptions/indicationLoader';

import { useAnalytics } from 'src/helpers/analytics';
import { Feature, useFeatureFlag } from 'src/helpers/featureFlags';
import Deductibles from './deductibles';
import Limits from './limits';
import Plans from './plans';
import TelematicsConnection from './plans/telematics-connection';
import TelematicsConnection2 from './plans/telematics-connection-2';
import { TelematicsCard } from './telematicsCard';
import { PreTelematicsCard } from './preTelematicsCard';

const REFETCH_INTERVAL = 10000; // 10 seconds
const INITIAL_REFETCH_DELAY = 2000; // 2 seconds

interface IIndication {
  applicationId: string;
  onLoadingChange?: (isLoading: boolean) => void;
}

const Indication = ({ applicationId, onLoadingChange }: IIndication) => {
  const { control, setValue, getValues } = useFormContext();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { applicationDetails } = useApplicationDetailsContext();
  const { capture } = useAnalytics();
  const getFeatureValue = useFeatureFlag();
  const isNFTelematicsNewUxEnabled = getFeatureValue(
    Feature.NF_TELEMATICS_CONSENT_NEW_UX,
    false,
  );

  const enableCombinedDeductibles = useMemo(() => {
    const enabledCoverages =
      applicationDetails?.admitted?.operationsForm?.coverages
        ?.filter((record) => record.isRequired)
        .map((record) => record.coverageType) ?? [];

    return (
      enabledCoverages.includes(CoverageType.CoverageMotorTruckCargo) &&
      enabledCoverages.includes(CoverageType.CoverageAutoPhysicalDamage)
    );
  }, [applicationDetails?.admitted?.operationsForm?.coverages]);

  const [
    isTelematicsConnectionDrawerOpen,
    setIsTelematicsConnectionDrawerOpen,
  ] = useState(false);
  const [indicationStatus, setIndicationStatus] = useState('running');
  const [loaderAnimationStatus, setLoaderAnimationStatus] = useState('idle');
  const [isUpdating, setIsUpdating] = useState(false);
  const [hidePrices, setHidePrices] = useState(false);

  const isNoCreditHitEnabled = getFeatureValue(Feature.NO_CREDIT_HIT, false);

  const isPreTelematicsFlowEnabled = getFeatureValue(
    Feature.PRE_TELEMATICS_EXPERIMENT,
    false,
  );

  const {
    data: creditStatusResponse,
    refetch: refetchCreditStatus,
    isLoading: isCheckingCreditStatus,
  } = useQuery({
    queryKey: ['creditStatus', applicationId],
    queryFn: () => fetchCreditStatus(applicationId),
    enabled: !!applicationId,
  });

  useEffect(() => {
    if (!creditStatusResponse) {
      return;
    }

    if (
      isNoCreditHitEnabled &&
      (creditStatusResponse.status === CreditReportStatus.NoHit ||
        creditStatusResponse.status === CreditReportStatus.ThinFile ||
        creditStatusResponse.status === CreditReportStatus.Failed)
    ) {
      setHidePrices(true);
    } else if (
      isNoCreditHitEnabled &&
      creditStatusResponse.status === CreditReportStatus.InProgress
    ) {
      setTimeout(() => {
        refetchCreditStatus();
      }, INITIAL_REFETCH_DELAY);
    } else {
      setHidePrices(false);
    }
  }, [creditStatusResponse, isNoCreditHitEnabled, refetchCreditStatus]);

  const { data, isLoading: isLoadingPackages } = useQuery({
    queryKey: ['indication', applicationId],
    queryFn: () => fetchIndication(applicationId),
    enabled: indicationStatus === 'running',
    refetchInterval: REFETCH_INTERVAL,
    onSuccess: (data: GetAdmittedAppIndicationResponse) => {
      setIndicationStatus(data.status);
    },
  });

  const { mutate: submitIndicationMutation } = useMutation(submitQuote, {
    onSuccess: () => {
      setIndicationStatus('running');
      setIsUpdating(false);
    },
    onError: () => {
      navigate(`/non-fleet/application/${applicationId}/decline`);
    },
  });

  const { mutate: updateApplicationMutation } = useMutation(
    updateApplicationDetails,
    {
      onMutate: async (newApplication) => {
        // Optimistically update the isAPDMTCDeductibleCombined value

        await queryClient.cancelQueries({
          queryKey: ['application', applicationId],
        });

        const previousApplication = queryClient.getQueryData([
          'application',
          applicationId,
        ]);

        queryClient.setQueryData(
          ['application', applicationId],
          (old?: ApplicationDetails) => {
            const updated = { ...old };
            if (updated?.admitted?.indicationForm?.coverages) {
              updated.admitted.indicationForm.coverages.isAPDMTCDeductibleCombined =
                newApplication.payload.admitted?.indicationForm?.coverages?.isAPDMTCDeductibleCombined;
            }

            return updated;
          },
        );

        // Return a context object with the snapshotted value
        return { previousApplication };
      },
      onSuccess: () => {
        submitIndicationMutation(applicationId);
      },
    },
  );

  const isLoading = useMemo(
    () =>
      isLoadingPackages ||
      isUpdating ||
      indicationStatus === 'running' ||
      isCheckingCreditStatus,
    [isLoadingPackages, isUpdating, indicationStatus, isCheckingCreditStatus],
  );

  const isRenewalApplication = useMemo(
    () => applicationDetails?.renewalMetadata?.originalApplicationId,
    [applicationDetails],
  );

  const isPremierTelematicsProviderSelected =
    applicationDetails?.admitted?.operationsForm?.companyInfo
      ?.selectedProviderType === 'Premier';

  const isExpressLaneFlow = applicationDetails?.isExpressLaneApplication;

  const onSubmit = (data: AdmittedApp) => {
    updateApplicationMutation({
      applicationId,
      payload: {
        pageState: PageState.PageStateSubmitted,
        admitted: {
          ...data,
        },
      },
    });
  };

  const handleDeductibleLimitChange = () => {
    setIsUpdating(true);
    onSubmit(getValues());
    setValue('indicationForm.selectedIndication.id', '');
  };

  useEffect(() => {
    capture(NF_QUOTE_PAGE_VIEW, {
      page: 'indication',
      applicationId,
    });
  }, [capture, applicationId]);

  useEffect(() => {
    if (onLoadingChange) {
      onLoadingChange(isLoading || loaderAnimationStatus === 'loading');
    }
  }, [isLoading, indicationStatus, loaderAnimationStatus, onLoadingChange]);

  useEffect(() => {
    if (
      indicationStatus === 'success' &&
      loaderAnimationStatus === 'complete'
    ) {
      setLoaderAnimationStatus('idle');
      queryClient.invalidateQueries(['application', applicationId]);
    }
  }, [
    indicationStatus,
    loaderAnimationStatus,
    applicationId,
    queryClient,
    setValue,
  ]);

  const telematicsInfo = useMemo(() => {
    return applicationDetails?.admitted?.indicationForm?.telematicsInfo;
  }, [applicationDetails]);

  if (
    applicationDetails?.appStatus !== 'AppStateQuoteGenerated' &&
    applicationDetails?.appStatus !== 'AppStateUnderUWReview' &&
    (indicationStatus === 'running' || loaderAnimationStatus === 'loading')
  ) {
    return (
      <>
        <Box
          flex="1"
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <Grid item position="relative">
            <Box>
              <Controller
                control={control}
                name="indicationForm.selectedIndication.id"
                rules={{
                  required: 'Please select a plan',
                }}
                defaultValue=""
                render={() => {
                  return (
                    <Loader
                      onStatusChange={setLoaderAnimationStatus}
                      steps={[
                        'Pulling FMCSA inspections & crashes',
                        'Pulling BASIC scores',
                        'Pulling company history',
                        'Processing drivers',
                        'Generating an indication',
                      ]}
                    />
                  );
                }}
              />
            </Box>
          </Grid>
        </Box>
      </>
    );
  }

  return (
    <Box mt={4}>
      <Grid container spacing={4} direction="column">
        <Show when={isExpressLaneFlow}>
          <Grid item>
            <div className="flex flex-row w-full gap-2 px-4 py-3 border rounded-lg bg-tw-green-100">
              <GreenTick />
              <div className="flex flex-col gap-1">
                <p className="text-sm font-semibold text-tw-gray-1000">
                  Congratulations! Your application is eligible for Express
                  Lane.
                </p>
                <p className="text-sm font-normal text-tw-gray-1000">
                  Once your application is complete, including a connected
                  telematics device, you’ll receive a faster quote.
                </p>
              </div>
            </div>
          </Grid>
        </Show>
        <Grid item>
          <Deductibles
            options={data?.options}
            loading={isLoadingPackages}
            disabled={isLoading}
            enableCombinedDeductibles={enableCombinedDeductibles}
            onChange={handleDeductibleLimitChange}
          />
        </Grid>

        <Grid item>
          <Limits
            options={data?.options}
            loading={isLoadingPackages}
            disabled={isLoading}
            onChange={handleDeductibleLimitChange}
          />
        </Grid>

        <Show when={!isRenewalApplication}>
          <Grid item>
            <Divider />
          </Grid>

          <Show
            when={
              !isPremierTelematicsProviderSelected ||
              !isPreTelematicsFlowEnabled
            }
            fallback={
              <>
                <Grid item>
                  <PreTelematicsCard
                    telematicsInfo={telematicsInfo}
                    onEditTelematics={() => {
                      capture(NF_CONNECT_TELEMATICS_START, {
                        page: 'indication',
                        applicationId,
                      });
                      setIsTelematicsConnectionDrawerOpen(true);
                    }}
                  />
                </Grid>
                <Grid item>
                  <Divider />
                </Grid>
              </>
            }
          >
            <Grid item>
              <Controller
                control={control}
                name="indicationForm.telematicsInfo"
                rules={{
                  validate: {
                    required: (value) => {
                      return !!value?.link || 'Please connect the telematics';
                    },
                  },
                }}
                render={({ field: { value }, fieldState }) => {
                  return (
                    <>
                      <TelematicsCard
                        telematicsInfo={value}
                        onEditTelematics={() => {
                          capture(NF_CONNECT_TELEMATICS_START, {
                            page: 'indication',
                            applicationId,
                          });
                          setIsTelematicsConnectionDrawerOpen(true);
                        }}
                        error={!!fieldState.error?.message}
                      />
                      <FormHelperText error>
                        {fieldState.error?.message}
                      </FormHelperText>
                    </>
                  );
                }}
              />
            </Grid>
          </Show>

          <Grid item>
            <Plans
              options={data?.packages}
              loading={isLoading}
              hidePrices={hidePrices}
            />
          </Grid>
        </Show>
      </Grid>

      <Show
        when={isNFTelematicsNewUxEnabled}
        fallback={
          <TelematicsConnection
            open={isTelematicsConnectionDrawerOpen}
            applicationId={applicationId}
            telematicsInfo={
              applicationDetails?.admitted?.indicationForm?.telematicsInfo
            }
            onClose={(isLinkGenerated) => {
              setIsTelematicsConnectionDrawerOpen(false);
              if (isLinkGenerated) {
                setValue(
                  'indicationForm.telematicsInfo',
                  applicationDetails?.admitted?.indicationForm?.telematicsInfo,
                );
              }
            }}
          />
        }
      >
        <TelematicsConnection2
          open={isTelematicsConnectionDrawerOpen}
          onClose={() => setIsTelematicsConnectionDrawerOpen(false)}
          applicationId={applicationId}
          telematicsInfo={
            applicationDetails?.admitted?.indicationForm?.telematicsInfo
          }
          onCreateLink={(data) => {
            setIsTelematicsConnectionDrawerOpen(false);
            if (data.url) {
              setValue('indicationForm.telematicsInfo', {
                link: data.url,
                email: data.email,
                name: data.name,
              });
            }
          }}
        />
      </Show>
    </Box>
  );
};

export default Indication;
