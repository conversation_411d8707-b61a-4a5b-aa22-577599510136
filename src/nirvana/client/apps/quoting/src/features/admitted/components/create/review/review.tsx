import { Box, CircularProgress, Grid } from '@material-ui/core';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { NF_REVIEW_PAGE_VIEW } from 'src/constants/analytics';
import { useApplicationDetailsContext } from 'src/features/admitted/hooks/useApplicationDetails';
import { useAnalytics } from 'src/helpers/analytics';
import { Show } from '@nirvana/ui';
import GreenTick from 'src/assets/icons/green-tick.svg?react';
import {
  DriverSection,
  EquipmentSection,
  IndicationSection,
  OperationsView,
} from './sections';

export interface IReviewProps {
  onEdit: (key: string) => void;
}

/**
 * Review screen that displays the data filled up in each step of the application creation process.
 * The user is allowed to move to any previous step and edit the information that was previously submitted.
 * Corresponds to Review Page (https://www.figma.com/file/FsHg1J71OVKiejQYo8TxtR/Agents?type=design&node-id=29980-29157&mode=design&t=Ymx2Yp0mbbpUrr8Q-0)
 * @component
 */
const Review = ({ onEdit }: IReviewProps) => {
  const { applicationDetails, isLoading } = useApplicationDetailsContext();
  const { capture } = useAnalytics();
  const { applicationId } = useParams();

  useEffect(() => {
    capture(NF_REVIEW_PAGE_VIEW, {
      applicationId,
    });
  }, [capture, applicationId]);

  if (isLoading || !applicationDetails?.admitted) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  const isExpressLaneFlow = applicationDetails?.isExpressLaneApplication;

  return (
    <Grid container direction="column" wrap="nowrap" spacing={3}>
      <Show when={isExpressLaneFlow}>
        <Grid item>
          <div className="flex flex-row w-full gap-2 px-4 py-3 border rounded-lg bg-tw-green-100">
            <GreenTick />
            <div className="flex flex-col gap-1">
              <p className="text-sm font-semibold text-tw-gray-1000">
                Congratulations! Your application is eligible for Express Lane.
              </p>
              <p className="text-sm font-normal text-tw-gray-1000">
                Once your application is complete, including a connected
                telematics device, you’ll receive a faster quote.
              </p>
            </div>
          </div>
        </Grid>
      </Show>
      <OperationsView
        editable
        onEdit={onEdit}
        applicationData={applicationDetails?.admitted}
      />
      <EquipmentSection
        editable
        onEdit={onEdit}
        applicationData={applicationDetails?.admitted}
      />
      <DriverSection
        editable
        onEdit={onEdit}
        applicationData={applicationDetails?.admitted}
      />
      <IndicationSection
        editable
        onEdit={onEdit}
        applicationData={applicationDetails?.admitted}
      />
    </Grid>
  );
};

export default Review;
