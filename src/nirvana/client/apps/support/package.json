{"name": "@nirvana/support", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "tsc": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "build:staging": "vite build --mode staging", "build:test": "vite build --mode testing", "build:production": "NODE_OPTIONS=--max-old-space-size=8192 vite build", "serve": "vite preview", "ci:graphql": "graphql-codegen --config codegen.yml", "api": "graphql-codegen --config codegen.yml --watch"}, "dependencies": {"@clerk/clerk-react": "^5.42.1", "@clerk/testing": "^1.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@impler/react": "^0.29.0", "@material-ui/core": "5.0.0-alpha.29", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "5.0.0-alpha.29", "@nirvana/api": "*", "@nirvana/core": "*", "@nirvana/ui": "*", "@nirvana/ui-kit": "*", "@rjsf/core": "^5.24.12", "@rjsf/material-ui": "^5.24.10", "@rjsf/utils": "^5.24.11", "@rjsf/validator-ajv8": "^5.24.11", "@sentry/vite-plugin": "^3.5.0", "constate": "^3.3.3", "cronstrue": "^2.61.0", "date-fns": "^2.28.0", "dompurify": "^3.2.6", "dotenv": "^17.2.1", "formik": "^2.4.6", "html-react-parser": "^5.2.6", "launchdarkly-react-client-sdk": "^3.8.1", "lodash-es": "^4.17.21", "notistack": "2.0.1-alpha.5", "posthog-js": "^1.257.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.57.0", "react-icons": "^5.4.0", "react-query": "^3.39.3", "react-resizable-panels": "^3.0.3", "yup": "^0.32.11"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.7", "@graphql-codegen/typescript": "^2.4.7", "@graphql-codegen/typescript-operations": "^2.3.4", "@graphql-codegen/typescript-react-apollo": "^3.3.7", "@vitejs/plugin-react": "^4.7.0", "vite": "^6.3.5", "vite-plugin-checker": "^0.8.0", "vite-tsconfig-paths": "^5.1.4"}}