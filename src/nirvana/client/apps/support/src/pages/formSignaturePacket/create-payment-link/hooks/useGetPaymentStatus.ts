import { paymentsClient } from 'utils';
import { useQuery, UseQueryOptions } from 'react-query';
import { GetPaymentStatusResult } from '@nirvana/api/payments';

type Options = Omit<
  UseQueryOptions<
    GetPaymentStatusResult,
    Error,
    GetPaymentStatusResult,
    string[]
  >,
  'queryKey' | 'queryFn'
>;

export const useGetPaymentStatus = (applicationId: string, opts: Options) => {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['getPaymentStatus', applicationId],
    queryFn: async () => {
      const queryResult = await paymentsClient.getPaymentStatus(applicationId);
      return queryResult.data;
    },
    ...opts,
  });
  return { data, isLoading, error, refetch };
};
