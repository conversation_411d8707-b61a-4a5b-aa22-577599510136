import { format, parseISO } from 'date-fns/esm';
import { Button } from '@material-ui/core';
import { createColumnHelper } from '@tanstack/react-table';

import { ProvisionalLossRunsReport } from '@nirvana/api/insured';
import { ColumnsType } from '@nirvana/ui-kit/src/components/table/table-v8';

export const getLossRunsReportsColumns = () => {
  const columnHelper = createColumnHelper<ProvisionalLossRunsReport>();
  return [
    columnHelper.accessor('policy.dotNumber', {
      enableSorting: true,
      header: 'DOT',
      size: 50,
    }),
    columnHelper.accessor('policy.insuredName', {
      enableSorting: true,
      header: 'Name',
      cell: ({ getValue, row }) => {
        return (
          <div className="flex flex-col">
            <div className="mb-1">{getValue()}</div>
            <div className="text-sm text-gray-400">
              {row.original.policy.policyNumber}
            </div>
          </div>
        );
      },
    }),
    columnHelper.accessor('policy.effectiveDate', {
      enableSorting: true,
      header: 'Policy Term',
      cell: ({ getValue, row }) => {
        const { expirationDate } = row.original.policy;
        return `${format(parseISO(getValue()), 'MMM dd, yyyy')} - ${format(
          parseISO(expirationDate),
          'MMM dd, yyyy',
        )}`;
      },
    }),
    columnHelper.accessor('requestedBy', {
      enableSorting: true,
      header: 'Generated By',
    }),
    columnHelper.accessor('generatedAt', {
      enableSorting: true,
      header: 'As of date',
      cell: ({ getValue }) => {
        return format(parseISO(getValue()), 'MMM dd, yyyy - hh:mm a');
      },
    }),
    columnHelper.accessor('link', {
      header: '',
      cell: ({ getValue }) => {
        const link = getValue();

        return (
          <a href={link ?? undefined} target="_blank" rel="noopener noreferrer">
            <Button variant="outlined" disabled={!link}>
              Download
            </Button>
          </a>
        );
      },
    }),
  ] as ColumnsType<ProvisionalLossRunsReport>;
};
