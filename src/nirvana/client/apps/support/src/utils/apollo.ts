import {
  <PERSON><PERSON><PERSON>,
  ApolloError,
  createHttpLink,
  from,
  InMemoryCache,
} from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { cookieStorage } from '@nirvana/core/utils';
import { onError } from '@apollo/client/link/error';
import {
  setContext as sentrySetContext,
  captureException,
} from '@sentry/react';

const httpLink = createHttpLink({
  uri: import.meta.env.VITE_GRAPHQL_ENDPOINT,
});

/**
 * Extracts GraphQL error messages from an error object.
 * @param error - The error to extract messages from
 * @returns Array of error messages, or empty array if no GraphQL errors found
 */
export const getGqlErrorMessages = (error: unknown): string[] => {
  // GraphQLFormattedError[] case
  if (Array.isArray(error)) {
    return error as unknown as string[];
  }
  // ApolloError case
  if (error instanceof ApolloError) {
    return error.graphQLErrors as unknown as string[];
  }
  return [];
};

const isKnownError = (error: string) => {
  switch (error) {
    case 'unauthorized':
      return true;
    default:
      return false;
  }
};

const errorLink = onError(({ graphQLErrors, operation }) => {
  const correctlyTypedGraphQLErrors = getGqlErrorMessages(graphQLErrors);
  if (correctlyTypedGraphQLErrors?.some(isKnownError)) {
    return;
  }
  if (graphQLErrors) {
    sentrySetContext('variables', operation.variables);
    const error = new Error(JSON.stringify(correctlyTypedGraphQLErrors));
    error.name = operation.operationName;
    captureException(error);
  }
});

let getClerkToken: (() => Promise<string | null>) | null = null;

export const configureApolloAuthInterceptor = (
  tokenGetter: () => Promise<string | null>,
) => {
  getClerkToken = tokenGetter;

  // allow caller to clean up
  return () => {
    getClerkToken = null;
  };
};

const authLink = setContext(async (_, { headers }) => {
  const authHeaders = { ...(headers ?? {}) };

  // Try to get Clerk token first
  if (getClerkToken) {
    try {
      const clerkToken = await getClerkToken();
      if (clerkToken) {
        authHeaders['CLERK-AUTHORIZATION'] = `Bearer ${clerkToken}`;
        return { headers: authHeaders };
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to get Clerk token:', error);
    }
  }

  // Fallback to cookie token
  const accessToken = cookieStorage.get({
    key: import.meta.env.VITE_AUTH_TOKEN,
  });

  authHeaders.JSESSIONID = accessToken ?? '';

  return { headers: authHeaders };
});

export const apolloClient = new ApolloClient({
  cache: new InMemoryCache(),
  link: from([errorLink, authLink, httpLink]),
});
