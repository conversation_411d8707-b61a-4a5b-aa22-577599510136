import { z } from 'zod';
import React, { useCallback, useMemo } from 'react';
import { useMutation, useQueries } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Dialog,
  Form,
  InputNumber,
  RadioGroup,
  Select,
  Show,
  Textarea,
} from '@nirvana/ui';
import { uniqBy } from 'lodash-es';

import { RecommendedAction } from '@nirvana/api/uw';

import { CircularProgress } from '@material-ui/core';
import { cn } from '@nirvana/core/utils';
import { Switch } from '@nirvana/ui-kit';
import { useForm } from 'react-hook-form';
import DeclineApplicationIcon from 'src/assets/icons/decline-application.svg?react';
import NotesIcon from 'src/assets/icons/notes-icon.svg?react';
import RecommendationIcon from 'src/assets/icons/recommendation-feedback.svg?react';
import {
  fetchApplicationCloseReasons,
  fetchApplicationReviewById,
  fetchRecommendationsData,
  postApplicationCloseReasons,
} from 'src/queries/applications';
import { queryClient } from 'src/utils/query-client';

type CloseModalProps = {
  open: boolean;
  onClose: () => void;
};

const allowedActionsForFeedback = [
  RecommendedAction.StronglyQuote,
  RecommendedAction.Quote,
  RecommendedAction.Neutral,
  RecommendedAction.Decline,
];

const getCloseValidationSchema = (showRecommendationFeedback?: boolean) =>
  z
    .object({
      reasonId: z.string().trim().min(1, 'Please select a reason'),
      subReasonId: z.string().min(1, 'Please select a sub reason'),
      comments: z.string().trim().min(1, 'Please enter comments'),
      winCarrier: z.string().optional(),
      intentionToQuote: z.boolean().optional(),
      recommendedActionFeedback: z.string().optional(),

      autoLiability: z.number().optional(),
      fullPrice: z.number().optional(),
    })
    .refine((data) => typeof data.intentionToQuote === 'boolean', {
      message: 'Please select an option',
      path: ['intentionToQuote'],
    })
    .refine(
      (data) =>
        !showRecommendationFeedback || !!data.recommendedActionFeedback?.trim(),
      {
        message: 'Please enter recommendation feedback',
        path: ['recommendedActionFeedback'],
      },
    );

const SectionHeader = ({
  icon,
  title,
  className = '',
}: {
  icon: React.ReactNode;
  title: string;
  className?: string;
}) => (
  <div className={cn('flex flex-row justify-start', className)}>
    <div className="flex flex-col justify-center p-1 rounded-lg bg-gold-tint1-opacity-35">
      {icon}
    </div>
    <p className="ml-2 text-lg font-bold text-left text-text-primary">
      {title}
    </p>
  </div>
);

export default function CloseModal({ open, onClose }: CloseModalProps) {
  const navigate = useNavigate();
  const { appReviewId = '' } = useParams();

  const [
    { data, isLoading },
    { data: recommendationsData },
    { data: appReview },
  ] = useQueries([
    {
      queryKey: ['close-reasons', appReviewId],
      queryFn: () => fetchApplicationCloseReasons(appReviewId),
      cacheTime: 3600,
      enabled: !!appReviewId,
    },
    {
      queryKey: ['overviewRecommendationsAction', appReviewId],
      queryFn: () => fetchRecommendationsData(appReviewId),
      enabled: !!appReviewId,
    },
    {
      queryKey: ['applications', appReviewId],
      queryFn: () => fetchApplicationReviewById(appReviewId),
      enabled: !!appReviewId,
    },
  ]);

  const showRecommendationFeedback =
    !!appReview?.panelWiseReviewInfo.overview &&
    recommendationsData?.recommendedAction?.action &&
    allowedActionsForFeedback.includes(
      recommendationsData.recommendedAction.action,
    );

  const closeDialogValidationSchema = useMemo(
    () => getCloseValidationSchema(showRecommendationFeedback),
    [showRecommendationFeedback],
  );

  const form = useForm<z.infer<typeof closeDialogValidationSchema>>({
    resolver: zodResolver(closeDialogValidationSchema),
    defaultValues: {
      reasonId: '',
      subReasonId: '',
      comments: '',
      winCarrier: '',
      recommendedActionFeedback: '',
      intentionToQuote: undefined,
      autoLiability: undefined,
      fullPrice: undefined,
    },
  });

  const { mutate, isLoading: isUpdating } = useMutation(
    postApplicationCloseReasons,
    {
      onSuccess: () => {
        onClose();
        queryClient.invalidateQueries(['applications']);
        navigate('/fleet/applications');
      },
    },
  );

  const onSubmit = useCallback(
    (values: z.infer<typeof closeDialogValidationSchema>) => {
      // since wincarrier is stored as string converting back to Number before POST call
      const winCarrier =
        values.winCarrier === '' || isNaN(Number(values.winCarrier))
          ? undefined
          : Number(values.winCarrier);

      const sanitizedReasons = data?.reasons?.filter(
        (reason) =>
          reason.reasonId === values.reasonId &&
          reason.subReasonId === values.subReasonId,
      );

      const { fullPrice, autoLiability } = values;

      const winCarrierPricing = Object.fromEntries(
        Object.entries({ fullPrice, autoLiability }).filter(
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          ([_, value]) => value !== undefined,
        ),
      );

      const formattedData = {
        reasonsArray: sanitizedReasons || [],
        comments: values.comments,
        recommendedActionFeedback: values.recommendedActionFeedback,
        ...(fullPrice || autoLiability ? { winCarrierPricing } : {}),
      };

      mutate({
        appReviewId,
        body: {
          ...formattedData,
          winCarrier,
          intentionToQuote: values.intentionToQuote,
        },
      });
    },
    [appReviewId, data?.reasons, mutate],
  );

  const selectedReason = form.watch('reasonId');
  const values = form.getValues();

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog onOpenChange={handleOpenChange} open={open}>
      <Dialog.Content
        className="max-h-[90vh] overflow-y-scroll"
        showCloseButton={false}
      >
        <Dialog.Header className="flex-row items-center gap-4">
          <DeclineApplicationIcon />
          <Dialog.Title>Close Application?</Dialog.Title>
        </Dialog.Header>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <Switch>
              <Switch.Match when={isLoading}>
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <CircularProgress />
                    <p className="text-sm text-text-secondary">Loading...</p>
                  </div>
                </div>
              </Switch.Match>
              <Switch.Match when={!isLoading}>
                <Form.Field
                  name="intentionToQuote"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>
                        Did you intend to quote this application?
                      </Form.Label>

                      <Form.Control>
                        <RadioGroup
                          onValueChange={(value) =>
                            field.onChange(value === 'true')
                          }
                          value={String(field.value)}
                          className="flex gap-6 mt-2"
                        >
                          <Form.Item className="flex items-center gap-2 space-y-0">
                            <Form.Control>
                              <RadioGroup.Item value="false" />
                            </Form.Control>
                            <Form.Label>No</Form.Label>
                          </Form.Item>

                          <Form.Item className="flex items-center gap-2 space-y-0">
                            <Form.Control>
                              <RadioGroup.Item value="true" />
                            </Form.Control>
                            <Form.Label>Yes</Form.Label>
                          </Form.Item>
                        </RadioGroup>
                      </Form.Control>
                      <Form.Message />
                    </Form.Item>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <Form.Field
                    name="reasonId"
                    control={form.control}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>Select Reason</Form.Label>
                        <Select
                          onValueChange={(val) => {
                            form.setValue('subReasonId', '');
                            return field.onChange(val);
                          }}
                          value={field.value}
                          className="w-full"
                        >
                          <Form.Control>
                            <Select.Trigger>
                              <Select.Value placeholder="Select a reason" />
                            </Select.Trigger>
                          </Form.Control>
                          <Select.Content>
                            {uniqBy(data?.reasons ?? [], 'reasonId').map(
                              (reason) => (
                                <Select.Item
                                  key={reason.reasonId}
                                  value={reason.reasonId}
                                >
                                  {reason.reason}
                                </Select.Item>
                              ),
                            )}
                          </Select.Content>
                        </Select>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />

                  <Form.Field
                    name="subReasonId"
                    control={form.control}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Label>Select Sub Reason</Form.Label>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={!selectedReason}
                          className="w-full"
                        >
                          <Form.Control>
                            <Select.Trigger>
                              <Select.Value placeholder="Select a sub reason" />
                            </Select.Trigger>
                          </Form.Control>
                          <Select.Content>
                            {selectedReason &&
                              data?.reasons
                                ?.filter(
                                  (reason) =>
                                    reason.reasonId === values?.reasonId,
                                )
                                .map((reason) => (
                                  <Select.Item
                                    key={reason.subReasonId}
                                    value={reason.subReasonId}
                                  >
                                    {reason.subReason}
                                  </Select.Item>
                                ))}
                          </Select.Content>
                        </Select>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </div>

                <Form.Field
                  name="winCarrier"
                  control={form.control}
                  render={({ field }) => (
                    <Form.Item>
                      <Form.Label>Select Win Carrier</Form.Label>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        className="w-full"
                      >
                        <Form.Control>
                          <Select.Trigger>
                            <Select.Value placeholder="Please Select" />
                          </Select.Trigger>
                        </Form.Control>
                        <Select.Content>
                          {data?.winCarriers?.map((carrier) => (
                            <Select.Item
                              key={carrier.Id}
                              // converting tostring since value (expects a string type) and data(carrier.Id) is in Number format
                              value={carrier.Id?.toString()}
                            >
                              {carrier.CarrierName}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                      <Form.Message />
                    </Form.Item>
                  )}
                />

                <div className="mt-2">
                  Winning Price{' '}
                  <span className="italic font-light"> (if available)</span>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Form.Field
                    control={form.control}
                    name="autoLiability"
                    render={({ field: { ...field } }) => {
                      return (
                        <Form.Item>
                          <Form.Label>Auto Liability Only</Form.Label>
                          <Form.Control>
                            <InputNumber
                              min={0}
                              prefix="$"
                              value={field.value}
                              thousandSeparator=","
                              fixedDecimalScale={true}
                              placeholder="Enter if known"
                              onValueChange={field.onChange}
                              className="flex max-w-64"
                            />
                          </Form.Control>
                          <Form.Message />
                        </Form.Item>
                      );
                    }}
                  />

                  <Form.Field
                    control={form.control}
                    name="fullPrice"
                    render={({ field: { ...field } }) => {
                      return (
                        <Form.Item>
                          <Form.Label>Full Package</Form.Label>
                          <Form.Control>
                            <InputNumber
                              min={0}
                              prefix="$"
                              value={field.value}
                              thousandSeparator=","
                              fixedDecimalScale={true}
                              placeholder="Enter if known"
                              onValueChange={field.onChange}
                              className="flex max-w-64"
                            />
                          </Form.Control>
                          <Form.Message />
                        </Form.Item>
                      );
                    }}
                  />
                </div>

                <Show when={showRecommendationFeedback}>
                  <SectionHeader
                    icon={<RecommendationIcon />}
                    title="Recommendation Feedback"
                  />

                  <Form.Field
                    name="recommendedActionFeedback"
                    control={form.control}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Description>
                          We recommend to{' '}
                          {recommendationsData?.recommendedAction?.action ===
                          RecommendedAction.Neutral
                            ? 'Further Review'
                            : recommendationsData?.recommendedAction
                                ?.action}{' '}
                          this application. Help us improve our recommendations
                          by letting us know the reasons for closing.
                        </Form.Description>
                        <Form.Control>
                          <Textarea
                            {...field}
                            placeholder="Enter recommendations feedback"
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </Show>
                <>
                  <SectionHeader icon={<NotesIcon />} title=" Notes" />
                  <Form.Field
                    name="comments"
                    control={form.control}
                    render={({ field }) => (
                      <Form.Item>
                        <Form.Control>
                          <Textarea
                            {...field}
                            placeholder="Write additional information here"
                          />
                        </Form.Control>
                        <Form.Message />
                      </Form.Item>
                    )}
                  />
                </>
              </Switch.Match>
            </Switch>

            <Dialog.Footer>
              <Dialog.Close asChild>
                <Button variant="secondary" type="button">
                  Cancel
                </Button>
              </Dialog.Close>

              <Button variant="danger" type="submit" loading={isUpdating}>
                Confirm Close
              </Button>
            </Dialog.Footer>
          </form>
        </Form>
      </Dialog.Content>
    </Dialog>
  );
}
