import { Button } from '@material-ui/core';
import { useParams } from 'react-router-dom';

import Navbar from 'src/components/navbar';
import Spotlight from 'src/components/spotlight';
import { NFApplicationSummaryProvider } from 'src/hooks/use-nfApplication-summary';
import { NFPricingProvider } from 'src/hooks/use-nfPricing';

import Alerts from './components/alerts';
import Drivers from './components/drivers';
import Equipments from './components/equipments';
import Header from './components/header';
import Losses from './components/losses';
import Operations from './components/operations';
import Package from './components/package';
import Safety from './components/safety';
import NFSidebar from './components/sidebar';
import ExpressLane from './components/express-lane/express-lane';

export default function AdmittedApplication() {
  const { appReviewId = '' } = useParams();

  return (
    <NFApplicationSummaryProvider>
      <NFPricingProvider>
        <div className="flex flex-col w-screen h-screen">
          <Navbar />
          <Header />
          <Spotlight />
          <div className="flex flex-1 overflow-hidden">
            <section className="flex flex-col flex-1 px-8 py-4 space-y-4 overflow-y-auto border-r bg-gray-50">
              <ExpressLane/>
              <Alerts />
              <div className="flex items-center justify-end">
                <Button
                  target="_blank"
                  variant="outlined"
                  href={`${
                    import.meta.env.VITE_BOARDS_APP_URL
                  }?app_review_id=${appReviewId}&page=UW+Board&nonfleet=true`}
                >
                  See Telematics Data
                </Button>
              </div>
              <Package />
              <Operations />
              <Equipments />
              <Drivers />
              <Safety />
              <Losses />
            </section>
            <NFSidebar />
          </div>
        </div>
      </NFPricingProvider>
    </NFApplicationSummaryProvider>
  );
}
