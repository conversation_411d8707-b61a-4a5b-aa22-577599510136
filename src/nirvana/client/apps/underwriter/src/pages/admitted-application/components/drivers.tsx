import { useCallback, useEffect } from 'react';
import { Button, CircularProgress } from '@material-ui/core';
import { Show, TableV8 } from '@nirvana/ui-kit';
import clsx from 'clsx';
import { HiOutlineRefresh } from 'react-icons/hi';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';
import { useNFApplicationSummaryContext } from 'src/hooks/use-nfApplication-summary';
import { useNFPricingContext } from 'src/hooks/use-nfPricing';
import { Feature, useFeatureEnabled } from 'src/utils/feature-flags';
import { ApplicationReviewDriverRecord } from '@nirvana/api/nfuw';
import { Row } from '@tanstack/react-table';
import { driverColumns, getNewDriverColumns } from '../constants/drivers';
import {
  getNFApplicationDrivers,
  getNFApplicationDriversV2,
  getNFApplicationSummary,
  pullNFApplicationMVRs,
  updateNFApplicationDrivers,
} from '../queries';
import { isNewerThanTargetDate } from '../utils';
import MvrDetails from './mvr-details';
import NewMvrDetails from './new-mvr-details';
import Widget from './widget';

export default function Drivers() {
  const { appReviewId = '' } = useParams();
  const { refetchQuote } = useNFPricingContext();
  const { isDisabled } = useNFApplicationSummaryContext();
  const isMVRUpdateEnabled = useFeatureEnabled(Feature.NF_MVR_UPDATE, false);
  const queryClient = useQueryClient();

  const { data: summaryData } = useQuery(
    ['application-summary', appReviewId],
    () => getNFApplicationSummary(appReviewId),
  );

  const shouldUseNewDrivers =
    summaryData && summaryData.createdAt
      ? isNewerThanTargetDate(summaryData.createdAt) && isMVRUpdateEnabled
      : false;

  const queryKey = 'drivers';
  const { data: driversListOld } = useQuery(
    [queryKey, appReviewId],
    () => getNFApplicationDrivers(appReviewId),
    {
      enabled: !shouldUseNewDrivers,
    },
  );

  const { data: driversListNew } = useQuery(
    ['drivers-v2', appReviewId],
    () => getNFApplicationDriversV2(appReviewId),
    {
      enabled: shouldUseNewDrivers,
    },
  );

  const data = shouldUseNewDrivers ? driversListNew : driversListOld;

  const { mutate, isLoading } = useMutation(pullNFApplicationMVRs, {
    onSuccess: () => {
      refetchQuote({ appReviewId });
      queryClient.invalidateQueries(['drivers', appReviewId]);
      queryClient.invalidateQueries(['drivers-v2', appReviewId]);
    },
  });

  const onRow = useCallback((row: Row<ApplicationReviewDriverRecord>) => {
    return {
      className: clsx('cursor-pointer', {
        'text-text-disabled': row.original?.isExcluded,
      }),
      onClick: () => row.toggleExpanded(),
    };
  }, []);

  const expandedRowRender = useCallback(
    (row: ApplicationReviewDriverRecord) =>
      shouldUseNewDrivers ? (
        <NewMvrDetails {...row} />
      ) : (
        <MvrDetails {...row} />
      ),
    [shouldUseNewDrivers],
  );

  const isExpressLane = summaryData?.isExpressLaneApplication;
  // for express-lane refetch MVR for Nonfleet as soon as page mounts for express-lane
  useEffect(() => {
    if (isExpressLane && appReviewId) {
      mutate(appReviewId);
    }
  }, [appReviewId, isExpressLane, mutate]);

  return (
    <Widget
      title="Drivers"
      queryKey={queryKey}
      isReview={data?.isReviewed}
      mutationFn={updateNFApplicationDrivers}
    >
      <div className="p-4 border rounded-md border-primary-extraLight">
        <div className="flex items-center mb-4 space-x-2">
          <p className="font-semibold">Driver&apos;s List</p>
          <div className="flex-1" />
          <Button
            startIcon={
              <Show when={isLoading} fallback={<HiOutlineRefresh />}>
                <CircularProgress size={18} />
              </Show>
            }
            variant="outlined"
            onClick={() => mutate(appReviewId)}
            disabled={isLoading || isDisabled}
          >
            {data?.isMVRPulled ? 'Refresh MVR' : 'Pull MVR'}
          </Button>
        </div>

        <div className="p-4 border rounded-md border-primary-extraLight">
          <TableV8
            columns={
              shouldUseNewDrivers
                ? getNewDriverColumns(data?.isMVRPulled)
                : driverColumns
            }
            data={data?.records ?? []}
            expandedRowRender={expandedRowRender}
            onRow={onRow}
          />
        </div>
      </div>
    </Widget>
  );
}
