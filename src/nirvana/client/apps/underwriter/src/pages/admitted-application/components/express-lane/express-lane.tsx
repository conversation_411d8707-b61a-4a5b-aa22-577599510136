import { Show } from '@nirvana/ui-kit';
import { useNFApplicationSummaryContext } from 'src/hooks/use-nfApplication-summary';
import { useNFPricingContext } from 'src/hooks/use-nfPricing';
import formatNumber from 'src/utils/format-number';

export default function ExpressLane() {
  const { appSummary } = useNFApplicationSummaryContext();
  const { packageInfo } = useNFPricingContext();

  const isExpressLaneFlow = appSummary?.isExpressLaneApplication || false;
  const totalPremiumAmount = packageInfo?.originalPackage?.totalPremium ?? 0;

  return (
    <Show when={isExpressLaneFlow}>
      <div className="p-5 bg-white rounded-md shadow-md">
        <p className="mb-1 text-base font-semibold text-tw-gray-1000">{`ExpressLane Recommends Quoting at $${formatNumber(totalPremiumAmount)}`}</p>
        <p className="mb-6 text-sm text-tw-gray-700">
          Based on a combination of key risk factors and historical data. No
          major red flags were identified, and the risk profile aligns with
          similar accounts we’ve successfully written at this level.
        </p>
      </div>
    </Show>
  );
}
