import { Button } from '@material-ui/core';
import { ApplicationType } from '@nirvana/api/forms';
import {
  ApplicationReviewAction,
  ApplicationReviewStatus,
  ApplicationUserActionType,
  CoverageType,
} from '@nirvana/api/nfuw';
import { Tag } from '@nirvana/ui';
import { Chip, Show, Tooltip, constants } from '@nirvana/ui-kit';
import { useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import GreenIcon from 'src/assets/icons/green-lightining.svg?react';
import ActionTooltip from 'src/components/header/action-tooltip';
import { useNFApplicationSummaryContext } from 'src/hooks/use-nfApplication-summary';
import { useNFPricingContext } from 'src/hooks/use-nfPricing';
import { Feature, useFeatureEnabled } from 'src/utils/feature-flags';
import {
  fetchNonFleetActions,
  getNFApplicationDrivers,
  getNFApplicationDriversV2,
  getNFApplicationSummary,
} from '../queries';
import {
  isNewerThanTargetDate,
  isWithinLimits,
  normalizeCoverageKeys,
} from '../utils';
import ApproveModal from './approve-modal';
import CloseModal from './close-modal';
import DeclineModal from './decline-modal';
import DeclineModalNew from './decline-modal-new';
import DuplicateStatePopover from './duplicate-state-popover';
import ReopenModal from './reopen-modal';

const { Coverages } = constants;

export default function Header() {
  const { appReviewId = '' } = useParams();
  const { refetchQuote, packageInfo } = useNFPricingContext();
  const { appSummary: data, isStale: isApplicationStale } =
    useNFApplicationSummaryContext();
  const [declineModalVisibility, setDeclineModalVisibility] = useState(false);
  const [closeModalVisibility, setCloseModalVisibility] = useState(false);
  const [approveModalVisibility, setApproveModalVisibility] = useState(false);
  const [reopenModalVisibility, setReopenModalVisibility] = useState(false);
  const isMVRUpdateEnabled = useFeatureEnabled(Feature.NF_MVR_UPDATE, false);

  const isAuthoritiesApproveModalEnabled = useFeatureEnabled(
    Feature.AUTHORITY_APPROVE_MODAL,
    false,
  );
  const isFlexQuoteEnabled = useFeatureEnabled(
    Feature.PRE_TELEMATICS_EXPERIMENT,
    false,
  );

  const isNewDeclineModalNF = useFeatureEnabled(
    Feature.NF_DECLINE_MODAL_V2,
    false,
  );

  const { data: summaryData } = useQuery(
    ['application-summary', appReviewId],
    () => getNFApplicationSummary(appReviewId),
  );

  const { data: actionsData, refetch: refetchActions } = useQuery(
    ['actions', appReviewId],
    () => fetchNonFleetActions(appReviewId),
  );

  const shouldUseNewDrivers =
    summaryData && summaryData.createdAt
      ? isNewerThanTargetDate(summaryData.createdAt) && isMVRUpdateEnabled
      : false;

  const { data: driversListOld } = useQuery(
    ['drivers', appReviewId],
    () => getNFApplicationDrivers(appReviewId),
    {
      enabled: !shouldUseNewDrivers,
    },
  );

  const { data: driversListNew } = useQuery(
    ['drivers-v2', appReviewId],
    () => getNFApplicationDriversV2(appReviewId),
    {
      enabled: shouldUseNewDrivers,
    },
  );

  const driversData = shouldUseNewDrivers ? driversListNew : driversListOld;

  const actionMap = useMemo(
    () =>
      (actionsData?.visibleActions.reduce(
        (acc, curr) => ({ ...acc, [curr.actionType]: curr }),
        {},
      ) ?? {}) as Record<ApplicationUserActionType, ApplicationReviewAction>,
    [actionsData?.visibleActions],
  );

  function handleApprove() {
    refetchQuote({ appReviewId, bindable: true });
    setApproveModalVisibility(true);
  }

  const handleReopen = () => {
    setReopenModalVisibility(true);
  };

  const supportApplicationDocumentURL = `${
    import.meta.env.VITE_SUPPORT_APP_URL
  }/applications/${data?.applicationID}/forms?applicationType=${
    ApplicationType.ApplicationTypeNonFleetAdmitted
  }`;

  const isApproveDisabled =
    data?.panelReviewStatuses.some((item) => !item.isReviewed) ||
    !driversData?.isMVRPulled;

  const packageDetails =
    packageInfo?.updatedPackage ?? packageInfo?.originalPackage;
  const sortedCoverages = useMemo(() => {
    if (!packageDetails?.primaryCovs) {
      return undefined;
    }

    return Coverages.getSortedCoverages({
      coverages: packageDetails?.primaryCovs,
      getCoverageType: (record) => record.coverageType,
    });
  }, [packageDetails?.primaryCovs]);

  const percentChange = useMemo(() => {
    return {
      [CoverageType.CoverageAutoLiability]: packageInfo?.alPercentage ?? 0,
      [CoverageType.CoverageAutoPhysicalDamage]: packageInfo?.apdPercentage,
      [CoverageType.CoverageMotorTruckCargo]: packageInfo?.mtcPercentage,
      [CoverageType.CoverageGeneralLiability]: packageInfo?.glPercentage,
    };
  }, [
    packageInfo?.alPercentage,
    packageInfo?.apdPercentage,
    packageInfo?.mtcPercentage,
    packageInfo?.glPercentage,
  ]);
  const safetyPercentChange = packageInfo?.safetyCredits;

  const creditLimits = normalizeCoverageKeys(
    packageInfo?.aggregateCreditLimitsByCoverage,
  );

  const creditsPctMap = useMemo(() => {
    return (
      sortedCoverages?.reduce(
        (acc, { coverageType }) => {
          const value =
            percentChange[coverageType as keyof typeof percentChange];

          acc[coverageType] =
            (value || 0) +
            (coverageType === CoverageType.CoverageGeneralLiability
              ? 0
              : safetyPercentChange || 0);

          return acc;
        },
        {} as Record<CoverageType, number>,
      ) ?? ({} as Record<CoverageType, number>)
    );
  }, [sortedCoverages, percentChange, safetyPercentChange]);

  const isAnyCreditLimitOutOfRange =
    Object.keys(creditsPctMap).some(
      (coverageType) =>
        creditsPctMap[coverageType as CoverageType] &&
        !isWithinLimits(
          creditsPctMap[coverageType as CoverageType],
          creditLimits?.[coverageType],
        ),
    ) ?? false;

  // only mapping UserActionApproval
  const { isEnabled, disableReasons, actionType } =
    actionMap.UserActionApproval || {};

  const tooltipContent = useMemo(() => {
    if (!driversData?.isMVRPulled) {
      return 'Please pull MVRs';
    }

    if (isAnyCreditLimitOutOfRange) {
      return 'Please adjust the debits/credits to comply with the state requirements';
    }

    if (isApplicationStale) {
      return 'Application is stale';
    }

    if (isApproveDisabled) {
      return 'Please review all panels';
    }

    if (isAuthoritiesApproveModalEnabled && disableReasons) {
      const actionTooltipProps = { actionType, content: disableReasons };
      return <ActionTooltip {...actionTooltipProps} />;
    }

    return '';
  }, [
    driversData?.isMVRPulled,
    isAnyCreditLimitOutOfRange,
    isApplicationStale,
    isApproveDisabled,
    isAuthoritiesApproveModalEnabled,
    disableReasons,
    actionType,
  ]);

  const isPreTelematicsExpEnabled =
    !!summaryData?.preTelematicsQuoteState && isFlexQuoteEnabled;

  const showReviewCta =
    data?.state === ApplicationReviewStatus.Approved ||
    data?.state === ApplicationReviewStatus.Declined ||
    data?.state === ApplicationReviewStatus.Closed;

  const duplicateApplications = summaryData?.duplicateApplications ?? [];
  const isExpressLaneFlow = summaryData?.isExpressLaneApplication;

  return (
    <header className="flex items-center justify-between px-6 py-1 space-x-2 bg-white border-b border-primary-extraLight">
      <div className="flex items-center space-x-2">
        <Show when={isPreTelematicsExpEnabled}>
          <Chip
            label={
              <span className="text-base text-tw-teal-700">
                Flex Quote program: quote this account without telematics
              </span>
            }
            color="success"
            className="!bg-tw-teal-200"
          />
        </Show>
        <Show when={isExpressLaneFlow}>
          <Tag color="green" icon={<GreenIcon />}>
            Express Lane
          </Tag>
        </Show>
      </div>
      <div className="space-x-2">
        <Show
          when={!showReviewCta}
          fallback={
            <>
              <Button
                variant="outlined"
                href={supportApplicationDocumentURL}
                target="_blank"
              >
                View Released Documents
              </Button>
              <Button variant="contained" onClick={handleReopen}>
                Reopen Review
              </Button>
            </>
          }
        >
          <Show when={duplicateApplications?.length > 0}>
            <DuplicateStatePopover
              duplicateApplications={duplicateApplications}
            />
          </Show>
          <Button
            color="error"
            variant="outlined"
            onClick={() => setCloseModalVisibility(true)}
            disabled={isApplicationStale}
          >
            Close
          </Button>

          <Button
            color="error"
            variant="outlined"
            onClick={() => setDeclineModalVisibility(true)}
            disabled={isApplicationStale}
          >
            Decline
          </Button>

          <Tooltip title={tooltipContent} onMouseEnter={() => refetchActions()}>
            <span>
              <Button
                variant="contained"
                onClick={handleApprove}
                disabled={
                  isApproveDisabled ||
                  isApplicationStale ||
                  isAnyCreditLimitOutOfRange ||
                  (!isEnabled && isAuthoritiesApproveModalEnabled)
                }
              >
                Approve
              </Button>
            </span>
          </Tooltip>
        </Show>

        <CloseModal
          open={closeModalVisibility}
          onClose={() => setCloseModalVisibility(false)}
        />

        <Show
          when={isNewDeclineModalNF}
          fallback={
            <DeclineModal
              open={declineModalVisibility}
              onClose={() => setDeclineModalVisibility(false)}
            />
          }
        >
          <DeclineModalNew
            open={declineModalVisibility}
            onClose={() => setDeclineModalVisibility(false)}
          />
        </Show>

        <ReopenModal
          open={reopenModalVisibility}
          onClose={() => setReopenModalVisibility(false)}
        />

        <ApproveModal
          open={approveModalVisibility}
          onClose={() => setApproveModalVisibility(false)}
        />
      </div>
    </header>
  );
}
