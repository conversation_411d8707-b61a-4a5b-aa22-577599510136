import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  makeStyles,
  styled,
} from '@material-ui/core';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { ApplicationReviewStatus } from '@nirvana/api/nfuw';
import { InputNumeric, ITheme, Show, Toggle } from '@nirvana/ui-kit';
import { light } from '@nirvana/ui-kit/src/assets/themes/light';
import clsx from 'clsx';
import { ReactNode, useEffect, useState } from 'react';
import { HiExternalLink } from 'react-icons/hi';
import { useMutation, useQueryClient } from 'react-query';
import { useParams } from 'react-router-dom';
import { useNFApplicationSummaryContext } from 'src/hooks/use-nfApplication-summary';

type WidgetProps = {
  title: string;
  children: ReactNode;
  isReview: boolean | undefined;
  queryKey: string;
  mutationFn: (data: {
    appReviewId: string;
    payload: { isReviewed: boolean };
  }) => Promise<void>;
  badgeProps?: {
    showBadgeInput: boolean;
    badgeValue?: number;
    updateBadge: (badgeValue?: number) => void;
    disabled?: boolean;
  };
};

const useStyles = makeStyles((theme) => ({
  accordion: {
    border: 'none',
    borderRadius: theme.shape.borderRadius,
    '&:before': {
      display: 'none',
    },
  },
}));

const BadgeInput = styled(InputNumeric)(({ theme }: { theme: ITheme }) => ({
  width: '4rem',
  padding: '0.4rem 0.5rem',
  '&.Mui-disabled': {
    backgroundColor: theme.palette.primary.extraLight,
  },
  '& .MuiInputBase-input': { padding: 0 },
  '& .Mui-disabled': {
    cursor: 'not-allowed',
    color: theme.palette.text.secondary,
    WebkitTextFillColor: theme.palette.text.secondary,
  },
}));

export default function Widget({
  title,
  children,
  isReview,
  queryKey,
  mutationFn,
  badgeProps,
}: WidgetProps) {
  const classes = useStyles();
  const { appReviewId = '' } = useParams();

  const [isExpanded, setIsExpanded] = useState(true);

  const { appSummary, isDisabled } = useNFApplicationSummaryContext();

  const [defaultBadgeValue, setDefaultBadgeValue] = useState(
    badgeProps?.badgeValue,
  );

  useEffect(() => {
    setDefaultBadgeValue(badgeProps?.badgeValue);
  }, [badgeProps?.badgeValue]);

  const queryClient = useQueryClient();
  const { mutate } = useMutation(mutationFn, {
    onMutate: async (newData) => {
      // Cancel any outgoing refetches, so they don't overwrite our optimistic update
      await queryClient.cancelQueries([queryKey, appReviewId]);
      // Snapshot the previous value
      const prevValue = queryClient.getQueryData([queryKey, appReviewId]);
      // Optimistically update to the new value
      queryClient.setQueryData([queryKey, appReviewId], (old: any) => {
        return {
          ...old,
          isReviewed: newData.payload.isReviewed,
        };
      });
      return { prevValue };
    },
    onError: (_, __, context: any) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData([queryKey, appReviewId], context.prevValue);
    },
    // Always refetch after error or success:
    onSettled: () => {
      queryClient.invalidateQueries([queryKey, appReviewId]);
      queryClient.invalidateQueries(['drivers-v2', appReviewId]);
      queryClient.invalidateQueries(['app-summary', appReviewId]);
    },
  });

  const isPreTelematicsOverrideAllowed =
    appSummary?.allowSafetyCreditOverrideDueToPreTelematics;

  const isBadgeInputDisabled =
    !isPreTelematicsOverrideAllowed && (badgeProps?.disabled || isDisabled);

  const isExpressLane = appSummary?.isExpressLaneApplication;

  // If express lane then to default select the checked option in the reviewed section
  useEffect(() => {
    if (isExpressLane && appReviewId) {
      mutate({ appReviewId, payload: { isReviewed: true } });
    }
  }, [appReviewId, isExpressLane, mutate]);

  return (
    <Accordion
      expanded={isExpanded}
      className={classes.accordion}
      onChange={() => setIsExpanded((prev) => !prev)}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <div className="flex items-center w-full">
          <p className="text-base font-semibold">{title}</p>
          <div className="flex-1" />

          <div
            className="flex items-center px-2 space-x-3"
            onClick={(e) => e.stopPropagation()}
          >
            <span className="text-base text-primary-main">
              {isReview ? 'Reviewed' : 'Mark as reviewed'}
            </span>
            <Toggle
              checked={!!isReview}
              onChange={(_, checked) => {
                mutate({ appReviewId, payload: { isReviewed: checked } });
              }}
              disabled={
                !(
                  appSummary?.state === ApplicationReviewStatus.UnderUwReview ||
                  appSummary?.state ===
                    ApplicationReviewStatus.UnderReferralReview
                )
              }
              backgroundTrackColor={light.palette.primary.track}
            />
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails>{children}</AccordionDetails>
      <Show when={badgeProps?.showBadgeInput}>
        <hr className="mb-2 border-primary-extraLight" />
        <div className="flex items-center justify-end p-3">
          <div className="flex items-center gap-1 mr-2 text-xs">
            Safety Credit
            <a
              href="https://docs.google.com/spreadsheets/d/1Ibmr0-2s093O3SpuNVAl883-Ht4AqmT3E-63jH16l8E/edit#gid=0"
              target="_blank"
              rel="noreferrer"
              className="-mt-[2px] text-base text-primary-main"
            >
              <HiExternalLink />
            </a>
          </div>
          <BadgeInput
            allowNegative
            decimalScale={0}
            disabled={isBadgeInputDisabled}
            prefix={defaultBadgeValue && defaultBadgeValue > 0 ? '+' : ''}
            value={defaultBadgeValue}
            className={clsx({
              'text-green-500 bg-green-50':
                defaultBadgeValue && defaultBadgeValue > 0,
              'text-red-500 bg-red-50':
                defaultBadgeValue && defaultBadgeValue < 0,
              'text-text-hint bg-gold-tint': defaultBadgeValue === undefined,
            })}
            onBlur={() => {
              badgeProps?.updateBadge(defaultBadgeValue);
            }}
            onChange={(e) =>
              setDefaultBadgeValue(
                Number.isNaN(parseFloat(e.target.value))
                  ? undefined
                  : parseFloat(e.target.value),
              )
            }
          />
        </div>
      </Show>
    </Accordion>
  );
}
