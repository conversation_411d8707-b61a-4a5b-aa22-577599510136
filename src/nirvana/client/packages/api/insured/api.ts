/* tslint:disable */
/* eslint-disable */
/**
 * Insured API
 * Nirvana Insured API
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { Configuration } from './configuration';
import globalAxios, {
  AxiosPromise,
  AxiosInstance,
  AxiosRequestConfig,
} from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import {
  DUMMY_BASE_URL,
  assertParamExists,
  setApiKeyToObject,
  setBasicAuthToObject,
  setBearerAuthToObject,
  setOAuthToObject,
  setSearchParams,
  serializeDataIfNeeded,
  toPathString,
  createRequestFunction,
} from './common';
// @ts-ignore
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  RequestArgs,
  BaseAPI,
  RequiredError,
} from './base';

/**
 *
 * @export
 * @interface GenerateProvisionalLossRunsReportRequest
 */
export interface GenerateProvisionalLossRunsReportRequest {
  /**
   *
   * @type {Array<string>}
   * @memberof GenerateProvisionalLossRunsReportRequest
   */
  policyNumbers: Array<string>;
}
/**
 *
 * @export
 * @interface GenerateProvisionalLossRunsReportsForDotRequest
 */
export interface GenerateProvisionalLossRunsReportsForDotRequest {
  /**
   *
   * @type {number}
   * @memberof GenerateProvisionalLossRunsReportsForDotRequest
   */
  dotNumber: number;
}
/**
 *
 * @export
 * @enum {string}
 */

export enum LossRunReportStatus {
  Waiting = 'Waiting',
  Running = 'Running',
  Failed = 'Failed',
  Terminated = 'Terminated',
  Succeeded = 'Succeeded',
  Unknown = 'Unknown',
}

/**
 *
 * @export
 * @interface LossRunsReportLinkForPolicy
 */
export interface LossRunsReportLinkForPolicy {
  /**
   *
   * @type {string}
   * @memberof LossRunsReportLinkForPolicy
   */
  link: string;
  /**
   *
   * @type {string}
   * @memberof LossRunsReportLinkForPolicy
   */
  policyNumber: string;
}
/**
 *
 * @export
 * @interface Policy
 */
export interface Policy {
  /**
   *
   * @type {string}
   * @memberof Policy
   */
  policyNumber: string;
  /**
   *
   * @type {string}
   * @memberof Policy
   */
  insuredName: string;
  /**
   *
   * @type {string}
   * @memberof Policy
   */
  dotNumber: string;
  /**
   *
   * @type {string}
   * @memberof Policy
   */
  effectiveDate: string;
  /**
   *
   * @type {string}
   * @memberof Policy
   */
  expirationDate: string;
}
/**
 *
 * @export
 * @interface ProvisionalLossRunsReport
 */
export interface ProvisionalLossRunsReport {
  /**
   *
   * @type {string}
   * @memberof ProvisionalLossRunsReport
   */
  id: string;
  /**
   *
   * @type {Policy}
   * @memberof ProvisionalLossRunsReport
   */
  policy: Policy;
  /**
   *
   * @type {string}
   * @memberof ProvisionalLossRunsReport
   */
  generatedAt: string;
  /**
   *
   * @type {string}
   * @memberof ProvisionalLossRunsReport
   */
  requestedBy: string;
  /**
   *
   * @type {string}
   * @memberof ProvisionalLossRunsReport
   */
  link?: string;
  /**
   *
   * @type {LossRunReportStatus}
   * @memberof ProvisionalLossRunsReport
   */
  status: LossRunReportStatus;
}

/**
 * InsuredApi - axios parameter creator
 * @export
 */
export const InsuredApiAxiosParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * Generate a Provisional Loss Runs report for each of the given policy numbers
     * @param {GenerateProvisionalLossRunsReportRequest} generateProvisionalLossRunsReportRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    generateProvisionalLossRunsReport: async (
      generateProvisionalLossRunsReportRequest: GenerateProvisionalLossRunsReportRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'generateProvisionalLossRunsReportRequest' is not null or undefined
      assertParamExists(
        'generateProvisionalLossRunsReport',
        'generateProvisionalLossRunsReportRequest',
        generateProvisionalLossRunsReportRequest,
      );
      const localVarPath = `/loss-runs/provisional-reports`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        generateProvisionalLossRunsReportRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Generate Provisional Loss Runs reports for each active policy associated with the given DOT number
     * @param {GenerateProvisionalLossRunsReportsForDotRequest} generateProvisionalLossRunsReportsForDotRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    generateProvisionalLossRunsReportsForDOT: async (
      generateProvisionalLossRunsReportsForDotRequest: GenerateProvisionalLossRunsReportsForDotRequest,
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      // verify required parameter 'generateProvisionalLossRunsReportsForDotRequest' is not null or undefined
      assertParamExists(
        'generateProvisionalLossRunsReportsForDOT',
        'generateProvisionalLossRunsReportsForDotRequest',
        generateProvisionalLossRunsReportsForDotRequest,
      );
      const localVarPath = `/loss-runs/provisional-reports/dot-number`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'POST',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      localVarHeaderParameter['Content-Type'] = 'application/json';

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };
      localVarRequestOptions.data = serializeDataIfNeeded(
        generateProvisionalLossRunsReportsForDotRequest,
        localVarRequestOptions,
        configuration,
      );

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
    /**
     * Get all Provisional Loss Runs reports
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAllProvisionalLossRunsReports: async (
      options: AxiosRequestConfig = {},
    ): Promise<RequestArgs> => {
      const localVarPath = `/loss-runs/provisional-reports`;
      // use dummy base URL string because the URL constructor only accepts absolute URLs.
      const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
      let baseOptions;
      if (configuration) {
        baseOptions = configuration.baseOptions;
      }

      const localVarRequestOptions = {
        method: 'GET',
        ...baseOptions,
        ...options,
      };
      const localVarHeaderParameter = {} as any;
      const localVarQueryParameter = {} as any;

      setSearchParams(localVarUrlObj, localVarQueryParameter);
      let headersFromBaseOptions =
        baseOptions && baseOptions.headers ? baseOptions.headers : {};
      localVarRequestOptions.headers = {
        ...localVarHeaderParameter,
        ...headersFromBaseOptions,
        ...options.headers,
      };

      return {
        url: toPathString(localVarUrlObj),
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * InsuredApi - functional programming interface
 * @export
 */
export const InsuredApiFp = function (configuration?: Configuration) {
  const localVarAxiosParamCreator = InsuredApiAxiosParamCreator(configuration);
  return {
    /**
     * Generate a Provisional Loss Runs report for each of the given policy numbers
     * @param {GenerateProvisionalLossRunsReportRequest} generateProvisionalLossRunsReportRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async generateProvisionalLossRunsReport(
      generateProvisionalLossRunsReportRequest: GenerateProvisionalLossRunsReportRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.generateProvisionalLossRunsReport(
          generateProvisionalLossRunsReportRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Generate Provisional Loss Runs reports for each active policy associated with the given DOT number
     * @param {GenerateProvisionalLossRunsReportsForDotRequest} generateProvisionalLossRunsReportsForDotRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async generateProvisionalLossRunsReportsForDOT(
      generateProvisionalLossRunsReportsForDotRequest: GenerateProvisionalLossRunsReportsForDotRequest,
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<LossRunsReportLinkForPolicy>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.generateProvisionalLossRunsReportsForDOT(
          generateProvisionalLossRunsReportsForDotRequest,
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
    /**
     * Get all Provisional Loss Runs reports
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    async getAllProvisionalLossRunsReports(
      options?: AxiosRequestConfig,
    ): Promise<
      (
        axios?: AxiosInstance,
        basePath?: string,
      ) => AxiosPromise<Array<ProvisionalLossRunsReport>>
    > {
      const localVarAxiosArgs =
        await localVarAxiosParamCreator.getAllProvisionalLossRunsReports(
          options,
        );
      return createRequestFunction(
        localVarAxiosArgs,
        globalAxios,
        BASE_PATH,
        configuration,
      );
    },
  };
};

/**
 * InsuredApi - factory interface
 * @export
 */
export const InsuredApiFactory = function (
  configuration?: Configuration,
  basePath?: string,
  axios?: AxiosInstance,
) {
  const localVarFp = InsuredApiFp(configuration);
  return {
    /**
     * Generate a Provisional Loss Runs report for each of the given policy numbers
     * @param {GenerateProvisionalLossRunsReportRequest} generateProvisionalLossRunsReportRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    generateProvisionalLossRunsReport(
      generateProvisionalLossRunsReportRequest: GenerateProvisionalLossRunsReportRequest,
      options?: any,
    ): AxiosPromise<void> {
      return localVarFp
        .generateProvisionalLossRunsReport(
          generateProvisionalLossRunsReportRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Generate Provisional Loss Runs reports for each active policy associated with the given DOT number
     * @param {GenerateProvisionalLossRunsReportsForDotRequest} generateProvisionalLossRunsReportsForDotRequest
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    generateProvisionalLossRunsReportsForDOT(
      generateProvisionalLossRunsReportsForDotRequest: GenerateProvisionalLossRunsReportsForDotRequest,
      options?: any,
    ): AxiosPromise<Array<LossRunsReportLinkForPolicy>> {
      return localVarFp
        .generateProvisionalLossRunsReportsForDOT(
          generateProvisionalLossRunsReportsForDotRequest,
          options,
        )
        .then((request) => request(axios, basePath));
    },
    /**
     * Get all Provisional Loss Runs reports
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     */
    getAllProvisionalLossRunsReports(
      options?: any,
    ): AxiosPromise<Array<ProvisionalLossRunsReport>> {
      return localVarFp
        .getAllProvisionalLossRunsReports(options)
        .then((request) => request(axios, basePath));
    },
  };
};

/**
 * InsuredApi - object-oriented interface
 * @export
 * @class InsuredApi
 * @extends {BaseAPI}
 */
export class InsuredApi extends BaseAPI {
  /**
   * Generate a Provisional Loss Runs report for each of the given policy numbers
   * @param {GenerateProvisionalLossRunsReportRequest} generateProvisionalLossRunsReportRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsuredApi
   */
  public generateProvisionalLossRunsReport(
    generateProvisionalLossRunsReportRequest: GenerateProvisionalLossRunsReportRequest,
    options?: AxiosRequestConfig,
  ) {
    return InsuredApiFp(this.configuration)
      .generateProvisionalLossRunsReport(
        generateProvisionalLossRunsReportRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Generate Provisional Loss Runs reports for each active policy associated with the given DOT number
   * @param {GenerateProvisionalLossRunsReportsForDotRequest} generateProvisionalLossRunsReportsForDotRequest
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsuredApi
   */
  public generateProvisionalLossRunsReportsForDOT(
    generateProvisionalLossRunsReportsForDotRequest: GenerateProvisionalLossRunsReportsForDotRequest,
    options?: AxiosRequestConfig,
  ) {
    return InsuredApiFp(this.configuration)
      .generateProvisionalLossRunsReportsForDOT(
        generateProvisionalLossRunsReportsForDotRequest,
        options,
      )
      .then((request) => request(this.axios, this.basePath));
  }

  /**
   * Get all Provisional Loss Runs reports
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof InsuredApi
   */
  public getAllProvisionalLossRunsReports(options?: AxiosRequestConfig) {
    return InsuredApiFp(this.configuration)
      .getAllProvisionalLossRunsReports(options)
      .then((request) => request(this.axios, this.basePath));
  }
}
