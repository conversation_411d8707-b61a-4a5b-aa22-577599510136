package verify

import (
	"fmt"

	"github.com/cockroachdb/errors"
	"github.com/spf13/cobra"
)

var ErrNoChangesInPlan = errors.Newf("no changes detected in plan, check the inputs or if the deployment already happened")

type jobberCluster string

const (
	JobberClusterQuoting    jobberCluster = "quoting"
	JobberClusterStandard   jobberCluster = "standard"
	JobberClusterDataInfra  jobberCluster = "data-infra"
	JobberClusterSafety     jobberCluster = "safety"
	JobberClusterEvent      jobberCluster = "event"
	JobberClusterSimulation jobberCluster = "simulation"
)

var clusterToServiceResourceAddress = map[jobberCluster]string{
	JobberClusterQuoting:    "module.quoting_job_processor_fg_service.aws_ecs_service.service",
	JobberClusterStandard:   "module.job_processor_fg_service.aws_ecs_service.service",
	JobberClusterDataInfra:  "module.data_infra_job_processor_fg_service.aws_ecs_service.service",
	JobberClusterSafety:     "module.safety_job_processor_fg_service.aws_ecs_service.service",
	JobberClusterEvent:      "module.event_job_processor_fg_service.aws_ecs_service.service",
	JobberClusterSimulation: "module.simulation_job_processor_fg_service.aws_ecs_service.service",
}

var clusterToTaskDefinitionResourceAddress = map[jobberCluster]string{
	JobberClusterQuoting:    "module.quoting_job_processor_td.aws_ecs_task_definition.td",
	JobberClusterStandard:   "module.job_processor_td.aws_ecs_task_definition.td",
	JobberClusterDataInfra:  "module.data_infra_job_processor_td.aws_ecs_task_definition.td",
	JobberClusterSafety:     "module.safety_job_processor_td.aws_ecs_task_definition.td",
	JobberClusterEvent:      "module.event_job_processor_td.aws_ecs_task_definition.td",
	JobberClusterSimulation: "module.simulation_job_processor_td.aws_ecs_task_definition.td",
}

func verifyJobProcessorChangeOnly(clusterName jobberCluster) error {
	somethingChanged := false
	for _, res_delta := range Plan.ResourceChanges {
		if res_delta.Change.Actions.NoOp() {
			continue
		}
		fmt.Printf("Detected diff on %s ...\n", res_delta.Address)
		delta := res_delta.Change
		switch res_delta.Type {
		case "aws_ecs_service":
			if !delta.Actions.Update() {
				return errors.Newf("expected action 'updated' on ecs_service, instead found %v in plan", delta.Actions)
			}
			if res_delta.Address != clusterToServiceResourceAddress[clusterName] {
				return errors.Newf("expected only ecs_service %s to change, instead found %v",
					clusterToServiceResourceAddress[clusterName], res_delta.Address)
			}
			var before, after ECSService
			if err := parseBeforeAfterFromResourceChange(delta, &before, &after); err != nil {
				return errors.Wrapf(err, "failed parsing %s", res_delta.Address)
			}
			if before.Partial.Name != after.Partial.Name {
				return errors.Newf("ecs_service name should not change, but found %s vs %s in %s",
					before.Partial.Name, after.Partial.Name, res_delta.Address,
				)
			}
			if err := before.AssertNoUnknownDiff(res_delta.Address, &after); err != nil {
				return err
			}
			somethingChanged = true
		case "aws_ecs_task_definition":
			if res_delta.Address != clusterToTaskDefinitionResourceAddress[clusterName] {
				return errors.Newf("expected only task_definition %s to change, instead found %v",
					clusterToTaskDefinitionResourceAddress[clusterName], res_delta.Address)
			}
			somethingChanged = true
		default:
			return errors.Newf("Unexpected diff, only ecs_service and task_definition can change")
		}
	}
	if somethingChanged {
		return nil
	}
	return ErrNoChangesInPlan
}

func VerifyJobProcessorChangeOnly(cmd *cobra.Command, args []string) error {
	var cluster jobberCluster
	switch c := args[1]; c {
	case "quoting":
		cluster = JobberClusterQuoting
	case "standard":
		cluster = JobberClusterStandard
	case "data-infra":
		cluster = JobberClusterDataInfra
	case "safety":
		cluster = JobberClusterSafety
	case "event":
		cluster = JobberClusterEvent
	case "simulation":
		cluster = JobberClusterSimulation
	default:
		return errors.Newf("Unknown cluster %s", c)
	}
	return verifyJobProcessorChangeOnly(cluster)
}
