package recommended_action_rubric

import (
	"context"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"github.com/volatiletech/null/v8"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

type RecommendedActionCalculator interface {
	CalculateRecommendedAction(ctx context.Context, req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error)
}

type RecommendedActionCalculatorV1 struct{}

func (c RecommendedActionCalculatorV1) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest,
) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error) {
	if !req.IsException {
		if req.MarketCategory == nil {
			return appetite_factor.RecommendedActionPending, nil, nil, nil
		}
		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		actionMap := map[string]map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
			"Extended": {
				appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
			},
			"Acceptable": {
				appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
			},
			"Target": {
				appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionStronglyQuote,
				appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionNeutral,
			},
		}

		if actions, ok := actionMap[*req.MarketCategory]; ok {
			if action, ok := actions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}
		return invalidAction, nil, nil, errors.Newf("target market %+v not handled", *req.MarketCategory)
	}

	if req.IsException {
		exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
		}

		if action, ok := exceptionActions[req.AppetiteScore]; ok {
			return action, nil, nil, nil
		}
		return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
	}

	return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, marketCategory: %+v, isException: %t",
		req.AppetiteScore.String(), req.MarketCategory, req.IsException)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV1{}

type RecommendedActionCalculatorV2 struct{}

// CalculateRecommendedAction version 2 updates the rubric as:
// TRS Target + Marginal Appetite: Neutral -> Quote
// TRS Target + Acceptable Appetite: Quote -> Strongly quote
// TRS Acceptable + Preferred Appetite: Quote -> Strong quote
// TRS Extended + Preferred Appetite: Neutral -> Quote
func (c RecommendedActionCalculatorV2) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest,
) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error) {
	if !req.IsException {
		if req.MarketCategory == nil {
			return appetite_factor.RecommendedActionPending, nil, nil, nil
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		actionMap := map[string]map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
			"Extended": {
				appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
			},
			"Acceptable": {
				appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
				appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
			},
			"Target": {
				appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionStronglyQuote,
				appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionStronglyQuote,
				appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionQuote,
				appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionNeutral,
			},
		}

		if actions, ok := actionMap[*req.MarketCategory]; ok {
			if action, ok := actions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}
		return invalidAction, nil, nil, errors.Newf("target market %+v not handled", *req.MarketCategory)
	}

	if req.IsException {
		exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
		}

		if action, ok := exceptionActions[req.AppetiteScore]; ok {
			return action, nil, nil, nil
		}
		return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
	}

	return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, marketCategory: %+v, isException: %t",
		req.AppetiteScore.String(), req.MarketCategory, req.IsException)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV2{}

type RecommendedActionCalculatorV3 struct{}

// CalculateRecommendedAction version 3 updates the rubric as:
//  1. Follow the rubric for any TSP in the list: Lytx, PeopleNet, Teletrac Navman, Orbcomm, Azuga, Transflo,
//     ELD Mandate, Linxup, Monarch Tracking, ELD Rider, Optima ELD, Omnitracs; for TSP not in the list, action is Decline
//  2. If Conditional DOT rating is recent (i.e., less than 2 years from app review effective date), action is Decline
//  3. For Short Haul operations (with Premier accounts), follow the exception rubric
//  4. AB Testing to move Decline to Further Review for 50% of the applications after 15th July 2024
func (c RecommendedActionCalculatorV3) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest,
) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error) {
	if req.IsCRatingRecent {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating), nil, nil
	}
	// exceptionActions when TSP is an Exception one or risk score trend is of a Short Haul operation
	exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
		appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
		appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
		appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
	}
	if !req.IsException {
		if req.MarketCategory == nil {
			return appetite_factor.RecommendedActionPending, nil, nil, nil
		}
		if req.IsShortHaul != nil && *req.IsShortHaul {
			// Treat Short Haul as an exception case (since Risk Score is not reliable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		recommendedAction, err := getRecommendationActionFromRubricV3(req)
		if err != nil {
			return invalidAction, nil, nil, err
		}

		return recommendedAction, nil, nil, nil
	}

	if !req.IsException {
		return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, "+
			"marketCategory: %+v, isException: %t", req.AppetiteScore.String(), req.MarketCategory, req.IsException)
	}

	// Exception case:
	if req.TSPKind == nil {
		return invalidAction, nil, nil, errors.New("tspKind is required for exception case")
	}
	// Directly return Decline for any appetite score if TSP is not in the list
	if _, ok := allowedExceptionTSPs[*req.TSPKind]; !ok {
		return appetite_factor.RecommendedActionDecline, nil, nil, nil
	}

	if action, ok := exceptionActions[req.AppetiteScore]; ok {
		return action, nil, nil, nil
	}
	return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
}

func getRecommendationActionFromRubricV3(req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction, error) {
	actionMap := map[string]map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		"Extended": {
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
		},
		"Acceptable": {
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
		},
		"Target": {
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionStronglyQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionStronglyQuote,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionNeutral,
		},
	}

	actions, ok := actionMap[*req.MarketCategory]
	if !ok {
		return invalidAction, errors.Newf("market category %+v not handled", *req.MarketCategory)
	}

	action, ok := actions[req.AppetiteScore]
	if !ok {
		return invalidAction, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
	}
	return action, nil
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV3{}

type RecommendedActionCalculatorV4 struct{}

// CalculateRecommendedAction version 4 updates the rubric as:
// 1. Handle supplemental actions for Premier TSPs: Hazard Zone evaluation
// 2. Handle supplemental actions for Non-Premier TSPs: Hazard Zone evaluation, Large Fleet evaluation
func (c RecommendedActionCalculatorV4) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction,
	*appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error,
) {
	if req.IsCRatingRecent {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating), nil, nil
	}
	// exceptionActions when TSP is an Exception one or risk score trend is of a Short Haul operation
	exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
		appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
		appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
		appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
	}
	// Premier TSP case
	if !req.IsException {
		if req.MarketCategory == nil {
			return appetite_factor.RecommendedActionPending, nil, nil, nil
		}
		if req.IsShortHaul != nil && *req.IsShortHaul {
			// Treat Short Haul as an exception case (since Risk Score is not reliable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		action, err := getRecommendationActionFromRubricV3(req)
		if err != nil {
			return invalidAction, nil, nil, err
		}
		newAction, reason, supposedAction := supplementaryActionsForPremierTSP(ctx, action, req.HazardZone)
		return newAction, reason, supposedAction, nil
	}

	if !req.IsException {
		return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, "+
			"marketCategory: %+v, isException: %t", req.AppetiteScore.String(), req.MarketCategory, req.IsException)
	}

	// Exception case:
	if req.TSPKind == nil {
		return invalidAction, nil, nil, errors.New("tspKind is required for exception case")
	}

	// Directly return Decline for any appetite score if number of PUs is greater than 100
	if req.NumOfPU.Valid && req.NumOfPU.Int32 > 100 {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier), nil, nil
	}

	// Directly return Decline for any appetite score if TSP is not in the list
	if _, ok := allowedExceptionTSPs[*req.TSPKind]; !ok {
		return appetite_factor.RecommendedActionDecline, nil, nil, nil
	}

	if action, ok := exceptionActions[req.AppetiteScore]; ok {
		newAction, reason, supposedAction := supplementaryActionsForNonPremierTSP(ctx, action, req.HazardZone, req.NumOfPU)
		return newAction, reason, supposedAction, nil
	}
	return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV4{}

type RecommendedActionCalculatorV5 struct{}

// CalculateRecommendedAction version 5 updates the rubric as:
//  1. If Market Category is not available for a premier TSP and risk score trend is not present in feature records
//     table row, we follow the exception rubric and return the appropriate recommended action
//     with a concrete reason ("Safety Score Market Category Unavailable")
func (c RecommendedActionCalculatorV5) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction,
	*appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error,
) {
	if req.IsCRatingRecent {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating), nil, nil
	}
	// exceptionActions when TSP is an Exception one or risk score trend is of a Short Haul operation
	exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
		appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
		appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
		appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
	}
	// Premier TSP case
	if !req.IsException {
		// Just FYI: MarketCategory is set as nil when no safety score is present, vin visibility is not computed
		// as well when safety score is not present:
		// therefore vin visibility should also be not computed for the application review
		if req.MarketCategory == nil {
			if !req.IsRiskScoreElementNotPresent {
				return appetite_factor.RecommendedActionPending, nil, nil, nil
			}
			// Entry (db row) was present in feature records table,
			// but "risk score trend" element data was not present in this entry
			reasonPtr := pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, reasonPtr, nil, nil
			}
			return invalidAction, reasonPtr, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		if req.IsShortHaul != nil && *req.IsShortHaul {
			// Treat Short Haul as an exception case (since Risk Score is not reliable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		action, err := getRecommendationActionFromRubricV3(req)
		if err != nil {
			return invalidAction, nil, nil, err
		}

		newAction, reason, supposedAction := supplementaryActionsForPremierTSP(ctx, action, req.HazardZone)
		return newAction, reason, supposedAction, nil
	}

	if !req.IsException {
		return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, "+
			"marketCategory: %+v, isException: %t", req.AppetiteScore.String(), req.MarketCategory, req.IsException)
	}

	// Exception case:
	if req.TSPKind == nil {
		return invalidAction, nil, nil, errors.New("tspKind is required for exception case")
	}

	// Directly return Decline for any appetite score if number of PUs is greater than 100
	if req.NumOfPU.Valid && req.NumOfPU.Int32 > 100 {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier), nil, nil
	}

	// Directly return Decline for any appetite score if TSP is not in the list
	if _, ok := allowedExceptionTSPs[*req.TSPKind]; !ok {
		return appetite_factor.RecommendedActionDecline, nil, nil, nil
	}

	if action, ok := exceptionActions[req.AppetiteScore]; ok {
		newAction, reason, supposedAction := supplementaryActionsForNonPremierTSP(ctx, action, req.HazardZone, req.NumOfPU)
		return newAction, reason, supposedAction, nil
	}
	return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV5{}

type RecommendedActionCalculatorV6 struct{}

// CalculateRecommendedAction version 6 updates the rubric as:
//  1. Updates the premier tsp recommendation rubric:
//     TRS Extended x App Marginal moves from neutral -> decline
//     TRS Extended x App Acceptable moves from neutral -> quote
//     TRS Acceptable x App HighRisk moves from decline -> neutral
//     TRS Acceptable x App Preferred moves from strongly quote -> quote
//     TRS Target x AppHighRisk moves from neutral -> quote
func (c RecommendedActionCalculatorV6) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction,
	*appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error,
) {
	if req.IsCRatingRecent {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating), nil, nil
	}
	// exceptionActions when TSP is an Exception one or risk score trend is of a Short Haul operation
	exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
		appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
		appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
		appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
	}
	// Premier TSP case
	if !req.IsException {
		// Just FYI: MarketCategory is set as nil when no safety score is present, vin visibility is not computed
		// as well when safety score is not present:
		// therefore vin visibility should also be not computed for the application review
		if req.MarketCategory == nil {
			if !req.IsRiskScoreElementNotPresent {
				return appetite_factor.RecommendedActionPending, nil, nil, nil
			}
			// Entry (db row) was present in feature records table,
			// but "risk score trend" element data was not present in this entry
			reasonPtr := pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, reasonPtr, nil, nil
			}
			return invalidAction, reasonPtr, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		if req.IsShortHaul != nil && *req.IsShortHaul {
			// Treat Short Haul as an exception case (since Risk Score is not reliable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		action, err := getRecommendationActionFromRubricV6(req)
		if err != nil {
			return invalidAction, nil, nil, err
		}

		newAction, reason, supposedAction := supplementaryActionsForPremierTSP(ctx, action, req.HazardZone)
		return newAction, reason, supposedAction, nil
	}

	if !req.IsException {
		return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, "+
			"marketCategory: %+v, isException: %t", req.AppetiteScore.String(), req.MarketCategory, req.IsException)
	}

	// Exception case:
	if req.TSPKind == nil {
		return invalidAction, nil, nil, errors.New("tspKind is required for exception case")
	}

	// Directly return Decline for any appetite score if number of PUs is greater than 100
	if req.NumOfPU.Valid && req.NumOfPU.Int32 > 100 {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier), nil, nil
	}

	// Directly return Decline for any appetite score if TSP is not in the list
	if _, ok := allowedExceptionTSPs[*req.TSPKind]; !ok {
		return appetite_factor.RecommendedActionDecline, nil, nil, nil
	}

	if action, ok := exceptionActions[req.AppetiteScore]; ok {
		newAction, reason, supposedAction := supplementaryActionsForNonPremierTSP(ctx, action, req.HazardZone, req.NumOfPU)
		return newAction, reason, supposedAction, nil
	}
	return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV6{}

type RecommendedActionCalculatorV7 struct{}

// CalculateRecommendedAction version 7 updates the rubric as:
//  1. Checks for Losses Burn Rate greater than 20K and returns Decline with a specific reason
func (c RecommendedActionCalculatorV7) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction,
	*appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error,
) {
	switch {
	case req.IsCRatingRecent:
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating), nil, nil
	case req.IsLossesBurnRateGreaterThan20K:
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonLossesBurnRateGreaterThan20K), nil, nil
	}
	// exceptionActions when TSP is an Exception one or risk score trend is of a Short Haul operation
	exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
		appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
		appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
		appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
	}
	// Premier TSP case
	if !req.IsException {
		// Just FYI: MarketCategory is set as nil when no safety score is present, vin visibility is not computed
		// as well when safety score is not present:
		// therefore vin visibility should also be not computed for the application review
		if req.MarketCategory == nil {
			if !req.IsRiskScoreElementNotPresent {
				return appetite_factor.RecommendedActionPending, nil, nil, nil
			}
			// Entry (db row) was present in feature records table,
			// but "risk score trend" element data was not present in this entry
			reasonPtr := pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, reasonPtr, nil, nil
			}
			return invalidAction, reasonPtr, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		if req.IsShortHaul != nil && *req.IsShortHaul {
			// Treat Short Haul as an exception case (since Risk Score is not reliable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		action, err := getRecommendationActionFromRubricV6(req)
		if err != nil {
			return invalidAction, nil, nil, err
		}

		newAction, reason, supposedAction := supplementaryActionsForPremierTSP(ctx, action, req.HazardZone)
		return newAction, reason, supposedAction, nil
	}

	if !req.IsException {
		return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, "+
			"marketCategory: %+v, isException: %t", req.AppetiteScore.String(), req.MarketCategory, req.IsException)
	}

	// Exception case:
	if req.TSPKind == nil {
		return invalidAction, nil, nil, errors.New("tspKind is required for exception case")
	}

	// Directly return Decline for any appetite score if number of PUs is greater than 100
	if req.NumOfPU.Valid && req.NumOfPU.Int32 > 100 {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier), nil, nil
	}

	// Directly return Decline for any appetite score if TSP is not in the list
	if _, ok := allowedExceptionTSPs[*req.TSPKind]; !ok {
		return appetite_factor.RecommendedActionDecline, nil, nil, nil
	}

	if action, ok := exceptionActions[req.AppetiteScore]; ok {
		newAction, reason, supposedAction := supplementaryActionsForNonPremierTSP(ctx, action, req.HazardZone, req.NumOfPU)
		return newAction, reason, supposedAction, nil
	}
	return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV7{}

type RecommendedActionCalculatorV8 struct{}

// CalculateRecommendedAction version 8 updates the rubric as:
//  1. Adds a condition for appetite guidelines decline rules
func (c RecommendedActionCalculatorV8) CalculateRecommendedAction(
	ctx context.Context,
	req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction,
	*appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction, error,
) {
	if req.AppetiteGuidelinesInput.IsDecline {
		action, reason, err := getDeclineRecommendedActionReason(req.AppetiteGuidelinesInput)
		if err != nil {
			return invalidAction, nil, nil, errors.Wrap(err, "failed to get decline recommended action reason")
		}

		return action, &reason, nil, nil
	}

	// exceptionActions when TSP is an Exception one or risk score trend is of a Short Haul operation
	exceptionActions := map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
		appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionNeutral,
		appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
		appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
	}
	// Premier TSP case
	if !req.IsException {
		// Just FYI: MarketCategory is set as nil when no safety score is present, vin visibility is not computed
		// as well when safety score is not present:
		// therefore vin visibility should also be not computed for the application review
		if req.MarketCategory == nil {
			if !req.IsRiskScoreElementNotPresent {
				return appetite_factor.RecommendedActionPending, nil, nil, nil
			}
			// Entry (db row) was present in feature records table,
			// but "risk score trend" element data was not present in this entry
			reasonPtr := pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, reasonPtr, nil, nil
			}
			return invalidAction, reasonPtr, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		if req.IsShortHaul != nil && *req.IsShortHaul {
			// Treat Short Haul as an exception case (since Risk Score is not reliable)
			if action, ok := exceptionActions[req.AppetiteScore]; ok {
				return action, nil, nil, nil
			}
			return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
		}

		// Directly return Decline for any appetite score if marketCategory is Decline
		if *req.MarketCategory == "Decline" {
			return appetite_factor.RecommendedActionDecline, nil, nil, nil
		}

		action, err := getRecommendationActionFromRubricV6(req)
		if err != nil {
			return invalidAction, nil, nil, err
		}

		newAction, reason, supposedAction := supplementaryActionsForPremierTSP(ctx, action, req.HazardZone)
		return newAction, reason, supposedAction, nil
	}

	if !req.IsException {
		return invalidAction, nil, nil, errors.Newf("unable to calculate recommendation for score: %s, "+
			"marketCategory: %+v, isException: %t", req.AppetiteScore.String(), req.MarketCategory, req.IsException)
	}

	// Exception case:
	if req.TSPKind == nil {
		return invalidAction, nil, nil, errors.New("tspKind is required for exception case")
	}

	// Directly return Decline for any appetite score if number of PUs is greater than 100
	if req.NumOfPU.Valid && req.NumOfPU.Int32 > 100 {
		return appetite_factor.RecommendedActionDecline,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier), nil, nil
	}

	// Directly return Decline for any appetite score if TSP is not in the list
	if _, ok := allowedExceptionTSPs[*req.TSPKind]; !ok {
		return appetite_factor.RecommendedActionDecline, nil, nil, nil
	}

	if action, ok := exceptionActions[req.AppetiteScore]; ok {
		newAction, reason, supposedAction := supplementaryActionsForNonPremierTSP(ctx, action, req.HazardZone, req.NumOfPU)
		return newAction, reason, supposedAction, nil
	}
	return invalidAction, nil, nil, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV8{}

func getRecommendationActionFromRubricV6(req CalculateRecommendationActionRequest) (appetite_factor.RecommendedAction, error) {
	actionMap := map[string]map[appetite_factor.AppetiteScore]appetite_factor.RecommendedAction{
		"Extended": {
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionDecline,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionDecline,
		},
		"Acceptable": {
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionNeutral,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionNeutral,
		},
		"Target": {
			appetite_factor.AppetiteScorePreferred:  appetite_factor.RecommendedActionStronglyQuote,
			appetite_factor.AppetiteScoreAcceptable: appetite_factor.RecommendedActionStronglyQuote,
			appetite_factor.AppetiteScoreMarginal:   appetite_factor.RecommendedActionQuote,
			appetite_factor.AppetiteScoreHighRisk:   appetite_factor.RecommendedActionQuote,
		},
	}

	actions, ok := actionMap[*req.MarketCategory]
	if !ok {
		return invalidAction, errors.Newf("market category %+v not handled", *req.MarketCategory)
	}

	action, ok := actions[req.AppetiteScore]
	if !ok {
		return invalidAction, errors.Newf("appetite score %+v not handled", req.AppetiteScore)
	}
	return action, nil
}

var _ RecommendedActionCalculator = RecommendedActionCalculatorV6{}

func supplementaryActionsForPremierTSP(
	ctx context.Context,
	action appetite_factor.RecommendedAction,
	hz HazardZone,
) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction) {
	newAction := evaluateHazardZone(action, hz)
	if action != newAction {
		log.Info(ctx, "Hazard Zone evaluation changed the recommended action for Premier TSP",
			log.String("newAction", newAction.String()),
			log.String("oldAction", action.String()))
		return newAction,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
			pointer_utils.ToPointer(action)
	}
	// action remains the same
	return action, nil, nil
}

// supplementaryActionsForNonPremierTSP evaluates the recommended action based on hazard zone and number of PUs
// for Non-Premier TSPs. Order of evaluation: Fleet Size evaluation -> Hazard Zone evaluation
func supplementaryActionsForNonPremierTSP(
	ctx context.Context,
	action appetite_factor.RecommendedAction,
	hz HazardZone,
	puCount null.Int32,
) (appetite_factor.RecommendedAction, *appetite_factor.RecommendedActionReason, *appetite_factor.RecommendedAction) {
	newAction := evaluateFleetSize(action, puCount)
	if action != newAction {
		// If the action is changed by hazard zone evaluation, return the new action
		log.Info(ctx, "Fleet size evaluation changed the recommended action for Non-Premier TSP",
			log.String("newAction", newAction.String()),
			log.String("oldAction", action.String()))
		return newAction,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
			pointer_utils.ToPointer(action)
	}
	newAction = evaluateHazardZone(action, hz)
	if action != newAction {
		log.Info(ctx, "Hazard Zone evaluation changed the recommended action for Non-Premier TSP",
			log.String("newAction", newAction.String()),
			log.String("oldAction", action.String()))
		return newAction,
			pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
			pointer_utils.ToPointer(action)
	}
	// action remains the same
	return action, nil, nil
}

func evaluateHazardZone(
	action appetite_factor.RecommendedAction,
	hz HazardZone,
) appetite_factor.RecommendedAction {
	if hz.DistancePercentage == nil || hz.DurationPercentage == nil {
		return action
	}
	// if the action is not Quote or Strongly Quote or Neutral, return the action as is
	if action != appetite_factor.RecommendedActionStronglyQuote && action != appetite_factor.RecommendedActionQuote &&
		action != appetite_factor.RecommendedActionNeutral {
		return action
	}
	switch {
	case *hz.DistancePercentage >= 25 || *hz.DurationPercentage >= 75:
		return appetite_factor.RecommendedActionDecline
	case (*hz.DistancePercentage > 10 && *hz.DistancePercentage < 25) ||
		(*hz.DurationPercentage > 25 && *hz.DurationPercentage < 75):
		return appetite_factor.RecommendedActionNeutral
	default:
		return action
	}
}

func evaluateFleetSize(
	action appetite_factor.RecommendedAction,
	puCount null.Int32,
) appetite_factor.RecommendedAction {
	// if the action is not Quote or Strongly Quote, return the action as is
	if (action != appetite_factor.RecommendedActionStronglyQuote && action != appetite_factor.RecommendedActionQuote) || !puCount.Valid {
		return action
	}
	switch {
	case puCount.Int32 >= 50 && puCount.Int32 <= 100:
		return appetite_factor.RecommendedActionNeutral
	default:
		return action
	}
}

func getDeclineRecommendedActionReason(
	input AppetiteGuidelinesInput,
) (
	appetite_factor.RecommendedAction,
	appetite_factor.RecommendedActionReason,
	error,
) {
	if input.RuleName == nil {
		return invalidAction, invalidReason, errors.New("rule name is required for decline")
	}

	rule := *input.RuleName
	variant := input.Variant

	type handlerFunc func(*models.Variant) (appetite_factor.RecommendedActionReason, error)

	handlers := map[models.Rule]handlerFunc{
		models.RuleYearsInBusiness: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonYearsInBusinessLessThanTwoYears, nil
		},
		models.RuleDotRating: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonRecentConditionalDOTRating, nil
		},
		models.RuleLossesBurnRate: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonLossesBurnRateGreaterThan20K, nil
		},
		models.RuleTelematicsRiskScore: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonTrsMarketCategoryIsDecline, nil
		},
		models.RuleUnsupportedTSP: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonUnsupportedTSP, nil
		},
		models.RuleDriverTurnover: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold, nil
		},
		models.RuleUtilization: func(v *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			if v == nil {
				return invalidReason, errors.New("variant is required for utilization rule")
			}
			switch *v {
			case models.VariantHalfYearly:
				return appetite_factor.RecommendedActionReasonHalfYearlyUtilizationIsGreaterThanThreshold, nil
			case models.VariantQuarterly:
				return appetite_factor.RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold, nil
			default:
				return invalidReason, errors.Newf("unhandled utilization variant: %s", v.String())
			}
		},
		models.RuleFleetSize: func(v *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			if v == nil {
				return invalidReason, errors.New("variant is required for fleet size rule")
			}
			switch *v {
			case models.VariantDriver:
				return appetite_factor.RecommendedActionReasonDriversCountIsLessThanThreshold, nil
			case models.VariantVehicle:
				return appetite_factor.RecommendedActionReasonVehiclesCountIsLessThanThreshold, nil
			default:
				return invalidReason, errors.Newf("unhandled fleet size variant: %s", v.String())
			}
		},
		models.RuleHazardZones: func(v *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			if v == nil {
				return invalidReason, errors.New("variant is required for hazard zones rule")
			}
			switch *v {
			case models.VariantDistancePercentage:
				return appetite_factor.RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold, nil
			case models.VariantDurationPercentage:
				return appetite_factor.RecommendedActionReasonHazardZoneDurationPercentageIsGreaterThanThreshold, nil
			case models.VariantDistancePercentageNJ:
				return appetite_factor.RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold, nil
			default:
				return invalidReason, errors.Newf("unhandled hazard zone variant: %s", v.String())
			}
		},
		models.RuleVinVisibility: func(_ *models.Variant) (appetite_factor.RecommendedActionReason, error) {
			return appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold, nil
		},
	}

	handler, ok := handlers[rule]
	if !ok {
		return invalidAction, invalidReason, errors.Newf("rule %s is not handled", rule.String())
	}

	reason, err := handler(variant)
	if err != nil {
		return invalidAction, invalidReason, err
	}

	return appetite_factor.RecommendedActionDecline, reason, nil
}
