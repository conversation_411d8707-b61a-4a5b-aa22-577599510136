package recommended_action_rubric

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nirvanatech.com/nirvana/underwriting/appetite_guidelines/models"

	"github.com/volatiletech/null/v8"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/underwriting/rule-engine/appetite_factors/appetite_factor"
)

func TestCalculateRecommendedActionV1(t *testing.T) {
	reviewId := uuid.New().String()
	tests := []struct {
		name           string
		appetiteScore  appetite_factor.AppetiteScore
		marketCategory string
		isException    bool
		puCount        null.Int32
		hazardZone     HazardZone
		want           appetite_factor.RecommendedAction
	}{
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Acceptable",
			isException:    false,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Acceptable",
			isException:    false,
			want:           appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			want:           appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Extended",
			isException:    false,
			want:           appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Decline",
			isException:    false,
			want:           appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    true,
			want:           appetite_factor.RecommendedActionDecline,
		},
	}

	calc := RecommendedActionCalculatorV1{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, _, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, &tt.marketCategory, tt.isException, nil, false, nil,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewId, tt.hazardZone, tt.puCount, false,
					false,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV1() error = %v", err)
				return
			}
			if got != tt.want {
				t.Errorf("CalculateRecommendedActionV1() = %v, wantAction %v", got, tt.want)
			}
		})
	}
}

func TestCalculateRecommendedActionV2(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name           string
		appetiteScore  appetite_factor.AppetiteScore
		marketCategory string
		isException    bool
		puCount        null.Int32
		hazardZone     HazardZone
		want           appetite_factor.RecommendedAction
	}{
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    false,
			want:           appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Acceptable",
			isException:    false,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Acceptable",
			isException:    false,
			want:           appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Extended",
			isException:    false,
			want:           appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Decline",
			isException:    false,
			want:           appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			want:           appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    true,
			want:           appetite_factor.RecommendedActionDecline,
		},
	}

	calc := RecommendedActionCalculatorV2{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _, _, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, &tt.marketCategory, tt.isException, nil, false, nil,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount, false,
					false,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV2() error = %v", err)
				return
			}
			if got != tt.want {
				t.Errorf("CalculateRecommendedActionV2() = %v, wantAction %v", got, tt.want)
			}
		})
	}
}

func TestCalculateRecommendedActionV3(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name               string
		appetiteScore      appetite_factor.AppetiteScore
		marketCategory     string
		isException        bool
		tspKind            *telematics.TSP
		wantAction         appetite_factor.RecommendedAction
		isCDotRatingRecent bool
		isShortHaul        *bool
		puCount            null.Int32
		hazardZone         HazardZone
		wantReason         *appetite_factor.RecommendedActionReason
	}{
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Acceptable",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Acceptable",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Decline",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "High Risk Score, Any Market, Exception (not in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPBlueInkTechnology),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:               "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     "Target",
			isException:        false,
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:               "High Risk Score, Any Market, Exception (in the allowed list), Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScoreHighRisk,
			marketCategory:     "Target",
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Acceptable Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "High Risk Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
	}

	calc := RecommendedActionCalculatorV3{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, reason, _, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, &tt.marketCategory, tt.isException, tt.tspKind, tt.isCDotRatingRecent, tt.isShortHaul,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount, false,
					false,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV3() error = %v", err)
				return
			}
			if got != tt.wantAction {
				t.Errorf("CalculateRecommendedActionV3() = %v, wantAction %v", got, tt.wantAction)
			}
			if tt.wantReason != nil {
				assert.NotNil(t, reason)
				assert.Equal(t, *tt.wantReason, *reason,
					fmt.Sprintf("CalculateRecommendedActionV3() = %v, wantReason %v", reason, tt.wantReason))
			}
		})
	}
}

func TestCalculateRecommendedActionV4(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name               string
		appetiteScore      appetite_factor.AppetiteScore
		marketCategory     string
		isException        bool
		tspKind            *telematics.TSP
		wantAction         appetite_factor.RecommendedAction
		wantSupposedAction *appetite_factor.RecommendedAction
		isCDotRatingRecent bool
		isShortHaul        *bool
		puCount            null.Int32
		hazardZone         HazardZone
		wantReason         *appetite_factor.RecommendedActionReason
	}{
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Acceptable",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Acceptable",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Decline",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "High Risk Score, Any Market, Exception (not in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPBlueInkTechnology),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:               "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     "Target",
			isException:        false,
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:               "High Risk Score, Any Market, Exception (in the allowed list), Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScoreHighRisk,
			marketCategory:     "Target",
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Acceptable Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "High Risk Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionStronglyQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Extended",
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(4)),
				DurationPercentage: pointer_utils.ToPointer(float32(4)),
			},
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantAction:         appetite_factor.RecommendedActionDecline,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(1)),
				DurationPercentage: pointer_utils.ToPointer(float32(1)),
			},
			wantAction: appetite_factor.RecommendedActionQuote,
		},
		{
			name:               "Preferred Score, Target Market, Exception (in the allowed list), moderate fleet",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     "Target",
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:            null.Int32From(50),
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), large fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(101),
			wantAction:     appetite_factor.RecommendedActionDecline,
			wantReason:     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), small fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: "Target",
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(49),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
	}

	calc := RecommendedActionCalculatorV4{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action, reason, supposedAction, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, &tt.marketCategory, tt.isException, tt.tspKind, tt.isCDotRatingRecent, tt.isShortHaul,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount, false,
					false,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV4() error = %v", err)
				return
			}
			if action != tt.wantAction {
				t.Errorf("CalculateRecommendedActionV4() = %v, wantAction %v", action, tt.wantAction)
			}
			if tt.wantSupposedAction != nil {
				assert.NotNil(t, supposedAction)
				assert.Equal(t, *tt.wantSupposedAction, *supposedAction,
					fmt.Sprintf("CalculateRecommendedActionV4() = %v, wantSupposedAction %v", supposedAction, tt.wantSupposedAction))
			} else {
				assert.Nil(t, supposedAction)
			}
			if tt.wantReason != nil {
				assert.NotNil(t, reason)
				assert.Equal(t, *tt.wantReason, *reason,
					fmt.Sprintf("CalculateRecommendedActionV4() = %v, wantReason %v", reason, tt.wantReason))
			} else {
				assert.Nil(t, reason)
			}
		})
	}
}

func TestCalculateRecommendedActionV5(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name                       string
		appetiteScore              appetite_factor.AppetiteScore
		marketCategory             *string
		isException                bool
		tspKind                    *telematics.TSP
		isCDotRatingRecent         bool
		isShortHaul                *bool
		puCount                    null.Int32
		hazardZone                 HazardZone
		isRiskScoreTrendNotPresent bool
		wantReason                 *appetite_factor.RecommendedActionReason
		wantAction                 appetite_factor.RecommendedAction
		wantSupposedAction         *appetite_factor.RecommendedAction
	}{
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Decline"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "High Risk Score, Any Market, Exception (not in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPBlueInkTechnology),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:               "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        false,
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:               "High Risk Score, Any Market, Exception (in the allowed list), Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScoreHighRisk,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Acceptable Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "High Risk Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionStronglyQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(4)),
				DurationPercentage: pointer_utils.ToPointer(float32(4)),
			},
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantAction:         appetite_factor.RecommendedActionDecline,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(1)),
				DurationPercentage: pointer_utils.ToPointer(float32(1)),
			},
			wantAction: appetite_factor.RecommendedActionQuote,
		},
		{
			name:               "Preferred Score, Target Market, Exception (in the allowed list), moderate fleet",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:            null.Int32From(50),
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), large fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(101),
			wantAction:     appetite_factor.RecommendedActionDecline,
			wantReason:     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), small fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(49),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:                       "Preferred Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScorePreferred,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionQuote,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
		{
			name:                       "Marginal Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScoreMarginal,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionDecline,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
	}

	calc := RecommendedActionCalculatorV5{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action, reason, supposedAction, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, tt.marketCategory, tt.isException, tt.tspKind, tt.isCDotRatingRecent, tt.isShortHaul,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount,
					tt.isRiskScoreTrendNotPresent, false,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV5() error = %v", err)
				return
			}
			if action != tt.wantAction {
				t.Errorf("CalculateRecommendedActionV5() = %v, wantAction %v", action, tt.wantAction)
			}
			if tt.wantSupposedAction != nil {
				assert.NotNil(t, supposedAction)
				assert.Equal(t, *tt.wantSupposedAction, *supposedAction,
					fmt.Sprintf("CalculateRecommendedActionV5() = %v, wantSupposedAction %v", supposedAction, tt.wantSupposedAction))
			} else {
				assert.Nil(t, supposedAction)
			}
			if tt.wantReason != nil {
				assert.NotNil(t, reason)
				assert.Equal(t, *tt.wantReason, *reason,
					fmt.Sprintf("CalculateRecommendedActionV5() = %v, wantReason %v", reason, tt.wantReason))
			} else {
				assert.Nil(t, reason)
			}
		})
	}
}

func TestCalculateRecommendedActionV6(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name                       string
		appetiteScore              appetite_factor.AppetiteScore
		marketCategory             *string
		isException                bool
		tspKind                    *telematics.TSP
		isCDotRatingRecent         bool
		isShortHaul                *bool
		puCount                    null.Int32
		hazardZone                 HazardZone
		isRiskScoreTrendNotPresent bool
		wantReason                 *appetite_factor.RecommendedActionReason
		wantAction                 appetite_factor.RecommendedAction
		wantSupposedAction         *appetite_factor.RecommendedAction
	}{
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Acceptable Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Decline"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "High Risk Score, Any Market, Exception (not in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPBlueInkTechnology),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:               "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        false,
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:               "High Risk Score, Any Market, Exception (in the allowed list), Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScoreHighRisk,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Acceptable Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "High Risk Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionStronglyQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(4)),
				DurationPercentage: pointer_utils.ToPointer(float32(4)),
			},
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantAction:         appetite_factor.RecommendedActionDecline,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(1)),
				DurationPercentage: pointer_utils.ToPointer(float32(1)),
			},
			wantAction: appetite_factor.RecommendedActionQuote,
		},
		{
			name:               "Preferred Score, Target Market, Exception (in the allowed list), moderate fleet",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:            null.Int32From(50),
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), large fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(101),
			wantAction:     appetite_factor.RecommendedActionDecline,
			wantReason:     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), small fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(49),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:                       "Preferred Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScorePreferred,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionQuote,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
		{
			name:                       "Marginal Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScoreMarginal,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionDecline,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
	}

	calc := RecommendedActionCalculatorV6{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action, reason, supposedAction, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, tt.marketCategory, tt.isException, tt.tspKind, tt.isCDotRatingRecent, tt.isShortHaul,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount,
					tt.isRiskScoreTrendNotPresent, false,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV6() error = %v", err)
				return
			}
			if action != tt.wantAction {
				t.Errorf("CalculateRecommendedActionV6() = %v, wantAction %v", action, tt.wantAction)
			}
			if tt.wantSupposedAction != nil {
				assert.NotNil(t, supposedAction)
				assert.Equal(t, *tt.wantSupposedAction, *supposedAction,
					fmt.Sprintf("CalculateRecommendedActionV6() = %v, wantSupposedAction %v", supposedAction, tt.wantSupposedAction))
			} else {
				assert.Nil(t, supposedAction)
			}
			if tt.wantReason != nil {
				assert.NotNil(t, reason)
				assert.Equal(t, *tt.wantReason, *reason,
					fmt.Sprintf("CalculateRecommendedActionV6() = %v, wantReason %v", reason, tt.wantReason))
			} else {
				assert.Nil(t, reason)
			}
		})
	}
}

func TestCalculateRecommendedActionV7(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name                           string
		appetiteScore                  appetite_factor.AppetiteScore
		marketCategory                 *string
		isException                    bool
		tspKind                        *telematics.TSP
		isCDotRatingRecent             bool
		isShortHaul                    *bool
		puCount                        null.Int32
		hazardZone                     HazardZone
		isRiskScoreTrendNotPresent     bool
		isLossesBurnRateGreaterThan20K bool
		wantReason                     *appetite_factor.RecommendedActionReason
		wantAction                     appetite_factor.RecommendedAction
		wantSupposedAction             *appetite_factor.RecommendedAction
	}{
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Acceptable Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Decline"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "High Risk Score, Any Market, Exception (not in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPBlueInkTechnology),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:               "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        false,
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:               "High Risk Score, Any Market, Exception (in the allowed list), Conditional Rating Recent",
			appetiteScore:      appetite_factor.AppetiteScoreHighRisk,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:         appetite_factor.RecommendedActionDecline,
			isCDotRatingRecent: true,
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:                           "Preferred Score, Target Market, No Exception, Losses Burn Rate Greater Than 20K",
			appetiteScore:                  appetite_factor.AppetiteScorePreferred,
			marketCategory:                 pointer_utils.ToPointer("Target"),
			isException:                    false,
			wantAction:                     appetite_factor.RecommendedActionDecline,
			isLossesBurnRateGreaterThan20K: true,
			wantReason:                     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonLossesBurnRateGreaterThan20K),
		},
		{
			name:                           "High Risk Score, Any Market, Exception (in the allowed list), Losses Burn Rate Greater Than 20K",
			appetiteScore:                  appetite_factor.AppetiteScoreHighRisk,
			marketCategory:                 pointer_utils.ToPointer("Target"),
			isException:                    true,
			tspKind:                        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:                     appetite_factor.RecommendedActionDecline,
			isLossesBurnRateGreaterThan20K: true,
			wantReason:                     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonLossesBurnRateGreaterThan20K),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Acceptable Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "High Risk Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionStronglyQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(4)),
				DurationPercentage: pointer_utils.ToPointer(float32(4)),
			},
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantAction:         appetite_factor.RecommendedActionDecline,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(1)),
				DurationPercentage: pointer_utils.ToPointer(float32(1)),
			},
			wantAction: appetite_factor.RecommendedActionQuote,
		},
		{
			name:               "Preferred Score, Target Market, Exception (in the allowed list), moderate fleet",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:            null.Int32From(50),
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), large fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(101),
			wantAction:     appetite_factor.RecommendedActionDecline,
			wantReason:     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), small fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(49),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:                       "Preferred Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScorePreferred,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionQuote,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
		{
			name:                       "Marginal Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScoreMarginal,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionDecline,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
	}

	calc := RecommendedActionCalculatorV7{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action, reason, supposedAction, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, tt.marketCategory, tt.isException, tt.tspKind, tt.isCDotRatingRecent, tt.isShortHaul,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount,
					tt.isRiskScoreTrendNotPresent, tt.isLossesBurnRateGreaterThan20K,
					AppetiteGuidelinesInput{},
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV7() error = %v", err)
				return
			}
			if action != tt.wantAction {
				t.Errorf("CalculateRecommendedActionV7() = %v, wantAction %v", action, tt.wantAction)
			}
			if tt.wantSupposedAction != nil {
				assert.NotNil(t, supposedAction)
				assert.Equal(t, *tt.wantSupposedAction, *supposedAction,
					fmt.Sprintf("CalculateRecommendedActionV7() = %v, wantSupposedAction %v", supposedAction, tt.wantSupposedAction))
			} else {
				assert.Nil(t, supposedAction)
			}
			if tt.wantReason != nil {
				assert.NotNil(t, reason)
				assert.Equal(t, *tt.wantReason, *reason,
					fmt.Sprintf("CalculateRecommendedActionV7() = %v, wantReason %v", reason, tt.wantReason))
			} else {
				assert.Nil(t, reason)
			}
		})
	}
}

func TestCalculateRecommendedActionV8(t *testing.T) {
	reviewID := uuid.New().String()
	tests := []struct {
		name                       string
		appetiteScore              appetite_factor.AppetiteScore
		marketCategory             *string
		isException                bool
		tspKind                    *telematics.TSP
		isShortHaul                *bool
		puCount                    null.Int32
		hazardZone                 HazardZone
		isRiskScoreTrendNotPresent bool
		appetiteGuidelinesInput    AppetiteGuidelinesInput
		wantReason                 *appetite_factor.RecommendedActionReason
		wantAction                 appetite_factor.RecommendedAction
		wantSupposedAction         *appetite_factor.RecommendedAction
	}{
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Acceptable Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Preferred Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Acceptable Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionStronglyQuote,
		},
		{
			name:           "Marginal Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreMarginal,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Target Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "Preferred Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Acceptable Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Acceptable"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
		},
		{
			name:           "Preferred Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Extended Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Decline Market, No Exception",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Decline"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:           "High Risk Score, Any Market, Exception (in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "High Risk Score, Any Market, Exception (not in the allowed list)",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPBlueInkTechnology),
			wantAction:     appetite_factor.RecommendedActionDecline,
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			appetiteGuidelinesInput: AppetiteGuidelinesInput{
				IsDecline: true,
				RuleName:  pointer_utils.ToPointer(models.RuleDotRating),
			},
			wantReason: pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonRecentConditionalDOTRating),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Conditional Rating Recent",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			appetiteGuidelinesInput: AppetiteGuidelinesInput{
				IsDecline: true,
				RuleName:  pointer_utils.ToPointer(models.RuleHazardZones),
				Variant:   pointer_utils.ToPointer(models.VariantDistancePercentage),
			},
			wantReason: pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHazardZoneDistancePercentageIsGreaterThanThreshold),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Acceptable Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreAcceptable,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "High Risk Score, Target Market, No Exception, Short Haul",
			appetiteScore:  appetite_factor.AppetiteScoreHighRisk,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			isShortHaul:    pointer_utils.ToPointer(true),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionDecline,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, No Exception, Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionNeutral,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionStronglyQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Extended Market, No Exception, Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Extended"),
			isException:    false,
			wantAction:     appetite_factor.RecommendedActionQuote,
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(4)),
				DurationPercentage: pointer_utils.ToPointer(float32(4)),
			},
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), High Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(5)),
				DurationPercentage: pointer_utils.ToPointer(float32(80)),
			},
			wantAction:         appetite_factor.RecommendedActionDecline,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(23)),
				DurationPercentage: pointer_utils.ToPointer(float32(35)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Medium Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(15)),
				DurationPercentage: pointer_utils.ToPointer(float32(15)),
			},
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonHighHazardZonesExposure),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), Low Hazard Zone Exposure",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			hazardZone: HazardZone{
				DistancePercentage: pointer_utils.ToPointer(float32(1)),
				DurationPercentage: pointer_utils.ToPointer(float32(1)),
			},
			wantAction: appetite_factor.RecommendedActionQuote,
		},
		{
			name:               "Preferred Score, Target Market, Exception (in the allowed list), moderate fleet",
			appetiteScore:      appetite_factor.AppetiteScorePreferred,
			marketCategory:     pointer_utils.ToPointer("Target"),
			isException:        true,
			tspKind:            pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:            null.Int32From(50),
			wantAction:         appetite_factor.RecommendedActionNeutral,
			wantSupposedAction: pointer_utils.ToPointer(appetite_factor.RecommendedActionQuote),
			wantReason:         pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonFiftyToHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), large fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(101),
			wantAction:     appetite_factor.RecommendedActionDecline,
			wantReason:     pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonGTHundredFleetSizeWithNonPremier),
		},
		{
			name:           "Preferred Score, Target Market, Exception (in the allowed list), small fleet",
			appetiteScore:  appetite_factor.AppetiteScorePreferred,
			marketCategory: pointer_utils.ToPointer("Target"),
			isException:    true,
			tspKind:        pointer_utils.ToPointer(telematics.TSPAgilisLinxup),
			puCount:        null.Int32From(49),
			wantAction:     appetite_factor.RecommendedActionQuote,
		},
		{
			name:                       "Preferred Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScorePreferred,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionQuote,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
		{
			name:                       "Marginal Score, Nil Market, Exception rubric outcome with a reason",
			appetiteScore:              appetite_factor.AppetiteScoreMarginal,
			marketCategory:             nil,
			isException:                false,
			tspKind:                    pointer_utils.ToPointer(telematics.TSPSamsara),
			isRiskScoreTrendNotPresent: true,
			wantAction:                 appetite_factor.RecommendedActionDecline,
			wantReason:                 pointer_utils.ToPointer(appetite_factor.RecommendedActionReasonSafetyScoreMarketCategoryUnavailable),
		},
	}

	calc := RecommendedActionCalculatorV8{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			action, reason, supposedAction, err := calc.CalculateRecommendedAction(
				context.TODO(),
				CalculateRecommendationActionRequest{
					tt.appetiteScore, tt.marketCategory, tt.isException, tt.tspKind, false, tt.isShortHaul,
					time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), reviewID, tt.hazardZone, tt.puCount,
					tt.isRiskScoreTrendNotPresent, false,
					tt.appetiteGuidelinesInput,
				})
			if err != nil {
				t.Errorf("CalculateRecommendedActionV8() error = %v", err)
				return
			}
			if action != tt.wantAction {
				t.Errorf("CalculateRecommendedActionV8() = %v, wantAction %v", action, tt.wantAction)
			}
			if tt.wantSupposedAction != nil {
				assert.NotNil(t, supposedAction)
				assert.Equal(t, *tt.wantSupposedAction, *supposedAction,
					fmt.Sprintf("CalculateRecommendedActionV8() = %v, wantSupposedAction %v", supposedAction, tt.wantSupposedAction))
			} else {
				assert.Nil(t, supposedAction)
			}
			if tt.wantReason != nil {
				assert.NotNil(t, reason)
				assert.Equal(t, *tt.wantReason, *reason,
					fmt.Sprintf("CalculateRecommendedActionV8() = %v, wantReason %v", reason, tt.wantReason))
			} else {
				assert.Nil(t, reason)
			}
		})
	}
}

func Test_getDeclineRecommendedActionReason(t *testing.T) {
	quarterly := models.VariantQuarterly
	driver := models.VariantDriver
	unsupported := models.VariantInvalid
	ruleUtil := models.RuleUtilization
	ruleFleet := models.RuleFleetSize
	ruleHazard := models.RuleHazardZones

	tests := []struct {
		name        string
		input       AppetiteGuidelinesInput
		wantAction  appetite_factor.RecommendedAction
		wantReason  appetite_factor.RecommendedActionReason
		wantErr     bool
		errContains string
	}{
		{
			name:        "Nil RuleName returns error",
			input:       AppetiteGuidelinesInput{},
			wantAction:  invalidAction,
			wantReason:  invalidReason,
			wantErr:     true,
			errContains: "rule name is required",
		},
		{
			name: "Years in Business",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleYearsInBusiness),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonYearsInBusinessLessThanTwoYears,
			wantErr:    false,
		},
		{
			name: "DOT Rating",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleDotRating),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonRecentConditionalDOTRating,
			wantErr:    false,
		},
		{
			name: "Losses Burn Rate",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleLossesBurnRate),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonLossesBurnRateGreaterThan20K,
			wantErr:    false,
		},
		{
			name: "Telematics Risk Score",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleTelematicsRiskScore),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonTrsMarketCategoryIsDecline,
			wantErr:    false,
		},
		{
			name: "Unsupported TSP",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleUnsupportedTSP),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonUnsupportedTSP,
			wantErr:    false,
		},
		{
			name: "Driver Turnover",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleDriverTurnover),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonDriverTurnoverIsGreaterThanThreshold,
			wantErr:    false,
		},
		{
			name: "Utilization - Quarterly",
			input: AppetiteGuidelinesInput{
				RuleName: &ruleUtil,
				Variant:  &quarterly,
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonQuarterlyUtilizationIsGreaterThanThreshold,
			wantErr:    false,
		},
		{
			name: "Utilization - Nil Variant",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(ruleUtil),
			},
			wantAction:  invalidAction,
			wantReason:  invalidReason,
			wantErr:     true,
			errContains: "variant is required for utilization",
		},
		{
			name: "Utilization - Unsupported Variant",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(ruleUtil),
				Variant:  &unsupported,
			},
			wantAction:  invalidAction,
			wantReason:  invalidReason,
			wantErr:     true,
			errContains: "unhandled utilization variant",
		},
		{
			name: "Fleet Size - Driver",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(ruleFleet),
				Variant:  &driver,
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonDriversCountIsLessThanThreshold,
			wantErr:    false,
		},
		{
			name: "Fleet Size - Nil Variant",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(ruleFleet),
			},
			wantAction:  invalidAction,
			wantReason:  invalidReason,
			wantErr:     true,
			errContains: "variant is required for fleet size",
		},
		{
			name: "Hazard Zone - DistancePercentageNJ",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(ruleHazard),
				Variant:  pointer_utils.ToPointer(models.VariantDistancePercentageNJ),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonHazardZoneDistancePercentageNJIsGreaterThanThreshold,
			wantErr:    false,
		},
		{
			name: "Hazard Zone - Nil Variant",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(ruleHazard),
			},
			wantAction:  invalidAction,
			wantReason:  invalidReason,
			wantErr:     true,
			errContains: "variant is required for hazard zones",
		},
		{
			name: "Unhandled Rule",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleInvalid),
			},
			wantAction:  invalidAction,
			wantReason:  invalidReason,
			wantErr:     true,
			errContains: "is not handled",
		},
		{
			name: "Vin Visibility",
			input: AppetiteGuidelinesInput{
				RuleName: pointer_utils.ToPointer(models.RuleVinVisibility),
			},
			wantAction: appetite_factor.RecommendedActionDecline,
			wantReason: appetite_factor.RecommendedActionReasonVinVisibilityLessThanThreshold,
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotAction, gotReason, err := getDeclineRecommendedActionReason(tt.input)

			assert.Equal(t, tt.wantAction, gotAction)
			assert.Equal(t, tt.wantReason, gotReason)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errContains)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
