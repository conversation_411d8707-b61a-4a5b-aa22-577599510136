// Code generated by "enumer -type=Feature -trimprefix=Feature -transform=kebab"; DO NOT EDIT.

package feature_flag_lib

import (
	"fmt"
	"strings"
)

const _FeatureName = "state-tntelematics-safety-report-linkstate-motelematics-emailerstate-ksstate-nestate-iastate-txtelematics-validation-errorscoverage-motor-truck-cargostate-pastate-alstate-scstate-inlarge-loss-proxystate-ncfleet-typestate-okstate-ohdemo-appstelematics-statusstate-gastate-mntelematics-consent-reminder-emailforms-destinationstate-witelematics-consent-reminder-emails-pre-threshold-date-intervaltelematics-consent-reminder-emails-post-threshold-date-intervaldata-availability-evaluation-taskdata-availability-threshold-percentagesubmission-acknowledgement-emailtelematics-connection-successful-emailstate-mipost-bind-onboarding-emailindication-pricing-experimenttelematics-consent-trucker-clouddriver-safety-scorevehicles-service-vin-decodertelematics-consent-flowclose-reasons-modalstate-azstate-coapp-review-persistenceindication-pricing-experiment-quartile-discountindication-pricing-experiment-state-discountnew-quote-pdfstate-utapp-review-persistence-widgetsstate-nmstate-nvuse-pdfcpustate-wastate-orpdfcpu-cutoffcamera-programauthority-controlfronter-xendorsement-review-automated-pricingstate-kytelematics-connection-email-on-agent-behalfvalidate-duplicate-applicationsappetite-scorestate-capibit-trial-runnative-salesforce-integrationmvr-overridesfs-forms-compilation-enablednf-state-tnnf-state-panf-state-ganf-state-ncfleet-cw-modelnf-state-scfs-forms-compilation-shadow-enabledtelematics-sign-up-screennf-scrape-pgrnotify-faulty-tsp-connectionsafety-inviteselect-safety-score-on-uw-appzero-deposituw-target-price-check-on-actionuw-clearance-check-on-approveviolation-notification-job-emailsln-credit-scorenf-new-model-releasenf-endorsement-opportunityrecommended-action-authoritypreferred-underwriter-assignmentreview-readiness-task-assignment-logicreview-readiness-task-assigneereview-readiness-rollout-overridereview-readiness-rollout-appetite-scoremagic-link-violation-emailnfib-migration-statereview-readiness-enabled-taskspricing-exp-low-vin-visibilityapplication-type-accessmst-referralreview-readiness-rollout-agencyapp-review-list-non-terminal-state-filteringprefill-and-auto-review-vin-problemsdriver-yoe-from-agent-inputnf-state-txclaim-update-summary-emailrecommended-action-version-six-launchreview-readiness-jira-creationnew-endorsement-review-sfdc-integrationstate-arshow-recommendation-range-pricingpricing-range-v2nf-endorsement-files-through-ib-for-claimsuse-llm-ops-for-finolause-auto-submit-for-finolaauto-submit-to-snapsheetnf-safety-discountfs-form-fill-enablednf-async-uw-assignmentnf-state-winf-state-iarollout-finola-two-promptsrollout-finola-snapsheetnf-state-aznf-state-nvconsent-reminder-to-insuredrisk-iq-rolloutnf-safety-score-auto-selectiontelematics-connection-email-v2agent-dashboard-goalstelematics-templatefs-form-fill-migrated-formsappetite-score-v7risk-factor-pricing-v2m1-score-integrationnf-telematics-templateappetite-score-v8pre-quote-telematics-v1-experimenteld-provider-experiment-v1auto-generate-uw-notespibit-loss-run-parsingbusiness-auto-producer-allowlistsnapsheet-claimsinsurance-bundle-fleet-migration-phase2nf-auto-app-submissionchange-form-migrationexpress-lane-v1"

var _FeatureIndex = [...]uint16{0, 8, 37, 45, 63, 71, 79, 87, 95, 123, 149, 157, 165, 173, 181, 197, 205, 215, 223, 231, 240, 257, 265, 273, 306, 323, 331, 393, 456, 489, 527, 559, 597, 605, 631, 660, 692, 711, 739, 762, 781, 789, 797, 819, 866, 910, 923, 931, 961, 969, 977, 987, 995, 1003, 1016, 1030, 1047, 1056, 1092, 1100, 1143, 1174, 1188, 1196, 1211, 1240, 1253, 1281, 1292, 1303, 1314, 1325, 1339, 1350, 1385, 1410, 1423, 1451, 1464, 1493, 1505, 1536, 1565, 1598, 1613, 1633, 1659, 1687, 1719, 1757, 1787, 1820, 1859, 1885, 1905, 1935, 1965, 1988, 2000, 2031, 2075, 2111, 2138, 2149, 2175, 2212, 2242, 2281, 2289, 2322, 2338, 2380, 2402, 2428, 2452, 2470, 2490, 2512, 2523, 2534, 2560, 2584, 2595, 2606, 2633, 2648, 2678, 2708, 2729, 2748, 2775, 2792, 2814, 2834, 2856, 2873, 2907, 2933, 2955, 2977, 3009, 3025, 3064, 3086, 3107, 3122}

const _FeatureLowerName = "state-tntelematics-safety-report-linkstate-motelematics-emailerstate-ksstate-nestate-iastate-txtelematics-validation-errorscoverage-motor-truck-cargostate-pastate-alstate-scstate-inlarge-loss-proxystate-ncfleet-typestate-okstate-ohdemo-appstelematics-statusstate-gastate-mntelematics-consent-reminder-emailforms-destinationstate-witelematics-consent-reminder-emails-pre-threshold-date-intervaltelematics-consent-reminder-emails-post-threshold-date-intervaldata-availability-evaluation-taskdata-availability-threshold-percentagesubmission-acknowledgement-emailtelematics-connection-successful-emailstate-mipost-bind-onboarding-emailindication-pricing-experimenttelematics-consent-trucker-clouddriver-safety-scorevehicles-service-vin-decodertelematics-consent-flowclose-reasons-modalstate-azstate-coapp-review-persistenceindication-pricing-experiment-quartile-discountindication-pricing-experiment-state-discountnew-quote-pdfstate-utapp-review-persistence-widgetsstate-nmstate-nvuse-pdfcpustate-wastate-orpdfcpu-cutoffcamera-programauthority-controlfronter-xendorsement-review-automated-pricingstate-kytelematics-connection-email-on-agent-behalfvalidate-duplicate-applicationsappetite-scorestate-capibit-trial-runnative-salesforce-integrationmvr-overridesfs-forms-compilation-enablednf-state-tnnf-state-panf-state-ganf-state-ncfleet-cw-modelnf-state-scfs-forms-compilation-shadow-enabledtelematics-sign-up-screennf-scrape-pgrnotify-faulty-tsp-connectionsafety-inviteselect-safety-score-on-uw-appzero-deposituw-target-price-check-on-actionuw-clearance-check-on-approveviolation-notification-job-emailsln-credit-scorenf-new-model-releasenf-endorsement-opportunityrecommended-action-authoritypreferred-underwriter-assignmentreview-readiness-task-assignment-logicreview-readiness-task-assigneereview-readiness-rollout-overridereview-readiness-rollout-appetite-scoremagic-link-violation-emailnfib-migration-statereview-readiness-enabled-taskspricing-exp-low-vin-visibilityapplication-type-accessmst-referralreview-readiness-rollout-agencyapp-review-list-non-terminal-state-filteringprefill-and-auto-review-vin-problemsdriver-yoe-from-agent-inputnf-state-txclaim-update-summary-emailrecommended-action-version-six-launchreview-readiness-jira-creationnew-endorsement-review-sfdc-integrationstate-arshow-recommendation-range-pricingpricing-range-v2nf-endorsement-files-through-ib-for-claimsuse-llm-ops-for-finolause-auto-submit-for-finolaauto-submit-to-snapsheetnf-safety-discountfs-form-fill-enablednf-async-uw-assignmentnf-state-winf-state-iarollout-finola-two-promptsrollout-finola-snapsheetnf-state-aznf-state-nvconsent-reminder-to-insuredrisk-iq-rolloutnf-safety-score-auto-selectiontelematics-connection-email-v2agent-dashboard-goalstelematics-templatefs-form-fill-migrated-formsappetite-score-v7risk-factor-pricing-v2m1-score-integrationnf-telematics-templateappetite-score-v8pre-quote-telematics-v1-experimenteld-provider-experiment-v1auto-generate-uw-notespibit-loss-run-parsingbusiness-auto-producer-allowlistsnapsheet-claimsinsurance-bundle-fleet-migration-phase2nf-auto-app-submissionchange-form-migrationexpress-lane-v1"

func (i Feature) String() string {
	if i < 0 || i >= Feature(len(_FeatureIndex)-1) {
		return fmt.Sprintf("Feature(%d)", i)
	}
	return _FeatureName[_FeatureIndex[i]:_FeatureIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _FeatureNoOp() {
	var x [1]struct{}
	_ = x[FeatureStateTN-(0)]
	_ = x[FeatureTelematicsSafetyReportLink-(1)]
	_ = x[FeatureStateMO-(2)]
	_ = x[FeatureTelematicsEmailer-(3)]
	_ = x[FeatureStateKS-(4)]
	_ = x[FeatureStateNE-(5)]
	_ = x[FeatureStateIA-(6)]
	_ = x[FeatureStateTX-(7)]
	_ = x[FeatureTelematicsValidationErrors-(8)]
	_ = x[FeatureCoverageMotorTruckCargo-(9)]
	_ = x[FeatureStatePA-(10)]
	_ = x[FeatureStateAL-(11)]
	_ = x[FeatureStateSC-(12)]
	_ = x[FeatureStateIN-(13)]
	_ = x[FeatureLargeLossProxy-(14)]
	_ = x[FeatureStateNC-(15)]
	_ = x[FeatureFleetType-(16)]
	_ = x[FeatureStateOK-(17)]
	_ = x[FeatureStateOH-(18)]
	_ = x[FeatureDemoApps-(19)]
	_ = x[FeatureTelematicsStatus-(20)]
	_ = x[FeatureStateGA-(21)]
	_ = x[FeatureStateMN-(22)]
	_ = x[FeatureTelematicsConsentReminderEmail-(23)]
	_ = x[FeatureFormsDestination-(24)]
	_ = x[FeatureStateWI-(25)]
	_ = x[FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval-(26)]
	_ = x[FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval-(27)]
	_ = x[FeatureDataAvailabilityEvaluationTask-(28)]
	_ = x[FeatureDataAvailabilityThresholdPercentage-(29)]
	_ = x[FeatureSubmissionAcknowledgementEmail-(30)]
	_ = x[FeatureTelematicsConnectionSuccessfulEmail-(31)]
	_ = x[FeatureStateMI-(32)]
	_ = x[FeaturePostBindOnboardingEmail-(33)]
	_ = x[FeatureIndicationPricingExperiment-(34)]
	_ = x[FeatureTelematicsConsentTruckerCloud-(35)]
	_ = x[FeatureDriverSafetyScore-(36)]
	_ = x[FeatureVehiclesServiceVINDecoder-(37)]
	_ = x[FeatureTelematicsConsentFlow-(38)]
	_ = x[FeatureCloseReasonsModal-(39)]
	_ = x[FeatureStateAZ-(40)]
	_ = x[FeatureStateCO-(41)]
	_ = x[FeatureAppReviewPersistence-(42)]
	_ = x[FeatureIndicationPricingExperimentQuartileDiscount-(43)]
	_ = x[FeatureIndicationPricingExperimentStateDiscount-(44)]
	_ = x[FeatureNewQuotePDF-(45)]
	_ = x[FeatureStateUT-(46)]
	_ = x[FeatureAppReviewPersistenceWidgets-(47)]
	_ = x[FeatureStateNM-(48)]
	_ = x[FeatureStateNV-(49)]
	_ = x[FeatureUsePdfcpu-(50)]
	_ = x[FeatureStateWA-(51)]
	_ = x[FeatureStateOR-(52)]
	_ = x[FeaturePdfcpuCutoff-(53)]
	_ = x[FeatureCameraProgram-(54)]
	_ = x[FeatureAuthorityControl-(55)]
	_ = x[FeatureFronterX-(56)]
	_ = x[FeatureEndorsementReviewAutomatedPricing-(57)]
	_ = x[FeatureStateKY-(58)]
	_ = x[FeatureTelematicsConnectionEmailOnAgentBehalf-(59)]
	_ = x[FeatureValidateDuplicateApplications-(60)]
	_ = x[FeatureAppetiteScore-(61)]
	_ = x[FeatureStateCA-(62)]
	_ = x[FeaturePibitTrialRun-(63)]
	_ = x[FeatureNativeSalesforceIntegration-(64)]
	_ = x[FeatureMVROverrides-(65)]
	_ = x[FeatureFSFormsCompilationEnabled-(66)]
	_ = x[FeatureNFStateTN-(67)]
	_ = x[FeatureNFStatePA-(68)]
	_ = x[FeatureNFStateGA-(69)]
	_ = x[FeatureNFStateNC-(70)]
	_ = x[FeatureFleetCWModel-(71)]
	_ = x[FeatureNFStateSC-(72)]
	_ = x[FeatureFSFormsCompilationShadowEnabled-(73)]
	_ = x[FeatureTelematicsSignUpScreen-(74)]
	_ = x[FeatureNFScrapePGR-(75)]
	_ = x[FeatureNotifyFaultyTSPConnection-(76)]
	_ = x[FeatureSafetyInvite-(77)]
	_ = x[FeatureSelectSafetyScoreOnUwApp-(78)]
	_ = x[FeatureZeroDeposit-(79)]
	_ = x[FeatureUwTargetPriceCheckOnAction-(80)]
	_ = x[FeatureUwClearanceCheckOnApprove-(81)]
	_ = x[FeatureViolationNotificationJobEmails-(82)]
	_ = x[FeatureLNCreditScore-(83)]
	_ = x[FeatureNFNewModelRelease-(84)]
	_ = x[FeatureNFEndorsementOpportunity-(85)]
	_ = x[FeatureRecommendedActionAuthority-(86)]
	_ = x[FeaturePreferredUnderwriterAssignment-(87)]
	_ = x[FeatureReviewReadinessTaskAssignmentLogic-(88)]
	_ = x[FeatureReviewReadinessTaskAssignee-(89)]
	_ = x[FeatureReviewReadinessRolloutOverride-(90)]
	_ = x[FeatureReviewReadinessRolloutAppetiteScore-(91)]
	_ = x[FeatureMagicLinkViolationEmail-(92)]
	_ = x[FeatureNFIBMigrationState-(93)]
	_ = x[FeatureReviewReadinessEnabledTasks-(94)]
	_ = x[FeaturePricingExpLowVinVisibility-(95)]
	_ = x[FeatureApplicationTypeAccess-(96)]
	_ = x[FeatureMstReferral-(97)]
	_ = x[FeatureReviewReadinessRolloutAgency-(98)]
	_ = x[FeatureAppReviewListNonTerminalStateFiltering-(99)]
	_ = x[FeaturePrefillAndAutoReviewVinProblems-(100)]
	_ = x[FeatureDriverYoeFromAgentInput-(101)]
	_ = x[FeatureNFStateTX-(102)]
	_ = x[FeatureClaimUpdateSummaryEmail-(103)]
	_ = x[FeatureRecommendedActionVersionSixLaunch-(104)]
	_ = x[FeatureReviewReadinessJiraCreation-(105)]
	_ = x[FeatureNewEndorsementReviewSFDCIntegration-(106)]
	_ = x[FeatureStateAR-(107)]
	_ = x[FeatureShowRecommendationRangePricing-(108)]
	_ = x[FeaturePricingRangeV2-(109)]
	_ = x[FeatureNfEndorsementFilesThroughIbForClaims-(110)]
	_ = x[FeatureUseLLMOpsForFinola-(111)]
	_ = x[FeatureUseAutoSubmitForFinola-(112)]
	_ = x[FeatureAutoSubmitToSnapsheet-(113)]
	_ = x[FeatureNFSafetyDiscount-(114)]
	_ = x[FeatureFSFormFillEnabled-(115)]
	_ = x[FeatureNFAsyncUWAssignment-(116)]
	_ = x[FeatureNFStateWI-(117)]
	_ = x[FeatureNFStateIA-(118)]
	_ = x[FeatureRolloutFinolaTwoPrompts-(119)]
	_ = x[FeatureRolloutFinolaSnapsheet-(120)]
	_ = x[FeatureNFStateAZ-(121)]
	_ = x[FeatureNFStateNV-(122)]
	_ = x[FeatureConsentReminderToInsured-(123)]
	_ = x[FeatureRiskIqRollout-(124)]
	_ = x[FeatureNFSafetyScoreAutoSelection-(125)]
	_ = x[FeatureTelematicsConnectionEmailV2-(126)]
	_ = x[FeatureAgentDashboardGoals-(127)]
	_ = x[FeatureTelematicsTemplate-(128)]
	_ = x[FeatureFSFormFillMigratedForms-(129)]
	_ = x[FeatureAppetiteScoreV7-(130)]
	_ = x[FeatureRiskFactorPricingV2-(131)]
	_ = x[FeatureM1ScoreIntegration-(132)]
	_ = x[FeatureNFTelematicsTemplate-(133)]
	_ = x[FeatureAppetiteScoreV8-(134)]
	_ = x[FeaturePreQuoteTelematicsV1Experiment-(135)]
	_ = x[FeatureELDProviderExperimentV1-(136)]
	_ = x[FeatureAutoGenerateUwNotes-(137)]
	_ = x[FeaturePibitLossRunParsing-(138)]
	_ = x[FeatureBusinessAutoProducerAllowlist-(139)]
	_ = x[FeatureSnapsheetClaims-(140)]
	_ = x[FeatureInsuranceBundleFleetMigrationPhase2-(141)]
	_ = x[FeatureNFAutoAppSubmission-(142)]
	_ = x[FeatureChangeFormMigration-(143)]
	_ = x[FeatureExpressLaneV1-(144)]
}

var _FeatureValues = []Feature{FeatureStateTN, FeatureTelematicsSafetyReportLink, FeatureStateMO, FeatureTelematicsEmailer, FeatureStateKS, FeatureStateNE, FeatureStateIA, FeatureStateTX, FeatureTelematicsValidationErrors, FeatureCoverageMotorTruckCargo, FeatureStatePA, FeatureStateAL, FeatureStateSC, FeatureStateIN, FeatureLargeLossProxy, FeatureStateNC, FeatureFleetType, FeatureStateOK, FeatureStateOH, FeatureDemoApps, FeatureTelematicsStatus, FeatureStateGA, FeatureStateMN, FeatureTelematicsConsentReminderEmail, FeatureFormsDestination, FeatureStateWI, FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval, FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval, FeatureDataAvailabilityEvaluationTask, FeatureDataAvailabilityThresholdPercentage, FeatureSubmissionAcknowledgementEmail, FeatureTelematicsConnectionSuccessfulEmail, FeatureStateMI, FeaturePostBindOnboardingEmail, FeatureIndicationPricingExperiment, FeatureTelematicsConsentTruckerCloud, FeatureDriverSafetyScore, FeatureVehiclesServiceVINDecoder, FeatureTelematicsConsentFlow, FeatureCloseReasonsModal, FeatureStateAZ, FeatureStateCO, FeatureAppReviewPersistence, FeatureIndicationPricingExperimentQuartileDiscount, FeatureIndicationPricingExperimentStateDiscount, FeatureNewQuotePDF, FeatureStateUT, FeatureAppReviewPersistenceWidgets, FeatureStateNM, FeatureStateNV, FeatureUsePdfcpu, FeatureStateWA, FeatureStateOR, FeaturePdfcpuCutoff, FeatureCameraProgram, FeatureAuthorityControl, FeatureFronterX, FeatureEndorsementReviewAutomatedPricing, FeatureStateKY, FeatureTelematicsConnectionEmailOnAgentBehalf, FeatureValidateDuplicateApplications, FeatureAppetiteScore, FeatureStateCA, FeaturePibitTrialRun, FeatureNativeSalesforceIntegration, FeatureMVROverrides, FeatureFSFormsCompilationEnabled, FeatureNFStateTN, FeatureNFStatePA, FeatureNFStateGA, FeatureNFStateNC, FeatureFleetCWModel, FeatureNFStateSC, FeatureFSFormsCompilationShadowEnabled, FeatureTelematicsSignUpScreen, FeatureNFScrapePGR, FeatureNotifyFaultyTSPConnection, FeatureSafetyInvite, FeatureSelectSafetyScoreOnUwApp, FeatureZeroDeposit, FeatureUwTargetPriceCheckOnAction, FeatureUwClearanceCheckOnApprove, FeatureViolationNotificationJobEmails, FeatureLNCreditScore, FeatureNFNewModelRelease, FeatureNFEndorsementOpportunity, FeatureRecommendedActionAuthority, FeaturePreferredUnderwriterAssignment, FeatureReviewReadinessTaskAssignmentLogic, FeatureReviewReadinessTaskAssignee, FeatureReviewReadinessRolloutOverride, FeatureReviewReadinessRolloutAppetiteScore, FeatureMagicLinkViolationEmail, FeatureNFIBMigrationState, FeatureReviewReadinessEnabledTasks, FeaturePricingExpLowVinVisibility, FeatureApplicationTypeAccess, FeatureMstReferral, FeatureReviewReadinessRolloutAgency, FeatureAppReviewListNonTerminalStateFiltering, FeaturePrefillAndAutoReviewVinProblems, FeatureDriverYoeFromAgentInput, FeatureNFStateTX, FeatureClaimUpdateSummaryEmail, FeatureRecommendedActionVersionSixLaunch, FeatureReviewReadinessJiraCreation, FeatureNewEndorsementReviewSFDCIntegration, FeatureStateAR, FeatureShowRecommendationRangePricing, FeaturePricingRangeV2, FeatureNfEndorsementFilesThroughIbForClaims, FeatureUseLLMOpsForFinola, FeatureUseAutoSubmitForFinola, FeatureAutoSubmitToSnapsheet, FeatureNFSafetyDiscount, FeatureFSFormFillEnabled, FeatureNFAsyncUWAssignment, FeatureNFStateWI, FeatureNFStateIA, FeatureRolloutFinolaTwoPrompts, FeatureRolloutFinolaSnapsheet, FeatureNFStateAZ, FeatureNFStateNV, FeatureConsentReminderToInsured, FeatureRiskIqRollout, FeatureNFSafetyScoreAutoSelection, FeatureTelematicsConnectionEmailV2, FeatureAgentDashboardGoals, FeatureTelematicsTemplate, FeatureFSFormFillMigratedForms, FeatureAppetiteScoreV7, FeatureRiskFactorPricingV2, FeatureM1ScoreIntegration, FeatureNFTelematicsTemplate, FeatureAppetiteScoreV8, FeaturePreQuoteTelematicsV1Experiment, FeatureELDProviderExperimentV1, FeatureAutoGenerateUwNotes, FeaturePibitLossRunParsing, FeatureBusinessAutoProducerAllowlist, FeatureSnapsheetClaims, FeatureInsuranceBundleFleetMigrationPhase2, FeatureNFAutoAppSubmission, FeatureChangeFormMigration, FeatureExpressLaneV1}

var _FeatureNameToValueMap = map[string]Feature{
	_FeatureName[0:8]:            FeatureStateTN,
	_FeatureLowerName[0:8]:       FeatureStateTN,
	_FeatureName[8:37]:           FeatureTelematicsSafetyReportLink,
	_FeatureLowerName[8:37]:      FeatureTelematicsSafetyReportLink,
	_FeatureName[37:45]:          FeatureStateMO,
	_FeatureLowerName[37:45]:     FeatureStateMO,
	_FeatureName[45:63]:          FeatureTelematicsEmailer,
	_FeatureLowerName[45:63]:     FeatureTelematicsEmailer,
	_FeatureName[63:71]:          FeatureStateKS,
	_FeatureLowerName[63:71]:     FeatureStateKS,
	_FeatureName[71:79]:          FeatureStateNE,
	_FeatureLowerName[71:79]:     FeatureStateNE,
	_FeatureName[79:87]:          FeatureStateIA,
	_FeatureLowerName[79:87]:     FeatureStateIA,
	_FeatureName[87:95]:          FeatureStateTX,
	_FeatureLowerName[87:95]:     FeatureStateTX,
	_FeatureName[95:123]:         FeatureTelematicsValidationErrors,
	_FeatureLowerName[95:123]:    FeatureTelematicsValidationErrors,
	_FeatureName[123:149]:        FeatureCoverageMotorTruckCargo,
	_FeatureLowerName[123:149]:   FeatureCoverageMotorTruckCargo,
	_FeatureName[149:157]:        FeatureStatePA,
	_FeatureLowerName[149:157]:   FeatureStatePA,
	_FeatureName[157:165]:        FeatureStateAL,
	_FeatureLowerName[157:165]:   FeatureStateAL,
	_FeatureName[165:173]:        FeatureStateSC,
	_FeatureLowerName[165:173]:   FeatureStateSC,
	_FeatureName[173:181]:        FeatureStateIN,
	_FeatureLowerName[173:181]:   FeatureStateIN,
	_FeatureName[181:197]:        FeatureLargeLossProxy,
	_FeatureLowerName[181:197]:   FeatureLargeLossProxy,
	_FeatureName[197:205]:        FeatureStateNC,
	_FeatureLowerName[197:205]:   FeatureStateNC,
	_FeatureName[205:215]:        FeatureFleetType,
	_FeatureLowerName[205:215]:   FeatureFleetType,
	_FeatureName[215:223]:        FeatureStateOK,
	_FeatureLowerName[215:223]:   FeatureStateOK,
	_FeatureName[223:231]:        FeatureStateOH,
	_FeatureLowerName[223:231]:   FeatureStateOH,
	_FeatureName[231:240]:        FeatureDemoApps,
	_FeatureLowerName[231:240]:   FeatureDemoApps,
	_FeatureName[240:257]:        FeatureTelematicsStatus,
	_FeatureLowerName[240:257]:   FeatureTelematicsStatus,
	_FeatureName[257:265]:        FeatureStateGA,
	_FeatureLowerName[257:265]:   FeatureStateGA,
	_FeatureName[265:273]:        FeatureStateMN,
	_FeatureLowerName[265:273]:   FeatureStateMN,
	_FeatureName[273:306]:        FeatureTelematicsConsentReminderEmail,
	_FeatureLowerName[273:306]:   FeatureTelematicsConsentReminderEmail,
	_FeatureName[306:323]:        FeatureFormsDestination,
	_FeatureLowerName[306:323]:   FeatureFormsDestination,
	_FeatureName[323:331]:        FeatureStateWI,
	_FeatureLowerName[323:331]:   FeatureStateWI,
	_FeatureName[331:393]:        FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval,
	_FeatureLowerName[331:393]:   FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval,
	_FeatureName[393:456]:        FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval,
	_FeatureLowerName[393:456]:   FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval,
	_FeatureName[456:489]:        FeatureDataAvailabilityEvaluationTask,
	_FeatureLowerName[456:489]:   FeatureDataAvailabilityEvaluationTask,
	_FeatureName[489:527]:        FeatureDataAvailabilityThresholdPercentage,
	_FeatureLowerName[489:527]:   FeatureDataAvailabilityThresholdPercentage,
	_FeatureName[527:559]:        FeatureSubmissionAcknowledgementEmail,
	_FeatureLowerName[527:559]:   FeatureSubmissionAcknowledgementEmail,
	_FeatureName[559:597]:        FeatureTelematicsConnectionSuccessfulEmail,
	_FeatureLowerName[559:597]:   FeatureTelematicsConnectionSuccessfulEmail,
	_FeatureName[597:605]:        FeatureStateMI,
	_FeatureLowerName[597:605]:   FeatureStateMI,
	_FeatureName[605:631]:        FeaturePostBindOnboardingEmail,
	_FeatureLowerName[605:631]:   FeaturePostBindOnboardingEmail,
	_FeatureName[631:660]:        FeatureIndicationPricingExperiment,
	_FeatureLowerName[631:660]:   FeatureIndicationPricingExperiment,
	_FeatureName[660:692]:        FeatureTelematicsConsentTruckerCloud,
	_FeatureLowerName[660:692]:   FeatureTelematicsConsentTruckerCloud,
	_FeatureName[692:711]:        FeatureDriverSafetyScore,
	_FeatureLowerName[692:711]:   FeatureDriverSafetyScore,
	_FeatureName[711:739]:        FeatureVehiclesServiceVINDecoder,
	_FeatureLowerName[711:739]:   FeatureVehiclesServiceVINDecoder,
	_FeatureName[739:762]:        FeatureTelematicsConsentFlow,
	_FeatureLowerName[739:762]:   FeatureTelematicsConsentFlow,
	_FeatureName[762:781]:        FeatureCloseReasonsModal,
	_FeatureLowerName[762:781]:   FeatureCloseReasonsModal,
	_FeatureName[781:789]:        FeatureStateAZ,
	_FeatureLowerName[781:789]:   FeatureStateAZ,
	_FeatureName[789:797]:        FeatureStateCO,
	_FeatureLowerName[789:797]:   FeatureStateCO,
	_FeatureName[797:819]:        FeatureAppReviewPersistence,
	_FeatureLowerName[797:819]:   FeatureAppReviewPersistence,
	_FeatureName[819:866]:        FeatureIndicationPricingExperimentQuartileDiscount,
	_FeatureLowerName[819:866]:   FeatureIndicationPricingExperimentQuartileDiscount,
	_FeatureName[866:910]:        FeatureIndicationPricingExperimentStateDiscount,
	_FeatureLowerName[866:910]:   FeatureIndicationPricingExperimentStateDiscount,
	_FeatureName[910:923]:        FeatureNewQuotePDF,
	_FeatureLowerName[910:923]:   FeatureNewQuotePDF,
	_FeatureName[923:931]:        FeatureStateUT,
	_FeatureLowerName[923:931]:   FeatureStateUT,
	_FeatureName[931:961]:        FeatureAppReviewPersistenceWidgets,
	_FeatureLowerName[931:961]:   FeatureAppReviewPersistenceWidgets,
	_FeatureName[961:969]:        FeatureStateNM,
	_FeatureLowerName[961:969]:   FeatureStateNM,
	_FeatureName[969:977]:        FeatureStateNV,
	_FeatureLowerName[969:977]:   FeatureStateNV,
	_FeatureName[977:987]:        FeatureUsePdfcpu,
	_FeatureLowerName[977:987]:   FeatureUsePdfcpu,
	_FeatureName[987:995]:        FeatureStateWA,
	_FeatureLowerName[987:995]:   FeatureStateWA,
	_FeatureName[995:1003]:       FeatureStateOR,
	_FeatureLowerName[995:1003]:  FeatureStateOR,
	_FeatureName[1003:1016]:      FeaturePdfcpuCutoff,
	_FeatureLowerName[1003:1016]: FeaturePdfcpuCutoff,
	_FeatureName[1016:1030]:      FeatureCameraProgram,
	_FeatureLowerName[1016:1030]: FeatureCameraProgram,
	_FeatureName[1030:1047]:      FeatureAuthorityControl,
	_FeatureLowerName[1030:1047]: FeatureAuthorityControl,
	_FeatureName[1047:1056]:      FeatureFronterX,
	_FeatureLowerName[1047:1056]: FeatureFronterX,
	_FeatureName[1056:1092]:      FeatureEndorsementReviewAutomatedPricing,
	_FeatureLowerName[1056:1092]: FeatureEndorsementReviewAutomatedPricing,
	_FeatureName[1092:1100]:      FeatureStateKY,
	_FeatureLowerName[1092:1100]: FeatureStateKY,
	_FeatureName[1100:1143]:      FeatureTelematicsConnectionEmailOnAgentBehalf,
	_FeatureLowerName[1100:1143]: FeatureTelematicsConnectionEmailOnAgentBehalf,
	_FeatureName[1143:1174]:      FeatureValidateDuplicateApplications,
	_FeatureLowerName[1143:1174]: FeatureValidateDuplicateApplications,
	_FeatureName[1174:1188]:      FeatureAppetiteScore,
	_FeatureLowerName[1174:1188]: FeatureAppetiteScore,
	_FeatureName[1188:1196]:      FeatureStateCA,
	_FeatureLowerName[1188:1196]: FeatureStateCA,
	_FeatureName[1196:1211]:      FeaturePibitTrialRun,
	_FeatureLowerName[1196:1211]: FeaturePibitTrialRun,
	_FeatureName[1211:1240]:      FeatureNativeSalesforceIntegration,
	_FeatureLowerName[1211:1240]: FeatureNativeSalesforceIntegration,
	_FeatureName[1240:1253]:      FeatureMVROverrides,
	_FeatureLowerName[1240:1253]: FeatureMVROverrides,
	_FeatureName[1253:1281]:      FeatureFSFormsCompilationEnabled,
	_FeatureLowerName[1253:1281]: FeatureFSFormsCompilationEnabled,
	_FeatureName[1281:1292]:      FeatureNFStateTN,
	_FeatureLowerName[1281:1292]: FeatureNFStateTN,
	_FeatureName[1292:1303]:      FeatureNFStatePA,
	_FeatureLowerName[1292:1303]: FeatureNFStatePA,
	_FeatureName[1303:1314]:      FeatureNFStateGA,
	_FeatureLowerName[1303:1314]: FeatureNFStateGA,
	_FeatureName[1314:1325]:      FeatureNFStateNC,
	_FeatureLowerName[1314:1325]: FeatureNFStateNC,
	_FeatureName[1325:1339]:      FeatureFleetCWModel,
	_FeatureLowerName[1325:1339]: FeatureFleetCWModel,
	_FeatureName[1339:1350]:      FeatureNFStateSC,
	_FeatureLowerName[1339:1350]: FeatureNFStateSC,
	_FeatureName[1350:1385]:      FeatureFSFormsCompilationShadowEnabled,
	_FeatureLowerName[1350:1385]: FeatureFSFormsCompilationShadowEnabled,
	_FeatureName[1385:1410]:      FeatureTelematicsSignUpScreen,
	_FeatureLowerName[1385:1410]: FeatureTelematicsSignUpScreen,
	_FeatureName[1410:1423]:      FeatureNFScrapePGR,
	_FeatureLowerName[1410:1423]: FeatureNFScrapePGR,
	_FeatureName[1423:1451]:      FeatureNotifyFaultyTSPConnection,
	_FeatureLowerName[1423:1451]: FeatureNotifyFaultyTSPConnection,
	_FeatureName[1451:1464]:      FeatureSafetyInvite,
	_FeatureLowerName[1451:1464]: FeatureSafetyInvite,
	_FeatureName[1464:1493]:      FeatureSelectSafetyScoreOnUwApp,
	_FeatureLowerName[1464:1493]: FeatureSelectSafetyScoreOnUwApp,
	_FeatureName[1493:1505]:      FeatureZeroDeposit,
	_FeatureLowerName[1493:1505]: FeatureZeroDeposit,
	_FeatureName[1505:1536]:      FeatureUwTargetPriceCheckOnAction,
	_FeatureLowerName[1505:1536]: FeatureUwTargetPriceCheckOnAction,
	_FeatureName[1536:1565]:      FeatureUwClearanceCheckOnApprove,
	_FeatureLowerName[1536:1565]: FeatureUwClearanceCheckOnApprove,
	_FeatureName[1565:1598]:      FeatureViolationNotificationJobEmails,
	_FeatureLowerName[1565:1598]: FeatureViolationNotificationJobEmails,
	_FeatureName[1598:1613]:      FeatureLNCreditScore,
	_FeatureLowerName[1598:1613]: FeatureLNCreditScore,
	_FeatureName[1613:1633]:      FeatureNFNewModelRelease,
	_FeatureLowerName[1613:1633]: FeatureNFNewModelRelease,
	_FeatureName[1633:1659]:      FeatureNFEndorsementOpportunity,
	_FeatureLowerName[1633:1659]: FeatureNFEndorsementOpportunity,
	_FeatureName[1659:1687]:      FeatureRecommendedActionAuthority,
	_FeatureLowerName[1659:1687]: FeatureRecommendedActionAuthority,
	_FeatureName[1687:1719]:      FeaturePreferredUnderwriterAssignment,
	_FeatureLowerName[1687:1719]: FeaturePreferredUnderwriterAssignment,
	_FeatureName[1719:1757]:      FeatureReviewReadinessTaskAssignmentLogic,
	_FeatureLowerName[1719:1757]: FeatureReviewReadinessTaskAssignmentLogic,
	_FeatureName[1757:1787]:      FeatureReviewReadinessTaskAssignee,
	_FeatureLowerName[1757:1787]: FeatureReviewReadinessTaskAssignee,
	_FeatureName[1787:1820]:      FeatureReviewReadinessRolloutOverride,
	_FeatureLowerName[1787:1820]: FeatureReviewReadinessRolloutOverride,
	_FeatureName[1820:1859]:      FeatureReviewReadinessRolloutAppetiteScore,
	_FeatureLowerName[1820:1859]: FeatureReviewReadinessRolloutAppetiteScore,
	_FeatureName[1859:1885]:      FeatureMagicLinkViolationEmail,
	_FeatureLowerName[1859:1885]: FeatureMagicLinkViolationEmail,
	_FeatureName[1885:1905]:      FeatureNFIBMigrationState,
	_FeatureLowerName[1885:1905]: FeatureNFIBMigrationState,
	_FeatureName[1905:1935]:      FeatureReviewReadinessEnabledTasks,
	_FeatureLowerName[1905:1935]: FeatureReviewReadinessEnabledTasks,
	_FeatureName[1935:1965]:      FeaturePricingExpLowVinVisibility,
	_FeatureLowerName[1935:1965]: FeaturePricingExpLowVinVisibility,
	_FeatureName[1965:1988]:      FeatureApplicationTypeAccess,
	_FeatureLowerName[1965:1988]: FeatureApplicationTypeAccess,
	_FeatureName[1988:2000]:      FeatureMstReferral,
	_FeatureLowerName[1988:2000]: FeatureMstReferral,
	_FeatureName[2000:2031]:      FeatureReviewReadinessRolloutAgency,
	_FeatureLowerName[2000:2031]: FeatureReviewReadinessRolloutAgency,
	_FeatureName[2031:2075]:      FeatureAppReviewListNonTerminalStateFiltering,
	_FeatureLowerName[2031:2075]: FeatureAppReviewListNonTerminalStateFiltering,
	_FeatureName[2075:2111]:      FeaturePrefillAndAutoReviewVinProblems,
	_FeatureLowerName[2075:2111]: FeaturePrefillAndAutoReviewVinProblems,
	_FeatureName[2111:2138]:      FeatureDriverYoeFromAgentInput,
	_FeatureLowerName[2111:2138]: FeatureDriverYoeFromAgentInput,
	_FeatureName[2138:2149]:      FeatureNFStateTX,
	_FeatureLowerName[2138:2149]: FeatureNFStateTX,
	_FeatureName[2149:2175]:      FeatureClaimUpdateSummaryEmail,
	_FeatureLowerName[2149:2175]: FeatureClaimUpdateSummaryEmail,
	_FeatureName[2175:2212]:      FeatureRecommendedActionVersionSixLaunch,
	_FeatureLowerName[2175:2212]: FeatureRecommendedActionVersionSixLaunch,
	_FeatureName[2212:2242]:      FeatureReviewReadinessJiraCreation,
	_FeatureLowerName[2212:2242]: FeatureReviewReadinessJiraCreation,
	_FeatureName[2242:2281]:      FeatureNewEndorsementReviewSFDCIntegration,
	_FeatureLowerName[2242:2281]: FeatureNewEndorsementReviewSFDCIntegration,
	_FeatureName[2281:2289]:      FeatureStateAR,
	_FeatureLowerName[2281:2289]: FeatureStateAR,
	_FeatureName[2289:2322]:      FeatureShowRecommendationRangePricing,
	_FeatureLowerName[2289:2322]: FeatureShowRecommendationRangePricing,
	_FeatureName[2322:2338]:      FeaturePricingRangeV2,
	_FeatureLowerName[2322:2338]: FeaturePricingRangeV2,
	_FeatureName[2338:2380]:      FeatureNfEndorsementFilesThroughIbForClaims,
	_FeatureLowerName[2338:2380]: FeatureNfEndorsementFilesThroughIbForClaims,
	_FeatureName[2380:2402]:      FeatureUseLLMOpsForFinola,
	_FeatureLowerName[2380:2402]: FeatureUseLLMOpsForFinola,
	_FeatureName[2402:2428]:      FeatureUseAutoSubmitForFinola,
	_FeatureLowerName[2402:2428]: FeatureUseAutoSubmitForFinola,
	_FeatureName[2428:2452]:      FeatureAutoSubmitToSnapsheet,
	_FeatureLowerName[2428:2452]: FeatureAutoSubmitToSnapsheet,
	_FeatureName[2452:2470]:      FeatureNFSafetyDiscount,
	_FeatureLowerName[2452:2470]: FeatureNFSafetyDiscount,
	_FeatureName[2470:2490]:      FeatureFSFormFillEnabled,
	_FeatureLowerName[2470:2490]: FeatureFSFormFillEnabled,
	_FeatureName[2490:2512]:      FeatureNFAsyncUWAssignment,
	_FeatureLowerName[2490:2512]: FeatureNFAsyncUWAssignment,
	_FeatureName[2512:2523]:      FeatureNFStateWI,
	_FeatureLowerName[2512:2523]: FeatureNFStateWI,
	_FeatureName[2523:2534]:      FeatureNFStateIA,
	_FeatureLowerName[2523:2534]: FeatureNFStateIA,
	_FeatureName[2534:2560]:      FeatureRolloutFinolaTwoPrompts,
	_FeatureLowerName[2534:2560]: FeatureRolloutFinolaTwoPrompts,
	_FeatureName[2560:2584]:      FeatureRolloutFinolaSnapsheet,
	_FeatureLowerName[2560:2584]: FeatureRolloutFinolaSnapsheet,
	_FeatureName[2584:2595]:      FeatureNFStateAZ,
	_FeatureLowerName[2584:2595]: FeatureNFStateAZ,
	_FeatureName[2595:2606]:      FeatureNFStateNV,
	_FeatureLowerName[2595:2606]: FeatureNFStateNV,
	_FeatureName[2606:2633]:      FeatureConsentReminderToInsured,
	_FeatureLowerName[2606:2633]: FeatureConsentReminderToInsured,
	_FeatureName[2633:2648]:      FeatureRiskIqRollout,
	_FeatureLowerName[2633:2648]: FeatureRiskIqRollout,
	_FeatureName[2648:2678]:      FeatureNFSafetyScoreAutoSelection,
	_FeatureLowerName[2648:2678]: FeatureNFSafetyScoreAutoSelection,
	_FeatureName[2678:2708]:      FeatureTelematicsConnectionEmailV2,
	_FeatureLowerName[2678:2708]: FeatureTelematicsConnectionEmailV2,
	_FeatureName[2708:2729]:      FeatureAgentDashboardGoals,
	_FeatureLowerName[2708:2729]: FeatureAgentDashboardGoals,
	_FeatureName[2729:2748]:      FeatureTelematicsTemplate,
	_FeatureLowerName[2729:2748]: FeatureTelematicsTemplate,
	_FeatureName[2748:2775]:      FeatureFSFormFillMigratedForms,
	_FeatureLowerName[2748:2775]: FeatureFSFormFillMigratedForms,
	_FeatureName[2775:2792]:      FeatureAppetiteScoreV7,
	_FeatureLowerName[2775:2792]: FeatureAppetiteScoreV7,
	_FeatureName[2792:2814]:      FeatureRiskFactorPricingV2,
	_FeatureLowerName[2792:2814]: FeatureRiskFactorPricingV2,
	_FeatureName[2814:2834]:      FeatureM1ScoreIntegration,
	_FeatureLowerName[2814:2834]: FeatureM1ScoreIntegration,
	_FeatureName[2834:2856]:      FeatureNFTelematicsTemplate,
	_FeatureLowerName[2834:2856]: FeatureNFTelematicsTemplate,
	_FeatureName[2856:2873]:      FeatureAppetiteScoreV8,
	_FeatureLowerName[2856:2873]: FeatureAppetiteScoreV8,
	_FeatureName[2873:2907]:      FeaturePreQuoteTelematicsV1Experiment,
	_FeatureLowerName[2873:2907]: FeaturePreQuoteTelematicsV1Experiment,
	_FeatureName[2907:2933]:      FeatureELDProviderExperimentV1,
	_FeatureLowerName[2907:2933]: FeatureELDProviderExperimentV1,
	_FeatureName[2933:2955]:      FeatureAutoGenerateUwNotes,
	_FeatureLowerName[2933:2955]: FeatureAutoGenerateUwNotes,
	_FeatureName[2955:2977]:      FeaturePibitLossRunParsing,
	_FeatureLowerName[2955:2977]: FeaturePibitLossRunParsing,
	_FeatureName[2977:3009]:      FeatureBusinessAutoProducerAllowlist,
	_FeatureLowerName[2977:3009]: FeatureBusinessAutoProducerAllowlist,
	_FeatureName[3009:3025]:      FeatureSnapsheetClaims,
	_FeatureLowerName[3009:3025]: FeatureSnapsheetClaims,
	_FeatureName[3025:3064]:      FeatureInsuranceBundleFleetMigrationPhase2,
	_FeatureLowerName[3025:3064]: FeatureInsuranceBundleFleetMigrationPhase2,
	_FeatureName[3064:3086]:      FeatureNFAutoAppSubmission,
	_FeatureLowerName[3064:3086]: FeatureNFAutoAppSubmission,
	_FeatureName[3086:3107]:      FeatureChangeFormMigration,
	_FeatureLowerName[3086:3107]: FeatureChangeFormMigration,
	_FeatureName[3107:3122]:      FeatureExpressLaneV1,
	_FeatureLowerName[3107:3122]: FeatureExpressLaneV1,
}

var _FeatureNames = []string{
	_FeatureName[0:8],
	_FeatureName[8:37],
	_FeatureName[37:45],
	_FeatureName[45:63],
	_FeatureName[63:71],
	_FeatureName[71:79],
	_FeatureName[79:87],
	_FeatureName[87:95],
	_FeatureName[95:123],
	_FeatureName[123:149],
	_FeatureName[149:157],
	_FeatureName[157:165],
	_FeatureName[165:173],
	_FeatureName[173:181],
	_FeatureName[181:197],
	_FeatureName[197:205],
	_FeatureName[205:215],
	_FeatureName[215:223],
	_FeatureName[223:231],
	_FeatureName[231:240],
	_FeatureName[240:257],
	_FeatureName[257:265],
	_FeatureName[265:273],
	_FeatureName[273:306],
	_FeatureName[306:323],
	_FeatureName[323:331],
	_FeatureName[331:393],
	_FeatureName[393:456],
	_FeatureName[456:489],
	_FeatureName[489:527],
	_FeatureName[527:559],
	_FeatureName[559:597],
	_FeatureName[597:605],
	_FeatureName[605:631],
	_FeatureName[631:660],
	_FeatureName[660:692],
	_FeatureName[692:711],
	_FeatureName[711:739],
	_FeatureName[739:762],
	_FeatureName[762:781],
	_FeatureName[781:789],
	_FeatureName[789:797],
	_FeatureName[797:819],
	_FeatureName[819:866],
	_FeatureName[866:910],
	_FeatureName[910:923],
	_FeatureName[923:931],
	_FeatureName[931:961],
	_FeatureName[961:969],
	_FeatureName[969:977],
	_FeatureName[977:987],
	_FeatureName[987:995],
	_FeatureName[995:1003],
	_FeatureName[1003:1016],
	_FeatureName[1016:1030],
	_FeatureName[1030:1047],
	_FeatureName[1047:1056],
	_FeatureName[1056:1092],
	_FeatureName[1092:1100],
	_FeatureName[1100:1143],
	_FeatureName[1143:1174],
	_FeatureName[1174:1188],
	_FeatureName[1188:1196],
	_FeatureName[1196:1211],
	_FeatureName[1211:1240],
	_FeatureName[1240:1253],
	_FeatureName[1253:1281],
	_FeatureName[1281:1292],
	_FeatureName[1292:1303],
	_FeatureName[1303:1314],
	_FeatureName[1314:1325],
	_FeatureName[1325:1339],
	_FeatureName[1339:1350],
	_FeatureName[1350:1385],
	_FeatureName[1385:1410],
	_FeatureName[1410:1423],
	_FeatureName[1423:1451],
	_FeatureName[1451:1464],
	_FeatureName[1464:1493],
	_FeatureName[1493:1505],
	_FeatureName[1505:1536],
	_FeatureName[1536:1565],
	_FeatureName[1565:1598],
	_FeatureName[1598:1613],
	_FeatureName[1613:1633],
	_FeatureName[1633:1659],
	_FeatureName[1659:1687],
	_FeatureName[1687:1719],
	_FeatureName[1719:1757],
	_FeatureName[1757:1787],
	_FeatureName[1787:1820],
	_FeatureName[1820:1859],
	_FeatureName[1859:1885],
	_FeatureName[1885:1905],
	_FeatureName[1905:1935],
	_FeatureName[1935:1965],
	_FeatureName[1965:1988],
	_FeatureName[1988:2000],
	_FeatureName[2000:2031],
	_FeatureName[2031:2075],
	_FeatureName[2075:2111],
	_FeatureName[2111:2138],
	_FeatureName[2138:2149],
	_FeatureName[2149:2175],
	_FeatureName[2175:2212],
	_FeatureName[2212:2242],
	_FeatureName[2242:2281],
	_FeatureName[2281:2289],
	_FeatureName[2289:2322],
	_FeatureName[2322:2338],
	_FeatureName[2338:2380],
	_FeatureName[2380:2402],
	_FeatureName[2402:2428],
	_FeatureName[2428:2452],
	_FeatureName[2452:2470],
	_FeatureName[2470:2490],
	_FeatureName[2490:2512],
	_FeatureName[2512:2523],
	_FeatureName[2523:2534],
	_FeatureName[2534:2560],
	_FeatureName[2560:2584],
	_FeatureName[2584:2595],
	_FeatureName[2595:2606],
	_FeatureName[2606:2633],
	_FeatureName[2633:2648],
	_FeatureName[2648:2678],
	_FeatureName[2678:2708],
	_FeatureName[2708:2729],
	_FeatureName[2729:2748],
	_FeatureName[2748:2775],
	_FeatureName[2775:2792],
	_FeatureName[2792:2814],
	_FeatureName[2814:2834],
	_FeatureName[2834:2856],
	_FeatureName[2856:2873],
	_FeatureName[2873:2907],
	_FeatureName[2907:2933],
	_FeatureName[2933:2955],
	_FeatureName[2955:2977],
	_FeatureName[2977:3009],
	_FeatureName[3009:3025],
	_FeatureName[3025:3064],
	_FeatureName[3064:3086],
	_FeatureName[3086:3107],
	_FeatureName[3107:3122],
}

// FeatureString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func FeatureString(s string) (Feature, error) {
	if val, ok := _FeatureNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _FeatureNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to Feature values", s)
}

// FeatureValues returns all values of the enum
func FeatureValues() []Feature {
	return _FeatureValues
}

// FeatureStrings returns a slice of all String values of the enum
func FeatureStrings() []string {
	strs := make([]string, len(_FeatureNames))
	copy(strs, _FeatureNames)
	return strs
}

// IsAFeature returns "true" if the value is listed in the enum definition. "false" otherwise
func (i Feature) IsAFeature() bool {
	for _, v := range _FeatureValues {
		if i == v {
			return true
		}
	}
	return false
}
