package feature_flag_lib

// Feature holds all the available features in our feature flagging tool.
// Note: we assume flag names to follow kebab case.
// In addition, we need to make sure we run go:generate every time we add a
// new flag, or LaunchDarkly client won't work.
//
//go:generate go run github.com/dmarkham/enumer -type=Feature -trimprefix=Feature -transform=kebab
type Feature int

const (
	// FeatureStateTN is a flag to control TN expansion release.
	// Refer to https://app.launchdarkly.com/default/production/features/state-tn/targeting
	// for more information.
	FeatureStateTN Feature = iota
	// FeatureTelematicsSafetyReportLink is a flag to control rolling out a
	// link to the DOT's safety report page after the user consents to
	// telematics in the quoting app.
	FeatureTelematicsSafetyReportLink
	// FeatureStateMO is a flag to control MO expansion release.
	// Refer to https://app.launchdarkly.com/default/production/features/state-mo/targeting
	// for more information.
	FeatureStateMO
	// FeatureTelematicsEmailer is a flag to control rolling out a email to the user
	// after we receive the telematics data of a customer.
	// DEPRECATED
	FeatureTelematicsEmailer
	// FeatureStateKS is a flag to control KS expansion release.
	// Refer to https://app.launchdarkly.com/default/production/features/state-ks/targeting
	// for more information.
	// DEPRECATED
	FeatureStateKS
	// FeatureStateNE is a flag to control NE NR release.
	// Refer to https://app.launchdarkly.com/default/production/features/state-ne/targeting
	// for more information.
	// DEPRECATED
	FeatureStateNE
	// FeatureStateIA is a flag to control IA MTC expansion release + NR.
	// Refer to https://app.launchdarkly.com/default/production/features/state-ia/targeting
	// for more information.
	// DEPRECATED
	FeatureStateIA
	// FeatureStateTX is a flag to control TX expansion release.
	// Refer to https://app.launchdarkly.com/default/production/features/state-tx/targeting
	// for more information.
	// DEPRECATED
	FeatureStateTX
	// FeatureTelematicsValidationErrors controls whether we return validation errors
	// for new telematics connections that don't have sufficient matching VINs.
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-validation-errors/targeting
	// for more information.
	FeatureTelematicsValidationErrors
	// FeatureCoverageMotorTruckCargo is a flag to control MTC development.
	// Refer to https://app.launchdarkly.com/default/production/features/coverage-motor-truck-cargo/targeting
	// for more information.
	// DEPRECATED
	FeatureCoverageMotorTruckCargo
	// FeatureStatePA is a flag to control PA expansion release.
	// Refer to https://app.launchdarkly.com/default/production/features/state-pa/targeting
	// for more information.
	// DEPRECATED
	FeatureStatePA
	// FeatureStateAL is a flag to control AL expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-al/targeting
	// for more information.
	// DEPRECATED
	FeatureStateAL
	// FeatureStateSC is a flag to control SC expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-sc/targeting
	// for more information.
	// DEPRECATED
	FeatureStateSC
	// FeatureStateIN is a flag to control IN expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-in/targeting
	// for more information.
	FeatureStateIN
	// FeatureLargeLossProxy controls whether we proxy large losses during
	// indication run or not
	// DEPRECATED
	FeatureLargeLossProxy
	// FeatureStateNC is a flag to control NC expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-nc/targeting
	// for more information.
	// DEPRECATED
	FeatureStateNC
	// FeatureFleetType controls whether the agents/UW flow should be catered to either
	// fleets, wholesale non-fleets or retail non-fleets
	FeatureFleetType
	// FeatureStateOK is a flag to control OK expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-ok/targeting
	// DEPRECATED
	FeatureStateOK
	// FeatureStateOH is a flag to control OK expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-oh/targeting
	FeatureStateOH
	// FeatureDemoApps is a flag to control demo apps reset
	// Refer to https://app.launchdarkly.com/default/production/features/demo-apps/targeting
	FeatureDemoApps
	// FeatureTelematicsStatus controls whether we return telematics data status
	FeatureTelematicsStatus
	// FeatureStateGA is a flag to control AR expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-ga/targeting
	// DEPRECATED
	FeatureStateGA
	// FeatureStateMN is a flag to control NR expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-mn/targeting
	FeatureStateMN
	// FeatureTelematicsConsentReminderEmail is a flag to control the telematics consent reminder email feature. This
	// feature gets triggered in the application submission and uw app review flows.
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-consent-reminder-email/targeting
	FeatureTelematicsConsentReminderEmail
	// FeatureFormsDestination is a flag to control forms pdf templates destination
	// Refer to https://app.launchdarkly.com/default/production/features/forms-destination/targeting
	FeatureFormsDestination
	// FeatureStateWI is a flag to control NR expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-wi/targeting
	// DEPRECATED
	FeatureStateWI
	// FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval is a flag to determine the intervals at which the
	// telematics consent reminder emails will be sent when effectiveDate is before the threshold date. This flag is
	// primarily to be used for testing purposes. The returned value is in seconds.
	FeatureTelematicsConsentReminderEmailsPreThresholdDateInterval
	// FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval is a flag to determine the intervals at which the
	// telematics consent reminder emails will be sent when effectiveDate is after the threshold date. This flag is
	// primarily to be used for testing purposes. The returned value is in seconds.
	FeatureTelematicsConsentReminderEmailsPostThresholdDateInterval
	// FeatureDataAvailability is a flag to control data availability task which has been added as a task inside
	// the milliman handle workflow. This task evaluates whether a pipeline has sufficient data for
	// underwriters to review the account or not.
	FeatureDataAvailabilityEvaluationTask
	// FeatureDataAvailabilityThresholdPercentage is a feature flag with which we can control the threshold percentage
	// of the data availability task
	FeatureDataAvailabilityThresholdPercentage
	// FeatureSubmissionAcknowledgementEmail determines if Submission Acknowledgement Email should be sent.
	// Refer to https://app.launchdarkly.com/default/production/features/submission-acknowledgement-email/targeting
	FeatureSubmissionAcknowledgementEmail
	// FeatureTelematicsConnectionSuccessfulEmail determines if Telematics Connection Successful Email should be sent.
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-connection-successful-email/targeting
	FeatureTelematicsConnectionSuccessfulEmail
	// FeatureStateMI is a flag to control MI expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-mi/targeting
	FeatureStateMI
	// FeaturePostBindOnboardingEmail determines if post bind customer onboarding email should be sent.
	FeaturePostBindOnboardingEmail
	// FeatureIndicationPricingExperiment determines if indication pricing experiment ie
	// additional discount based on expected price, should be enabled.
	// DEPRECATED: This feature flag is no longer used.
	FeatureIndicationPricingExperiment
	// FeatureTelematicsConsentTruckerCloud determines if trucker cloud integration should be enabled.
	// DEPRECATED: This feature flag is no longer used.
	FeatureTelematicsConsentTruckerCloud
	// FeatureDriverSafetyScore determines if safety app will show linear safety score instead of risk score for drivers
	// Refer to https://app.launchdarkly.com/default/production/features/driver-safety-score/targeting
	FeatureDriverSafetyScore
	// FeatureVehiclesServiceVINDecoder determines if this user should view
	// VIN Decoder results from the Vehicles Service graph rather than from
	// the Application object. If enabled, we return Vehicles Services results.
	FeatureVehiclesServiceVINDecoder
	// FeatureTelematicsConsentFlow determines the overridable telematics consent flow
	// parameters for the telematics consent flow.
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-consent-flow/targeting
	FeatureTelematicsConsentFlow
	// FeatureCloseReasonsModal determines if the new end state reason is enabled
	// Refer to https://app.launchdarkly.com/default/production/features/close-reasons-modal/targeting
	// If enabled the new end state reason modal will be shown to the user
	FeatureCloseReasonsModal
	// FeatureStateAZ is a flag to control AZ expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-az/targeting
	// DEPRECATED
	FeatureStateAZ
	// FeatureStateCO is a flag to control CO expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-co/targeting
	FeatureStateCO
	// FeatureAppReviewPersistence determines if the app review persistence feature is enabled
	// for the user or not. This feature flag is used for GET APIs.
	FeatureAppReviewPersistence
	// FeatureIndicationPricingExperimentQuartileDiscount determines if the quartile based indication
	// pricing discount experiment should be enabled
	FeatureIndicationPricingExperimentQuartileDiscount
	// FeatureIndicationPricingExperimentStateDiscount determines if the state based indication
	// pricing discount experiment should be enabled
	FeatureIndicationPricingExperimentStateDiscount
	// FeatureNewQuotePDF determines if the new quote pdf is enabled
	// Refer to https://app.launchdarkly.com/default/production/features/new-quote-pdf/targeting
	// If enabled the new quote pdf will be served to the user
	// DEPRECATED
	FeatureNewQuotePDF
	// FeatureStateUT is a flag to control UT expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-ut/targeting
	// DEPRECATED
	FeatureStateUT
	// FeatureAppReviewPersistenceWidgets determines if the app review persistence feature is enabled
	// for a widget or not. This feature flag is used for GET APIs. This is a JSON variation type of
	// widget.
	FeatureAppReviewPersistenceWidgets
	// FeatureStateNM is a flag to control NM expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-nm/targeting
	// DEPRECATED
	FeatureStateNM
	// FeatureStateNV is a flag to control NV expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-nv/
	// DEPRECATED
	FeatureStateNV
	// FeatureUsePdfcpu determines if the pdfcpu library should be used for pdf operations or not.
	FeatureUsePdfcpu
	// FeatureStateWA is a flag to control WA expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-wa/
	FeatureStateWA
	// FeatureStateOR is a flag to control OR expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-or/
	// DEPRECATED
	FeatureStateOR
	// FeaturePdfcpuCutoff determines the cutoff date for using pdfcpu library for pdf operations.
	FeaturePdfcpuCutoff
	// FeatureCameraProgram determines if particular CameraProgram feature is enabled
	// DEPRECATED
	FeatureCameraProgram
	// FeatureAuthorityControl determines if particular authority control feature is enabled
	FeatureAuthorityControl
	// FeatureFronterX determines if fronter-x is enabled
	// Refer to https://app.launchdarkly.com/default/test/features/fronter-x/
	FeatureFronterX
	// FeatureEndorsementReviewAutomatedPricing is a flag to control the usage of the automated pricing features.
	// Refer to https://app.launchdarkly.com/default/production/features/endorsement-review-automated-pricing/targeting
	// DEPRECATED
	FeatureEndorsementReviewAutomatedPricing
	// FeatureStateKY is a flag to control KY expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-ky/
	// DEPRECATED
	FeatureStateKY
	// FeatureTelematicsConnectionEmailOnAgentBehalf is a flag to control email sent on behalf of agent
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-connection-email-on-agent-behalf/
	// DEPRECATED
	FeatureTelematicsConnectionEmailOnAgentBehalf

	// FeatureValidateDuplicateApplications is a flag to control the usage of the duplicate application validation.
	// Refer to https://app.launchdarkly.com/default/production/features/validate-duplicate-applications/targeting
	FeatureValidateDuplicateApplications

	// FeatureAppetiteScore determines whether overview panel is created/and shown for a new app review or not
	// Refer to https://app.launchdarkly.com/default/production/features/appetite-score/targeting
	FeatureAppetiteScore

	// FeatureStateCA is a flag to control CA expansion release
	// Refer to https://app.launchdarkly.com/default/production/features/state-ca/
	FeatureStateCA

	// FeaturePibitTrialRun determines whether we send files to pibit to get back the loss run summary excel file or not
	// (PROD ONLY!)
	// Refer to https://app.launchdarkly.com/default/production/features/pibit-trial-run/targeting
	FeaturePibitTrialRun

	// FeatureNativeSalesforceIntegration determines whether to use salesforce apis directly
	// Refer to https://app.launchdarkly.com/default/test/features/salesforce-api/targeting
	FeatureNativeSalesforceIntegration

	// FeatureMVROverrides determines whether we should re-pull MVR or use the overrides directly (Valid for NF only)
	FeatureMVROverrides

	// FeatureFSFormsCompilationEnabled determines whether we should use the new form schedule to generate
	// forms compilation or not
	FeatureFSFormsCompilationEnabled

	// FeatureNFStateTN is a flag to control Non-Fleet TN State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-tn/
	// DEPRECATED
	FeatureNFStateTN

	// FeatureNFStatePA is a flag to control Non-Fleet PA State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-pa/
	// DEPRECATED
	FeatureNFStatePA

	// FeatureNFStateGA is a flag to control Non-Fleet GA State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-ga/
	// DEPRECATED
	FeatureNFStateGA

	// FeatureNFStateNC is a flag to control Non-Fleet NC State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-nc/
	// DEPRECATED
	FeatureNFStateNC

	// FeatureFleetCWModel is a flag to control Fleet CW model release
	// Refer to https://app.launchdarkly.com/default/production/features/fleet-cw-model/
	// DEPRECATED
	FeatureFleetCWModel

	// FeatureNFStateSC is a flag to control Non-Fleet NC State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-sc/
	// DEPRECATED
	FeatureNFStateSC

	// FeatureFSFormsCompilationShadowEnabled determines whether we should run the new form schedule to generate
	// forms compilation in shadow or not. When this is enabled, the form compilation will be created and archived
	// immediately using this logic. This is used for analysing the correctness of the form compilation generation.
	FeatureFSFormsCompilationShadowEnabled

	// FeatureTelematicsSignUpScreen is a flag to prompt the user to create a user account after connecting telematics.
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-sign-up-screen
	FeatureTelematicsSignUpScreen

	// FeatureNFScrapePGR determines whether we should run the scraping logic for NF Indication generation
	// Refer to https://app.launchdarkly.com/default/production/features/nf-scrape-pgr/
	FeatureNFScrapePGR

	// FeatureNotifyFaultyTSPConnection is a flag to control if we should send the NotifyFaultyTSPConnectionEmail
	// Refer to https://app.launchdarkly.com/projects/default/flags/notify-faulty-tsp-connection
	FeatureNotifyFaultyTSPConnection

	// FeatureSafetyInvite determines if the user has access to inviting other users to the safety app
	// Refer to https://app.launchdarkly.com/projects/default/flags/Safety-invite
	FeatureSafetyInvite

	// FeatureSelectSafetyScoreOnUwApp enables blocking Approve button for Unselected Safety Score
	// (when there's a valid safety score trend present) on UW app.
	// Refer: https://app.launchdarkly.com/projects/default/flags/select-safety-score-on-uw-app
	FeatureSelectSafetyScoreOnUwApp

	// FeatureZeroDeposit determines if the zero deposit experiment should be enabled
	// Refer to https://app.launchdarkly.com/default/production/features/zero-deposit
	// DEPRECATED
	FeatureZeroDeposit

	// FeatureUwTargetPriceCheckOnAction determines whether UW action buttons should be disabled if target price is not entered
	// Refer to https://app.launchdarkly.com/projects/default/flags/uw-target-price-check-on-action
	FeatureUwTargetPriceCheckOnAction

	// FeatureUwClearanceCheckOnApprove determines whether UW approval should be disabled if application is not cleared
	// Refer to https://app.launchdarkly.com/projects/default/flags/uw-clearance-check-on-approve
	FeatureUwClearanceCheckOnApprove

	// FeatureViolationNotificationJobEmails controls which users get violation notification emails
	FeatureViolationNotificationJobEmails

	// FeatureLNCreditScore determines if the ln credit feature pull for score should be enabled
	// Refer to https://app.launchdarkly.com/default/production/features/ln-credit-score
	FeatureLNCreditScore

	// FeatureNFNewModelRelease is a flag to control Non-Fleet New Model Release
	// Refer to https://app.launchdarkly.com/default/production/features/nf-new-model-release
	FeatureNFNewModelRelease

	// FeatureNFEndorsementOpportunity is a flag to control Non-Fleet Endorsement Opportunity
	// Refer to https://app.launchdarkly.com/default/production/features/nf-endorsement-opportunity
	FeatureNFEndorsementOpportunity

	// FeatureRecommendedActionAuthority is a flag to control authorities for recommended actions (Strongly Quote
	// /Quote)
	FeatureRecommendedActionAuthority

	// FeaturePreferredUnderwriterAssignment is a flag to control the preferred underwriter assignment logic release
	// DEPRECATED
	FeaturePreferredUnderwriterAssignment

	// FeatureReviewReadinessTaskAssignmentLogic is a string variation that returns the algo to use for assigning review readiness tasks
	// Refer: https://app.launchdarkly.com/projects/default/flags/review-readiness-task-assignment-logic
	FeatureReviewReadinessTaskAssignmentLogic

	// FeatureReviewReadinessTaskAssignee returns the user ID to assign the review readiness tasks to.
	// Refer:https://app.launchdarkly.com/projects/default/flags/review-readiness-task-assignee
	FeatureReviewReadinessTaskAssignee

	// FeatureReviewReadinessRolloutOverride defines if review readiness rollout logic should be overridden for a user
	// Refer: https://app.launchdarkly.com/projects/default/flags/review-readiness-rollout-override
	FeatureReviewReadinessRolloutOverride

	// FeatureReviewReadinessRolloutAppetiteScore returns comma separated list of appetite for which task based review readiness should be rolled out
	// Refer: https://app.launchdarkly.com/projects/default/flags/review-readiness-rollout-appetite-score
	FeatureReviewReadinessRolloutAppetiteScore

	// FeatureMagicLinkViolationEmail is a flag to control sending a new sessionId in the violation notification email
	// Refer: https://app.launchdarkly.com/projects/default/flags/magic-link-violation-email
	FeatureMagicLinkViolationEmail

	// FeatureNFIBMigrationState is a flag to control IB migration state for Non Fleet, the values are Off, DualWriteEnabled,
	// ReadEnabled, Complete
	FeatureNFIBMigrationState

	// FeatureReviewReadinessEnabledTasks returns a comma separated list of enabled tasks for review readiness
	FeatureReviewReadinessEnabledTasks

	// FeaturePricingExpLowVinVisibility is a flag to enable/disable pricing experiment over low vin visibility.
	// Note - this only works with new generate_appetite_factors_job runs. Existing app reviews where pricing exp has
	// been already applied need their aforementioned job to be rerun for latest FF variation to make a change.
	FeaturePricingExpLowVinVisibility

	// FeatureApplicationTypeAccess is a flag to indicate whether an agent has "fleet", "non-fleet", or "all"
	// access in the quoting app.
	FeatureApplicationTypeAccess

	// FeatureMstReferral is a flag to control the referral rules for the application. This will control the approval
	// button's logic. It will also control the frontend UI for the referral rules.
	FeatureMstReferral

	// FeatureReviewReadinessRolloutAgency returns comma separated list of agency IDs for which task based review readiness should be rolled out
	FeatureReviewReadinessRolloutAgency

	// FeatureAppReviewListNonTerminalStateFiltering is a flag to indicate whether the app review list screen for
	// incomplete and ready for review tabs filters by all non terminal states instead of just pending state.
	FeatureAppReviewListNonTerminalStateFiltering

	// FeaturePrefillAndAutoReviewVinProblems indicates whether VIN problems should have fixes pre-filled
	// and whether the problems should be marked auto reviewed given the right fields are populated
	FeaturePrefillAndAutoReviewVinProblems

	// FeatureDriverYoeFromAgentInput is a flag to indicate whether driver YOE as part of the driver's list
	// should be picked from the agent input as opposed to the MVR data.
	FeatureDriverYoeFromAgentInput

	// FeatureNFStateTX is a flag to control Non-Fleet TX State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-tx/
	FeatureNFStateTX

	// FeatureClaimUpdateSummaryEmail is a rollout flag for AI-powered emails that summarize
	// open claims' notes, to be sent to fleet admins.
	FeatureClaimUpdateSummaryEmail

	// FeatureRecommendedActionVersionSixLaunch is a flag to indicate whether the app review should use
	// version 6 of the recommended action logic (latest version) or not.
	// Deprecated
	FeatureRecommendedActionVersionSixLaunch

	// FeatureReviewReadinessJiraCreation flag controls the creation of Jira tickets for review readiness task assignees
	FeatureReviewReadinessJiraCreation

	// FeatureNewEndorsementReviewSFDCIntegration flag controls the creation of sfdc expansion opportunities in the new
	// endorsement review platform
	FeatureNewEndorsementReviewSFDCIntegration

	// FeatureStateAR is a flag to control Fleet AR State Expansion
	// Deprecated
	FeatureStateAR
	// FeatureShowRecommendationRangePricing flag controls the display of the recommendation range pricing in
	// the pricing-panel
	FeatureShowRecommendationRangePricing

	// FeaturePricingRangeV2 flag controls the new version 2 of pricing ranges functionality
	// Refer to https://app.launchdarkly.com/default/production/features/pricing-range-v2/
	FeaturePricingRangeV2

	// FeatureNfEndorsementFilesThroughIbForClaims is a rollout flag for the launch of IB for NF policies.
	// In particular, it controls whether we should use the new data model for endorsements to get their
	// files, or the old one.
	// DEPRECATED
	FeatureNfEndorsementFilesThroughIbForClaims

	// FeatureUseLLMOpsForFinola is a rollout flag for using LLMOps infra for the LLM calls, instead
	// of using an OpenAI client directly.
	// DEPRECATED
	FeatureUseLLMOpsForFinola

	// FeatureUseAutoSubmitForFinola is a rollout flag for using the auto submit feature for Finola,
	// which determines if an FNOL can be auto submitted or not based on whether all necessary fields
	// are present.
	FeatureUseAutoSubmitForFinola

	// FeatureAutoSubmitToSnapsheet is a feature flag for using the auto submit feature for Snapsheet
	FeatureAutoSubmitToSnapsheet

	// FeatureNFSafetyDiscount is a flag to control the safety discount feature for NF indications
	// Refer to https://app.launchdarkly.com/default/production/features/ln-credit-score
	FeatureNFSafetyDiscount
	// FeatureFSFormFillEnabled is a flag to control the usage of new form fill methodology
	// for filling form compilations
	// DEPRECATED
	FeatureFSFormFillEnabled

	// FeatureNFAsyncUWAssignment is a flag that determines whether async uw assignment should happen for NF apps
	FeatureNFAsyncUWAssignment

	// FeatureNFStateWI is a flag to control Non-Fleet WI State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-wi/
	// Deprecated
	FeatureNFStateWI

	// FeatureNFStateIA is a flag to control Non-Fleet IA State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-ia/
	// Deprecated
	FeatureNFStateIA

	// FeatureRolloutFinolaTwoPrompts is a rollout flag for using Finola with an explicit classifier
	// and extractor prompts, instead of one general one.
	FeatureRolloutFinolaTwoPrompts

	// FeatureRolloutFinolaSnapsheet controls what percent of claims that are able to be sent to Snapsheet
	// are sent to Snapsheet. This means that if the flag is set to 50, then 50% of these claims will be sent
	// to Snapsheet. This doesn't mean that 50% of all Finola emails will be sent to Snapsheet, as any Falls
	// Lake or Sirius Point claims will always be sent to NARS. So the real percentage will always be lower
	// than the one indicated here.
	FeatureRolloutFinolaSnapsheet

	// FeatureNFStateAZ is a flag to control Non-Fleet AZ State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-az/
	// Deprecated
	FeatureNFStateAZ

	// FeatureNFStateNV is a flag to control Non-Fleet NV State Expansion
	// Refer to https://app.launchdarkly.com/default/production/features/nf-state-nc/
	// Deprecated
	FeatureNFStateNV

	// FeatureConsentReminderToInsured is a flag to control fleet consent reminders to the insured
	// using the DOT Number as the identifier.
	// Refer to https://app.launchdarkly.com/default/production/features/consent-reminder-to-insured/
	FeatureConsentReminderToInsured
	// FeatureRiskIqRollout is a flag to control the rollout of RiskIQ
	FeatureRiskIqRollout

	// FeatureNFSafetyScoreAutoSelection is a flag to control whether auto selection should happen for NF safety score
	FeatureNFSafetyScoreAutoSelection

	// FeatureTelematicsConnectionEmailV2 is a flag to control fleet telematics connection email to the insured
	// to be sent to in a new format
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-connection-email-v2/
	FeatureTelematicsConnectionEmailV2

	// FeatureAgentDashboardGoals is a flag to control the Telematics Agent Dashboard Goals
	// Refer to https://app.launchdarkly.com/projects/default/flags/agent-dashboard-goals/
	FeatureAgentDashboardGoals

	// FeatureTelematicsTemplate is a flag to control the telematics template selection
	// Refer to https://app.launchdarkly.com/default/production/features/telematics-template/
	FeatureTelematicsTemplate

	// FeatureFSFormFillMigratedForms is a JSON variation flag which contains
	// the list of migrated forms for the new form fill methodology
	// Refer to https://app.launchdarkly.com/default/production/features/fs-form-fill-migrated-forms/
	FeatureFSFormFillMigratedForms

	// FeatureAppetiteScoreV7 is a flag that controls whether we use the V7 version of the appetite score or not
	// Deprecated
	FeatureAppetiteScoreV7

	// FeatureRiskFactorPricingV2 is a flag to control whether an underwriter should get v2 pricing in risk factor worksheets
	// Refer to https://app.launchdarkly.com/default/production/features/risk-factor-pricing-v2/
	FeatureRiskFactorPricingV2
	// FeatureM1ScoreIntegration is a flag to control whether we should use the M1 score integration
	// Refer to https://app.launchdarkly.com/default/production/features/m1-score-integration/
	FeatureM1ScoreIntegration

	// FeatureNFTelematicsTemplate is a flag to control the telematics template selection
	// Refer to https://app.launchdarkly.com/default/production/features/nf-telematics-template/
	FeatureNFTelematicsTemplate

	// FeatureAppetiteScoreV8 is a flag that controls whether we use the V8 version of the appetite score or not
	FeatureAppetiteScoreV8
	// FeaturePreQuoteTelematicsV1Experiment is a flag to control the pre-quote telematics experiment
	FeaturePreQuoteTelematicsV1Experiment
	// FeatureELDProviderExperimentV1 is a flag to control the ELD provider dropdown on frontend
	FeatureELDProviderExperimentV1

	// FeatureAutoGenerateUwNotes determines if auto-generated UW notes are enabled
	// Refer to https://app.launchdarkly.com/default/production/features/auto-generate-uw-notes/
	FeatureAutoGenerateUwNotes
	// FeaturePibitLossRunParsing
	FeaturePibitLossRunParsing
	// FeatureBusinessAutoProducerAllowlist controls which producers are returned by
	// GET /available-producers endpoint in Business Auto. The flag value is a JSON array
	// of email addresses. Only users with these emails AND appropriate agency roles
	// (AgencyProducer or AgencyAdmin) will be included in the response.
	FeatureBusinessAutoProducerAllowlist

	// FeatureSnapsheetClaims is a flag to control the snapsheet claims feature
	// Refer to https://app.launchdarkly.com/default/production/features/snapsheet-claims/
	FeatureSnapsheetClaims
	// FeatureInsuranceBundleFleetMigrationPhase2 is a flag to control the migration of the fleet program to the
	// insurance bundle platform.
	FeatureInsuranceBundleFleetMigrationPhase2

	// FeatureNFAutoAppSubmission is a flag to control the auto-submission of NF applications
	// Refer to https://app.launchdarkly.com/default/production/features/nf-auto-app-submission/
	FeatureNFAutoAppSubmission

	// FeatureChangeFormMigration is a flag to control the migration of the change form generation
	// Refer to https://app.launchdarkly.com/default/production/features/change-form-migration/
	FeatureChangeFormMigration

	// FeatureExpressLaneV1 is a flag to control the Express Lane V1 feature
	// Refer to https://app.launchdarkly.com/default/production/features/express-lane-v1/
	FeatureExpressLaneV1
)
