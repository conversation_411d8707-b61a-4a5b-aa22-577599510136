// Code generated by "enumer -type=RequestState -json -text -trimprefix=RequestState"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _RequestStateName = "InvalidPendingUnderUWManagerReviewApprovedDeclined"

var _RequestStateIndex = [...]uint8{0, 7, 14, 34, 42, 50}

const _RequestStateLowerName = "invalidpendingunderuwmanagerreviewapproveddeclined"

func (i RequestState) String() string {
	if i < 0 || i >= RequestState(len(_RequestStateIndex)-1) {
		return fmt.Sprintf("RequestState(%d)", i)
	}
	return _RequestStateName[_RequestStateIndex[i]:_RequestStateIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _RequestStateNoOp() {
	var x [1]struct{}
	_ = x[RequestStateInvalid-(0)]
	_ = x[RequestStatePending-(1)]
	_ = x[RequestStateUnderUWManagerReview-(2)]
	_ = x[RequestStateApproved-(3)]
	_ = x[RequestStateDeclined-(4)]
}

var _RequestStateValues = []RequestState{RequestStateInvalid, RequestStatePending, RequestStateUnderUWManagerReview, RequestStateApproved, RequestStateDeclined}

var _RequestStateNameToValueMap = map[string]RequestState{
	_RequestStateName[0:7]:        RequestStateInvalid,
	_RequestStateLowerName[0:7]:   RequestStateInvalid,
	_RequestStateName[7:14]:       RequestStatePending,
	_RequestStateLowerName[7:14]:  RequestStatePending,
	_RequestStateName[14:34]:      RequestStateUnderUWManagerReview,
	_RequestStateLowerName[14:34]: RequestStateUnderUWManagerReview,
	_RequestStateName[34:42]:      RequestStateApproved,
	_RequestStateLowerName[34:42]: RequestStateApproved,
	_RequestStateName[42:50]:      RequestStateDeclined,
	_RequestStateLowerName[42:50]: RequestStateDeclined,
}

var _RequestStateNames = []string{
	_RequestStateName[0:7],
	_RequestStateName[7:14],
	_RequestStateName[14:34],
	_RequestStateName[34:42],
	_RequestStateName[42:50],
}

// RequestStateString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func RequestStateString(s string) (RequestState, error) {
	if val, ok := _RequestStateNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _RequestStateNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to RequestState values", s)
}

// RequestStateValues returns all values of the enum
func RequestStateValues() []RequestState {
	return _RequestStateValues
}

// RequestStateStrings returns a slice of all String values of the enum
func RequestStateStrings() []string {
	strs := make([]string, len(_RequestStateNames))
	copy(strs, _RequestStateNames)
	return strs
}

// IsARequestState returns "true" if the value is listed in the enum definition. "false" otherwise
func (i RequestState) IsARequestState() bool {
	for _, v := range _RequestStateValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for RequestState
func (i RequestState) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for RequestState
func (i *RequestState) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("RequestState should be a string, got %s", data)
	}

	var err error
	*i, err = RequestStateString(s)
	return err
}

// MarshalText implements the encoding.TextMarshaler interface for RequestState
func (i RequestState) MarshalText() ([]byte, error) {
	return []byte(i.String()), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for RequestState
func (i *RequestState) UnmarshalText(text []byte) error {
	var err error
	*i, err = RequestStateString(string(text))
	return err
}
