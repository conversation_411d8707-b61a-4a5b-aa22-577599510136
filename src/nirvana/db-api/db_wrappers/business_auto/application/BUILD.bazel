load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "application",
    srcs = [
        "deserialization.go",
        "filters.go",
        "fx.go",
        "interface.go",
        "serialisation.go",
        "wrapper.go",
    ],
    importpath = "nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/short_id",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_models/business_auto",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/fxregistry",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "application_test",
    srcs = ["serialisation_test.go"],
    embed = [":application"],
    deps = [
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/nirvanaapp/enums",
        "//nirvana/rating/rtypes",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
