package application

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
	"nirvanatech.com/nirvana/rating/rtypes"
)

func TestBusinessAutoAppSerialization(t *testing.T) {
	// BusinessAutoApp with complete data
	now := time.Now()
	usState, err := us_states.StrToUSState("OH")
	require.NoError(t, err)
	app := &model.BusinessAutoApp{
		ID:                     uuid.New(),
		EffectiveDurationStart: now,
		EffectiveDurationEnd:   now.AddDate(1, 0, 0),
		CompanyInfo: model.CompanyInfo{
			Name:                          "Test Company LLC",
			DOTNumber:                     pointer_utils.ToPointer(12345),
			FEIN:                          pointer_utils.ToPointer("*********"),
			NoOfPowerUnits:                pointer_utils.ToPointer(int32(10)),
			HasIndividualNamedInsured:     pointer_utils.ToPointer(true),
			NoOfEmployees:                 pointer_utils.ToPointer(int32(25)),
			PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationContractorTrucks),
			SecondaryIndustryClassifications: []enums.IndustryClassification{
				enums.IndustryClassificationWholesalersManufacturers,
				enums.IndustryClassificationWholesalersManufacturers,
			},
			USState: usState,
		},
		VehiclesInfo: pointer_utils.ToPointer([]model.VehicleInfo{
			{
				VIN:                              "1HGCM82633A123456",
				Year:                             2020,
				Make:                             "Ford",
				Model:                            "F-150",
				VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
				WeightClass:                      enums.WeightClassHeavy,
				SpecialtyVehicleType:             enums.SpecialtyVehicleTypeCatererVehicles,
				VehicleUse:                       enums.VehicleUseTowingOperations,
				BusinessUse:                      enums.BusinessUseRetail,
				StateUsage:                       enums.StateUsageIntrastate,
				RadiusClassification:             enums.RadiusClassification0To100,
				PrincipalGaragingLocationZipCode: "12345",
				IsGlassLinedTankTruckOrTrailer:   pointer_utils.ToPointer(true),
				HasAntiLockBrakes:                pointer_utils.ToPointer(false),
			},
			{
				VIN:                              "2HGCM82633A654321",
				Year:                             2021,
				Make:                             "Chevrolet",
				Model:                            "Silverado",
				VehicleType:                      nirvanaapp_enums.VehicleTypeTrailer,
				WeightClass:                      enums.WeightClassLight,
				VehicleUse:                       enums.VehicleUseTowingOperations,
				BusinessUse:                      enums.BusinessUseService,
				StateUsage:                       enums.StateUsageInterstate,
				RadiusClassification:             enums.RadiusClassification101To300,
				PrincipalGaragingLocationZipCode: "54321",
			},
		}),
		CoveragesInfo: &model.CoveragesInfo{
			PrimaryCoverages: []model.PrimaryCoverage{
				{
					ID: app_enums.CoverageAutoLiability,
					SubCoverageIDs: []app_enums.Coverage{
						app_enums.CoverageBodilyInjury,
						app_enums.CoveragePropertyDamage,
					},
				},
				{
					ID: app_enums.CoverageAutoPhysicalDamage,
					SubCoverageIDs: []app_enums.Coverage{
						app_enums.CoverageComprehensive,
						app_enums.CoverageCollision,
					},
				},
			},
			Limits: []model.Limit{
				{
					SubCoverageIDs: []app_enums.Coverage{
						app_enums.CoverageBodilyInjury,
						app_enums.CoveragePropertyDamage,
					},
					Amount:   1000000,
					Grouping: model.LimitGroupingCombined,
				},
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageComprehensive},
					Amount:         50000,
					Grouping:       model.LimitGroupingSingle,
				},
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageCollision},
					Amount:         50000,
					Grouping:       model.LimitGroupingSingle,
				},
			},
			Deductibles: []model.Deductible{
				{
					SubCoverageIDs: []app_enums.Coverage{
						app_enums.CoverageBodilyInjury,
						app_enums.CoveragePropertyDamage,
					},
					Amount: 0,
				},
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageComprehensive},
					Amount:         500,
				},
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageCollision},
					Amount:         500,
				},
			},
		},
		FilingsInfo: &model.FilingsInfo{
			HasMultiStateFilings:  true,
			HasSingleStateFilings: false,
			HasFMCSAFilings:       true,
			HasDOTFilings:         false,
		},
		ProducerID:      uuid.New(),
		CreatedBy:       uuid.New(),
		CreatedAt:       now,
		UpdatedAt:       now.Add(time.Hour),
		UWState:         enums.UWStateUnderReview,
		AgencyID:        uuid.New(),
		TSPConnHandleId: pointer_utils.UUID(uuid.New()),
		TelematicsInfo: &model.TelematicsInfo{
			FullName: "John Doe",
			Email:    "<EMAIL>",
		},
		SignaturePacketID:               pointer_utils.ToPointer(uuid.New()),
		SelectedQuotingPricingContextID: pointer_utils.ToPointer(uuid.New()),
		ModelPinConfig: &model.ModelPinConfigInfo{
			RateML: model.RateMLConfig{
				Provider: rtypes.ProviderSentry,
				USState:  us_states.CA,
				Version:  rtypes.Version100,
			},
		},
		DocumentsInfo: &model.DocumentsInfo{
			QuotePDFHandleID: pointer_utils.ToPointer(uuid.New()),
		},
	}

	// Test serialization to DB and back
	serialized, err := appToDB(app)
	require.NoError(t, err)

	deserialized, err := appFromDB(serialized)
	require.NoError(t, err)

	assert.Equal(t, app, deserialized)
	assert.Equal(t, app.CompanyInfo.USState, deserialized.CompanyInfo.USState)

	// Test with different state
	usState, err = us_states.StrToUSState("CA")
	require.NoError(t, err)
	app.CompanyInfo.USState = usState
	serialized, err = appToDB(app)
	require.NoError(t, err)

	deserialized, err = appFromDB(serialized)
	require.NoError(t, err)

	assert.Equal(t, app, deserialized)
	assert.Equal(t, us_states.CA, deserialized.CompanyInfo.USState)

	// Test with nil FilingsInfo
	app.FilingsInfo = nil
	serialized, err = appToDB(app)
	require.NoError(t, err)

	deserialized, err = appFromDB(serialized)
	require.NoError(t, err)

	assert.Equal(t, app, deserialized)

	// Test with empty VehiclesInfo
	app.VehiclesInfo = pointer_utils.ToPointer([]model.VehicleInfo{})
	serialized, err = appToDB(app)
	require.NoError(t, err)

	deserialized, err = appFromDB(serialized)
	require.NoError(t, err)

	assert.Equal(t, app, deserialized)

	// Test with empty CoveragesInfo
	app.CoveragesInfo = &model.CoveragesInfo{
		PrimaryCoverages: []model.PrimaryCoverage{},
		Limits:           []model.Limit{},
		Deductibles:      []model.Deductible{},
	}
	serialized, err = appToDB(app)
	require.NoError(t, err)

	deserialized, err = appFromDB(serialized)
	require.NoError(t, err)

	assert.Equal(t, app, deserialized)
}

func TestBusinessAutoAppSerializationErrorCases(t *testing.T) {
	// Test nil application
	_, err := appToDB(nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "nil application")

	// Test nil database application
	_, err = appFromDB(nil)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "nil database application")
}

func TestBusinessAutoAppSerializationWithUSState(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name     string
		usState  us_states.USState
		expected us_states.USState
	}{
		{
			name:     "Texas",
			usState:  us_states.TX,
			expected: us_states.TX,
		},
		{
			name:     "California",
			usState:  us_states.CA,
			expected: us_states.CA,
		},
		{
			name:     "New York",
			usState:  us_states.NY,
			expected: us_states.NY,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			app := &model.BusinessAutoApp{
				ID:                     uuid.New(),
				EffectiveDurationStart: now,
				EffectiveDurationEnd:   now.AddDate(1, 0, 0),
				CompanyInfo: model.CompanyInfo{
					Name:                          "Test Company LLC",
					USState:                       tt.usState,
					PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationContractorTrucks),
				},
				ProducerID:        uuid.New(),
				CreatedBy:         uuid.New(),
				CreatedAt:         now,
				UpdatedAt:         now,
				UWState:           enums.UWStateCreated,
				AgencyID:          uuid.New(),
				SignaturePacketID: pointer_utils.ToPointer(uuid.New()),
			}

			// Test serialization
			serialized, err := appToDB(app)
			require.NoError(t, err)

			// Test deserialization
			deserialized, err := appFromDB(serialized)
			require.NoError(t, err)

			// Verify the state was correctly preserved
			require.NotNil(t, deserialized.CompanyInfo.USState, "USState should not be nil")
			assert.Equal(t, tt.expected, deserialized.CompanyInfo.USState)
			assert.Equal(t, app, deserialized)
		})
	}
}

func TestBusinessAutoAppSignaturePacketIDSerialization(t *testing.T) {
	now := time.Now()
	baseApp := &model.BusinessAutoApp{
		ID:                     uuid.New(),
		EffectiveDurationStart: now,
		EffectiveDurationEnd:   now.AddDate(1, 0, 0),
		CompanyInfo: model.CompanyInfo{
			Name:                          "Test Company LLC",
			USState:                       us_states.OH,
			PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationContractorTrucks),
		},
		VehiclesInfo:  &[]model.VehicleInfo{},
		CoveragesInfo: &model.CoveragesInfo{},
		ProducerID:    uuid.New(),
		CreatedBy:     uuid.New(),
		CreatedAt:     now,
		UpdatedAt:     now,
		UWState:       enums.UWStateCreated,
		AgencyID:      uuid.New(),
	}

	testCases := []struct {
		name                    string
		signaturePacketID       *uuid.UUID
		expectedDBValid         bool
		expectedDeserializedNil bool
	}{
		{
			name:                    "with nil SignaturePacketID",
			signaturePacketID:       nil,
			expectedDBValid:         false,
			expectedDeserializedNil: true,
		},
		{
			name:                    "with valid SignaturePacketID",
			signaturePacketID:       pointer_utils.ToPointer(uuid.New()),
			expectedDBValid:         true,
			expectedDeserializedNil: false,
		},
		{
			name:                    "with another valid SignaturePacketID",
			signaturePacketID:       pointer_utils.ToPointer(uuid.New()),
			expectedDBValid:         true,
			expectedDeserializedNil: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup test application
			app := *baseApp
			app.SignaturePacketID = tc.signaturePacketID

			// Test serialization to DB
			serialized, err := appToDB(&app)
			require.NoError(t, err)
			assert.Equal(t, tc.expectedDBValid, serialized.SignaturePacketID.Valid)

			if tc.signaturePacketID != nil {
				assert.Equal(t, tc.signaturePacketID.String(), serialized.SignaturePacketID.String)
			}

			// Test deserialization from DB
			deserialized, err := appFromDB(serialized)
			require.NoError(t, err)

			// Verify SignaturePacketID field specifically
			if tc.expectedDeserializedNil {
				assert.Nil(t, deserialized.SignaturePacketID)
			} else {
				require.NotNil(t, deserialized.SignaturePacketID)
				assert.Equal(t, *tc.signaturePacketID, *deserialized.SignaturePacketID)
			}

			// Verify entire object equality (roundtrip consistency)
			assert.Equal(t, &app, deserialized)
		})
	}
}

func TestBusinessAutoAppSerializationWithTelematicsInfo(t *testing.T) {
	// Test serialization with TelematicsInfo and TSPConnHandleId
	now := time.Now()
	tspConnHandleId := uuid.New()

	app := &model.BusinessAutoApp{
		ID:                     uuid.New(),
		EffectiveDurationStart: now,
		EffectiveDurationEnd:   now.AddDate(1, 0, 0),
		CompanyInfo: model.CompanyInfo{
			Name:                          "Test Company LLC",
			USState:                       us_states.OH,
			PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationContractorTrucks),
		},
		TelematicsInfo: &model.TelematicsInfo{
			FullName: "John Agent",
			Email:    "<EMAIL>",
		},
		TSPConnHandleId: &tspConnHandleId,
		ProducerID:      uuid.New(),
		CreatedBy:       uuid.New(),
		CreatedAt:       now,
		UpdatedAt:       now.Add(time.Hour),
		UWState:         enums.UWStateCreated,
		AgencyID:        uuid.New(),
	}

	// Test serialization to DB and back
	serialized, err := appToDB(app)
	require.NoError(t, err)

	deserialized, err := appFromDB(serialized)
	require.NoError(t, err)

	// Verify all fields match
	assert.Equal(t, app, deserialized)

	// Specifically verify telematics fields
	require.NotNil(t, deserialized.TelematicsInfo)
	assert.Equal(t, "John Agent", deserialized.TelematicsInfo.FullName)
	assert.Equal(t, "<EMAIL>", deserialized.TelematicsInfo.Email)

	require.NotNil(t, deserialized.TSPConnHandleId)
	assert.Equal(t, tspConnHandleId, *deserialized.TSPConnHandleId)

	// Test with nil TelematicsInfo and TSPConnHandleId
	app.TelematicsInfo = nil
	app.TSPConnHandleId = nil

	serialized, err = appToDB(app)
	require.NoError(t, err)

	deserialized, err = appFromDB(serialized)
	require.NoError(t, err)

	assert.Equal(t, app, deserialized)
	assert.Nil(t, deserialized.TelematicsInfo)
	assert.Nil(t, deserialized.TSPConnHandleId)
}

func TestBusinessAutoAppSerializationWithNilNewFields(t *testing.T) {
	// Test with nil SelectedQuotingPricingContextID and ModelPinConfig
	now := time.Now()
	usState, err := us_states.StrToUSState("TX")
	require.NoError(t, err)

	app := &model.BusinessAutoApp{
		ID:                     uuid.New(),
		EffectiveDurationStart: now,
		EffectiveDurationEnd:   now.AddDate(1, 0, 0),
		CompanyInfo: model.CompanyInfo{
			Name:                          "Test Company LLC",
			USState:                       usState,
			PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationContractorTrucks),
		},
		ProducerID:                      uuid.New(),
		CreatedBy:                       uuid.New(),
		CreatedAt:                       now,
		UpdatedAt:                       now,
		UWState:                         enums.UWStateCreated,
		AgencyID:                        uuid.New(),
		SelectedQuotingPricingContextID: nil,
		ModelPinConfig:                  nil,
	}

	// Test serialization to DB and back
	serialized, err := appToDB(app)
	require.NoError(t, err)

	// Verify nil fields are properly handled in DB
	assert.False(t, serialized.SelectedQuotingPricingContextID.Valid)
	assert.False(t, serialized.ModelPinConfig.Valid)

	deserialized, err := appFromDB(serialized)
	require.NoError(t, err)

	// Verify round-trip consistency
	assert.Equal(t, app, deserialized)
	assert.Nil(t, deserialized.SelectedQuotingPricingContextID)
	assert.Nil(t, deserialized.ModelPinConfig)
}
