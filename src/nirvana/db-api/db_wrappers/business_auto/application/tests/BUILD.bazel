load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "tests_test",
    srcs = ["wrapper_test.go"],
    deps = [
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/infra/fx/testfixtures/biz_auto_app_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/nirvanaapp/enums",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
