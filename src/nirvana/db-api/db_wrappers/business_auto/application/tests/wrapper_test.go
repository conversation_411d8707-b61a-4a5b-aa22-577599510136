package tests

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"nirvanatech.com/nirvana/business-auto/enums"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/biz_auto_app_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
)

type BusinessAutoApplicationWrapperTestSuite struct {
	suite.Suite
	ctx            context.Context
	fxapp          *fxtest.App
	appWrapper     application.Wrapper
	bizAutoFixture *biz_auto_app_fixture.BizAutoAppFixture
}

func TestBusinessAutoApplicationWrapperTestSuite(t *testing.T) {
	suite.Run(t, new(BusinessAutoApplicationWrapperTestSuite))
}

func (s *BusinessAutoApplicationWrapperTestSuite) SetupTest() {
	s.ctx = context.Background()
	var env struct {
		fx.In
		AppWrapper     application.Wrapper
		BizAutoFixture *biz_auto_app_fixture.BizAutoAppFixture
	}
	s.fxapp = testloader.RequireStart(s.T(), &env)
	s.appWrapper = env.AppWrapper
	s.bizAutoFixture = env.BizAutoFixture
}

func (s *BusinessAutoApplicationWrapperTestSuite) TearDownTest() {
	s.fxapp.RequireStop()
}

func (s *BusinessAutoApplicationWrapperTestSuite) TestInsert() {
	tests := []struct {
		name        string
		buildApp    func() *model.BusinessAutoApp
		expectError bool
	}{
		{
			name: "Basic application with required fields",
			buildApp: func() *model.BusinessAutoApp {
				return &model.BusinessAutoApp{
					ID:                     uuid.New(),
					ShortID:                short_id.ShortID("WRAPPER1"),
					EffectiveDurationStart: time.Now(),
					EffectiveDurationEnd:   time.Now().AddDate(1, 0, 0),
					CompanyInfo: model.CompanyInfo{
						Name:                          "Test Company",
						NoOfPowerUnits:                pointer_utils.ToPointer(int32(5)),
						HasIndividualNamedInsured:     pointer_utils.ToPointer(true),
						NoOfEmployees:                 pointer_utils.ToPointer(int32(10)),
						PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationWholesalersManufacturers),
						USState:                       us_states.OH,
					},
					VehiclesInfo: &[]model.VehicleInfo{
						{
							VIN:                              "1HGCM82633A123456",
							Year:                             2020,
							Make:                             "Ford",
							Model:                            "F-150",
							VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
							WeightClass:                      enums.WeightClassHeavy,
							VehicleUse:                       enums.VehicleUseTowingOperations,
							BusinessUse:                      enums.BusinessUseRetail,
							StateUsage:                       enums.StateUsageIntrastate,
							RadiusClassification:             enums.RadiusClassification0To100,
							PrincipalGaragingLocationZipCode: "12345",
						},
					},
					CoveragesInfo: &model.CoveragesInfo{
						PrimaryCoverages: []model.PrimaryCoverage{
							{
								ID: app_enums.CoverageAutoLiability,
								SubCoverageIDs: []app_enums.Coverage{
									app_enums.CoverageBodilyInjury,
									app_enums.CoveragePropertyDamage,
								},
							},
						},
						Limits: []model.Limit{
							{
								SubCoverageIDs: []app_enums.Coverage{
									app_enums.CoverageBodilyInjury,
									app_enums.CoveragePropertyDamage,
								},
								Amount:   1000000,
								Grouping: model.LimitGroupingCombined,
							},
						},
						Deductibles: []model.Deductible{
							{
								SubCoverageIDs: []app_enums.Coverage{
									app_enums.CoverageBodilyInjury,
									app_enums.CoveragePropertyDamage,
								},
								Amount: 0,
							},
						},
					},
					FilingsInfo: &model.FilingsInfo{
						HasMultiStateFilings: true,
					},
					ProducerID: uuid.New(),
					CreatedBy:  uuid.New(),
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					UWState:    enums.UWStateCreated,
				}
			},
			expectError: false,
		},
		{
			name: "Application with optional fields",
			buildApp: func() *model.BusinessAutoApp {
				return &model.BusinessAutoApp{
					ID:                     uuid.New(),
					ShortID:                short_id.ShortID("WRAPPER2"),
					EffectiveDurationStart: time.Now(),
					EffectiveDurationEnd:   time.Now().AddDate(1, 0, 0),
					CompanyInfo: model.CompanyInfo{
						Name:                          "Test Company",
						DOTNumber:                     pointer_utils.ToPointer(12345),
						FEIN:                          pointer_utils.ToPointer("*********"),
						NoOfPowerUnits:                pointer_utils.ToPointer(int32(5)),
						HasIndividualNamedInsured:     pointer_utils.ToPointer(true),
						NoOfEmployees:                 pointer_utils.ToPointer(int32(10)),
						PrimaryIndustryClassification: pointer_utils.ToPointer(enums.IndustryClassificationContractorTrucks),
						SecondaryIndustryClassifications: []enums.IndustryClassification{
							enums.IndustryClassificationWholesalersManufacturers,
						},
						USState: us_states.OH,
					},
					VehiclesInfo: &[]model.VehicleInfo{
						{
							VIN:                              "1HGCM82633A123456",
							Year:                             2020,
							Make:                             "Ford",
							Model:                            "F-150",
							VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
							WeightClass:                      enums.WeightClassLight,
							SpecialtyVehicleType:             enums.SpecialtyVehicleTypeCatererVehicles,
							VehicleUse:                       enums.VehicleUseTowingOperations,
							BusinessUse:                      enums.BusinessUseService,
							StateUsage:                       enums.StateUsageIntrastate,
							RadiusClassification:             enums.RadiusClassification0To100,
							PrincipalGaragingLocationZipCode: "12345",
							IsGlassLinedTankTruckOrTrailer:   pointer_utils.ToPointer(true),
							HasAntiLockBrakes:                pointer_utils.ToPointer(true),
						},
					},
					CoveragesInfo: &model.CoveragesInfo{
						PrimaryCoverages: []model.PrimaryCoverage{
							{
								ID: app_enums.CoverageAutoLiability,
								SubCoverageIDs: []app_enums.Coverage{
									app_enums.CoverageBodilyInjury,
									app_enums.CoveragePropertyDamage,
								},
							},
						},
						Limits: []model.Limit{
							{
								SubCoverageIDs: []app_enums.Coverage{
									app_enums.CoverageBodilyInjury,
									app_enums.CoveragePropertyDamage,
								},
								Amount:   1000000,
								Grouping: model.LimitGroupingCombined,
							},
						},
						Deductibles: []model.Deductible{
							{
								SubCoverageIDs: []app_enums.Coverage{
									app_enums.CoverageBodilyInjury,
									app_enums.CoveragePropertyDamage,
								},
								Amount: 0,
							},
						},
					},
					FilingsInfo: &model.FilingsInfo{
						HasMultiStateFilings:  true,
						HasSingleStateFilings: true,
						HasFMCSAFilings:       true,
						HasDOTFilings:         true,
					},
					ProducerID: uuid.New(),
					CreatedBy:  uuid.New(),
					CreatedAt:  time.Now(),
					UpdatedAt:  time.Now(),
					UWState:    enums.UWStateCreated,
				}
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			// Create a new business auto application
			app := tt.buildApp()

			// Insert the application
			err := s.appWrapper.Insert(s.ctx, app)

			if tt.expectError {
				s.Error(err)
			} else {
				s.NoError(err)
			}
		})
	}
}

func (s *BusinessAutoApplicationWrapperTestSuite) TestUpdateUWState() {
	// First insert the application into the database
	app := *s.bizAutoFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("UPDATESTATE1")
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	tests := []struct {
		name        string
		appID       uuid.UUID
		newState    enums.UWState
		expectError bool
	}{
		{
			name:        "Update to UnderReview",
			appID:       app.ID,
			newState:    enums.UWStateUnderReview,
			expectError: false,
		},
		{
			name:        "Update to QuoteGenerating",
			appID:       app.ID,
			newState:    enums.UWStateQuoteGenerating,
			expectError: false,
		},
		{
			name:        "Update to QuoteGenerated",
			appID:       app.ID,
			newState:    enums.UWStateQuoteGenerated,
			expectError: false,
		},
		{
			name:        "Update to Declined",
			appID:       app.ID,
			newState:    enums.UWStateDeclined,
			expectError: false,
		},
		{
			name:        "Update non-existent application",
			appID:       uuid.New(),
			newState:    enums.UWStateUnderReview,
			expectError: true,
		},
	}

	for _, tt := range tests {
		s.Run(tt.name, func() {
			// Update the UW state using UpdateApp
			err := s.appWrapper.UpdateApp(s.ctx, tt.appID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
				app.UWState = tt.newState
				app.UpdatedAt = time.Now()
				return app, nil
			})

			if tt.expectError {
				s.Error(err)
			} else {
				s.NoError(err)

				// Verify the state was actually updated
				getAppResp, err := s.appWrapper.GetByID(s.ctx, tt.appID)
				s.NoError(err)
				s.Equal(tt.newState, getAppResp.UWState)
			}
		})
	}
}

func (s *BusinessAutoApplicationWrapperTestSuite) TestUpdateApp() {
	// Test successful app update using updateFn
	originalApp := *s.bizAutoFixture.MinimalApp
	originalApp.ID = uuid.New()
	originalApp.ShortID = short_id.ShortID("UPDATEAPP1")
	err := s.appWrapper.Insert(s.ctx, &originalApp)
	s.NoError(err)
	newName := "Updated Test Company"

	// Update company name using updateFn
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		app.CompanyInfo.Name = newName
		return app, nil
	})
	s.NoError(err)

	// Verify update was applied by fetching and checking state
	// (We can verify through state since we already have GetUWState method)
	// For a more complete test, we would need a GetApp method, but this demonstrates the updateFn pattern
	app, err := s.appWrapper.GetByID(s.ctx, originalApp.ID)
	s.NoError(err)
	s.Equal(enums.UWStateCreated, app.UWState) // State should remain unchanged

	// Test error handling in updateFn
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		return nil, errors.New("intentional error in updateFn")
	})
	s.Error(err)
	s.Contains(err.Error(), "intentional error in updateFn")

	// Test updating non-existent application
	nonExistentID := uuid.New()
	err = s.appWrapper.UpdateApp(s.ctx, nonExistentID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		return app, nil
	})
	s.Error(err)
}

func (s *BusinessAutoApplicationWrapperTestSuite) TestGetAppByShortID() {
	// Insert the minimal app from fixture
	app := *s.bizAutoFixture.MinimalApp
	app.ID = uuid.New()
	app.ShortID = short_id.ShortID("GETBYSHORT1")
	err := s.appWrapper.Insert(s.ctx, &app)
	s.Require().NoError(err)

	// Test getting application by ShortID
	retrievedApp, err := s.appWrapper.GetAppByShortID(s.ctx, string(app.ShortID))
	s.NoError(err)
	s.NotNil(retrievedApp)
	s.Equal(app.ID, retrievedApp.ID)
	s.Equal(app.ShortID, retrievedApp.ShortID)
	s.Equal(app.CompanyInfo.Name, retrievedApp.CompanyInfo.Name)

	// Test getting application with non-existent ShortID
	_, err = s.appWrapper.GetAppByShortID(s.ctx, "NONEXISTENT")
	s.Error(err)
	s.Contains(err.Error(), "failed to get application by short id")
}

func (s *BusinessAutoApplicationWrapperTestSuite) TestUpdateApp_TelematicsInfo() {
	// Test updating telematics info and TSP connection handle ID
	originalApp := *s.bizAutoFixture.MinimalApp
	originalApp.ID = uuid.New()
	originalApp.ShortID = short_id.ShortID("TELEINFO1")
	err := s.appWrapper.Insert(s.ctx, &originalApp)
	s.NoError(err)

	// Initially, telematics info should be nil
	s.Nil(originalApp.TelematicsInfo)
	s.Nil(originalApp.TSPConnHandleId)

	// Generate new values
	newTSPHandleId := uuid.New()
	newTelematicsInfo := &model.TelematicsInfo{
		FullName: "Jane Agent",
		Email:    "<EMAIL>",
	}

	// Update telematics info using UpdateApp
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		app.TelematicsInfo = newTelematicsInfo
		app.TSPConnHandleId = &newTSPHandleId
		return app, nil
	})
	s.NoError(err)

	// Verify the update by fetching the app and checking the fields
	// Note: We would normally use a Get method here, but since it's not exposed in the test suite,
	// we'll verify by doing another update and checking the values passed to the update function
	var capturedApp *model.BusinessAutoApp
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		capturedApp = app
		return app, nil
	})
	s.NoError(err)

	// Verify the telematics info was properly persisted
	s.Require().NotNil(capturedApp.TelematicsInfo)
	s.Equal("Jane Agent", capturedApp.TelematicsInfo.FullName)
	s.Equal("<EMAIL>", capturedApp.TelematicsInfo.Email)

	s.Require().NotNil(capturedApp.TSPConnHandleId)
	s.Equal(newTSPHandleId, *capturedApp.TSPConnHandleId)

	// Test updating to different values
	updatedTSPHandleId := uuid.New()
	updatedTelematicsInfo := &model.TelematicsInfo{
		FullName: "Bob Agent",
		Email:    "<EMAIL>",
	}

	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		app.TelematicsInfo = updatedTelematicsInfo
		app.TSPConnHandleId = &updatedTSPHandleId
		return app, nil
	})
	s.NoError(err)

	// Verify the updated values
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		capturedApp = app
		return app, nil
	})
	s.NoError(err)

	s.Require().NotNil(capturedApp.TelematicsInfo)
	s.Equal("Bob Agent", capturedApp.TelematicsInfo.FullName)
	s.Equal("<EMAIL>", capturedApp.TelematicsInfo.Email)

	s.Require().NotNil(capturedApp.TSPConnHandleId)
	s.Equal(updatedTSPHandleId, *capturedApp.TSPConnHandleId)

	// Test setting telematics info back to nil
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		app.TelematicsInfo = nil
		app.TSPConnHandleId = nil
		return app, nil
	})
	s.NoError(err)

	// Verify the values are nil
	err = s.appWrapper.UpdateApp(s.ctx, originalApp.ID, func(app *model.BusinessAutoApp) (*model.BusinessAutoApp, error) {
		capturedApp = app
		return app, nil
	})
	s.NoError(err)

	s.Nil(capturedApp.TelematicsInfo)
	s.Nil(capturedApp.TSPConnHandleId)
}
