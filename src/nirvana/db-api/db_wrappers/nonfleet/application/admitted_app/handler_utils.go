package admitted_app

import (
	"context"

	"nirvanatech.com/nirvana/db-api/db_wrappers/sharing"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/oapi-codegen/runtime/types"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
)

func (a *AdmittedApp) RestToGo(
	applicationUpdateForm *nonfleet.ApplicationUpdateForm,
	info nf_app.AppInfo,
) (nf_app.AppInfo, error) {
	if applicationUpdateForm == nil || applicationUpdateForm.Admitted == nil {
		return a, nil
	}

	var err error
	err = bindOperationsFormRestToDB(applicationUpdateForm.Admitted.OperationsForm, a)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind operations form from REST To DB")
	}

	err = bindEquipmentsFormRestToDB(applicationUpdateForm.Admitted.EquipmentsForm, a)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind equipments form from REST To DB")
	}

	err = bindDriversFormRestToDB(applicationUpdateForm.Admitted.DriversForm, a)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind drivers form from REST To DB")
	}

	if applicationUpdateForm.Admitted.IndicationForm != nil {
		// Used to update limits & deductibles of Primary Covs
		err = bindCoveragesFormRestToDB(applicationUpdateForm.Admitted.IndicationForm.Coverages, a)
		if err != nil {
			return nil, errors.Wrap(err, "couldn't bind coverages form from REST To DB")
		}
	}

	return a, nil
}

func (a *AdmittedApp) GoToRest(
	ctx context.Context,
	authWrapper auth.DataWrapper,
	sharingWrapper sharing.DataWrapper,
	appData nf_app.AppData,
) (*nonfleet.ApplicationDetails, error) {
	if a == nil {
		// Empty Details
		return &nonfleet.ApplicationDetails{}, nil
	}

	operationsForm, err := bindOperationsFormDBToRest(authWrapper, a, appData)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind operations form from DB To Test")
	}

	equipmentsForm, err := bindEquipmentsFormDBToRest(a)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind equipments form from DB To Rest")
	}

	driversForm, err := bindDriversFormDBToRest(a)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind drivers form from DB To Rest")
	}

	indicationForm, err := bindIndicationFormFromDBToRest(ctx, sharingWrapper, a, appData)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't bind indication form from DB To Rest")
	}

	var underwriterName *string
	var underwriterEmail *types.Email

	// underwriters are assigned asynchronously after the application created event is emitted.
	// therefore this ID can be nil and it is important to make sure that we handle that scenario.
	if appData.UnderwriterID != uuid.Nil {
		underwriter, err := authWrapper.FetchAuthzUser(ctx, appData.UnderwriterID)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to fetch underwriter %s", appData.UnderwriterID)
		}
		underwriterName = pointer_utils.ToPointer(underwriter.FullName())
		underwriterEmail = pointer_utils.ToPointer(types.Email(underwriter.Email))
	}

	declineReason := appData.DeclineReason
	// If the application was submitted to UWs & they declined it, instead of
	// showing their reason, we show a generic message
	if appData.UWSubmissionID != nil && *appData.UWSubmissionID != uuid.Nil {
		declineReason = "Declined due to underwriting reasons"
	}

	var preTelematicsQuoteState nonfleet.PreTelematicsQuoteState
	if appData.PreTelematicsQuoteState != nil {
		preTelematicsQuoteState = ConvertToPreTelematicsQuoteStateRest(*appData.PreTelematicsQuoteState)
	}

	isExpressLaneApplication := appData.ExpressLaneMetadata.IsExpressLaneApplication()

	isOnNewCreditModel := model.IsNirvanaCreditModel(a.ModelPinConfigInfo.RateML.Version)
	return &nonfleet.ApplicationDetails{
		ApplicationID: pointer_utils.String(appData.AppID.String()),
		PageState:     pointer_utils.String(appData.PageState.String()),
		ShortID:       pointer_utils.String(string(appData.ShortID)),
		AppStatus:     pointer_utils.String(appData.State.String()),
		DeclineReason: pointer_utils.String(declineReason),
		ProgramType:   pointer_utils.ToPointer(nonfleet.ProgramTypeNonFleetAdmitted),
		Admitted: &nonfleet.AdmittedApp{
			OperationsForm: operationsForm,
			EquipmentsForm: equipmentsForm,
			DriversForm:    driversForm,
			IndicationForm: indicationForm,
		},
		UnderwriterName:    underwriterName,
		UnderwriterEmail:   underwriterEmail,
		IsOnNewCreditScore: pointer_utils.ToPointer(isOnNewCreditModel),
		RenewalMetadata:    appData.RenewalMetadata,
		IsEligibleForPreTelematicsQuoteExperiment: appData.IsEligibleForPreQuoteTelematicsExperiment,
		PreTelematicsQuoteState:                   pointer_utils.ToPointer(preTelematicsQuoteState),
		IsExpressLaneApplication:                  pointer_utils.Bool(isExpressLaneApplication),
	}, nil
}
