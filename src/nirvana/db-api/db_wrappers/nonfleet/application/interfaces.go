package application

import (
	"context"
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_models/non_fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	admittedapp_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/common"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/sharing"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	oapi_uw "nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
	"nirvanatech.com/nirvana/telematics"
)

type AppInfo interface {
	CoverageFuncs
	CompanyFuncs
	EntityFuncs
	SerdeFuncs
	ProgramFuncs
	LossFuncs
	TSPFuncs
	DriverFuncs
	EquipmentFuncs
	OperationFuncs
	UWFuncs
	ClosureInfoFuncs
	ScraperInfoFuncs
	ModelPinConfigInfoFuncs
}

type AppData struct {
	AppID                                     uuid.UUID
	PageState                                 enums.PageState
	ShortID                                   short_id.ShortID
	State                                     enums.AppState
	DeclineReason                             string
	ProducerID                                uuid.UUID
	SelectedIndication                        *IndicationOption
	UnderwriterID                             uuid.UUID
	UWSubmissionID                            *uuid.UUID
	RenewalMetadata                           *nonfleet.RenewalMetadata
	SelectedQuotingPricingContext             *quoting.PricingContext
	IsEligibleForPreQuoteTelematicsExperiment *bool
	PreTelematicsQuoteState                   *enums.PreTelematicsQuoteState
	ExpressLaneMetadata                       *ExpressLaneMetadata
}

type UWFuncs interface {
	// SetIndicationPricingOverrides sets the pricing overrides for the application
	// at Indication
	// For Ex: 5% Safety Discount for Admitted Apps
	SetIndicationPricingOverrides(
		ctx context.Context,
		authzUser authz.User,
		ffClient feature_flag_lib.Client,
	) error
}

type ClosureInfoFuncs interface {
	GetClosureInfo() ClosureInfo
}

type ModelPinConfigInfoFuncs interface {
	GetModelPinConfigInfo() ModelPinConfigInfo
	SetModelPinConfigInfo(modelPinConfigInfo ModelPinConfigInfo)
}

type ScraperInfoFuncs interface {
	GetScraperInfo() common.ScraperInfo
	SetScraperInfo(scraperInfo common.ScraperInfo)
}

type ProgramFuncs interface {
	GetProgramType() policy_enums.ProgramType
}

type TSPFuncs interface {
	GetTSPConnHandleID() *uuid.UUID
	GetTSPName() *telematics.TSP
}

// SerdeFuncs help us to transform the AppInfo back & forth in handlers
type SerdeFuncs interface {
	// GoToRest takes the generic Application struct and returns the OpenAPI REST Struct
	GoToRest(
		ctx context.Context,
		authWrapper auth.DataWrapper,
		sharingWrapper sharing.DataWrapper,
		appData AppData,
	) (*nonfleet.ApplicationDetails, error)

	// RestToGo takes the OpenAPI REST Struct and the generic Application Struct pointer
	// and then populates the Application Struct correctly
	RestToGo(
		applicationUpdateForm *nonfleet.ApplicationUpdateForm,
		appObj AppInfo,
	) (AppInfo, error)
}

type CoverageFuncs interface {
	GetEffectiveDates() *EffectiveDates
	GetQuoteOptions() *[]nonfleet.CoverageOption
	GetRequiredCoverages() []CoverageDetails
	GetCoverageInfo() *CoverageInfo
	GetQuoteMetadata() *nonfleet.CoverageOptionMetadata
	SetAncillaryCoverages(coverages []CoverageDetails)
	IsAPDMTCDeductibleCombined() bool
}

type CompanyFuncs interface {
	GetDot() int64
	GetState() us_states.USState
	GetCompanyName() string
	GetAddress() Address
	GetMailingAddress() Address
	GetPowerUnitCount() int
	GetActualPowerUnitCount() int
	GetProgramType() policy_enums.ProgramType
	GetTerminalLocationOAPI() *nonfleet.AdmittedAppTerminalLocation
	GetBusinessOwnerDetails() *nonfleet.AdmittedAppBusinessOwner
	GetEncryptedSSN() []byte
	GetEncryptedSSNLastFour() []byte
}

type EquipmentFuncs interface {
	GetEquipmentInfo() EquipmentInfo
}

type DriverFuncs interface {
	GetDriverDetails() []DriverDetails
	GetExcludedDrivers() []string
}

type EntityFuncs interface {
	GetEntities() []enums.InfoEntity
	UnmarshallEntity(enums.InfoEntity, []byte) error
	MarshallEntity(enums.InfoEntity) ([]byte, error)
}

type LossFuncs interface {
	GetLossRunFiles() []application.FileMetadata
	GetLossPanelInfo() oapi_uw.ApplicationReviewLosses
}

type OperationFuncs interface {
	GetOperationsCommodities() []oapi_uw.OperationsCommodity
	GetOperationsAdditionalInformation() oapi_uw.OperationsAdditionalInformation
	HasReeferOperation() bool
	HasHiredAuto() bool
}

type AppInfoWrapped[T any] interface {
	AppInfo
	*T
}

type AppUpdateFn[T AppInfo] func(Application[T]) (Application[T], error)

type SubmissionInsertFn[T AppInfo] func(submission Submission[T], app Application[T]) (Submission[T], Application[T], error)

type SubmissionUpdateFn[T AppInfo] func(object Submission[T]) (*Submission[T], error)

type Cursor struct {
	UpdatedAt time.Time
	AppID     string
}

type Wrapper[T AppInfo] interface {
	// InsertApp persists a new application. If another application with the
	// same id already exists, this will throw an error
	InsertApp(ctx context.Context, app Application[T]) error

	// UpdateApp offers serializable transaction guarantees.
	UpdateApp(ctx context.Context, appID uuid.UUID, updateFn AppUpdateFn[T]) error

	// GetAppById fetches details about an application
	GetAppById(ctx context.Context, id uuid.UUID) (*Application[T], error)

	GetAllApplications(ctx context.Context) ([]Application[T], error)

	GetAllApplicationsByIDs(ctx context.Context, ids []uuid.UUID) ([]Application[T], error)

	GetAllApplicationsByAgencyID(ctx context.Context, agencyID uuid.UUID) ([]Application[T], error)

	GetAllApplicationsByDotNumber(ctx context.Context, dotNumber int64) ([]Application[AppInfo], error)

	// GetSubmissionById fetches the submission identified by submissionId.
	GetSubmissionById(ctx context.Context, submissionId uuid.UUID) (*Submission[T], error)

	// GetSubmissionsByAppId fetches the submission identified by appId.
	GetSubmissionsByAppId(ctx context.Context, appId uuid.UUID) ([]Submission[T], error)

	InsertSubmission(ctx context.Context, submission Submission[T]) error

	UpdateSubmission(ctx context.Context, subId uuid.UUID, updateFn SubmissionUpdateFn[T]) error

	// InsertSubmissionFromApplicationSnapshot creates a new submission with the
	// provided submissionId by creating a snapshot of the application
	// identified by appId.
	//
	// submissionFn can be used to update both the application and submission
	// before persisting it. submissionFn should be idempotent, since it can be
	// retried if there are concurrent updates.
	//
	// InsertSubmissionFromApplicationSnapshot offers serializable transaction
	// guarantees.
	InsertSubmissionFromApplicationSnapshot(
		ctx context.Context,
		appID uuid.UUID,
		submissionID uuid.UUID,
		bindable bool,
		submissionFn SubmissionInsertFn[T],
	) error

	InsertIndOption(ctx context.Context, indOpt IndicationOption) error

	GetIndOptionById(ctx context.Context, indOptId uuid.UUID) (IndicationOption, error)

	GetIndOptionByPackageType(ctx context.Context, subObj Submission[T], packageType app_enums.IndicationOptionTag) (IndicationOption, error)

	// SetSelectedIndIDOnSub sets the selected indication id for a submission with the indication id passed
	SetSelectedIndIDOnSub(ctx context.Context, subID, indID uuid.UUID) error

	// GetApplicationByTSPConnHandleId fetches an application by TSPConnHandleId
	// TODO: Use `GetAllApplicationsByConnHandleId` instead. Since this method only fetches one
	//  application with the given handle id, whereas there can be multiple applications with the same
	//  handle id.
	GetApplicationByTSPConnHandleId(ctx context.Context, tspConnHandleId uuid.UUID) (*Application[T], error)

	// GetAllApplicationsByTSPConnHandleId fetches a list of applications for a TSPConnHandleId.
	GetAllApplicationsByTSPConnHandleId(ctx context.Context, tspConnHandleId uuid.UUID) ([]Application[T], error)

	GetApplicationByShortID(ctx context.Context, shortID short_id.ShortID) (*Application[AppInfo], error)

	// GetApplicationByProducerIdDotNumberAndAgencyID fetches an application for a producer id and dot number and agency id
	GetApplicationByProducerIdDotNumberAndAgencyID(ctx context.Context, producerId string, dotNumber int, agencyId string) (*Application[T], error)

	GetPaginatedApplications(
		ctx context.Context,
		options Options,
		cursor *Cursor,
	) ([]Application[T], error)

	GetApplicationsCount(ctx context.Context, agencyId *uuid.UUID) (int64, error)

	UpdateIndicationOption(
		ctx context.Context,
		indOpt IndicationOption,
	) error

	UpdateBDForApps(ctx context.Context, agencyId, assigedBd string, validStates []string) error

	// GetRenewalAppForOriginalAppID fetches the duplication renewal application for the incumbent application
	GetRenewalAppForOriginalAppID(ctx context.Context, incumbentAppId uuid.UUID) (*Application[T], error)

	// GetAllApplicationsByPreTelematicsQuoteState - This does not have an index on preTelematicsQuoteState, but considering
	// it's for an experiment and not a common use case, we can afford to do it.
	GetAllApplicationsByPreTelematicsQuoteState(ctx context.Context, preTelematicsQuoteState enums.PreTelematicsQuoteState, validAppStates []string) ([]Application[T], error)
	UpdatePreTelematicsQuoteStateForApps(ctx context.Context, appIds, validAppStates []string, preTelematicsQuoteState enums.PreTelematicsQuoteState) error

	quoting.PricingWrapper
}

type IndicationOption interface {
	GetID() string
	GetTotalPremium() int32
	GetSubTotalPolicyPremium() int32
	GetPremiumPerUnit() int32
	GetCoverages() []CoverageDetails
	GetTIV() int32
	GetApdPTiv() float64
	GetFlatCharges() int32
	GetSafetyDiscountPremium() int32
	GetTotalTrailerUnits() int32
	GetTotalPowerUnits() int32
	IndicationToDB() (*non_fleet.IndicationOption, error)
	GetProgram() policy_enums.ProgramType
	GetPackage() app_enums.IndicationOptionTag
	GetStateSurchargePremium() int32
	GetSafetyDiscountPercentage() float64
	GetVehiclesComputation() map[string]RMLVehicle
	GetCompanyComputation() RMLCompany
	GetTotalComprehensivePremium() (float64, error)
	GetTotalCollisionPremium() (float64, error)
	SetCoverageDetails(coverages []CoverageDetails)
	GetCreditScore() *admittedapp_enums.CreditScore
	GetSurplusTaxInfo() *SurplusTaxInfo
}
