package application

import (
	"encoding/json"
	"strings"

	"nirvanatech.com/nirvana/policy_common/constants"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/db-api/db_models/non_fleet"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
)

type AppConstructor[T AppInfo] func() *Application[T]

func appInfoFromDB[T any, PT AppInfoWrapped[T]](appDB *non_fleet.Application) (PT, error) {
	appInfoObj := PT(new(T))
	var err error
	for _, entity := range appInfoObj.GetEntities() {
		switch entity {
		case enums.CompanyInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.CompanyInfo.JSON)
		case enums.CoverageInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.CoverageInfo.JSON)
		case enums.LossInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.LossInfo.JSON)
		case enums.ClassInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.ClassInfo.JSON)
		case enums.CommodityInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.CommodityInfo.JSON)
		case enums.EquipmentInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.EquipmentInfo.JSON)
		case enums.DriverInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.DriverInfo.JSON)
		case enums.FinancialInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.FinancialInfo.JSON)
		case enums.TSPInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.TSPInfo.JSON)
		case enums.ClosureInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.ClosureInfo.JSON)
		case enums.ScraperInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.ScraperInfo.JSON)
		case enums.UnderwriterInfo:
			// Underwriter info is only required in submission
		case enums.ModelPinConfigInfo:
			err = appInfoObj.UnmarshallEntity(entity, appDB.ModelPinConfig.JSON)
		default:
			err = errors.Newf("Entity handler unimplemented for: %s", entity.String())
		}
		if err != nil {
			return nil, errors.Wrap(err, "couldn't unmarshall entities in admitted app")
		}
	}
	return appInfoObj, nil
}

// AppFromDB deserializes the application object from DB to Go Struct
func AppFromDB[T any, PT AppInfoWrapped[T]](appDB *non_fleet.Application) (
	*Application[PT], error,
) {
	var appObj Application[PT]

	appID, err := uuid.Parse(appDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse appID")
	}
	appObj.ID = appID

	appObj.DeclineReason = appDB.DeclineReason.String

	producerID, err := uuid.Parse(appDB.ProducerID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse producerID")
	}
	appObj.ProducerID = producerID

	underwriterID, err := uuid.Parse(appDB.UnderwriterID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse underwriterID")
	}
	appObj.UnderwriterID = underwriterID

	agencyID, err := uuid.Parse(appDB.AgencyID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse agencyID")
	}
	appObj.AgencyID = agencyID

	appObj.ShortID = short_id.ShortID(appDB.ShortID)

	createdBy, err := uuid.Parse(appDB.CreatedBy.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse createdBy")
	}
	appObj.CreatedBy = createdBy

	marketerId, err := uuid.Parse(appDB.MarketerID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse marketerId")
	}
	appObj.MarketerId = marketerId

	if appDB.EffectiveDate.Valid {
		appObj.EffectiveDate = appDB.EffectiveDate.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse effective date")
	}

	if appDB.EffectiveDateTo.Valid {
		appObj.EffectiveDateTo = appDB.EffectiveDateTo.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse effective date to")
	}

	quoteSubmissionID, err := uuid.Parse(appDB.QuoteSubmissionID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse quote submission id")
	}
	appObj.QuoteSubmissionID = quoteSubmissionID

	appObj.UwSubmissionID = nil
	if appDB.UwSubmissionID.Valid {
		uwSubmissionID, err := uuid.Parse(appDB.UwSubmissionID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse uw submission id")
		}
		appObj.UwSubmissionID = pointer_utils.UUID(uwSubmissionID)
	}

	bindableSubmissionID, err := uuid.Parse(appDB.BindableSubmissionID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse bindable submission id")
	}
	appObj.BindableSubmissionID = bindableSubmissionID

	if appDB.ProgramType.Valid {
		programType, err := policy_enums.ProgramTypeString(appDB.ProgramType.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse app program type")
		}
		appObj.ProgramType = programType
	}

	state, err := enums.AppStateString(appDB.State.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse app state")
	}
	appObj.State = state

	pageState, err := enums.PageStateString(appDB.PageState.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse page state")
	}
	appObj.PageState = pageState

	if appDB.CreatedAt.Valid {
		appObj.CreatedAt = appDB.CreatedAt.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse createdAt")
	}

	if appDB.UpdatedAt.Valid {
		appObj.UpdatedAt = appDB.UpdatedAt.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse updatedAt")
	}

	// State Metadata
	err = json.Unmarshal(appDB.StateMetadata.JSON, &appObj.StateMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall state metadata")
	}

	var dataContextId *uuid.UUID
	if appDB.DataContextID.Valid {
		id, err := uuid.Parse(appDB.DataContextID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse data context id")
		}
		dataContextId = pointer_utils.ToPointer(id)
	}
	appObj.DataContextId = dataContextId

	if appDB.AssignedBD.Valid {
		assignedBD, err := uuid.Parse(appDB.AssignedBD.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse assigned BD")
		}
		appObj.AssignedBD = &assignedBD

	}

	if appDB.FilingType.Valid {
		filingType, err := constants.FilingTypeString(appDB.FilingType.String)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to parse filing type %s", appDB.FilingType.String)
		}
		appObj.FilingType = pointer_utils.ToPointer(filingType)
	}

	if appDB.InsuranceCarrier.Valid {
		appObj.InsuranceCarrier = pointer_utils.ToPointer(constants.GetInsuranceCarrier(appDB.InsuranceCarrier.String))
	}

	if appDB.RenewalMetadata.Valid {
		err = json.Unmarshal(appDB.RenewalMetadata.JSON, &appObj.RenewalMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall renewal metadata")
		}
	}

	// There is a unique bug, where "null" string is not being able to be parsed as a null value while fetching from database
	// thus this fails, this is a temporary fix to handle this. The ideal should be to fix the database to store null values as null values.
	if appDB.PreTelematicsQuoteState.Valid && strings.ToLower(appDB.PreTelematicsQuoteState.String) != "null" {
		preTelematicsQuoteState, err := enums.PreTelematicsQuoteStateString(appDB.PreTelematicsQuoteState.String)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to pre telematics quote state type %s", appDB.PreTelematicsQuoteState.String)
		}
		appObj.PreTelematicsQuoteState = pointer_utils.ToPointer(preTelematicsQuoteState)
	}

	if appDB.ExpressLaneMetadata.Valid {
		err = json.Unmarshal(appDB.ExpressLaneMetadata.JSON, &appObj.ExpressLaneMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall express lane metadata")
		}
	}

	appObj.Info, err = appInfoFromDB[T, PT](appDB)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall appInfo")
	}
	return &appObj, nil
}

func GenericAppFromDB[T any, PT AppInfoWrapped[T]](appDB *non_fleet.Application) (
	*Application[AppInfo], error,
) {
	var appObj Application[AppInfo]

	appID, err := uuid.Parse(appDB.ID)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse appID")
	}
	appObj.ID = appID

	appObj.DeclineReason = appDB.DeclineReason.String

	producerID, err := uuid.Parse(appDB.ProducerID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse producerID")
	}
	appObj.ProducerID = producerID

	underwriterID, err := uuid.Parse(appDB.UnderwriterID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse underwriterID")
	}
	appObj.UnderwriterID = underwriterID

	agencyID, err := uuid.Parse(appDB.AgencyID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse agencyID")
	}
	appObj.AgencyID = agencyID

	appObj.ShortID = short_id.ShortID(appDB.ShortID)

	createdBy, err := uuid.Parse(appDB.CreatedBy.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse createdBy")
	}
	appObj.CreatedBy = createdBy

	marketerId, err := uuid.Parse(appDB.MarketerID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse marketerId")
	}
	appObj.MarketerId = marketerId

	if appDB.EffectiveDate.Valid {
		appObj.EffectiveDate = appDB.EffectiveDate.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse effective date")
	}

	if appDB.EffectiveDateTo.Valid {
		appObj.EffectiveDateTo = appDB.EffectiveDateTo.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse effective date to")
	}

	quoteSubmissionID, err := uuid.Parse(appDB.QuoteSubmissionID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse quote submission id")
	}
	appObj.QuoteSubmissionID = quoteSubmissionID

	bindableSubmissionID, err := uuid.Parse(appDB.BindableSubmissionID.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse bindable submission id")
	}
	appObj.BindableSubmissionID = bindableSubmissionID

	if appDB.ProgramType.Valid {
		programType, err := policy_enums.ProgramTypeString(appDB.ProgramType.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse app program type")
		}
		appObj.ProgramType = programType
	}

	state, err := enums.AppStateString(appDB.State.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse app state")
	}
	appObj.State = state

	pageState, err := enums.PageStateString(appDB.PageState.String)
	if err != nil {
		return nil, errors.Wrap(err, "unable to parse page state")
	}
	appObj.PageState = pageState

	if appDB.CreatedAt.Valid {
		appObj.CreatedAt = appDB.CreatedAt.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse createdAt")
	}

	if appDB.UpdatedAt.Valid {
		appObj.UpdatedAt = appDB.UpdatedAt.Time
	} else {
		return nil, errors.Wrap(err, "unable to parse updatedAt")
	}

	// State Metadata
	err = json.Unmarshal(appDB.StateMetadata.JSON, &appObj.StateMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall state metadata")
	}

	if appDB.RenewalMetadata.Valid {
		err = json.Unmarshal(appDB.RenewalMetadata.JSON, &appObj.RenewalMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall renewal metadata")
		}
	}

	if appDB.ExpressLaneMetadata.Valid {
		err = json.Unmarshal(appDB.ExpressLaneMetadata.JSON, &appObj.ExpressLaneMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to unmarshall express lane metadata")
		}
	}

	var dataContextId *uuid.UUID
	if appDB.DataContextID.Valid {
		id, err := uuid.Parse(appDB.DataContextID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse data context id")
		}
		dataContextId = pointer_utils.ToPointer(id)
	}
	appObj.DataContextId = dataContextId
	appObj.Info, err = appInfoFromDB[T, PT](appDB)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshall appInfo")
	}
	return &appObj, nil
}

// AppToDB serializes the application object from Go Struct to DB object
func AppToDB[T AppInfo](appObj Application[T]) (*non_fleet.Application, error) {
	stateMetadata, err := json.Marshal(appObj.StateMetadata)
	if err != nil {
		return nil, errors.Wrap(err, "unable to marshal state metadata")
	}

	var renewalMetadata null.JSON
	if appObj.RenewalMetadata != nil {
		marshaledData, err := json.Marshal(appObj.RenewalMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to marshal renewal metadata")
		}
		renewalMetadata = null.JSONFrom(marshaledData)
	}

	var expressLaneMetadata null.JSON
	if appObj.ExpressLaneMetadata != nil {
		marshaledData, err := json.Marshal(appObj.ExpressLaneMetadata)
		if err != nil {
			return nil, errors.Wrap(err, "unable to marshal express lane metadata")
		}
		expressLaneMetadata = null.JSONFrom(marshaledData)
	}

	uwSubID := uuid.Nil.String()
	if appObj.UwSubmissionID != nil {
		uwSubID = appObj.UwSubmissionID.String()
	}

	var dataContextId null.String
	if appObj.DataContextId != nil {
		dataContextId = null.StringFrom(appObj.DataContextId.String())
	}

	var assignedBD null.String
	if appObj.AssignedBD != nil {
		assignedBD = null.StringFrom(appObj.AssignedBD.String())
	}

	appDb := non_fleet.Application{
		ID:                   appObj.ID.String(),
		DeclineReason:        null.StringFrom(appObj.DeclineReason),
		ProducerID:           null.StringFrom(appObj.ProducerID.String()),
		EffectiveDate:        null.TimeFrom(appObj.EffectiveDate),
		State:                null.StringFrom(appObj.State.String()),
		PageState:            null.StringFrom(appObj.PageState.String()),
		CreatedBy:            null.StringFrom(appObj.CreatedBy.String()),
		CreatedAt:            null.TimeFrom(appObj.CreatedAt),
		UpdatedAt:            null.TimeFrom(appObj.UpdatedAt),
		ProgramType:          null.StringFrom(appObj.ProgramType.String()),
		AgencyID:             null.StringFrom(appObj.AgencyID.String()),
		UwSubmissionID:       null.StringFrom(uwSubID),
		QuoteSubmissionID:    null.StringFrom(appObj.QuoteSubmissionID.String()),
		BindableSubmissionID: null.StringFrom(appObj.BindableSubmissionID.String()),
		ShortID:              string(appObj.ShortID),
		EffectiveDateTo:      null.TimeFrom(appObj.EffectiveDateTo),
		UnderwriterID:        null.StringFrom(appObj.UnderwriterID.String()),
		StateMetadata:        null.JSONFrom(stateMetadata),
		MarketerID:           null.StringFrom(appObj.MarketerId.String()),
		DataContextID:        dataContextId,
		AssignedBD:           assignedBD,
		RenewalMetadata:      renewalMetadata,
		ExpressLaneMetadata:  expressLaneMetadata,
	}

	if appObj.FilingType != nil {
		appDb.FilingType = null.StringFrom(appObj.FilingType.String())
	}

	if appObj.PreTelematicsQuoteState != nil {
		appDb.PreTelematicsQuoteState = null.StringFrom(appObj.PreTelematicsQuoteState.String())
	}

	if appObj.InsuranceCarrier != nil {
		appDb.InsuranceCarrier = null.StringFrom(appObj.InsuranceCarrier.String())
	}

	infoPtr := appObj.Info
	for _, entity := range infoPtr.GetEntities() {
		bytes, err := infoPtr.MarshallEntity(entity)
		if err != nil {
			return nil, err
		}
		switch entity {
		case enums.CompanyInfo:
			appDb.CompanyInfo = null.JSONFrom(bytes)
		case enums.CoverageInfo:
			appDb.CoverageInfo = null.JSONFrom(bytes)
		case enums.LossInfo:
			appDb.LossInfo = null.JSONFrom(bytes)
		case enums.ClassInfo:
			appDb.ClassInfo = null.JSONFrom(bytes)
		case enums.CommodityInfo:
			appDb.CommodityInfo = null.JSONFrom(bytes)
		case enums.EquipmentInfo:
			appDb.EquipmentInfo = null.JSONFrom(bytes)
		case enums.DriverInfo:
			appDb.DriverInfo = null.JSONFrom(bytes)
		case enums.FinancialInfo:
			appDb.FinancialInfo = null.JSONFrom(bytes)
		case enums.TSPInfo:
			appDb.TSPInfo = null.JSONFrom(bytes)
		case enums.ClosureInfo:
			appDb.ClosureInfo = null.JSONFrom(bytes)
		case enums.ScraperInfo:
			appDb.ScraperInfo = null.JSONFrom(bytes)
		case enums.UnderwriterInfo:
			// Underwriter info is only required in submission
		case enums.ModelPinConfigInfo:
			appDb.ModelPinConfig = null.JSONFrom(bytes)
		default:
			err = errors.Newf("Unable to marshall entity: %s", entity.String())
		}
		if err != nil {
			return nil, err
		}
	}
	return &appDb, nil
}

func SubmissionToDb[T AppInfo](submission Submission[T]) (*non_fleet.Submission, error) {
	jobRunInfo, err := json.Marshal(submission.JobRunInfo)
	if err != nil {
		return nil, err
	}

	var signaturePacketID *string
	var policyFormID *string
	if submission.SignaturePacketFormID != nil {
		signaturePacketID = pointer_utils.String(submission.SignaturePacketFormID.String())
	}
	if submission.PolicyFormID != nil {
		policyFormID = pointer_utils.String(submission.PolicyFormID.String())
	}

	var quotePDFHandleID *string
	var appPDFHandleID *string
	if submission.QuotePDFHandleID != nil {
		quotePDFHandleID = pointer_utils.String(submission.QuotePDFHandleID.String())
	}
	if submission.AppPDFHandleID != nil {
		appPDFHandleID = pointer_utils.String(submission.AppPDFHandleID.String())
	}

	effectiveDates := submission.Info.GetEffectiveDates()
	if effectiveDates == nil {
		return nil, errors.New("nil effective dates")
	}

	marshalledIndicationIDs, err := json.Marshal(submission.IndicationIDs)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't marshall indication ids")
	}

	marshalledQuotingPricingContextIDs, err := json.Marshal(submission.QuotingPricingContextIDs)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't marshall quoting pricing context ids")
	}

	var dataContextId null.String
	if submission.DataContextId != nil {
		dataContextId = null.StringFrom(submission.DataContextId.String())
	}

	submissionDB := non_fleet.Submission{
		ID:                              submission.ID.String(),
		ApplicationID:                   submission.ApplicationID.String(),
		Bindable:                        null.BoolFrom(submission.Bindable),
		SelectedIndicationID:            null.StringFrom(submission.SelectedIndicationID.String()),
		SelectedQuotingPricingContextID: null.StringFrom(submission.SelectedQuotingPricingContextID.String()),
		IndicationIds:                   null.JSONFrom(marshalledIndicationIDs),
		QuotingPricingContextIds:        null.JSONFrom(marshalledQuotingPricingContextIDs),
		JobRunInfo:                      null.JSONFrom(jobRunInfo),
		UnderwriterID:                   null.StringFrom(submission.UnderwriterID.String()),
		PolicyFormID:                    null.StringFromPtr(policyFormID),
		SignaturePacketFormID:           null.StringFromPtr(signaturePacketID),
		CreatedAt:                       null.TimeFromPtr(&submission.CreatedAt),
		UpdatedAt:                       null.TimeFromPtr(&submission.UpdatedAt),
		AppPDFHandleID:                  null.StringFromPtr(appPDFHandleID),
		QuotePDFHandleID:                null.StringFromPtr(quotePDFHandleID),
		EffectiveDate:                   null.TimeFrom(effectiveDates.Start),
		EffectiveDateTo:                 null.TimeFrom(effectiveDates.End),
		DataContextID:                   dataContextId,
	}

	for _, entity := range submission.Info.GetEntities() {
		bytes, err := submission.Info.MarshallEntity(entity)
		if err != nil {
			return nil, err
		}
		switch entity {
		case enums.CompanyInfo:
			submissionDB.CompanyInfo = null.JSONFrom(bytes)
		case enums.CoverageInfo:
			submissionDB.CoverageInfo = null.JSONFrom(bytes)
		case enums.LossInfo:
			submissionDB.LossInfo = null.JSONFrom(bytes)
		case enums.ClassInfo:
			submissionDB.ClassInfo = null.JSONFrom(bytes)
		case enums.CommodityInfo:
			submissionDB.CommodityInfo = null.JSONFrom(bytes)
		case enums.EquipmentInfo:
			submissionDB.EquipmentInfo = null.JSONFrom(bytes)
		case enums.DriverInfo:
			submissionDB.DriverInfo = null.JSONFrom(bytes)
		case enums.FinancialInfo:
			submissionDB.FinancialInfo = null.JSONFrom(bytes)
		case enums.UnderwriterInfo:
			submissionDB.UnderwriterInput = null.JSONFrom(bytes)
		case enums.TSPInfo:
			submissionDB.TSPInfo = null.JSONFrom(bytes)
		case enums.ClosureInfo:
			// Closure info is not required in Submission
		case enums.ScraperInfo:
			// Scraper info is not required in Submission
		case enums.ModelPinConfigInfo:
			submissionDB.ModelPinConfig = null.JSONFrom(bytes)
		default:
			err = errors.Newf("Unable to marshall entity: %s", entity.String())
		}
		if err != nil {
			return nil, err
		}
	}

	return &submissionDB, nil
}

func SubmissionFromDb[T any, PT AppInfoWrapped[T]](
	submissionDb non_fleet.Submission,
) (*Submission[PT], error) {
	var submissionObject Submission[PT]
	p := PT(new(T))
	submissionObject.Info = p

	subID, err := uuid.Parse(submissionDb.ID)
	if err != nil {
		return nil, errors.Wrapf(err, "couldn't parse subID: %s", submissionDb.ID)
	}
	appID, err := uuid.Parse(submissionDb.ApplicationID)
	if err != nil {
		return nil, errors.Wrapf(err, "couldn't parse appID: %s", submissionDb.ApplicationID)
	}
	submissionObject.ID = subID
	submissionObject.ApplicationID = appID
	submissionObject.Bindable = submissionDb.Bindable.Bool
	submissionObject.CreatedAt = submissionDb.CreatedAt.Time
	submissionObject.UpdatedAt = submissionDb.UpdatedAt.Time
	var dataContextId *uuid.UUID
	if submissionDb.DataContextID.Valid {
		id, err := uuid.Parse(submissionDb.DataContextID.String)
		if err != nil {
			return nil, errors.Wrapf(err, "couldn't parse data context id: %s", submissionDb.DataContextID.String)
		}
		dataContextId = &id
	}
	submissionObject.DataContextId = dataContextId

	if submissionDb.SelectedIndicationID.Valid {
		uID, err := uuid.Parse(submissionDb.SelectedIndicationID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse Indication id as uuid")
		}
		submissionObject.SelectedIndicationID = uID
	}
	if submissionDb.SelectedQuotingPricingContextID.Valid {
		uID, err := uuid.Parse(submissionDb.SelectedQuotingPricingContextID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse quoting pricing context id as uuid")
		}
		submissionObject.SelectedQuotingPricingContextID = uID
	}
	if submissionDb.UnderwriterID.Valid {
		uID, err := uuid.Parse(submissionDb.UnderwriterID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse underwriter id as uuid")
		}
		submissionObject.UnderwriterID = uID
	}
	if submissionDb.IndicationIds.Valid {
		indicationIDs := make([]uuid.UUID, 0)
		err := json.Unmarshal(submissionDb.IndicationIds.JSON, &indicationIDs)
		if err != nil {
			return nil, errors.Wrap(err, "couldn't unmarshal indication ids array")
		}

		submissionObject.IndicationIDs = indicationIDs
	}
	if submissionDb.QuotingPricingContextIds.Valid {
		quotingPricingContextIDs := make([]uuid.UUID, 0)
		err := json.Unmarshal(submissionDb.QuotingPricingContextIds.JSON, &quotingPricingContextIDs)
		if err != nil {
			return nil, errors.Wrap(err, "couldn't unmarshal quoting pricing context ids array")
		}

		submissionObject.QuotingPricingContextIDs = quotingPricingContextIDs
	}

	for _, entity := range submissionObject.Info.GetEntities() {
		switch entity {
		case enums.CompanyInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.CompanyInfo.JSON)
		case enums.CoverageInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.CoverageInfo.JSON)
		case enums.LossInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.LossInfo.JSON)
		case enums.ClassInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.ClassInfo.JSON)
		case enums.CommodityInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.CommodityInfo.JSON)
		case enums.EquipmentInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.EquipmentInfo.JSON)
		case enums.DriverInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.DriverInfo.JSON)
		case enums.FinancialInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.FinancialInfo.JSON)
		case enums.UnderwriterInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.UnderwriterInput.JSON)
		case enums.TSPInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.TSPInfo.JSON)
		case enums.ClosureInfo:
			// Closure info is not required in Submission
		case enums.ScraperInfo:
			// Scraper info is not required in Submission
		case enums.ModelPinConfigInfo:
			err = submissionObject.Info.UnmarshallEntity(entity, submissionDb.ModelPinConfig.JSON)
		default:
			err = errors.Newf("Entity handler unimplemented for: %s", entity.String())
		}
		if err != nil {
			return nil, err
		}
	}

	if !submissionDb.JobRunInfo.Valid {
		submissionObject.JobRunInfo = nil
	} else {
		err = json.Unmarshal(
			submissionDb.JobRunInfo.JSON,
			&submissionObject.JobRunInfo)
		if err != nil {
			return nil, err
		}
	}

	if submissionDb.SignaturePacketFormID.Ptr() != nil {
		id, err := uuid.Parse(submissionDb.SignaturePacketFormID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse signature form id")
		}
		submissionObject.SignaturePacketFormID = &id
	}
	if submissionDb.PolicyFormID.Ptr() != nil {
		id, err := uuid.Parse(submissionDb.PolicyFormID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse policy form id")
		}
		submissionObject.PolicyFormID = &id
	}
	if submissionDb.QuotePDFHandleID.Ptr() != nil {
		id, err := uuid.Parse(submissionDb.QuotePDFHandleID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse quote pdf handle id")
		}
		submissionObject.QuotePDFHandleID = &id
	}
	if submissionDb.AppPDFHandleID.Ptr() != nil {
		id, err := uuid.Parse(submissionDb.AppPDFHandleID.String)
		if err != nil {
			return nil, errors.Wrap(err, "unable to parse app pdf handle id")
		}
		submissionObject.AppPDFHandleID = &id
	}
	return &submissionObject, nil
}
