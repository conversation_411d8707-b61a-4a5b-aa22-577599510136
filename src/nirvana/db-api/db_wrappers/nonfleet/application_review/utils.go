package application_review

import (
	"time"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"

	"github.com/google/uuid"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/type_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/common"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/openapi-specs/components/underwriting"
)

var (
	getScheduleModFromPercent = func(val *int) float64 {
		if val != nil {
			if *val == 0 {
				return 0
			}
			// Positive Percent denotes credit, so the price should come down,
			// hence doing the negation here
			return float64(100+(-1)*(*val)) / 100.0
		}
		return 0
	}

	getSafetyScheduleModFromPercent = func(val *int) float64 {
		if val != nil {
			if *val == 0 {
				return 0
			}
			return float64(*val) / 100.0
		}
		return 0
	}
)

// CompileSubmissionFromAdmittedAppReview "snapshots" the current Submission and creates a new
// Submission with the Admitted appReview overrides.
func CompileSubmissionFromAdmittedAppReview(
	appReviewObj ApplicationReview,
	oldSubObj application.Submission[*admitted_app.AdmittedApp],
	subID uuid.UUID,
	bindable bool,
) (*application.Submission[*admitted_app.AdmittedApp], error) {
	coverageInfo := oldSubObj.Info.CoverageInfo

	overrides := appReviewObj.GetOverrides()

	// Use the coverages from Overrides
	if overrides.CoverageInfo != nil {
		primaryCovs := make([]application.CoverageDetails, 0)

		// Persist only those coverages which are required in the application
		for _, c := range overrides.CoverageInfo.PrimaryCovs {
			if c.IsRequired {
				primaryCovs = append(primaryCovs, c)
			}
		}
		coverageInfo.PrimaryCovs = primaryCovs
	}

	// Use the effective date from Overrides
	if overrides.EffectiveDate != nil {
		coverageInfo.EffectiveDate = *overrides.EffectiveDate
		coverageInfo.EffectiveDateTo = *overrides.EffectiveDateTo
	}

	// We persist all the ancillary coverage changes in the compiled submission
	ancillaryCovs := compileAdmittedAncillaryCoverages(oldSubObj, overrides)
	coverageInfo.AncillaryCovs = ancillaryCovs

	oldUWInfo := oldSubObj.Info.UnderwriterInfo
	// We add safety credits to all coverage lines
	uwInput := admitted_app.UnderwriterInput{
		LiabScheduleMod: type_utils.ProcessValueOrDefault[int, float64](
			pointer_utils.AddOrReturnNil(overrides.ALPercent, overrides.SafetyCredit),
			int(oldUWInfo.LiabScheduleMod),
			getScheduleModFromPercent,
		),
		ApdScheduleMod: type_utils.ProcessValueOrDefault[int, float64](
			pointer_utils.AddOrReturnNil(overrides.APDPercent, overrides.SafetyCredit),
			int(oldUWInfo.ApdScheduleMod),
			getScheduleModFromPercent,
		),
		// GL Schedule Mod is not affected by Safety Credit
		GlScheduleMod: type_utils.ProcessValueOrDefault[int, float64](
			overrides.GLPercent,
			int(oldUWInfo.GlScheduleMod),
			getScheduleModFromPercent,
		),
		MtcScheduleMod: type_utils.ProcessValueOrDefault[int, float64](
			pointer_utils.AddOrReturnNil(overrides.MTCPercent, overrides.SafetyCredit),
			int(oldUWInfo.MtcScheduleMod),
			getScheduleModFromPercent,
		),
		AllCovSafetyMod: type_utils.ProcessValueOrDefault[int, float64](
			overrides.SafetyCredit,
			int(oldUWInfo.AllCovSafetyMod),
			getSafetyScheduleModFromPercent,
		),
		ViolationPoints: type_utils.GetValueOrDefault(
			overrides.ViolationPoints,
			oldUWInfo.ViolationPoints,
		),
		CountViolationClassA: type_utils.GetValueOrDefault(
			overrides.CountViolationClassA,
			oldUWInfo.CountViolationClassA,
		),
		CountViolationClassB: type_utils.GetValueOrDefault(
			overrides.CountViolationClassB,
			oldUWInfo.CountViolationClassB,
		),
		CountViolationClassC: type_utils.GetValueOrDefault(
			overrides.CountViolationClassC,
			oldUWInfo.CountViolationClassC,
		),
		CountViolationClassD: type_utils.GetValueOrDefault(
			overrides.CountViolationClassD,
			oldUWInfo.CountViolationClassD,
		),
		CountViolationClassE: type_utils.GetValueOrDefault(
			overrides.CountViolationClassE,
			oldUWInfo.CountViolationClassE,
		),
		CountViolationClassF: type_utils.GetValueOrDefault(
			overrides.CountViolationClassF,
			oldUWInfo.CountViolationClassF,
		),
		CountViolationClassN: type_utils.GetValueOrDefault(
			overrides.CountViolationClassN,
			oldUWInfo.CountViolationClassN,
		),
		CountViolationClassNA: type_utils.GetValueOrDefault(
			overrides.CountViolationClassNA,
			oldUWInfo.CountViolationClassNA,
		),
		CreditScore: type_utils.GetValueOrDefault(
			overrides.BizOwnerCreditScore,
			oldUWInfo.CreditScore,
		),
		USDOTScore: type_utils.GetValueOrDefault(
			overrides.USDotScore,
			oldUWInfo.USDOTScore,
		),
		PaymentPlan: type_utils.GetValueOrDefault(
			overrides.PaymentPlan,
			oldUWInfo.PaymentPlan,
		),
		DriverViolations: type_utils.GetValueOrDefault(
			overrides.DriverViolations,
			oldUWInfo.DriverViolations,
		),
	}

	di := oldSubObj.Info.DriverInfo
	for idx := range di.Drivers {
		isExcluded, ok := overrides.ExcludedDrivers[di.Drivers[idx].LicenseNumber]
		if ok {
			di.Drivers[idx].IsIncluded = !isExcluded
		}
	}

	for idx := range di.Drivers {
		isOutOfState, ok := overrides.OutOfStateDrivers[di.Drivers[idx].LicenseNumber]
		if ok {
			// If the driver is explicitly marked as out of state, then we use that value
			di.Drivers[idx].IsOutOfState = isOutOfState
		} else {
			// If the driver is not explicitly marked as out of state, then we calculate it & persist
			di.Drivers[idx].IsOutOfState = admitted_app.IsDriverOutOfState(di.Drivers[idx].LicenseState, oldSubObj.Info.CompanyInfo.USState)
		}
	}

	return &application.Submission[*admitted_app.AdmittedApp]{
		ID:            subID,
		ApplicationID: appReviewObj.GetApplicationID(),
		Bindable:      bindable,
		Info: &admitted_app.AdmittedApp{
			CompanyInfo:        oldSubObj.Info.CompanyInfo,
			CoverageInfo:       coverageInfo,
			LossInfo:           oldSubObj.Info.LossInfo,
			FinancialInfo:      oldSubObj.Info.FinancialInfo,
			ClassInfo:          oldSubObj.Info.ClassInfo,
			CommodityInfo:      oldSubObj.Info.CommodityInfo,
			EquipmentInfo:      oldSubObj.Info.EquipmentInfo,
			DriverInfo:         di,
			TSPInfo:            oldSubObj.Info.TSPInfo,
			UnderwriterInfo:    uwInput,
			ModelPinConfigInfo: oldSubObj.Info.ModelPinConfigInfo,
		},
		UnderwriterID:                   oldSubObj.UnderwriterID,
		SelectedIndicationID:            uuid.Nil,
		SelectedQuotingPricingContextID: uuid.Nil,
		JobRunInfo:                      nil,
		CreatedAt:                       time.Now(),
		UpdatedAt:                       time.Now(),
		DataContextId:                   oldSubObj.DataContextId,
	}, nil
}

func compileAdmittedAncillaryCoverages(submission application.Submission[*admitted_app.AdmittedApp], overrides Overrides) []application.CoverageDetails {
	submissionInfo := submission.Info
	ancillaryCoverages := overrides.AncillaryCoverages
	var newSubmissionCoverages []application.CoverageDetails
	for _, coverage := range ancillaryCoverages {
		// if the coverage is enabled then we need to add it to the submission
		if coverage.IsEnabled {
			deductible := coverage.GetIntDeductible()
			newSubmissionCoverages = append(newSubmissionCoverages, application.CoverageDetails{
				CoverageType:          coverage.CoverageType,
				IsRequired:            true,
				Limit:                 coverage.GetIntLimit(),
				Deductible:            &deductible,
				SymbolsAndDefinitions: coverage.GetSymbolsAndDefinitions(),
			})
		} else {
			// check if the coverage was present in the submission
			// if yes, remove it from the submission.
			isCoveragePresent := submissionInfo.CoverageInfo.
				ContainsCoverageInAncillaryCoverages(coverage.CoverageType)
			if isCoveragePresent {
				submissionInfo.CoverageInfo.DeleteAncillaryCoverage(coverage.CoverageType)
			}
		}
	}
	submissionInfo.SetAncillaryCoverages(newSubmissionCoverages)
	return submissionInfo.CoverageInfo.AncillaryCovs
}

// getQMForFetchingPaginatedApplicationReviews returns the QMod slice
// which contains all the SQL queries required to fetch the correct
// set of application reviews given the params
func getQMForFetchingPaginatedApplicationReviews(
	pageSize int,
	query string,
	underwriterID *string,
	effectiveDateOnOrBefore *time.Time,
	effectiveDateOnOrAfter *time.Time,
	states []interface{},
	effectiveDate time.Time,
	appReviewId string,
	agencyId string,
	cursor *Cursor,
	options Options,
) common.QueryModSlice {
	var (
		qmSelect               = qm.Select("non_fleet.application_review.*")
		qmInnerJoinApplication = qm.InnerJoin(
			"non_fleet.application ON non_fleet.application_review.application_id = non_fleet.application.id")
		qmOrderBy  = qm.OrderBy("non_fleet.application_review.effective_date, non_fleet.application_review.id")
		qmLimit    = qm.Limit(pageSize)
		qmPaginate = qm.Where(
			"(non_fleet.application_review.effective_date, non_fleet.application_review.id) > (?, ?)",
			effectiveDate, appReviewId)
		qmQuery = qm.Where(
			"non_fleet.application.company_info->>'DOTNumber' like ? or non_fleet.application."+
				"short_id::text like ? or upper(non_fleet.application.company_info->>'Name') like ?",
			`%`+query+`%`,
			`%`+query+`%`,
			`%`+query+`%`,
		)
		qmStates = qm.WhereIn("non_fleet.application_review.state IN ?", states...)
		// Used to filter out application reviews which are ready for pre-telematics
		qmInPreTelematicsReady    = qm.WhereIn("non_fleet.application.pre_telematics_quote_state IN ?", getPreTelematicsReadyStates()...)
		qmNotInPreTelematicsReady = qm.WhereIn("non_fleet.application.pre_telematics_quote_state IS NULL OR non_fleet.application.pre_telematics_quote_state::text = 'null' OR non_fleet.application.pre_telematics_quote_state NOT IN ?", getPreTelematicsReadyStates()...)
		qmInExpressLane           = qm.WhereIn("non_fleet.application.express_lane_metadata->>'State' IN ?", getExpressLaneValidStates()...)
		qmNotInExpressLane        = qm.WhereIn("non_fleet.application.express_lane_metadata IS NULL OR non_fleet.application.express_lane_metadata::text = 'null' OR non_fleet.application.express_lane_metadata->>'State' NOT IN ?", getExpressLaneValidStates()...)

		qmArchivedAtNull  = qm.Where("non_fleet.application_review.archived_at IS NULL")
		qmInTestAgencyIDs = qm.WhereIn("non_fleet.application.agency_id IN ?",
			constants.TestAgenciesArray()...)
		qmNotInTestAgencyIDs = qm.WhereNotIn("non_fleet.application.agency_id NOT IN ?",
			constants.TestAgenciesArray()...)
	)

	mods := common.QueryModSlice{}

	switch {
	case cursor == nil:
		{
			mods = append(mods, qmSelect, qmInnerJoinApplication, qmArchivedAtNull, qmStates, qmOrderBy, qmLimit)
			if options.Query != nil {
				mods = common.InsertQM(mods, 3, qmQuery)
			}
			if options.EffectiveDateOnOrBefore != nil {
				qmEffectiveDateOnOrBefore := qm.Where("non_fleet.application_review.effective_date <= ?", *effectiveDateOnOrBefore)
				mods = common.InsertQM(mods, 3, qmEffectiveDateOnOrBefore)
			}
			if options.EffectiveDateOnOrAfter != nil {
				qmEffectiveDateOnOrAfter := qm.Where("non_fleet.application_review.effective_date >= ?", *effectiveDateOnOrAfter)
				mods = common.InsertQM(mods, 3, qmEffectiveDateOnOrAfter)
			}
			if options.UnderwriterID != nil && *options.UnderwriterID != "" {
				qmUnderwriter := qm.Where("non_fleet.application_review.underwriter_id = ?", *underwriterID)
				mods = common.InsertQM(mods, 3, qmUnderwriter)
			}
			if options.Tab != nil {
				//nolint:exhaustive
				switch *options.Tab {
				case underwriting.ApplicationReviewTabPreTelematicsExperiment:
					if slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInPreTelematicsReady)
					} else {
						// If the agency is not in test agencies, then we
						// need to fetch all app reviews which are not in test agencies
						// and are in pre-telematics ready state since the tab is PreTelematicsExperiment
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInPreTelematicsReady)
					}
				case underwriting.ApplicationReviewTabExpressLane:
					if slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInExpressLane)
					} else {
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInExpressLane)
					}
				case underwriting.ApplicationReviewTabInternal:
					if !slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
					} else {
						// exclude test agencies from the results &
						// also exclude pre-telematics/express lane ready state since
						// those would be handled by the PreTelematicsExperiment/ Express Lane tab
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmNotInPreTelematicsReady)
						mods = common.InsertQM(mods, 3, qmNotInExpressLane)
					}
				default:
					if slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
					} else {
						// exclude test agencies from the results &
						// also exclude pre-telematics ready/ express lane state since
						// those would be handled by the PreTelematicsExperiment/Express Lane tab
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmNotInPreTelematicsReady)
						mods = common.InsertQM(mods, 3, qmNotInExpressLane)
					}
				}
			}
		}
	case cursor != nil:
		{
			mods = append(mods, qmSelect, qmInnerJoinApplication, qmArchivedAtNull, qmStates, qmPaginate,
				qmOrderBy, qmLimit)
			if options.Query != nil {
				mods = common.InsertQM(mods, 3, qmQuery)
			}
			if options.EffectiveDateOnOrBefore != nil {
				qmEffectiveDateOnOrBefore := qm.Where("non_fleet.application_review.effective_date <= ?",
					*effectiveDateOnOrBefore)
				mods = common.InsertQM(mods, 3, qmEffectiveDateOnOrBefore)
			}
			if options.EffectiveDateOnOrAfter != nil {
				qmEffectiveDateOnOrAfter := qm.Where("non_fleet.application_review.effective_date >= ?",
					*effectiveDateOnOrAfter)
				mods = common.InsertQM(mods, 3, qmEffectiveDateOnOrAfter)
			}
			if options.UnderwriterID != nil && *options.UnderwriterID != "" {
				qmUnderwriter := qm.Where("non_fleet.application_review.underwriter_id = ?", *underwriterID)
				mods = common.InsertQM(mods, 3, qmUnderwriter)
			}
			if options.Tab != nil {
				//nolint:exhaustive
				switch *options.Tab {
				case underwriting.ApplicationReviewTabPreTelematicsExperiment:
					if slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInPreTelematicsReady)
					} else {
						// If the agency is not in test agencies, then we
						// need to fetch all app reviews which are not in test agencies
						// and are in pre-telematics ready state since the tab is PreTelematicsExperiment
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInPreTelematicsReady)
					}
				case underwriting.ApplicationReviewTabExpressLane:
					if slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInExpressLane)
					} else {
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmInExpressLane)
					}
				case underwriting.ApplicationReviewTabInternal:
					if !slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						// When in test agencies, we do not need to filter by pre-telematics ready state
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
					} else {
						// exclude test agencies from the results &
						// also exclude pre-telematics ready/express lane state since
						// those would be handled by the PreTelematicsExperiment/ Express Lane tab
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmNotInPreTelematicsReady)
						mods = common.InsertQM(mods, 3, qmNotInExpressLane)
					}
				default:
					if slice_utils.Contains(constants.TestExternalAgenciesStrArray(), agencyId) {
						// When in test agencies, we do not need to filter by pre-telematics ready state
						mods = common.InsertQM(mods, 3, qmInTestAgencyIDs)
					} else {
						// exclude test agencies from the results &
						// also exclude pre-telematics ready/ express lane state since
						// those would be handled by the PreTelematicsExperiment/Express Lane tab
						mods = common.InsertQM(mods, 3, qmNotInTestAgencyIDs)
						mods = common.InsertQM(mods, 3, qmNotInPreTelematicsReady)
						mods = common.InsertQM(mods, 3, qmNotInExpressLane)
					}
				}
			}
		}
	}

	// We send all app reviews in pending state when the tab asked is either
	// ReadyForReview or Incomplete.
	if options.Tab != nil &&
		slice_utils.Contains([]underwriting.ApplicationReviewTab{
			underwriting.ApplicationReviewTabReadyForReview,
			underwriting.ApplicationReviewTabIncomplete,
		}, *options.Tab) {
		// Removing the last limit QM Mod to fetch all app reviews
		mods = mods[:len(mods)-1]
	}

	return mods
}

// getStatesToFetchForPaginatedApplicationReviews returns a list of valid states
// for the tab passed as param for filtering application reviews.
// Here, tab refers to the frontend tabs in the UW app
func getStatesToFetchForPaginatedApplicationReviews(options Options, states []interface{}) []interface{} {
	if options.Tab != nil {
		//nolint:exhaustive
		switch *options.Tab {
		case underwriting.ApplicationReviewTabAll,
			underwriting.ApplicationReviewTabInternal,
			underwriting.ApplicationReviewTabPreTelematicsExperiment,
			underwriting.ApplicationReviewTabExpressLane:
			states = append(
				states,
				AppReviewStatePending.String(),
				AppReviewStateApproved.String(),
				AppReviewStateDeclined.String(),
				AppReviewStateStale.String(),
				AppReviewStateClosed.String(),
				AppReviewStateReferral.String(),
			)
		case underwriting.ApplicationReviewTabPending:
			states = append(states, AppReviewStatePending.String())
		case underwriting.ApplicationReviewTabApproved:
			states = append(states, AppReviewStateApproved.String())
		case underwriting.ApplicationReviewTabDeclined:
			states = append(states, AppReviewStateDeclined.String())
		case underwriting.ApplicationReviewTabStale:
			states = append(states, AppReviewStateStale.String())
		case underwriting.ApplicationReviewTabClosed:
			states = append(states, AppReviewStateClosed.String())
		case underwriting.ApplicationReviewTabReferral:
			states = append(states, AppReviewStateReferral.String())
		}

		return states
	} else {
		return []interface{}{
			AppReviewStatePending.String(),
			AppReviewStateApproved.String(),
			AppReviewStateDeclined.String(),
			AppReviewStateStale.String(),
			AppReviewStateClosed.String(),
			AppReviewStateReferral.String(),
		}
	}
}

// getPreTelematicsReadyStates returns a list of pre-telematics ready states
// which are used to filter application reviews in the PreTelematicsExperiment tab
// Making this as an array since it can be changed and would be easier to maintain.
func getPreTelematicsReadyStates() []interface{} {
	return []interface{}{
		enums.PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelinePending.String(),
	}
}

// getExpressLaneValidStates returns a list of valid states for the Express Lane tab
func getExpressLaneValidStates() []interface{} {
	return []interface{}{
		enums.ExpressLaneStateWaitingForTelematics.String(),
		enums.ExpressLaneStateAutomationInProgress.String(),
		enums.ExpressLaneStateAutomationCompleted.String(),
	}
}
