package enums

//go:generate go run github.com/dmarkham/enumer -type=InfoEntity -json
type InfoEntity int

const (
	CompanyInfo InfoEntity = iota + 1
	CoverageInfo
	LossInfo
	ClassInfo
	CommodityInfo
	EquipmentInfo
	DriverInfo
	FinancialInfo
	UnderwriterInfo
	TSPInfo
	ClosureInfo
	ScraperInfo
	ModelPinConfigInfo
)

//go:generate go run github.com/dmarkham/enumer -type=BusinessStructure -json
type BusinessStructure int

const (
	BusinessStructureSoleProprietor BusinessStructure = iota + 1
	BusinessStructurePartnership
	BusinessStructureCorporation
	BusinessStructureLLC
)

// AppState represents the state of an application within the non-fleet insurance workflow.
// Note: Some components rely on terminal states being accurately defined.
// For example, see: runOnDataPipelineStateChange.updateNFApplicationPreQuoteTelematicsStatus.
//
//go:generate go run github.com/dmarkham/enumer -type=AppState -json
type AppState int

const (
	AppStateIncomplete AppState = iota + 1
	AppStateComplete
	AppStateQuoteGenerating
	AppStateQuoteGenerated
	AppStateUnderUWReview
	AppStateUnderReferralReview
	AppStateApproved
	AppStateDeclined
	AppStatePolicyCreated
	AppStateClosed
	AppStatePanic
	AppStateBindableQuoteGenerated
)

//go:generate go run github.com/dmarkham/enumer -type=PageState -json
type PageState int

const (
	PageStateUnsubmitted PageState = iota + 1
	PageStateOperations
	PageStateClassesAndCommodities
	PageStateEquipments
	PageStateDrivers
	PageStateSubmitted
	PageStateRecalculateQuote
	PageStateDecline
	PageStateReferral
	PageStateReview
)

//go:generate go run github.com/dmarkham/enumer -type=RuleResult -json -transform=snake
type RuleResult int

const (
	Fail RuleResult = iota + 1
	Pass
	Referral
	Flagged
	SoftErrored
	HardErrored
)

//go:generate go run github.com/dmarkham/enumer -type=Category -json
type Category int

const (
	CategoryAlcoholBeerWine Category = iota + 1
	CategoryAppliances
	CategoryAutoParts
	CategoryBuildingMaterials
	CategoryBulkBaggedNuts
	CategoryCannedGoods
	CategoryChemicals
	CategoryComputers
	CategoryCosmetics
	CategoryDVDCD
	CategoryDryFreight
	CategoryElectronics
	CategoryFarm
	CategoryFrozenFoods
	CategoryFurnitureNew
	CategoryFurs
	CategoryGarments
	CategoryGeneralFreight
	CategoryGrain
	CategoryHayStraw
	CategoryLogsPulpwood
	CategoryLumberFinished
	CategoryMachineryLightHeavy
	CategoryMeat
	CategoryMilkDairyProducts
	CategoryMotorsSwitches
	CategoryNonFerrousMetals
	CategoryOfficeEquipment
	CategoryPharmaceuticals
	CategoryPoultryDressed
	CategoryProduceNonRefrigerated
	CategorySandAndGravel
	CategorySeafoodUnlessCanned
	CategorySolarPanels
	CategorySportingGoods
	CategoryTires
	CategoryTobacco
	CategoryTools
	CategoryToys
	CategoryFerrousMetals
	CategoryPaperPlasticProducts
)

func (i Category) GetLabel() string {
	switch i {
	case CategoryAlcoholBeerWine:
		return "Alcohol, Beer, Wine"
	case CategoryAppliances:
		return "Appliances (small/household)"
	case CategoryAutoParts:
		return "Auto Parts"
	case CategoryBuildingMaterials:
		return "Building Materials (N.O.C.)"
	case CategoryBulkBaggedNuts:
		return "Bulk and Bagged Nuts"
	case CategoryCannedGoods:
		return "Canned Goods, non-perishables"
	case CategoryChemicals:
		return "Chemicals (packaged or bulk)"
	case CategoryComputers:
		return "Computers"
	case CategoryCosmetics:
		return "Cosmetics"
	case CategoryDVDCD:
		return "DVD/CD (blank/pre-recorded)"
	case CategoryDryFreight:
		return "Containerized/Dry Freight"
	case CategoryElectronics:
		return "Electronics"
	case CategoryFarm:
		return "Farm (in bulk/packaged)"
	case CategoryFrozenFoods:
		return "Frozen Foods"
	case CategoryFurnitureNew:
		return "Furniture (new)"
	case CategoryFurs:
		return "Furs"
	case CategoryGarments:
		return "Garments (not high fashion)"
	case CategoryGeneralFreight:
		return "General Freight"
	case CategoryGrain:
		return "Grain"
	case CategoryHayStraw:
		return "Hay/Straw"
	case CategoryLogsPulpwood:
		return "Logs, Pulpwood"
	case CategoryLumberFinished:
		return "Lumber (finished)"
	case CategoryMachineryLightHeavy:
		return "Machinery (light/heavy)"
	case CategoryMeat:
		return "Meat (fresh/frozen/boxed)"
	case CategoryMilkDairyProducts:
		return "Milk, Dairy Products"
	case CategoryMotorsSwitches:
		return "Motors, Switches"
	case CategoryNonFerrousMetals:
		return "Non-Ferrous Metal"
	case CategoryOfficeEquipment:
		return "Office Equipment"
	case CategoryPharmaceuticals:
		return "Pharmaceuticals"
	case CategoryPoultryDressed:
		return "Poultry (dressed)"
	case CategoryProduceNonRefrigerated:
		return "Produce (non-refrigerated)"
	case CategorySandAndGravel:
		return "Sand and Gravel"
	case CategorySeafoodUnlessCanned:
		return "Seafood (unless canned)"
	case CategorySolarPanels:
		return "Solar Panels"
	case CategorySportingGoods:
		return "Sporting Goods"
	case CategoryTires:
		return "Tires"
	case CategoryTobacco:
		return "Tobacco"
	case CategoryTools:
		return "Tools"
	case CategoryToys:
		return "Toys"
	case CategoryFerrousMetals:
		return "Steel, Iron, Pipe"
	case CategoryPaperPlasticProducts:
		return "Paper, Plastic Products"
	default:
		return ""
	}
}

func (i Category) IsTargetCategory() bool {
	switch i {
	case CategoryAlcoholBeerWine,
		CategoryElectronics,
		CategoryFurs,
		CategoryGarments,
		CategoryNonFerrousMetals,
		CategorySolarPanels,
		CategoryBulkBaggedNuts,
		CategoryPharmaceuticals,
		CategorySeafoodUnlessCanned,
		CategoryTobacco:
		return true
	default:
		return false
	}
}

//go:generate go run github.com/dmarkham/enumer -type=USDotScore -json -trimprefix=USDotScore
type USDotScore int

const (
	USDotScoreX USDotScore = iota
	USDotScoreA01
	USDotScoreA02
	USDotScoreA03
	USDotScoreA04
	USDotScoreA05
	USDotScoreA06
	USDotScoreA07
	USDotScoreA08
	USDotScoreA09
	USDotScoreA10
	USDotScoreA11
	USDotScoreA12
	USDotScoreA13
	USDotScoreB01
	USDotScoreB02
	USDotScoreB03
	USDotScoreB04
	USDotScoreB05
	USDotScoreB06
	USDotScoreB07
	USDotScoreB08
	USDotScoreB09
	USDotScoreB10
	USDotScoreB11
	USDotScoreB12
	USDotScoreB13
	USDotScoreZ93
	USDotScoreZ94
	USDotScoreZ95
	USDotScoreZ97
	USDotScoreZ98
	USDotScoreZ99
	USDotScoreZ92
)

//go:generate go run github.com/dmarkham/enumer -type=PaymentPlan -linecomment
type PaymentPlan int

const (
	// Installments Via Electronic Funds Transfer
	PaymentPlanInstallmentWithEFT PaymentPlan = iota // InstallmentWithEFT
	// Paid In Full
	PaymentPlanPaidInFull // PaidInFull
)

//go:generate go run github.com/dmarkham/enumer -type=PreTelematicsQuoteState -json -trimprefix=PreTelematicsQuoteState
type PreTelematicsQuoteState int

const (
	// PreTelematicsQuoteStateNotReadyBecauseDataPipelinePending indicates that the quote is not ready because the data pipeline is still pending.
	PreTelematicsQuoteStateNotReadyBecauseDataPipelinePending PreTelematicsQuoteState = iota
	// PreTelematicsQuoteStateNotReadyBecauseDataPipelineIssues indicates that the quote is not ready due to data pipeline failure.
	PreTelematicsQuoteStateNotReadyBecauseDataPipelineIssues
	// PreTelematicsQuoteStateReadyBecauseDataPipelineSuccess indicates readiness for the quote as the data pipeline has successfully completed within time limits.
	PreTelematicsQuoteStateReadyBecauseDataPipelineSuccess
	// PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelineSuccess PreTelematicsQuoteStateReadyBecauseTimeElapsed indicates readiness due to elapsed time. and data pipeline success.
	PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelineSuccess
	// PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelinePending indicates readiness due to elapsed time but with pending data pipeline.
	PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelinePending
)

//go:generate go run github.com/dmarkham/enumer -type=ExpressLaneState -json -trimprefix=ExpressLaneState
type ExpressLaneState int

const (
	ExpressLaneStateInvalid ExpressLaneState = iota
	ExpressLaneStateWaitingForTelematics
	// ExpressLaneStateError indicates that the express lane automation has errored.
	// This is the final state of the express lane automation. (Not a part of express lanes anymore)
	ExpressLaneStateError
	// ExpressLaneStateAutomationInProgress indicates that the express lane automation is in progress.
	ExpressLaneStateAutomationInProgress
	// ExpressLaneStateAutomationCompleted indicates that the express lane automation has completed.
	// This is the final state of the express lane automation.
	ExpressLaneStateAutomationCompleted
)
