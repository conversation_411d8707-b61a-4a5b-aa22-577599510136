// Code generated by "enumer -type=ExpressLaneState -json -trimprefix=ExpressLaneState"; DO NOT EDIT.

package enums

import (
	"encoding/json"
	"fmt"
	"strings"
)

const _ExpressLaneStateName = "InvalidWaitingForTelematicsErrorAutomationInProgressAutomationCompleted"

var _ExpressLaneStateIndex = [...]uint8{0, 7, 27, 32, 52, 71}

const _ExpressLaneStateLowerName = "invalidwaitingfortelematicserrorautomationinprogressautomationcompleted"

func (i ExpressLaneState) String() string {
	if i < 0 || i >= ExpressLaneState(len(_ExpressLaneStateIndex)-1) {
		return fmt.Sprintf("ExpressLaneState(%d)", i)
	}
	return _ExpressLaneStateName[_ExpressLaneStateIndex[i]:_ExpressLaneStateIndex[i+1]]
}

// An "invalid array index" compiler error signifies that the constant values have changed.
// Re-run the stringer command to generate them again.
func _ExpressLaneStateNoOp() {
	var x [1]struct{}
	_ = x[ExpressLaneStateInvalid-(0)]
	_ = x[ExpressLaneStateWaitingForTelematics-(1)]
	_ = x[ExpressLaneStateError-(2)]
	_ = x[ExpressLaneStateAutomationInProgress-(3)]
	_ = x[ExpressLaneStateAutomationCompleted-(4)]
}

var _ExpressLaneStateValues = []ExpressLaneState{ExpressLaneStateInvalid, ExpressLaneStateWaitingForTelematics, ExpressLaneStateError, ExpressLaneStateAutomationInProgress, ExpressLaneStateAutomationCompleted}

var _ExpressLaneStateNameToValueMap = map[string]ExpressLaneState{
	_ExpressLaneStateName[0:7]:        ExpressLaneStateInvalid,
	_ExpressLaneStateLowerName[0:7]:   ExpressLaneStateInvalid,
	_ExpressLaneStateName[7:27]:       ExpressLaneStateWaitingForTelematics,
	_ExpressLaneStateLowerName[7:27]:  ExpressLaneStateWaitingForTelematics,
	_ExpressLaneStateName[27:32]:      ExpressLaneStateError,
	_ExpressLaneStateLowerName[27:32]: ExpressLaneStateError,
	_ExpressLaneStateName[32:52]:      ExpressLaneStateAutomationInProgress,
	_ExpressLaneStateLowerName[32:52]: ExpressLaneStateAutomationInProgress,
	_ExpressLaneStateName[52:71]:      ExpressLaneStateAutomationCompleted,
	_ExpressLaneStateLowerName[52:71]: ExpressLaneStateAutomationCompleted,
}

var _ExpressLaneStateNames = []string{
	_ExpressLaneStateName[0:7],
	_ExpressLaneStateName[7:27],
	_ExpressLaneStateName[27:32],
	_ExpressLaneStateName[32:52],
	_ExpressLaneStateName[52:71],
}

// ExpressLaneStateString retrieves an enum value from the enum constants string name.
// Throws an error if the param is not part of the enum.
func ExpressLaneStateString(s string) (ExpressLaneState, error) {
	if val, ok := _ExpressLaneStateNameToValueMap[s]; ok {
		return val, nil
	}

	if val, ok := _ExpressLaneStateNameToValueMap[strings.ToLower(s)]; ok {
		return val, nil
	}
	return 0, fmt.Errorf("%s does not belong to ExpressLaneState values", s)
}

// ExpressLaneStateValues returns all values of the enum
func ExpressLaneStateValues() []ExpressLaneState {
	return _ExpressLaneStateValues
}

// ExpressLaneStateStrings returns a slice of all String values of the enum
func ExpressLaneStateStrings() []string {
	strs := make([]string, len(_ExpressLaneStateNames))
	copy(strs, _ExpressLaneStateNames)
	return strs
}

// IsAExpressLaneState returns "true" if the value is listed in the enum definition. "false" otherwise
func (i ExpressLaneState) IsAExpressLaneState() bool {
	for _, v := range _ExpressLaneStateValues {
		if i == v {
			return true
		}
	}
	return false
}

// MarshalJSON implements the json.Marshaler interface for ExpressLaneState
func (i ExpressLaneState) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.String())
}

// UnmarshalJSON implements the json.Unmarshaler interface for ExpressLaneState
func (i *ExpressLaneState) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return fmt.Errorf("ExpressLaneState should be a string, got %s", data)
	}

	var err error
	*i, err = ExpressLaneStateString(s)
	return err
}
