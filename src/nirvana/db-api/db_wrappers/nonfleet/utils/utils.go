package utils

import (
	"context"
	"fmt"

	non_fleet_experiment "nirvanatech.com/nirvana/application/experiments/non_fleet"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/pre_telematics_quote"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"

	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/ctx_utils"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/math_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	oapi_common "nirvanatech.com/nirvana/openapi-specs/components/common"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet_underwriting"
)

type Deps struct {
	fx.In
	AdmittedAppWrapper application.Wrapper[*admitted_app.AdmittedApp]
	ExperimentManager  *non_fleet_experiment.Manager
}

func AppObjToAppData[T application.AppInfo](appObj application.Application[T], deps Deps) application.AppData {
	ctx := context.WithValue(context.TODO(), ctx_utils.ContextKeyProgramType, appObj.Info.GetProgramType().String())
	var indication *application.IndicationOption
	var qpc *quoting.PricingContext
	retval := application.AppData{
		AppID:                   appObj.ID,
		PageState:               appObj.PageState,
		ShortID:                 appObj.ShortID,
		State:                   appObj.State,
		DeclineReason:           appObj.DeclineReason,
		ProducerID:              appObj.ProducerID,
		SelectedIndication:      indication,
		UnderwriterID:           appObj.UnderwriterID,
		UWSubmissionID:          appObj.UwSubmissionID,
		PreTelematicsQuoteState: appObj.PreTelematicsQuoteState,
		ExpressLaneMetadata:     appObj.ExpressLaneMetadata,
	}
	if appObj.RenewalMetadata != nil {
		retval.RenewalMetadata = &nonfleet.RenewalMetadata{
			OriginalApplicationId: appObj.RenewalMetadata.OriginalApplicationId,
		}
	}
	if appObj.QuoteSubmissionID != uuid.Nil {

		if appObj.Info.GetProgramType() != policy_enums.ProgramTypeNonFleetAdmitted {
			log.Error(ctx, fmt.Sprintf("invalid program type: %s", appObj.Info.GetProgramType().String()))
		} else {
			subObj, err := deps.AdmittedAppWrapper.GetSubmissionById(ctx, appObj.QuoteSubmissionID)
			if err != nil {
				log.Error(ctx, fmt.Sprintf("couldn't get submission with id: %s", appObj.QuoteSubmissionID), log.Err(err))
				return retval
			}

			if subObj.SelectedIndicationID != uuid.Nil {
				indObj, err := deps.AdmittedAppWrapper.GetIndOptionById(ctx, subObj.SelectedIndicationID)
				if err != nil {
					log.Error(ctx, fmt.Sprintf("couldn't get indication with id: %s", subObj.SelectedIndicationID), log.Err(err))
					return retval
				}
				indication = &indObj
			}

			if subObj.SelectedQuotingPricingContextID != uuid.Nil {
				qpcObj, err := deps.AdmittedAppWrapper.GetQuotingPricingContextById(ctx,
					subObj.SelectedQuotingPricingContextID.String())
				if err != nil {
					log.Error(ctx, "couldn't get quoting pricing context",
						log.String("SelectedQuotingPricingContextID",
							subObj.SelectedQuotingPricingContextID.String()), log.Err(err))
					return retval
				}
				qpc = qpcObj
			}
		}
	}
	retval.SelectedIndication = indication
	// TODO Move usage of SelectedIndication to SelectedQuotingPricingContext
	retval.SelectedQuotingPricingContext = qpc

	isApplicable, err := deps.ExperimentManager.IsExperimentApplicableToApp(ctx, appObj.ID, pre_telematics_quote.PreQuoteTelematicsV1ExperimentId)
	if err != nil {
		log.Error(ctx, "couldn't check if pre quote telematics experiment is applicable", log.Err(err))
		return retval
	}
	if isApplicable != nil {
		retval.IsEligibleForPreQuoteTelematicsExperiment = isApplicable
	}

	return retval
}

func GeneratePackageDetails(ind application.IndicationOption) nonfleet_underwriting.PackageDetails {
	primaryCovs := make([]nonfleet.CoverageDetails, 0)
	ancillaryCovs := make([]nonfleet.CoverageDetails, 0)
	for _, c := range ind.GetCoverages() {
		cov := nonfleet.CoverageDetails{
			CoverageType:   oapi_common.CoverageType(c.CoverageType.String()),
			Deductible:     c.Deductible,
			IsRequired:     c.IsRequired,
			Label:          str_utils.PrettyEnumString(c.CoverageType.String(), "Coverage"),
			Limit:          c.Limit,
			Premium:        c.Premium,
			PremiumPerUnit: c.PremiumPerUnit,
		}

		if c.CoverageType.IsPrimaryCoverage() {
			primaryCovs = append(primaryCovs, cov)
		} else {
			ancillaryCovs = append(ancillaryCovs, cov)
		}
	}

	if ind.GetProgram() == policy_enums.ProgramTypeNonFleetCanopiusNRB {
		ancillaryCovs = executeNRBAncillaryCoveragesLogic(ancillaryCovs)
	}

	return nonfleet_underwriting.PackageDetails{
		TIV:             ind.GetTIV(),
		TIVPercentage:   math_utils.RoundFloat(float32(ind.GetApdPTiv()), 2),
		AncillaryCovs:   ancillaryCovs,
		FlatCharges:     ind.GetFlatCharges(),
		PrimaryCovs:     primaryCovs,
		StateSurcharge:  ind.GetStateSurchargePremium(),
		SubTotalPremium: ind.GetSubTotalPolicyPremium(),
		TotalPremium:    ind.GetTotalPremium(),
	}
}

type metadata struct {
	isPresent      bool
	limit          *int
	deductible     *int
	premium        *int
	premiumPerUnit *int
}

func executeNRBAncillaryCoveragesLogic(ancillaryCovs []nonfleet.CoverageDetails) []nonfleet.CoverageDetails {
	var nonOwnedTrailer, trailerInterchange, uiia metadata
	for _, c := range ancillaryCovs {
		if slice_utils.Contains([]oapi_common.CoverageType{
			oapi_common.CoverageMTCNonOwnedTrailer,
			oapi_common.CoverageAPDNonOwnedTrailer,
		}, c.CoverageType) && c.IsRequired {
			nonOwnedTrailer = metadata{
				isPresent:      true,
				limit:          c.Limit,
				deductible:     c.Deductible,
				premium:        c.Premium,
				premiumPerUnit: c.PremiumPerUnit,
			}
			continue
		}

		if slice_utils.Contains([]oapi_common.CoverageType{
			oapi_common.CoverageMTCTrailerInterchange,
			oapi_common.CoverageAPDTrailerInterchange,
		}, c.CoverageType) && c.IsRequired {
			trailerInterchange = metadata{
				isPresent:      true,
				limit:          c.Limit,
				deductible:     c.Deductible,
				premium:        c.Premium,
				premiumPerUnit: c.PremiumPerUnit,
			}
			continue
		}

		if slice_utils.Contains([]oapi_common.CoverageType{
			oapi_common.CoverageMTCUIIA,
			oapi_common.CoverageAPDUIIA,
		}, c.CoverageType) && c.IsRequired {
			uiia = metadata{
				isPresent:      true,
				limit:          c.Limit,
				deductible:     c.Deductible,
				premium:        c.Premium,
				premiumPerUnit: c.PremiumPerUnit,
			}
			continue
		}
	}

	if uiia.isPresent {
		ancillaryCovs = append(ancillaryCovs, nonfleet.CoverageDetails{
			CoverageType:   oapi_common.CoverageUIIA,
			IsRequired:     true,
			Label:          str_utils.PrettyEnumString(string(oapi_common.CoverageUIIA), "Coverage"),
			Limit:          uiia.limit,
			Premium:        uiia.premium,
			PremiumPerUnit: uiia.premiumPerUnit,
		})
	}

	if trailerInterchange.isPresent {
		ancillaryCovs = append(ancillaryCovs, nonfleet.CoverageDetails{
			CoverageType:   oapi_common.CoverageTrailerInterchange,
			IsRequired:     true,
			Label:          str_utils.PrettyEnumString(string(oapi_common.CoverageTrailerInterchange), "Coverage"),
			Limit:          trailerInterchange.limit,
			Premium:        trailerInterchange.premium,
			PremiumPerUnit: trailerInterchange.premiumPerUnit,
		})
	}

	if nonOwnedTrailer.isPresent {
		ancillaryCovs = append(ancillaryCovs, nonfleet.CoverageDetails{
			CoverageType:   oapi_common.CoverageNonOwnedTrailer,
			IsRequired:     true,
			Label:          str_utils.PrettyEnumString(string(oapi_common.CoverageNonOwnedTrailer), "Coverage"),
			Limit:          nonOwnedTrailer.limit,
			Premium:        nonOwnedTrailer.premium,
			PremiumPerUnit: nonOwnedTrailer.premiumPerUnit,
		})
	}

	ancillaryCovs = slice_utils.Filter[nonfleet.CoverageDetails](
		ancillaryCovs,
		func(c nonfleet.CoverageDetails) bool {
			return !slice_utils.Contains([]oapi_common.CoverageType{
				oapi_common.CoverageMTCNonOwnedTrailer,
				oapi_common.CoverageAPDNonOwnedTrailer,
				oapi_common.CoverageMTCUIIA,
				oapi_common.CoverageAPDUIIA,
				oapi_common.CoverageMTCTrailerInterchange,
				oapi_common.CoverageAPDTrailerInterchange,
			}, c.CoverageType)
		},
	)
	return ancillaryCovs
}
