load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "business_auto",
    srcs = [
        "field_config_generators.go",
        "field_config_helpers.go",
        "field_config_validators.go",
        "field_configs.go",
        "handler.go",
        "helper.go",
        "inputs_generator.go",
    ],
    importpath = "nirvanatech.com/nirvana/forms/fill_inputs/business_auto",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/business-auto/state_machine",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/str_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/forms/fill_inputs",
        "//nirvana/forms/fill_inputs/core",
        "//nirvana/forms/fill_inputs/models",
        "//nirvana/forms/fill_inputs/models/business_auto",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/proto",
        "//nirvana/pdffill/requests",
        "//nirvana/policy/business_auto",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
    ],
)
