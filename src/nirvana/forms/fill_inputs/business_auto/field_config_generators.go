package business_auto

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"

	baModel "nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/pdffill/requests"
	"nirvanatech.com/nirvana/policy_common/constants"
)

var carrierNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrier = constants.InsuranceCarrierMSTransverse.String()
	return *data, nil
}

var carrierMailingAddressGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrierMailingAddress = data.IB.DefaultCarrier.PolicyCommonInsuranceCarrier().MailingAddress()
	return *data, nil
}

var insuranceCarrierOnFormFooterGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	_, ok := fill_inputs.StateRequiresCarrierOnForms[data.App.CompanyInfo.USState]
	if !ok {
		data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = constants.InsuranceCarrierEmpty.String()
		return *data, nil
	}

	data.FillInputs.ComputedFields.InsuranceCarrierOnFormFooter = constants.InsuranceCarrierMSTransverse.String()
	return *data, nil
}

var insuranceCarrierWebAddressGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrierWebsite = data.IB.DefaultCarrier.PolicyCommonInsuranceCarrier().WebAddress()
	return *data, nil
}

var insuranceCarrierPhoneNumberGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.InsuranceCarrierPhoneNumber = data.IB.DefaultCarrier.PolicyCommonInsuranceCarrier().PhoneNumber()
	return *data, nil
}

var dateNowGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.Date = time.Now().Format(time_utils.USLayout)
	return *data, nil
}

var insuredNameGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.CompanyInfo.Name = data.App.CompanyInfo.Name
	return *data, nil
}

var agentNameGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	data.FillInputs.ComputedFields.AgentName = constants.InsuranceProducerNirvana.String()
	return *data, nil
}

var agentNumberGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	agentNumber, err := constants.InsuranceProducerNirvana.AgentPhoneNumber(policyenums.ProgramTypeBusinessAuto)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get agent phone number for business auto")
	}
	data.FillInputs.ComputedFields.AgentNumber = agentNumber
	return *data, nil
}

var policyEffectiveDateGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	date := data.App.EffectiveDurationStart.Format(time_utils.USLayout)
	data.FillInputs.ComputedFields.PolicyInfo.PolicyPeriod.From.Date = date
	return *data, nil
}

var policyExpirationDateGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	date := data.App.EffectiveDurationEnd.Format(time_utils.USLayout)
	data.FillInputs.ComputedFields.PolicyInfo.PolicyPeriod.To.Date = date
	return *data, nil
}

var acceptanceOfTerrorismCoverageGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasTerrorismCoverage, err := hasTerrorismCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for terrorism coverage")
	}
	data.FillInputs.ComputedFields.PolicyInfo.AcceptanceOfTerrorismCoverage = requests.ReturnExportedValue(hasTerrorismCoverage)
	return *data, nil
}

var declineOfTerrorismCoverageGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasTerrorismCoverage, err := hasTerrorismCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for terrorism coverage")
	}
	data.FillInputs.ComputedFields.PolicyInfo.DeclineOfTerrorismCoverage = requests.ReturnExportedValue(!hasTerrorismCoverage)
	return *data, nil
}

var brokerNameGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	entityLicense, err := deps.EntityLicence.GetByEntityIDAndUSState(ctx, data.App.AgencyID, data.App.CompanyInfo.USState)
	if err != nil {
		log.Info(
			ctx,
			"failed to get entity license for agency ID %s and US state %s: %v",
			log.Any("agencyID", data.App.AgencyID),
			log.Any("USState", data.App.CompanyInfo.USState),
			log.Err(err),
		)
		return *data, nil
	}
	if entityLicense != nil && entityLicense.EntityName != nil {
		data.FillInputs.ComputedFields.Broker = *entityLicense.EntityName
	}
	return *data, nil
}

var brokerMailingAddressGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	entityLicense, err := deps.EntityLicence.GetByEntityIDAndUSState(
		ctx,
		data.App.AgencyID,
		data.App.CompanyInfo.USState,
	)
	if err != nil {
		log.Info(
			ctx,
			"failed to get entity license for agency ID %s and US state %s: %v",
			log.Any("agencyID", data.App.AgencyID),
			log.Any("USState", data.App.CompanyInfo.USState),
			log.Err(err),
		)
		return *data, nil
	}

	if entityLicense != nil && entityLicense.BusinessAddress != nil {
		data.FillInputs.ComputedFields.BrokerMailingAddress = *entityLicense.BusinessAddress.AsString()
	}
	return *data, nil
}

var formsOfBusinessLLCGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	if fill_inputs.GetFormOfBusiness(data.App.CompanyInfo.Name) == fill_inputs.FormOfBusinessLLC {
		data.FillInputs.ComputedFields.CompanyInfo.FormOfBusinessLLC = requests.ReturnExportedValue(true)
	}
	return *data, nil
}

var formsOfBusinessCorporationGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	if fill_inputs.GetFormOfBusiness(data.App.CompanyInfo.Name) == fill_inputs.FormOfBusinessCorporation {
		data.FillInputs.ComputedFields.CompanyInfo.FormOfBusinessCorporation = requests.ReturnExportedValue(true)
	}
	return *data, nil
}

var totalPremiumGen = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	totalCharge, err := getTotalPremium(data.IB)
	if err != nil {
		log.Error(ctx, "failed to get total premium: %v", log.Err(err))
		return *data, errors.Wrapf(err, "failed to get total premium")
	}

	data.FillInputs.ComputedFields.PolicyInfo.TotalPremium = str_utils.NumberToLocaleString(totalCharge, 0)

	return *data, nil
}

var insuredPhysicalAddressGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	primaryInsuredAddress, err := getPrimaryInsuredAddress(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get primary insured address")
	}
	if primaryInsuredAddress == nil {
		return *data, nil // No address found
	}

	// Safely build the physical address string with nil checks
	var addressParts []string

	if primaryInsuredAddress.Street != nil && *primaryInsuredAddress.Street != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.Street)
	}
	if primaryInsuredAddress.City != nil && *primaryInsuredAddress.City != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.City)
	}
	if primaryInsuredAddress.State != nil && *primaryInsuredAddress.State != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.State)
	}
	if primaryInsuredAddress.ZipCode != nil && *primaryInsuredAddress.ZipCode != "" {
		addressParts = append(addressParts, *primaryInsuredAddress.ZipCode)
	}

	physicalAddress := ""
	if len(addressParts) > 0 {
		physicalAddress = addressParts[0]
		for i := 1; i < len(addressParts); i++ {
			physicalAddress += ", " + addressParts[i]
		}
	}

	data.FillInputs.ComputedFields.CompanyInfo.Address.PhysicalAddress = physicalAddress
	return *data, nil
}

var coverageUninsuredMotoristPropertyDamageLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	limit := getSubCoverageLimit(data.IB, enums.CoverageUninsuredMotoristPropertyDamage)
	if limit != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageUninsuredMotoristPropertyDamage.Limit = limit
	}
	return *data, nil
}

var coveredALHiredAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasHiredAuto, err := hasHiredAutoLiabilityCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for hired auto liability coverage")
	}
	if hasHiredAuto {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageHiredAutoLiability.CoveredAutosValue = "8"
	}
	return *data, nil
}

var coveredALNonOwnedAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasNonOwned, err := hasNonOwnedAutoCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for non-owned auto coverage")
	}
	if hasNonOwned {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageNonOwnedAutoLiability.CoveredAutosValue = "9"
	}
	return *data, nil
}

var umCoveredAutosHiredAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	state := data.App.CompanyInfo.USState
	hasHiredAuto, err := hasHiredAutoLiabilityCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for hired auto liability coverage")
	}
	hasUM, err := hasUMCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for UM coverage")
	}
	if (state == us_states.AZ || state == us_states.IL) && hasHiredAuto && hasUM {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageUninsuredMotoristHiredAutos.CoveredAutosValue = "8"
	}
	return *data, nil
}

var umCoveredAutosNonOwnedAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	state := data.App.CompanyInfo.USState
	hasNonOwned, err := hasNonOwnedAutoCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for non-owned auto coverage")
	}
	hasUM, err := hasUMCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for UM coverage")
	}
	if (state == us_states.AZ || state == us_states.IL) && hasNonOwned && hasUM {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageUninsuredMotoristNonOwnedAutos.CoveredAutosValue = "9"
	}
	return *data, nil
}

var uimCoveredAutosHiredAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	state := data.App.CompanyInfo.USState
	hasHiredAuto, err := hasHiredAutoLiabilityCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for hired auto liability coverage")
	}
	hasUIM, err := hasUIMCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for UIM coverage")
	}
	if (state == us_states.AZ || state == us_states.IL) && hasHiredAuto && hasUIM {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageUnderinsuredMotoristHiredAutos.CoveredAutosValue = "8"
	}
	return *data, nil
}

var uimCoveredAutosNonOwnedAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	state := data.App.CompanyInfo.USState
	hasNonOwned, err := hasNonOwnedAutoCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for non-owned auto coverage")
	}
	hasUIM, err := hasUIMCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for UIM coverage")
	}
	if (state == us_states.AZ || state == us_states.IL) && hasNonOwned && hasUIM {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageUnderinsuredMotoristNonOwnedAutos.CoveredAutosValue = "9"
	}
	return *data, nil
}

var towingAndLaborCoveredAutosGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasTowing, err := hasTowingCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for towing coverage")
	}
	if hasTowing {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageAPDTowingLabor.CoveredAutosValue = "7"
	}
	return *data, nil
}

var towingAndLaborLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasTowing, err := hasTowingCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for towing coverage")
	}
	if hasTowing {
		limit := getSubCoverageLimit(data.IB, enums.CoverageTowingLaborAndStorage)
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageAPDTowingLabor.Limit = limit
	}
	return *data, nil
}

var towingAndLaborPremiumGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	hasTowing, err := hasTowingCoverage(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to check for towing coverage")
	}
	if hasTowing {
		premium, err := getSubCoveragePremium(data.IB, enums.CoverageTowingLaborAndStorage)
		if err != nil {
			return *data, errors.Wrapf(err, "failed to get towing and labor premium")
		}
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageAPDTowingLabor.Premium = premium
	}
	return *data, nil
}

var slTaxGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	allCharges, err := getAllChargesFromBundle(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get charges from bundle")
	}

	policyCharges, err := model.CalculatePolicyCharges(allCharges, nil)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to calculate policy charges")
	}

	var surplusLinesTax string
	if !policyCharges.SurplusTax.IsZero() {
		taxFloat, _ := policyCharges.SurplusTax.Float64()
		surplusLinesTax = str_utils.NumberToLocaleString(taxFloat, 0)
	}
	data.FillInputs.ComputedFields.PolicyInfo.TotalSurplusTax = surplusLinesTax

	return *data, nil
}

var slStampingFeeGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	allCharges, err := getAllChargesFromBundle(data.IB)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get charges from bundle")
	}

	policyCharges, err := model.CalculatePolicyCharges(allCharges, nil)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to calculate policy charges")
	}

	var stampingFee string
	if !policyCharges.StampingFee.IsZero() {
		feeFloat, _ := policyCharges.StampingFee.Float64()
		stampingFee = str_utils.NumberToLocaleString(feeFloat, 0)
	}
	data.FillInputs.ComputedFields.PolicyInfo.TotalStampingFee = stampingFee

	return *data, nil
}

var coveragePersonalInjuryProtectionLimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	limit := getSubCoverageLimit(data.IB, enums.CoveragePersonalInjuryProtection)
	if limit != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoveragePersonalInjuryProtection.Limit = limit
	}
	return *data, nil
}

var coveragePersonalInjuryProtectionPremiumGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	premium, err := getSubCoveragePremium(data.IB, enums.CoveragePersonalInjuryProtection)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get personal injury protection premium")
	}
	if premium != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoveragePersonalInjuryProtection.Premium = premium
	}
	return *data, nil
}

var coverageUninsuredMotoristBILimitGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	limit := getSubCoverageLimit(data.IB, enums.CoverageUninsuredMotoristBodilyInjury)
	if limit != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageUninsuredMotoristBI.Limit = limit
	}
	return *data, nil
}

var coverageComprehensiveAndCollisionPremiumGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	premium, err := getSubCoveragePremium(data.IB, enums.CoverageAutoPhysicalDamage)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get APD premium")
	}
	if premium != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageAutoPhysicalDamage.Premium = premium
	}
	return *data, nil
}

var coverageComprehensivePremiumGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	premium, err := getSubCoveragePremium(data.IB, enums.CoverageComprehensive)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get comprehensive premium")
	}
	if premium != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageComprehensive.Premium = premium
	}
	return *data, nil
}

var coverageCollisionPremiumGen = func(
	_ context.Context,
	_ *fill_inputs.Deps,
	data *models.CommonData[*baModel.BusinessAutoApp, any, any],
) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
	premium, err := getSubCoveragePremium(data.IB, enums.CoverageCollision)
	if err != nil {
		return *data, errors.Wrapf(err, "failed to get collision premium")
	}
	if premium != "" {
		data.FillInputs.ComputedFields.CoveragesInfo.CoverageCollision.Premium = premium
	}
	return *data, nil
}
