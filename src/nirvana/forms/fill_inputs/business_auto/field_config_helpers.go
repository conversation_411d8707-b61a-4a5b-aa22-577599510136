package business_auto

import (
	"context"

	"github.com/cockroachdb/errors"

	baModel "nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/str_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/forms/fill_inputs/models/business_auto"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	policy_utils "nirvanatech.com/nirvana/policy/business_auto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

// hasSubCoverage checks if a specific sub-coverage exists under a specific primary coverage across all segments
func hasSubCoverage(ib *model.InsuranceBundle, primaryCoverage, subCoverage enums.Coverage) (bool, error) {
	segments := ib.GetSegments()
	if len(segments) == 0 {
		return false, errors.New("no insurance bundle segments found")
	}

	for _, segment := range segments {
		if segment == nil {
			continue
		}

		found, err := hasSubCoverageInSegment(segment, primaryCoverage, subCoverage)
		if err != nil {
			return false, err
		}
		if found {
			return true, nil
		}
	}

	return false, nil
}

// hasSubCoverageInSegment checks if a specific sub-coverage exists under a specific primary coverage in a single segment
func hasSubCoverageInSegment(segment *model.InsuranceBundleSegment, primaryCoverage, subCoverage enums.Coverage) (bool, error) {
	policies := segment.GetPolicies()
	for _, policy := range policies {
		if policy == nil {
			return false, errors.New("policy in insurance bundle segment is nil")
		}
		covs := policy.GetCoverages()
		for _, cov := range covs {
			if cov == nil {
				return false, errors.New("coverage in policy is nil")
			}
			covEnum, err := enums.CoverageString(cov.GetId())
			if err != nil {
				return false, errors.Wrapf(err, "failed to parse coverage ID %s", cov.GetId())
			}
			if covEnum != primaryCoverage {
				continue
			}
			for _, subCov := range cov.GetSubCoverages() {
				if subCov == nil {
					return false, errors.New("sub-coverage in coverage is nil")
				}
				subCovEnum, err := enums.CoverageString(subCov.GetId())
				if err != nil {
					return false, errors.Wrapf(err, "failed to parse sub-coverage ID %s", subCov.GetId())
				}
				if subCovEnum == subCoverage {
					return true, nil
				}
			}
		}
	}
	return false, nil
}

// hasTerrorismCoverage checks if terrorism coverage is included in the application
func hasTerrorismCoverage(ib *model.InsuranceBundle) (bool, error) {
	return hasSubCoverage(ib, enums.CoverageAutoLiability, enums.CoverageTerrorism)
}

// hasTowingCoverage checks if towing coverage is included in the application
func hasTowingCoverage(ib *model.InsuranceBundle) (bool, error) {
	return hasSubCoverage(ib, enums.CoverageAutoPhysicalDamage, enums.CoverageTowingLaborAndStorage)
}

// isCoveragePresent checks if a coverage exists anywhere in the insurance bundle (as primary or sub-coverage) across all segments
func isCoveragePresent(ib *model.InsuranceBundle, coverage enums.Coverage) (bool, error) {
	segments := ib.GetSegments()
	if len(segments) == 0 {
		return false, errors.New("no insurance bundle segments found")
	}

	for _, segment := range segments {
		if segment == nil {
			continue
		}

		found, err := isCoveragePresentInSegment(segment, coverage)
		if err != nil {
			return false, err
		}
		if found {
			return true, nil
		}
	}

	return false, nil
}

// isCoveragePresentInSegment checks if a coverage exists in a single segment (as primary or sub-coverage)
func isCoveragePresentInSegment(segment *model.InsuranceBundleSegment, coverage enums.Coverage) (bool, error) {
	coverageID := coverage.String()

	policies := segment.GetPolicies()
	for _, policy := range policies {
		if policy == nil {
			continue
		}

		// Check primary coverages
		for _, cov := range policy.GetCoverages() {
			if cov == nil {
				continue
			}
			if cov.GetId() == coverageID {
				return true, nil
			}

			// Check sub-coverages
			for _, subCov := range cov.GetSubCoverages() {
				if subCov != nil && subCov.GetId() == coverageID {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// hasHiredAutoLiabilityCoverage checks if hired auto liability coverage is present
func hasHiredAutoLiabilityCoverage(ib *model.InsuranceBundle) (bool, error) {
	return isCoveragePresent(ib, enums.CoverageHiredAutoLiab)
}

// hasNonOwnedAutoCoverage checks if non-owned auto coverage is present
func hasNonOwnedAutoCoverage(ib *model.InsuranceBundle) (bool, error) {
	return isCoveragePresent(ib, enums.CoverageNonOwnedAuto)
}

// hasUMCoverage checks if uninsured motorist coverage is present
func hasUMCoverage(ib *model.InsuranceBundle) (bool, error) {
	return isCoveragePresent(ib, enums.CoverageUM)
}

// hasUIMCoverage checks if underinsured motorist coverage is present
func hasUIMCoverage(ib *model.InsuranceBundle) (bool, error) {
	return isCoveragePresent(ib, enums.CoverageUIM)
}

// getPrimaryInsuredAddress retrieves the first available primary insured address from segments
func getPrimaryInsuredAddress(ib *model.InsuranceBundle) (*proto.Address, error) {
	segments := ib.GetSegments()
	if len(segments) == 0 {
		return nil, errors.New("no insurance bundle segments found")
	}

	// Check segments in normal order
	for _, segment := range segments {
		if segment == nil {
			continue
		}

		primaryInsured := segment.GetPrimaryInsured()
		if primaryInsured != nil && primaryInsured.Address != nil {
			return primaryInsured.Address, nil
		}
	}

	return nil, nil //nolint:nilnil
}

func createPolicyNumberConfig(coverage enums.Coverage, required bool) models.FieldConfig[*baModel.BusinessAutoApp, any, any] {
	return business_auto.NewFieldConfig(
		func(
			ctx context.Context,
			_ *fill_inputs.Deps,
			data *models.CommonData[*baModel.BusinessAutoApp, any, any],
		) (models.CommonData[*baModel.BusinessAutoApp, any, any], error) {
			shortId := data.App.ShortID
			effectiveDate := data.App.EffectiveDurationStart

			policyNumber, err := policy_utils.GeneratePolicyNumber(
				coverage,
				effectiveDate,
				string(shortId),
			)
			if err != nil {
				return *data, errors.Wrapf(err, "failed to generate policy number for coverage %s", coverage)
			}

			if err := models.SetPolicyNumberForCoverage(data, coverage, policyNumber.String()); err != nil {
				return *data, err
			}
			return *data, nil
		},
		nil,
		required,
	)
}

// getSubCoverageLimit retrieves the limit amount for a Coverage
func getSubCoverageLimit(ib *model.InsuranceBundle, coverage enums.Coverage) string {
	result, _ := getSubCoverageValue(ib, coverage, func(ib *model.InsuranceBundle, coverage enums.Coverage) (float64, bool, error) {
		amount, found := getLimit(ib, coverage)
		return amount, found, nil
	})
	return result
}

// getSubCoveragePremium retrieves the premium amount for a Coverage
func getSubCoveragePremium(ib *model.InsuranceBundle, coverage enums.Coverage) (string, error) {
	return getSubCoverageValue(ib, coverage, getPremium)
}

// getSubCoverageValue is a generic helper that retrieves a value for a Coverage
func getSubCoverageValue(ib *model.InsuranceBundle, coverage enums.Coverage, getter func(*model.InsuranceBundle, enums.Coverage) (float64, bool, error)) (string, error) {
	amount, found, err := getter(ib, coverage)
	if err != nil {
		return "", err
	}
	if found {
		return str_utils.NumberToLocaleString(amount, 0), nil
	}
	return "", nil
}

// getLimit retrieves the limit amount for a Coverage from all segments
func getLimit(ib *model.InsuranceBundle, coverage enums.Coverage) (float64, bool) {
	segments := ib.GetSegments()
	if len(segments) == 0 {
		return 0, false
	}

	coverageStr := coverage.String()

	// Check segments in normal order
	for _, segment := range segments {
		if segment == nil {
			continue
		}

		coverageLimits := segment.GetCoverageCriteria().GetLimits()
		for _, limit := range coverageLimits {
			for _, subCoverage := range limit.SubCoverageIds {
				if subCoverage == coverageStr {
					return limit.Amount, true
				}
			}
		}
	}
	return 0, false
}

// getTotalPremium retrieves the total premium for all charges excluding surplus tax and stamping fees
func getTotalPremium(ib *model.InsuranceBundle) (float64, error) {
	allCharges, err := getAllChargesFromBundle(ib)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to get all charges from bundle")
	}

	// Calculate total premium excluding surplus tax and stamping fees
	totalPremium, err := model.CalculateFilteredChargesTotalPremium(allCharges, nil)
	if err != nil {
		return 0, errors.Wrapf(err, "failed to calculate total premium")
	}

	totalCharge, _ := totalPremium.Float64()
	return totalCharge, nil
}

// getPremium retrieves the premium amount for a Coverage
func getPremium(ib *model.InsuranceBundle, coverage enums.Coverage) (float64, bool, error) {
	allCharges, err := getAllChargesFromBundle(ib)
	if err != nil {
		return 0, false, errors.Wrapf(err, "failed to get all charges from bundle")
	}

	// Calculate base charges by sub-coverage
	_, _, chargesBySubCov, _, err := model.CalculateBaseChargeDistributionAtAppSubCovLevel(allCharges, nil)
	if err != nil {
		return 0, false, errors.Wrapf(err, "failed to calculate charge distribution at sub-coverage level")
	}

	amount, exists := chargesBySubCov[coverage]
	if !exists {
		return 0, false, nil
	}

	return *amount, true, nil
}

func getAllChargesFromBundle(ib *model.InsuranceBundle) ([]*ptypes.Charge, error) {
	segments := ib.GetSegments()
	if len(segments) == 0 {
		return nil, errors.New("no segments found")
	}

	var allCharges []*ptypes.Charge
	for _, segment := range segments {
		if segment == nil {
			continue
		}
		for _, policy := range segment.GetPolicies() {
			if policy == nil || policy.Charges == nil {
				continue
			}
			allCharges = append(allCharges, policy.Charges.Charges...)
		}
	}
	return allCharges, nil
}
