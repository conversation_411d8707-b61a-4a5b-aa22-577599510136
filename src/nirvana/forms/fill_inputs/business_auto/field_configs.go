package business_auto

import (
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/forms/fill_inputs/models/business_auto"
)

var policyNumberAL = createPolicyNumberConfig(app_enums.CoverageAutoLiability, true)

var policyEffectiveDate = business_auto.NewFieldConfig(policyEffectiveDateGen, nil, true)

var policyExpirationDate = business_auto.NewFieldConfig(policyExpirationDateGen, nil, true)

var carrierName = business_auto.NewFieldConfig(carrierNameGen, carrierNameValidator, true)

var insuredName = business_auto.NewFieldConfig(insuredNameGen, nil, true)

var insuranceCarrierOnFormFooter = business_auto.NewFieldConfig(insuranceCarrierOnFormFooterGen, nil, false)

var insuranceCarrierWebAddress = business_auto.NewFieldConfig(insuranceCarrierWebAddressGen, nil, false)

var insuranceCarrierPhoneNumber = business_auto.NewFieldConfig(insuranceCarrierPhoneNumberGen, nil, false)

var dateNow = business_auto.NewFieldConfig(dateNowGen, nil, false)

var acceptanceOfTerrorismCoverage = business_auto.NewFieldConfig(acceptanceOfTerrorismCoverageGen, nil, true)

var declineOfTerrorismCoverage = business_auto.NewFieldConfig(declineOfTerrorismCoverageGen, nil, true)

var carrierMailingAddress = business_auto.NewFieldConfig(carrierMailingAddressGen, nil, false)

var brokerName = business_auto.NewFieldConfig(brokerNameGen, nil, false)

var brokerMailingAddress = business_auto.NewFieldConfig(brokerMailingAddressGen, nil, false)

var formsOfBusinessLLC = business_auto.NewFieldConfig(formsOfBusinessLLCGen, nil, false)

var formsOfBusinessCorporation = business_auto.NewFieldConfig(formsOfBusinessCorporationGen, nil, false)

var totalPremium = business_auto.NewFieldConfig(totalPremiumGen, nil, true)

var insuredPhysicalAddress = business_auto.NewFieldConfig(insuredPhysicalAddressGen, nil, false)

var coverageUninsuredMotoristPropertyDamageLimit = business_auto.NewFieldConfig(coverageUninsuredMotoristPropertyDamageLimitGen, nil, false)

var agentName = business_auto.NewFieldConfig(agentNameGen, nil, false)

var agentNumber = business_auto.NewFieldConfig(agentNumberGen, nil, false)

var coveredALHiredAutos = business_auto.NewFieldConfig(coveredALHiredAutosGen, nil, false)

var coveredALNonOwnedAutos = business_auto.NewFieldConfig(coveredALNonOwnedAutosGen, nil, false)

var umCoveredAutosHiredAutos = business_auto.NewFieldConfig(umCoveredAutosHiredAutosGen, nil, false)

var umCoveredAutosNonOwnedAutos = business_auto.NewFieldConfig(umCoveredAutosNonOwnedAutosGen, nil, false)

var uimCoveredAutosHiredAutos = business_auto.NewFieldConfig(uimCoveredAutosHiredAutosGen, nil, false)

var uimCoveredAutosNonOwnedAutos = business_auto.NewFieldConfig(uimCoveredAutosNonOwnedAutosGen, nil, false)

var towingAndLaborCoveredAutos = business_auto.NewFieldConfig(towingAndLaborCoveredAutosGen, nil, false)

var towingAndLaborLimit = business_auto.NewFieldConfig(towingAndLaborLimitGen, nil, false)

var towingAndLaborPremium = business_auto.NewFieldConfig(towingAndLaborPremiumGen, nil, false)

var slTax = business_auto.NewFieldConfig(slTaxGen, nil, false)

var slStampingFee = business_auto.NewFieldConfig(slStampingFeeGen, nil, false)

var coveragePersonalInjuryProtectionLimit = business_auto.NewFieldConfig(coveragePersonalInjuryProtectionLimitGen, nil, false)

var coveragePersonalInjuryProtectionPremium = business_auto.NewFieldConfig(coveragePersonalInjuryProtectionPremiumGen, nil, false)

var coverageUninsuredMotoristBILimit = business_auto.NewFieldConfig(coverageUninsuredMotoristBILimitGen, nil, false)

var coverageComprehensiveAndCollisionPremium = business_auto.NewFieldConfig(coverageComprehensiveAndCollisionPremiumGen, nil, false)

var coverageComprehensivePremium = business_auto.NewFieldConfig(coverageComprehensivePremiumGen, nil, false)

var coverageCollisionPremium = business_auto.NewFieldConfig(coverageCollisionPremiumGen, nil, false)
