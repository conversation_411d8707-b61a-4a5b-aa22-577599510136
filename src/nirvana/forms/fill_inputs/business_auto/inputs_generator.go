package business_auto

import (
	"context"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/forms/fill_inputs"
	"nirvanatech.com/nirvana/forms/fill_inputs/core"
	"nirvanatech.com/nirvana/forms/fill_inputs/models"
	"nirvanatech.com/nirvana/policy_common/forms_generator"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

var inputsGeneratorFunc = func(
	ctx context.Context,
	deps *fill_inputs.Deps,
	formComp compilation.FormsCompilation,
	handler *core.FillInputsEntityHandler[*model.BusinessAutoApp, any, any],
) (*forms_generator.FillInputsNew, error) {
	if formComp == nil || deps == nil {
		return nil, errors.New("nil form compilation or dependencies")
	}

	data, err := handler.GenerateCommonData(ctx, deps, formComp)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate common data")
	}

	data, err = handler.GenerateComputedFields(ctx, deps, *data)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to generate computed fields")
	}

	return data.FillInputs, nil
}

var computedFieldConfigs = models.FieldConfigs[*model.BusinessAutoApp, any, any]{
	"PolicyInfo.PolicyNumbers.CoverageAutoLiability": policyNumberAL,
	"Broker":                                   brokerName,
	"BrokerMailingAddress":                     brokerMailingAddress,
	"CompanyInfo.Address.PhysicalAddress":      insuredPhysicalAddress,
	"CompanyInfo.FormOfBusinessCorporation":    formsOfBusinessCorporation,
	"CompanyInfo.FormOfBusinessLLC":            formsOfBusinessLLC,
	"CompanyInfo.Name":                         insuredName,
	"Date":                                     dateNow,
	"InsuranceCarrier":                         carrierName,
	"InsuranceCarrierMailingAddress":           carrierMailingAddress,
	"InsuranceCarrierOnFormFooter":             insuranceCarrierOnFormFooter,
	"AgentName":                                agentName,
	"AgentNumber":                              agentNumber,
	"PolicyInfo.AcceptanceOfTerrorismCoverage": acceptanceOfTerrorismCoverage,
	"PolicyInfo.DeclineOfTerrorismCoverage":    declineOfTerrorismCoverage,
	"PolicyInfo.PolicyPeriod.From.Date":        policyEffectiveDate,
	"PolicyInfo.PolicyPeriod.To.Date":          policyExpirationDate,
	"PolicyInfo.TotalPremium":                  totalPremium,
	"CoveragesInfo.CoverageUninsuredMotoristPropertyDamage.Limit":               coverageUninsuredMotoristPropertyDamageLimit,
	"InsuranceCarrierPhoneNumber":                                               insuranceCarrierPhoneNumber,
	"InsuranceCarrierWebsite":                                                   insuranceCarrierWebAddress,
	"PolicyInfo.TotalSurplusTax":                                                slTax,
	"PolicyInfo.TotalStampingFee":                                               slStampingFee,
	"CoveragesInfo.CoverageHiredAutoLiability.CoveredAutosValue":                coveredALHiredAutos,
	"CoveragesInfo.CoverageNonOwnedAutoLiability.CoveredAutosValue":             coveredALNonOwnedAutos,
	"CoveragesInfo.CoverageUninsuredMotoristHiredAutos.CoveredAutosValue":       umCoveredAutosHiredAutos,
	"CoveragesInfo.CoverageUninsuredMotoristNonOwnedAutos.CoveredAutosValue":    umCoveredAutosNonOwnedAutos,
	"CoveragesInfo.CoverageUnderinsuredMotoristHiredAutos.CoveredAutosValue":    uimCoveredAutosHiredAutos,
	"CoveragesInfo.CoverageUnderinsuredMotoristNonOwnedAutos.CoveredAutosValue": uimCoveredAutosNonOwnedAutos,
	"CoveragesInfo.CoverageAPDTowingLabor.CoveredAutosValue":                    towingAndLaborCoveredAutos,
	"CoveragesInfo.CoverageAPDTowingLabor.Limit":                                towingAndLaborLimit,
	"CoveragesInfo.CoverageAPDTowingLabor.Premium":                              towingAndLaborPremium,
	"CoveragesInfo.CoveragePersonalInjuryProtection.Limit":                      coveragePersonalInjuryProtectionLimit,
	"CoveragesInfo.CoveragePersonalInjuryProtection.Premium":                    coveragePersonalInjuryProtectionPremium,
	"CoveragesInfo.CoverageUninsuredMotoristBI.Limit":                           coverageUninsuredMotoristBILimit,
	"CoveragesInfo.CoverageComprehensiveAndCollision.Premium":                   coverageComprehensiveAndCollisionPremium,
	"CoveragesInfo.CoverageComprehensive.Premium":                               coverageComprehensivePremium,
	"CoveragesInfo.CoverageCollision.Premium":                                   coverageCollisionPremium,
}
