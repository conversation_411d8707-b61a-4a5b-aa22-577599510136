databases:
  snowflake:
    connections:
      fmcsaDbt:
        dbname: "FMCSA_BULK"
        schema: "DBT"
        warehouse: "COMPUTE_WH"
      datascience:
        dbname: "analytics_prod"
        schema: "ds"
        warehouse: "ds_wh"
      smartdrive:
        dbname: "analytics_db"
        schema: "smartdrive"
        warehouse: "compute_wh"
      analyticsCore:
        dbname: "analytics_prod"
        schema: "core"
        warehouse: "compute_wh"
    users:
      fmcsa:
        accountname: "ad91921.us-east-2.aws"
        username: "prod_fmcsa"
        role: "FMCSA_ADMIN"
      analyticsCore:
        accountname: "ad91921.us-east-2.aws"
        username: "PROD_REPORTING_JOB"
        role: "ANALYTICS_REPORTER"
  neo4j:
    name: vehicles
    host: 965cebdf.databases.neo4j.io
    port: 7687
    username: neo4j
    password: INVALID

scrapers:
  fmcsa: "http://fmcsa-scraper.default.app.nirvana.internal:8091"


services:
  metaflowServiceAddr: "metaflow-grpc.default.app.nirvana.internal:50042"
  dataInfraJobberAddr: "data-infra-job-processor.default.app.nirvana.internal:56225"
  quotingJobberAddr: "quoting-job-processor.default.app.nirvana.internal:56224"
  telematicsGRPCAddr: "telematics-grpc-server.default.app.nirvana.internal:9091"
  vehiclesServiceGRPCAddr: "vehicles-grpc-server.default.app.nirvana.internal:9092"
  quoteScraperAddr: "quote-scraper-grpc-server.default.app.nirvana.internal:9093"
  quotaManagerGRPCAddr: "quota-manager-grpc-server.default.app.nirvana.internal:9094"
  fmcsaDataProviderGRPCAddr: "fmcsa-data-provider-grpc-server.default.app.nirvana.internal:9095"
  llmOpsServiceGRPCAddr: "llmops-server.default.app.nirvana.internal:50051"
  uwAIServiceGRPCAddr: "uw-ai-server.default.app.nirvana.internal:50052"
  oauth:
    samsara: "samsara-oauth.default.app.nirvana.internal:8181"
    samsaraSafety: "samsarasafety-oauth.default.app.nirvana.internal:8181"
    keeptruckin: "keeptruckin-oauth.default.app.nirvana.internal:8181"
    keeptruckinSafety: "keeptruckinsafety-oauth.default.app.nirvana.internal:8181"
  distsemSingletonAddr: "distsem-server.default.app.nirvana.internal:34787"
  pdfGen:
    pdfgenGRPCAddr: "pdfgen-server.default.app.nirvana.internal:33435"
  mvrService:
    mvrCacheServerAddress: "mvr-cache-server.default.app.nirvana.internal"
    mvrCacheServerPort: 58727

verisk:
  useCertAuth: false

telematics:
  dataPlatformBackend: S3
  enabledProviders:
    - Samsara
    - KeepTruckin
    - Terminal
    - SmartDrive
  integrations:
    keepTruckin:
      maxConcurrentReqs: 5
    samsara:
      global:
        rate: 150
        burst: 150
      endpoints:
        /fleet/vehicles/stats/history:
          rate: 50
          burst: 50
        /fleet/drivers:
          rate: 5
          burst: 5
        /fleet/hos/logs:
          rate: 5
          burst: 5
        /fleet/vehicles:
          rate: 25
          burst: 25
        /gateways:
          rate: 5
          burst: 5
        /fleet/settings/safety:
          rate: 5
          burst: 5
        /fleet/safety-events:
          rate: 25
          burst: 25
        /v1/fleet/drivers/*/safety/score:
          rate: 5
          burst: 5
        /v1/fleet/vehicles/*/safety/score:
          rate: 5
          burst: 5
        /v1/fleet/vehicles/*/safety/harsh_event:
          rate: 50
          burst: 50
        /me:
          rate: 5
          burst: 5
        /v1/fleet/trips:
          rate: 50
          burst: 50
        /fleet/reports/ifta/vehicle:
          rate: 25
          burst: 25
        /fleet/driver-vehicle-assignments:
          rate: 5
          burst: 5
        /tags:
          rate: 5
          burst: 5
    terminal:
      url:
        host: api.withterminal.com
      globalRateLimit:
        rate: 50
        burst: 1
      connectionEndpointRateLimit: #context: https://nirvana-tech.slack.com/archives/C04HHKMKY10/p1751451924640749
        "/vehicles/*/stats/historical":
          rate: 5 #per connection per /vehicles/*/stats/historical
          burst: 1
        "/vehicles/*/locations":
          rate: 5 #per connection per vehicles/*/locations
          burst: 1
    openmeteo:
      historicalForecastHostname: https://customer-historical-forecast-api.open-meteo.com
      archiveHostname: https://customer-archive-api.open-meteo.com
      highResolutionCutoffDate: 2018-01-01
      ratelimit:
        rate: 100
        burst: 100
      maxBatchSize: 100
    carfaxHost: https://socket.carfaxbig.com
    cmt:
      host: https://prod.cmtelematics.com
      inputBucket: cmt-prod-nirvana-input
      outputBucket: cmt-prod-nirvana-output

jobberSingletonsConfig:
  ecsMonitorAddr: "jobber-monitor.default.app.nirvana.internal:56666"

jobberProcessors:
  standard:
    useEmbeddedStore: false
    stableAddress: "job-processor.default.app.nirvana.internal:56223"
    useECSOpsDirectorForProcessor: true
    registerGrpcServers: true
    storeConfig:
      type: PgTableBacked
      pgTableBackedSpec:
        tableNameWithSchema: "public.job_runs"
        storeId: "82eae320-21ed-41cc-abf3-5952e435c3e5"
    monitorConfig:
      type: DbBackedMonitor
  dataInfra:
    useEmbeddedStore: false
    maxResources: 5000
    stableAddress: "data-infra-job-processor.default.app.nirvana.internal:56225"
    useECSOpsDirectorForProcessor: true
    registerGrpcServers: true
    storeConfig:
      type: PgTableBacked
      pgTableBackedSpec:
        tableNameWithSchema: "public.job_runs"
        storeId: "73004be8-85bd-4ff8-9afa-07a597d2ba01"
    monitorConfig:
      type: DbBackedMonitor
  quoting:
    useEmbeddedStore: false
    stableAddress: "quoting-job-processor.default.app.nirvana.internal:56224"
    useECSOpsDirectorForProcessor: true
    registerGrpcServers: true
    storeConfig:
      type: PgTableBacked
      pgTableBackedSpec:
        tableNameWithSchema: "public.job_runs"
        storeId: "7c954af2-8c21-4c33-b982-ca667889cc33"
    monitorConfig:
      type: DbBackedMonitor
  event:
    useEmbeddedStore: false
    stableAddress: "event-job-processor.default.app.nirvana.internal:56222"
    useECSOpsDirectorForProcessor: true
    registerGrpcServers: true
    storeConfig:
      type: PgTableBacked
      pgTableBackedSpec:
        tableNameWithSchema: "jobber.events"
        storeId: "ed13f9dd-9322-40ea-9ef5-c7d450c4b663"
    monitorConfig:
      type: DbBackedMonitor

  safety:
    useEmbeddedStore: false
    stableAddress: "safety-job-processor.default.app.nirvana.internal:56226"
    useECSOpsDirectorForProcessor: true
    registerGrpcServers: true
    storeConfig:
      type: PgTableBacked
      pgTableBackedSpec:
        tableNameWithSchema: "public.job_runs"
        storeId: "002491c5-c22d-42ea-849e-00ada8be749b"
    monitorConfig:
      type: DbBackedMonitor
  simulation:
    useEmbeddedStore: false
    stableAddress: "simulation-job-processor.default.app.nirvana.internal:56227"
    useECSOpsDirectorForProcessor: true
    registerGrpcServers: true
    storeConfig:
      type: PgTableBacked
      pgTableBackedSpec:
        tableNameWithSchema: "public.job_runs"
        storeId: "b7e2a1d4-5c3f-4e8a-9f2b-8c1a7e6d4f3b"
    monitorConfig:
      type: DbBackedMonitor

infra:
  tracing:
    enabled: true
    sampleRate: 0.1
  logging:
    humanReadable: false
    level: info
  lambdaGrpc:
    functionName: "grpc_lambda"

fmcsa:
  dbtTransformationJobName: "prod/fmcsa"

refresherConfig:
  snowflakeS3Bucket: nirvana-snowflake-stage
  publicFilesS3Bucket: nirvana-fmcsa-public-files

productTools:
  pibitAiConfig:
    urlPrefix: "https://api.pibit.ai"
    apiKey: "<INVALID>"
  # TODO(@ayusha) move to TF env variables
  dsModelServer:
    host: "http://hosting.ml.production.nirvanatech.internal"
    port: 8000
  gorulesServer:
    url: "https://gorules.dev.nirvanatech.com/"
