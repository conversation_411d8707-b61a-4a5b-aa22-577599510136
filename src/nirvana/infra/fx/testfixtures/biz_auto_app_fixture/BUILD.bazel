load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "biz_auto_app_fixture",
    srcs = ["biz_auto_app_fixture.go"],
    importpath = "nirvanatech.com/nirvana/infra/fx/testfixtures/biz_auto_app_fixture",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/business-auto/enums",
        "//nirvana/business-auto/model",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/short_id",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/business_auto/application",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/nirvanaapp/enums",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/rating/rtypes",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)
