package biz_auto_app_fixture

import (
	"time"

	"github.com/google/uuid"
	"go.uber.org/fx"
	ba_enums "nirvanatech.com/nirvana/business-auto/enums"
	ba_models "nirvanatech.com/nirvana/business-auto/model"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/short_id"
	"nirvanatech.com/nirvana/common-go/us_states"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/business_auto/application"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	nirvanaapp_enums "nirvanatech.com/nirvana/nirvanaapp/enums"
	nirvanaapp_models "nirvanatech.com/nirvana/nirvanaapp/models/application"
	"nirvanatech.com/nirvana/rating/rtypes"
)

type BizAutoAppFixture struct {
	// MinimalApp is a small, valid Business-Auto application pre-inserted into the DB.
	MinimalApp *ba_models.BusinessAutoApp

	wrapper application.Wrapper
}

func NewBizAutoAppFixture(lc fx.Lifecycle, wrapper application.Wrapper, usersFixture *users_fixture.UsersFixture) *BizAutoAppFixture {
	fixture := &BizAutoAppFixture{
		MinimalApp: createMinimalApp(usersFixture.AgencyProducer.ID),
		wrapper:    wrapper,
	}

	return fixture
}

func createMinimalApp(producerID uuid.UUID) *ba_models.BusinessAutoApp {
	now := time.Now()
	return &ba_models.BusinessAutoApp{
		ID:                     uuid.New(),
		DataContextID:          uuid.New(),
		ShortID:                short_id.ShortID("TEST123"),
		EffectiveDurationStart: now,
		EffectiveDurationEnd:   now.AddDate(1, 0, 0),
		CompanyInfo: ba_models.CompanyInfo{
			Name:                            "Test Company",
			NoOfPowerUnits:                  pointer_utils.Int32(1),
			NoOfEmployees:                   pointer_utils.Int32(1),
			PerOfEmployeesOperatingOwnAutos: pointer_utils.Float32(0),
			PrimaryIndustryClassification:   pointer_utils.ToPointer(ba_enums.IndustryClassificationWholesalersManufacturers),
			AnnualCostOfHire:                pointer_utils.Float64(50000.0),
			HasIndividualNamedInsured:       pointer_utils.Bool(false),
			MaximumValueOfHiredAutos:        pointer_utils.Float64(100000.0),
			USState:                         us_states.OH,
			Address: &nirvanaapp_models.Address{
				State:  us_states.OH.String(),
				City:   "Columbus",
				Street: "123 Main St",
				Zip:    "43322",
			},
		},
		VehiclesInfo: &[]ba_models.VehicleInfo{
			{
				Year:                             2020,
				Make:                             "Ford",
				Model:                            "F-150",
				VIN:                              "1HGBH41JXMN109186",
				VehicleType:                      nirvanaapp_enums.VehicleTypeTruck,
				WeightClass:                      ba_enums.WeightClassLight,
				SpecialtyVehicleType:             ba_enums.SpecialtyVehicleTypeBoomTruck0To49Feet,
				VehicleUse:                       ba_enums.VehicleUseTowingOperations,
				BusinessUse:                      ba_enums.BusinessUseCommercial,
				StateUsage:                       ba_enums.StateUsageIntrastate,
				RadiusClassification:             ba_enums.RadiusClassification0To100,
				PrincipalGaragingLocationZipCode: "43322",
			},
		},
		CoveragesInfo: &ba_models.CoveragesInfo{
			PrimaryCoverages: []ba_models.PrimaryCoverage{
				{
					ID:             app_enums.CoverageAutoLiability,
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage},
				},
			},
			Limits: []ba_models.Limit{
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage},
					Amount:         1000000,
					Grouping:       ba_models.LimitGroupingCombined,
				},
			},
			Deductibles: []ba_models.Deductible{
				{
					SubCoverageIDs: []app_enums.Coverage{app_enums.CoverageBodilyInjury, app_enums.CoveragePropertyDamage},
					Amount:         0,
				},
			},
		},
		FilingsInfo: &ba_models.FilingsInfo{
			HasMultiStateFilings:  false,
			HasSingleStateFilings: true,
			HasFMCSAFilings:       true,
			HasDOTFilings:         true,
		},
		ProducerID:        producerID,
		CreatedBy:         uuid.New(),
		CreatedAt:         now,
		UpdatedAt:         now,
		AgencyID:          uuid.New(),
		UWState:           ba_enums.UWStateCreated,
		SignaturePacketID: nil,
		DocumentsInfo: &ba_models.DocumentsInfo{
			QuotePDFHandleID: nil,
		},
		ModelPinConfig: &ba_models.ModelPinConfigInfo{RateML: ba_models.RateMLConfig{
			Provider: rtypes.ProviderNico,
			USState:  us_states.OH,
			Version:  rtypes.Version000,
		}},
	}
}

var _ = fxregistry.RegisterForTest(fx.Provide(NewBizAutoAppFixture))
