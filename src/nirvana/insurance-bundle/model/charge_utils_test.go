package model

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/time_utils"
	appEnums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func TestCalculateBaseChargeDistributionAtAppSubCovLevel(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	tests := []struct {
		name                     string
		charges                  []*ptypes.Charge
		rateBasisValue           *ptypes.RateBasisValue
		expectedBaseCharges      *float64
		expectedSurcharges       *float64
		expectedChargesBySubCov  map[appEnums.Coverage]*float64
		expectedChargesByVehicle map[string]map[appEnums.Coverage]*float64
		expectError              bool
		errorMsg                 string
	}{
		{
			name:                     "empty charges slice",
			charges:                  []*ptypes.Charge{},
			rateBasisValue:           nil,
			expectedBaseCharges:      nil,
			expectedSurcharges:       nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "single charge without distributions - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.50", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
			},
			rateBasisValue:           nil,
			expectedBaseCharges:      nil,
			expectedSurcharges:       nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "single charge with vehicle distribution",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("200.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.6"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.4"),
						},
					).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(200.00),
			expectedSurcharges:  nil,
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoveragePropertyDamage: floatPtr(200.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoveragePropertyDamage: floatPtr(120.00),
				},
				"vehicle2": {
					appEnums.CoveragePropertyDamage: floatPtr(80.00),
				},
			},
			expectError: false,
		},
		{
			name: "multiple charges without distributions - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("150.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("300.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("250.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_Cargo).
					Build(),
			},
			rateBasisValue:           nil,
			expectedBaseCharges:      nil,
			expectedSurcharges:       nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "multiple charges with distributions - different coverages",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("150.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("300.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle2", "1.0"),
						},
					).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(450.00),
			expectedSurcharges:  nil,
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury:     floatPtr(150.00),
				appEnums.CoverageGeneralLiability: floatPtr(300.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury: floatPtr(150.00),
				},
				"vehicle2": {
					appEnums.CoverageGeneralLiability: floatPtr(300.00),
				},
			},
			expectError: false,
		},
		{
			name: "charge with multiple sub-coverages mapping to primary coverage",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("400.00", effectiveDate).
					WithChargeableSubCoverageGroup(
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(400.00),
			expectedSurcharges:  nil,
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageAutoLiability: floatPtr(400.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageAutoLiability: floatPtr(400.00),
				},
			},
			expectError: false,
		},
		{
			name: "charge without sub-coverage group - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargedItem(ptypes.NewChargeChargedPolicy("POLICY123")).
					Build(),
			},
			rateBasisValue:           nil,
			expectedBaseCharges:      nil,
			expectedSurcharges:       nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "complex scenario with multiple vehicles and coverages",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("600.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.5"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.3"),
							ptypes.NewChargeDistributionItem("vehicle3", "0.2"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("400.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_GeneralLiability).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "0.7"),
							ptypes.NewChargeDistributionItem("vehicle2", "0.3"),
						},
					).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(1000.00),
			expectedSurcharges:  nil,
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury:     floatPtr(600.00),
				appEnums.CoverageGeneralLiability: floatPtr(400.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury:     floatPtr(300.00),
					appEnums.CoverageGeneralLiability: floatPtr(280.00),
				},
				"vehicle2": {
					appEnums.CoverageBodilyInjury:     floatPtr(180.00),
					appEnums.CoverageGeneralLiability: floatPtr(120.00),
				},
				"vehicle3": {
					appEnums.CoverageBodilyInjury: floatPtr(120.00),
				},
			},
			expectError: false,
		},
		{
			name: "charge with invalid fraction - should return error",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "invalid_fraction"),
						},
					).
					Build(),
			},
			rateBasisValue: nil,
			expectError:    true,
			errorMsg:       "invalid fraction",
		},
		{
			name: "charges without distributions - should be skipped",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					Build(),
			},
			rateBasisValue:           nil,
			expectedBaseCharges:      nil,
			expectedSurcharges:       nil,
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
		{
			name: "accumulating vehicle charges for same coverage",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(150.00),
			expectedSurcharges:  nil,
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury:   floatPtr(100.00),
				appEnums.CoveragePropertyDamage: floatPtr(50.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury:   floatPtr(100.00),
					appEnums.CoveragePropertyDamage: floatPtr(50.00),
				},
			},
			expectError: false,
		},
		{
			name: "base charge with surcharge",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("200.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(200.00),
			expectedSurcharges:  floatPtr(50.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoverageBodilyInjury: floatPtr(200.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoverageBodilyInjury: floatPtr(200.00),
				},
			},
			expectError: false,
		},
		{
			name: "multiple surcharges",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("300.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_PropertyDamage).
					WithDistribution(
						ptypes.Charge_DistributionType_Vehicle,
						[]*ptypes.Charge_DistributionItem{
							ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
						},
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("75.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("25.00", effectiveDate).
					Build(),
			},
			rateBasisValue:      nil,
			expectedBaseCharges: floatPtr(300.00),
			expectedSurcharges:  floatPtr(100.00),
			expectedChargesBySubCov: map[appEnums.Coverage]*float64{
				appEnums.CoveragePropertyDamage: floatPtr(300.00),
			},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{
				"vehicle1": {
					appEnums.CoveragePropertyDamage: floatPtr(300.00),
				},
			},
			expectError: false,
		},
		{
			name: "surcharge only - no base charges",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					Build(),
			},
			rateBasisValue:           nil,
			expectedBaseCharges:      nil,
			expectedSurcharges:       floatPtr(100.00),
			expectedChargesBySubCov:  map[appEnums.Coverage]*float64{},
			expectedChargesByVehicle: map[string]map[appEnums.Coverage]*float64{},
			expectError:              false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
				tt.charges,
				tt.rateBasisValue,
			)

			if tt.expectError {
				require.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectedBaseCharges, baseCharges)
			assert.Equal(t, tt.expectedSurcharges, surcharges)
			assert.Equal(t, tt.expectedChargesBySubCov, chargesBySubCov)
			assert.Equal(t, tt.expectedChargesByVehicle, chargesByVehicle)
		})
	}
}

func TestCalculateBaseChargeDistributionAtAppSubCovLevel_EdgeCases(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	t.Run("nil charges slice", func(t *testing.T) {
		t.Parallel()

		baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			nil,
			nil,
		)

		require.NoError(t, err)
		assert.Nil(t, baseCharges)
		assert.Nil(t, surcharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{}, chargesByVehicle)
	})

	t.Run("charge with zero amount and distribution", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("0.00", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
					},
				).
				Build(),
		}

		baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			nil,
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(0.00), baseCharges)
		assert.Nil(t, surcharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(0.00),
		}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{
			"vehicle1": {
				appEnums.CoverageBodilyInjury: floatPtr(0.00),
			},
		}, chargesByVehicle)
	})

	t.Run("charge with fractional amounts", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("100.00", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "0.33"),
						ptypes.NewChargeDistributionItem("vehicle2", "0.67"),
					},
				).
				Build(),
		}

		baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			nil,
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(100.00), baseCharges)
		assert.Nil(t, surcharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(100.00),
		}, chargesBySubCov)

		// Check that vehicle charges are properly calculated
		require.Contains(t, chargesByVehicle, "vehicle1")
		require.Contains(t, chargesByVehicle, "vehicle2")
		assert.NotNil(t, chargesByVehicle["vehicle1"][appEnums.CoverageBodilyInjury])
		assert.NotNil(t, chargesByVehicle["vehicle2"][appEnums.CoverageBodilyInjury])

		// Verify the approximate distribution (33% and 67%)
		vehicle1Amount := *chargesByVehicle["vehicle1"][appEnums.CoverageBodilyInjury]
		vehicle2Amount := *chargesByVehicle["vehicle2"][appEnums.CoverageBodilyInjury]
		assert.InDelta(t, 33.00, vehicle1Amount, 0.01) // 100.00 * 0.33 = 33.00
		assert.InDelta(t, 67.00, vehicle2Amount, 0.01) // 100.00 * 0.67 = 67.00
	})

	t.Run("charge with miles rate basis value and distribution", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithRateBasedBillingDetails(
					"0.05",
					&proto.Interval{
						Start: effectiveDate,
						End:   effectiveDate,
					},
					ptypes.RateBasis_RateBasis_Miles,
				).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "0.6"),
						ptypes.NewChargeDistributionItem("vehicle2", "0.4"),
					},
				).
				Build(),
		}

		baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			ptypes.NewRateBasisValueWithMiles(1000),
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(50.00), baseCharges) // 0.05 * 1000 = 50.00
		assert.Nil(t, surcharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(50.00),
		}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{
			"vehicle1": {
				appEnums.CoverageBodilyInjury: floatPtr(30.00), // 50.00 * 0.6
			},
			"vehicle2": {
				appEnums.CoverageBodilyInjury: floatPtr(20.00), // 50.00 * 0.4
			},
		}, chargesByVehicle)
	})

	t.Run("charge without distribution - should be skipped", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithAmountBasedBillingDetails("100.00", effectiveDate).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				Build(),
		}

		baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			nil,
		)

		require.NoError(t, err)
		assert.Nil(t, baseCharges)
		assert.Nil(t, surcharges)
		assert.Equal(t, map[appEnums.Coverage]*float64{}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{}, chargesByVehicle)
	})

	t.Run("surcharge with rate basis value", func(t *testing.T) {
		t.Parallel()

		charges := []*ptypes.Charge{
			ptypes.NewChargeBuilder().
				WithBaseChargeTypeWithoutExtraInfo().
				WithRateBasedBillingDetails(
					"0.1",
					&proto.Interval{
						Start: effectiveDate,
						End:   effectiveDate,
					},
					ptypes.RateBasis_RateBasis_Miles,
				).
				WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
				WithDistribution(
					ptypes.Charge_DistributionType_Vehicle,
					[]*ptypes.Charge_DistributionItem{
						ptypes.NewChargeDistributionItem("vehicle1", "1.0"),
					},
				).
				Build(),
			ptypes.NewChargeBuilder().
				WithDefaultSurchargeType_TestOnly().
				WithRateBasedBillingDetails(
					"0.02",
					&proto.Interval{
						Start: effectiveDate,
						End:   effectiveDate,
					},
					ptypes.RateBasis_RateBasis_Miles,
				).
				Build(),
		}

		baseCharges, surcharges, chargesBySubCov, chargesByVehicle, err := CalculateBaseChargeDistributionAtAppSubCovLevel(
			charges,
			ptypes.NewRateBasisValueWithMiles(500),
		)

		require.NoError(t, err)
		assert.Equal(t, floatPtr(50.00), baseCharges) // 0.1 * 500 = 50.00
		assert.Equal(t, floatPtr(10.00), surcharges)  // 0.02 * 500 = 10.00
		assert.Equal(t, map[appEnums.Coverage]*float64{
			appEnums.CoverageBodilyInjury: floatPtr(50.00),
		}, chargesBySubCov)
		assert.Equal(t, map[string]map[appEnums.Coverage]*float64{
			"vehicle1": {
				appEnums.CoverageBodilyInjury: floatPtr(50.00),
			},
		}, chargesByVehicle)
	})
}

func TestCalculateFeeChargeAndSurchargeDistribution(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	tests := []struct {
		name           string
		charges        []*ptypes.Charge
		rateBasisValue *ptypes.RateBasisValue
		expected       *PolicyCharge
		expectError    bool
		errorMsg       string
	}{
		{
			name:           "empty charges slice",
			charges:        []*ptypes.Charge{},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.Zero,
				Surcharge:   decimal.Zero,
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "single surcharge",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(100.00),
				Surcharge:   decimal.NewFromFloat(100.00),
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "single fee charge",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketRegularAdditionalInsured_TestOnly().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(50.00),
				Surcharge:   decimal.Zero,
				FeeCharge:   decimal.NewFromFloat(50.00),
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "multiple surcharges and fee charges",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("75.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("25.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketRegularAdditionalInsured_TestOnly().
					WithAmountBasedBillingDetails("30.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketWaiverOfSubrogation_TestOnly().
					WithAmountBasedBillingDetails("20.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(150.00), // 75 + 25 + 30 + 20
				Surcharge:   decimal.NewFromFloat(100.00), // 75 + 25
				FeeCharge:   decimal.NewFromFloat(50.00),  // 30 + 20
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "base charge should be ignored",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("200.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(50.00),
				Surcharge:   decimal.NewFromFloat(50.00),
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "surcharge with rate basis value",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithRateBasedBillingDetails(
						"0.02",
						&proto.Interval{
							Start: effectiveDate,
							End:   effectiveDate,
						},
						ptypes.RateBasis_RateBasis_Miles,
					).
					Build(),
			},
			rateBasisValue: ptypes.NewRateBasisValueWithMiles(1000),
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(20.00), // 0.02 * 1000
				Surcharge:   decimal.NewFromFloat(20.00),
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "surplus tax surcharge",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithSurplusTaxSurchargeType().
					WithAmountBasedBillingDetails("15.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(15.00),
				Surcharge:   decimal.NewFromFloat(15.00),
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.NewFromFloat(15.00),
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "stamping fee surcharge",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithStampingFeeSurchargeType().
					WithAmountBasedBillingDetails("10.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(10.00),
				Surcharge:   decimal.NewFromFloat(10.00),
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.NewFromFloat(10.00),
			},
			expectError: false,
		},
		{
			name: "combined surcharge types",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithSurplusTaxSurchargeType().
					WithAmountBasedBillingDetails("12.50", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithStampingFeeSurchargeType().
					WithAmountBasedBillingDetails("7.50", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketRegularAdditionalInsured_TestOnly().
					WithAmountBasedBillingDetails("25.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(95.00), // 50 + 12.5 + 7.5 + 25
				Surcharge:   decimal.NewFromFloat(70.00), // 50 + 12.5 + 7.5
				FeeCharge:   decimal.NewFromFloat(25.00), // 25
				SurplusTax:  decimal.NewFromFloat(12.50), // 12.5
				StampingFee: decimal.NewFromFloat(7.50),  // 7.5
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := CalculateFeeChargeAndSurchargeDistribution(
				tt.charges,
				tt.rateBasisValue,
			)

			if tt.expectError {
				require.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)
			assert.True(t, tt.expected.TotalCharge.Equal(result.TotalCharge),
				"Expected TotalCharge %s, got %s", tt.expected.TotalCharge.String(), result.TotalCharge.String())
			assert.True(t, tt.expected.Surcharge.Equal(result.Surcharge),
				"Expected Surcharge %s, got %s", tt.expected.Surcharge.String(), result.Surcharge.String())
			assert.True(t, tt.expected.FeeCharge.Equal(result.FeeCharge),
				"Expected FeeCharge %s, got %s", tt.expected.FeeCharge.String(), result.FeeCharge.String())
			assert.True(t, tt.expected.SurplusTax.Equal(result.SurplusTax),
				"Expected SurplusTax %s, got %s", tt.expected.SurplusTax.String(), result.SurplusTax.String())
			assert.True(t, tt.expected.StampingFee.Equal(result.StampingFee),
				"Expected StampingFee %s, got %s", tt.expected.StampingFee.String(), result.StampingFee.String())
		})
	}
}

func TestCalculatePolicyCharges(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	tests := []struct {
		name           string
		charges        []*ptypes.Charge
		rateBasisValue *ptypes.RateBasisValue
		expected       *PolicyCharge
		expectError    bool
		errorMsg       string
	}{
		{
			name:           "nil charges slice",
			charges:        nil,
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.Zero,
				Surcharge:   decimal.Zero,
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
		{
			name: "comprehensive policy with all charge types",
			charges: []*ptypes.Charge{
				// Base charge (should be ignored in policy charges)
				ptypes.NewChargeBuilder().
					WithBaseChargeTypeWithoutExtraInfo().
					WithAmountBasedBillingDetails("1000.00", effectiveDate).
					WithChargeableSubCoverageGroup(ptypes.SubCoverageType_SubCoverageType_BodilyInjury).
					Build(),
				// Regular surcharge
				ptypes.NewChargeBuilder().
					WithDefaultSurchargeType_TestOnly().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					Build(),
				// Surplus tax surcharge
				ptypes.NewChargeBuilder().
					WithSurplusTaxSurchargeType().
					WithAmountBasedBillingDetails("35.00", effectiveDate).
					Build(),
				// Stamping fee surcharge
				ptypes.NewChargeBuilder().
					WithStampingFeeSurchargeType().
					WithAmountBasedBillingDetails("25.00", effectiveDate).
					Build(),
				// Fee charges
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketRegularAdditionalInsured_TestOnly().
					WithAmountBasedBillingDetails("75.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketWaiverOfSubrogation_TestOnly().
					WithAmountBasedBillingDetails("50.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(285.00), // 100 + 35 + 25 + 75 + 50
				Surcharge:   decimal.NewFromFloat(160.00), // 100 + 35 + 25
				FeeCharge:   decimal.NewFromFloat(125.00), // 75 + 50
				SurplusTax:  decimal.NewFromFloat(35.00),  // 35
				StampingFee: decimal.NewFromFloat(25.00),  // 25
			},
			expectError: false,
		},
		{
			name: "rate-based charges with rate basis value",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithSurplusTaxSurchargeType().
					WithRateBasedBillingDetails(
						"0.025",
						&proto.Interval{
							Start: effectiveDate,
							End:   effectiveDate,
						},
						ptypes.RateBasis_RateBasis_Miles,
					).
					Build(),
				ptypes.NewChargeBuilder().
					WithStampingFeeSurchargeType().
					WithRateBasedBillingDetails(
						"0.015",
						&proto.Interval{
							Start: effectiveDate,
							End:   effectiveDate,
						},
						ptypes.RateBasis_RateBasis_Miles,
					).
					Build(),
			},
			rateBasisValue: ptypes.NewRateBasisValueWithMiles(2000),
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(80.00), // (0.025 + 0.015) * 2000
				Surcharge:   decimal.NewFromFloat(80.00), // 50 + 30
				FeeCharge:   decimal.Zero,
				SurplusTax:  decimal.NewFromFloat(50.00), // 0.025 * 2000
				StampingFee: decimal.NewFromFloat(30.00), // 0.015 * 2000
			},
			expectError: false,
		},
		{
			name: "only fee charges",
			charges: []*ptypes.Charge{
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketRegularAdditionalInsured_TestOnly().
					WithAmountBasedBillingDetails("100.00", effectiveDate).
					Build(),
				ptypes.NewChargeBuilder().
					WithDefaultBaseChargeTypeWithBlanketWaiverOfSubrogation_TestOnly().
					WithAmountBasedBillingDetails("25.00", effectiveDate).
					Build(),
			},
			rateBasisValue: nil,
			expected: &PolicyCharge{
				TotalCharge: decimal.NewFromFloat(125.00),
				Surcharge:   decimal.Zero,
				FeeCharge:   decimal.NewFromFloat(125.00),
				SurplusTax:  decimal.Zero,
				StampingFee: decimal.Zero,
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result, err := CalculatePolicyCharges(
				tt.charges,
				tt.rateBasisValue,
			)

			if tt.expectError {
				require.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)
			assert.True(t, tt.expected.TotalCharge.Equal(result.TotalCharge),
				"Expected TotalCharge %s, got %s", tt.expected.TotalCharge.String(), result.TotalCharge.String())
			assert.True(t, tt.expected.Surcharge.Equal(result.Surcharge),
				"Expected Surcharge %s, got %s", tt.expected.Surcharge.String(), result.Surcharge.String())
			assert.True(t, tt.expected.FeeCharge.Equal(result.FeeCharge),
				"Expected FeeCharge %s, got %s", tt.expected.FeeCharge.String(), result.FeeCharge.String())
			assert.True(t, tt.expected.SurplusTax.Equal(result.SurplusTax),
				"Expected SurplusTax %s, got %s", tt.expected.SurplusTax.String(), result.SurplusTax.String())
			assert.True(t, tt.expected.StampingFee.Equal(result.StampingFee),
				"Expected StampingFee %s, got %s", tt.expected.StampingFee.String(), result.StampingFee.String())
		})
	}
}

// Test for backward compatibility
func TestCalculateFeeChargeAndSurchargeDistribution_BackwardCompatibility(t *testing.T) {
	t.Parallel()

	effectiveDate := timestamppb.New(time_utils.NewDate(2024, 4, 15).ToTime())

	charges := []*ptypes.Charge{
		ptypes.NewChargeBuilder().
			WithDefaultSurchargeType_TestOnly().
			WithAmountBasedBillingDetails("50.00", effectiveDate).
			Build(),
		ptypes.NewChargeBuilder().
			WithSurplusTaxSurchargeType().
			WithAmountBasedBillingDetails("12.50", effectiveDate).
			Build(),
		ptypes.NewChargeBuilder().
			WithDefaultBaseChargeTypeWithBlanketRegularAdditionalInsured_TestOnly().
			WithAmountBasedBillingDetails("25.00", effectiveDate).
			Build(),
	}

	// Test that both functions return the same result
	result1, err1 := CalculateFeeChargeAndSurchargeDistribution(charges, nil)
	result2, err2 := CalculatePolicyCharges(charges, nil)

	require.NoError(t, err1)
	require.NoError(t, err2)
	require.NotNil(t, result1)
	require.NotNil(t, result2)

	assert.True(t, result1.TotalCharge.Equal(result2.TotalCharge))
	assert.True(t, result1.Surcharge.Equal(result2.Surcharge))
	assert.True(t, result1.FeeCharge.Equal(result2.FeeCharge))
	assert.True(t, result1.SurplusTax.Equal(result2.SurplusTax))
	assert.True(t, result1.StampingFee.Equal(result2.StampingFee))
}

// floatPtr is a helper function to create float64 pointers for test assertions
func floatPtr(f float64) *float64 {
	return &f
}
