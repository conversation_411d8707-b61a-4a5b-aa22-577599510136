package clusterops

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/infra/config"
)

type AwsCluster string

const (
	AwsClusterQuoting    AwsCluster = "quoting"
	AwsClusterStandard   AwsCluster = "standard"
	AwsClusterDataInfra  AwsCluster = "data-infra"
	AwsClusterSafety     AwsCluster = "safety"
	AwsClusterEvent      AwsCluster = "event"
	AwsClusterSimulation AwsCluster = "simulation"
)

func JobberProcessorConfig(cfg *config.Config, cluster AwsCluster) (*config.JobberProcessorConfig, error) {
	switch cluster {
	case AwsClusterQuoting:
		return cfg.JobberProcessors.Quoting, nil
	case AwsClusterStandard:
		return cfg.JobberProcessors.Standard, nil
	case AwsClusterDataInfra:
		return cfg.JobberProcessors.DataInfra, nil
	case AwsClusterSafety:
		return cfg.JobberProcessors.Safety, nil
	case AwsClusterEvent:
		return cfg.JobberProcessors.Event, nil
	case AwsClusterSimulation:
		return cfg.JobberProcessors.Simulation, nil
	default:
		return nil, errors.Newf("unknown cluster: %v", cluster)
	}
}

func StringToAwsCluster(cluster string) (AwsCluster, error) {
	switch cluster {
	case "quoting":
		return AwsClusterQuoting, nil
	case "standard":
		return AwsClusterStandard, nil
	case "data-infra":
		return AwsClusterDataInfra, nil
	case "safety":
		return AwsClusterSafety, nil
	case "event":
		return AwsClusterEvent, nil
	case "simulation":
		return AwsClusterSimulation, nil
	default:
		return "", errors.Newf("unknown cluster: %v", cluster)
	}
}
