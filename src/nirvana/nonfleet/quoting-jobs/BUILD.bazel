load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "quoting-jobs",
    srcs = [
        "assign_underwriter.go",
        "auto_select_safety_score.go",
        "deps.go",
        "express_lane_auto_underwriting.go",
        "fx.go",
        "job_trigger_helper.go",
        "ncf_credit_features.go",
        "non_fleet_authorities_job.go",
        "registry.go",
        "types.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/quoting-jobs",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/handlers/forms/generate_inputs",
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/api-server/rule-engine",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/common-go/crypto_utils",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/distsem",
        "//nirvana/events",
        "//nirvana/events/nonfleet_events",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/store_management",
        "//nirvana/infra/authz",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-core/monitoring",
        "//nirvana/jobber",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/nirvanaapp/models/application",
        "//nirvana/nonfleet/authorities",
        "//nirvana/nonfleet/credit_report/lexis_nexis",
        "//nirvana/nonfleet/quote_generator",
        "//nirvana/nonfleet/underwriting_panels",
        "//nirvana/policy_common/forms_generator",
        "//nirvana/quoting/jobs",
        "//nirvana/underwriting/app_review/widgets/safety/scorev2",
        "//nirvana/underwriting/platform/safety_score",
        "//nirvana/underwriting/scheduler",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_hashicorp_go_multierror//:go-multierror",
        "@in_gopkg_segmentio_analytics_go_v3//:analytics-go_v3",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)
