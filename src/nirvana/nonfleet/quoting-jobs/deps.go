package quoting_jobs

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"gopkg.in/segmentio/analytics-go.v3"
	ruleengine "nirvanatech.com/nirvana/api-server/rule-engine"
	"nirvanatech.com/nirvana/application/experiments/non_fleet"
	"nirvanatech.com/nirvana/common-go/crypto_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/distsem"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/store_management"
	"nirvanatech.com/nirvana/underwriting/app_review/widgets/safety/scorev2"
	"nirvanatech.com/nirvana/underwriting/scheduler"

	insurance_eng "nirvanatech.com/nirvana/insurance-core/monitoring"

	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/read_from_store_interceptor"
	"nirvanatech.com/nirvana/external_data_management/interceptors_management/write_to_store_interceptor"

	"nirvanatech.com/nirvana/api-server/handlers/forms/generate_inputs"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	nf_application "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/nonfleet/quote_generator"
	"nirvanatech.com/nirvana/nonfleet/underwriting_panels"
	"nirvanatech.com/nirvana/policy_common/forms_generator"
)

type Deps struct {
	fx.In

	PDFGenDeps                      quote_generator.PDFGenDeps
	FormsGenerator                  forms_generator.FormsGenerator
	GenerateInputsDeps              generate_inputs.GenerateInputsDeps
	Jobber                          quoting_jobber.Client
	InsuranceEngPDClient            insurance_eng.PagerDutyClient
	FetcherClientFactory            data_fetching.FetcherClientFactory
	AdmittedAppWrapper              nf_application.Wrapper[*admitted.AdmittedApp]
	NFApplicationReviewWrapper      nf_app_review.Wrapper
	UWScheduler                     scheduler.UwScheduler
	DistSemManager                  distsem.Manager
	MetricsClient                   statsd.Statter
	RuleEngine                      *ruleengine.RuleEngine
	AuthWrapper                     auth.DataWrapper
	SegmentClient                   analytics.Client
	SafetyScoreFactory              scorev2.SafetyScoreWidgetFactory
	StoreManager                    store_management.StoreManager
	ProcessorClientFactory          data_processing.ProcessorClientFactory
	WriteToStoreInterceptorFactory  write_to_store_interceptor.Factory
	ReadFromStoreInterceptorFactory read_from_store_interceptor.Factory
	CryptoClient                    *crypto_utils.Client
	AgencyWrapper                   agency.DataWrapper
	AdmittedPanelsManager           underwriting_panels.AdmittedPanelsManager
	Clock                           clock.Clock
	NFApplicationExperimentManager  *non_fleet.Manager
}
