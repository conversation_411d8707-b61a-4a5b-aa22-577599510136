package quoting_jobs

import (
	"context"
	"database/sql"
	"strconv"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/quoting/jobs"
)

func NewExpressLaneAutoUnderwritingJob(deps *Deps) (*jtypes.Job[*jobs.ExpressLaneAutoUnderwritingMessage], error) {
	return jtypes.NewJob(
		jobs.ExpressLaneAutoUnderwriting,
		[]jtypes.TaskCreator[*jobs.ExpressLaneAutoUnderwritingMessage]{
			func() jtypes.Task[*jobs.ExpressLaneAutoUnderwritingMessage] {
				return &expressLaneAutoUnderwritingTask{deps: deps}
			},
		},
		jobs.ExpressLaneAutoUnderwritingMessageUnmarshalFn,
	)
}

type expressLaneAutoUnderwritingTask struct {
	deps *Deps

	job_utils.NonRetryableTask[*jobs.ExpressLaneAutoUnderwritingMessage]
	job_utils.NoopUndoTask[*jobs.ExpressLaneAutoUnderwritingMessage]
}

func (t *expressLaneAutoUnderwritingTask) ID() string {
	return "expressLaneAutoUnderwritingTask"
}

func (t *expressLaneAutoUnderwritingTask) Run(ctx jtypes.Context, message *jobs.ExpressLaneAutoUnderwritingMessage) (err error) {
	if message == nil || message.ApplicationID == uuid.Nil {
		return errors.Newf("ExpressLaneAutoUnderwriting - invalid or nil message: application id is required")
	}

	defer func() {
		if r := recover(); r != nil {
			err = errors.Newf("panic: %v", r)
		}
		if err != nil {
			log.Error(ctx, "ExpressLaneAutoUnderwriting job failed", log.Err(err))
		} else {
			log.Info(ctx, "ExpressLaneAutoUnderwriting job completed successfully")
		}
		t.emitJobCompletionMetric(ctx, err)
	}()

	ctx = ctx.WithUpdatedBaseCtx(func(c context.Context) context.Context {
		return log.ContextWithFields(c, log.Stringer("appId", message.ApplicationID))
	})

	log.Info(ctx, "ExpressLaneAutoUnderwriting job triggered", log.Stringer("appId", message.ApplicationID))

	err = t.run(ctx, message.ApplicationID)
	if err != nil {
		return errors.Wrap(err, "failed to trigger ExpressLaneAutoUnderwriting job")
	}

	return nil
}

func (t *expressLaneAutoUnderwritingTask) emitJobCompletionMetric(ctx jtypes.Context, err error) {
	statName := "express_lanes_auto_underwriting.count"
	jobSucceeded := err == nil
	err = t.deps.MetricsClient.Inc(
		statName, 1, 1, statsd.Tag{successTagKey, strconv.FormatBool(jobSucceeded)},
	)
	if err != nil {
		log.Error(ctx, "failed to emit metric", log.String("metricName", statName))
	}
}

func (t *expressLaneAutoUnderwritingTask) run(ctx context.Context, appID uuid.UUID) error {
	app, err := t.deps.AdmittedAppWrapper.GetAppById(ctx, appID)
	if err != nil {
		return errors.Wrapf(err, "failed to get application by ID %s", appID)
	}

	// If the application is not in the correct state, return.
	if app.ExpressLaneMetadata == nil {
		return errors.Newf("application express lane metadata is nil %s", appID)
	}

	// If the application review is not found, return.
	// This should never happen, since the application review should always be created when the application is submitted.
	appReview, err := t.deps.NFApplicationReviewWrapper.GetAppReviewByAppID(ctx, appID)
	if errors.Is(err, sql.ErrNoRows) {
		log.Warn(ctx, "application review not found for express lanes job", log.Stringer("appId", appID))
		return nil
	}
	if err != nil {
		return errors.Wrapf(err, "failed to fetch application review for express lanes job for appId %s", appID)
	}

	// STEP 1 - Pull MVRs for the drivers.
	if err := t.pullMVRs(ctx, appReview.GetID().String()); err != nil {
		return errors.Wrapf(err, "failed to pull MVRs for app review id %s", appReview.GetID().String())
	}

	// STEP 2 - Mark all target panels as reviewed for this application review.
	if err := t.markPanelsAsReviewed(ctx, appReview.GetID().String()); err != nil {
		return errors.Wrapf(err, "failed to mark panels as reviewed for app review id %s", appReview.GetID().String())
	}

	return nil
}

func (t *expressLaneAutoUnderwritingTask) pullMVRs(ctx context.Context, appReviewID string) error {
	mgr := &t.deps.AdmittedPanelsManager
	input, err := mgr.AdmittedBasePanel.GetPanelInput(ctx, appReviewID)
	if err != nil {
		return errors.Wrapf(err, "Failed to get base panel input for app review id %s", appReviewID)
	}
	return mgr.AdmittedDriversPanel.SetPullMVRAndUpdateOverrides(ctx, input, t.deps.FetcherClientFactory)
}

func (t *expressLaneAutoUnderwritingTask) markPanelsAsReviewed(ctx context.Context, appReviewID string) error {
	panelTypes := []application_review.PanelType{
		application_review.PackagesPanelType,
		application_review.DriverPanelType,
		application_review.OperationsPanelType,
		application_review.EquipmentPanelType,
		application_review.SafetyPanelType,
		application_review.LossesPanelType,
	}

	updateFn := func(review application_review.ApplicationReview) (application_review.ApplicationReview, error) {
		for _, panel := range panelTypes {
			// Set the panel as reviewed, thus we pass true along with the panel type.
			if err := review.SetPanelReview(panel, true); err != nil {
				return nil, errors.Wrapf(err, "failed to mark %s panel as reviewed", panel)
			}
		}
		return review, nil
	}

	if err := t.deps.NFApplicationReviewWrapper.UpdateAppReview(ctx, appReviewID, updateFn); err != nil {
		return errors.Wrapf(err, "unable to update application review %s", appReviewID)
	}
	return nil
}
