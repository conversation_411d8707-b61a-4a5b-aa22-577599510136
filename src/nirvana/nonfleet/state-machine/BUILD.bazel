load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "state-machine",
    srcs = [
        "actions.go",
        "callbacks.go",
        "deps.go",
        "events.go",
        "fx.go",
        "helpers.go",
        "object_defs.go",
        "salesforce_events.go",
        "segment_events.go",
        "state_machine.go",
    ],
    importpath = "nirvanatech.com/nirvana/nonfleet/state-machine",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/application/experiments/non_fleet/express_lane",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/fsm-util",
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/proto",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/agency",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/quoting",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/forms/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/nonfleet/rule_runs",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted",
        "//nirvana/db-api/db_wrappers/policy/program_data/shared",
        "//nirvana/db-api/db_wrappers/policy_set",
        "//nirvana/db-api/db_wrappers/prog_type_identifier",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/events",
        "//nirvana/events/nonfleet_events",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-bundle/model",
        "//nirvana/insurance-bundle/model/charges",
        "//nirvana/insurance-bundle/service",
        "//nirvana/insurance-core/coverage",
        "//nirvana/insurance-core/proto",
        "//nirvana/insured/model",
        "//nirvana/jobber",
        "//nirvana/jobber/event",
        "//nirvana/jobber/jtypes",
        "//nirvana/nonfleet/coverages",
        "//nirvana/nonfleet/model",
        "//nirvana/nonfleet/nfutils",
        "//nirvana/nonfleet/pdf-jobs",
        "//nirvana/nonfleet/quote_generator",
        "//nirvana/nonfleet/quoting-jobs",
        "//nirvana/nonfleet/rule_engine",
        "//nirvana/nonfleet/rule_engine/registry",
        "//nirvana/nonfleet/state-machine/enums",
        "//nirvana/openapi-specs/components/application",
        "//nirvana/openapi-specs/components/nonfleet",
        "//nirvana/policy/non_fleet",
        "//nirvana/policy/shared",
        "//nirvana/policy_common/constants",
        "//nirvana/policy_common/forms_generator/compilation",
        "//nirvana/quoting/ancillary_coverages",
        "//nirvana/quoting/utils",
        "//nirvana/salesforce",
        "//nirvana/salesforce/enums",
        "//nirvana/salesforce/event",
        "//nirvana/salesforce/jobs",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_go_cmp//cmp",
        "@com_github_google_go_cmp//cmp/cmpopts",
        "@com_github_google_uuid//:uuid",
        "@com_github_looplab_fsm//:fsm",
        "@in_gopkg_segmentio_analytics_go_v3//:analytics-go_v3",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
    ],
)
