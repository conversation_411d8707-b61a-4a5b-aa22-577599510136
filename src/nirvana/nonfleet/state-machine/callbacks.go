package statemachine

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"
	"github.com/looplab/fsm"
	"google.golang.org/protobuf/types/known/timestamppb"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/express_lane"

	fsmutil "nirvanatech.com/nirvana/common-go/fsm-util"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	appenums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/quoting"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	formenums "nirvanatech.com/nirvana/db-api/db_wrappers/forms/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"

	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	admittedEnums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	nfenums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	policydbenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	policyenums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/nonfleet/admitted"
	pdshared "nirvanatech.com/nirvana/db-api/db_wrappers/policy/program_data/shared"
	"nirvanatech.com/nirvana/insurance-bundle/model"
	"nirvanatech.com/nirvana/insurance-bundle/model/charges"
	"nirvanatech.com/nirvana/insurance-bundle/service"
	"nirvanatech.com/nirvana/insurance-core/coverage"
	insurance_core "nirvanatech.com/nirvana/insurance-core/proto"
	insuredmodel "nirvanatech.com/nirvana/insured/model"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	nfmodel "nirvanatech.com/nirvana/nonfleet/model"
	"nirvanatech.com/nirvana/nonfleet/nfutils"
	pdfjobs "nirvanatech.com/nirvana/nonfleet/pdf-jobs"
	quoting_jobs "nirvanatech.com/nirvana/nonfleet/quoting-jobs"
	"nirvanatech.com/nirvana/nonfleet/rule_engine/registry"
	asmenums "nirvanatech.com/nirvana/nonfleet/state-machine/enums"
	oapiapp "nirvanatech.com/nirvana/openapi-specs/components/application"
	"nirvanatech.com/nirvana/openapi-specs/components/nonfleet"
	non_fleet "nirvanatech.com/nirvana/policy/non_fleet"
	common_policy_utils "nirvanatech.com/nirvana/policy/shared"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/policy_common/forms_generator/compilation"
)

var (
	ErrEmptyArgument                = errors.New("empty arguments passed to callback function")
	ErrTypeAssertion                = errors.New("type assertion failed")
	ErrAppIDWithProgramTypeNotFound = errors.New("AppID & ProgramType not found within event arguments")
	ErrSubIDNotFound                = errors.New("SubID not found within event arguments")
	ErrIBCreationFailed             = errors.New("failed to create IB")
)

// Source of Information: https://pkg.go.dev/github.com/looplab/fsm#section-readme
//
// State machine flow:
// - before_ callbacks are called, first the named(beforeEventKey = "before_ <EVENT>") then the general version(beforeEventDefaultKey = "before_event")
// - If the current state is already destination, call the after_ callbacks and return with No TransitionError
// - Make the transition
// - enterStateCallbacks calls the enter_ callbacks, first the named (enterStateKey = "enter_<EVENT>") then the general version (enterStateDefaultKey = "enter_state")
// - afterEventCallbacks calls the after_ callbacks, first the named (afterEventKey = "after_<EVENT>")then the general version. (afterEventDefaultKey = "after_event")
// App related updates on db should be done in enterState.
// App review related updates on db should be done in beforeState.
const (
	beforeEventDefaultKey = "before_event"
	afterEventDefaultKey  = "after_event"
	enterStateDefaultKey  = "enter_state"
	afterEventKey         = "after_"
	enterStateKey         = "enter_"
	beforeEventKey        = "before_"
)

var AppUpdateReviewEvents = []asmenums.Event{
	asmenums.EventReferToCanopius,
	asmenums.EventCanopiusDecline,
	asmenums.EventCanopiusApprove,
	asmenums.EventRollbackAppReview,
}

func isEventInAppUpdateReviewEvents(eventToCheck asmenums.Event) bool {
	for _, event := range AppUpdateReviewEvents {
		if eventToCheck == event {
			return true
		}
	}
	return false
}

func generateCallbackMap(deps NFAppStateMachineWrapperDeps) map[string]fsm.Callback {
	callbackMap := make(map[string]fsm.Callback)
	populateDefaultCallbackFunctions(callbackMap, deps)
	populateStateSpecificCallbackFunctions(callbackMap, deps)
	populateEventSpecificCallbackFunctions(callbackMap, deps)
	return callbackMap
}

func populateDefaultCallbackFunctions(callbackMap map[string]fsm.Callback, deps NFAppStateMachineWrapperDeps) {
	callbackMap[beforeEventDefaultKey] = beforeEventDefaultCallbackFunc(deps)
	callbackMap[enterStateDefaultKey] = enterStateDefaultCallbackFunc(deps)
	callbackMap[afterEventDefaultKey] = afterEventDefaultCallbackFunc(deps)
}

func populateStateSpecificCallbackFunctions(callbackMap map[string]fsm.Callback, deps NFAppStateMachineWrapperDeps) {
	callbackMap[enterStateKey+nfenums.AppStateUnderUWReview.String()] = enterUnderUWReviewStateCallbackFunc(deps)
	callbackMap[enterStateKey+nfenums.AppStateApproved.String()] = enterApprovedStateCallbackFunc(deps)
}

func populateEventSpecificCallbackFunctions(callbackMap map[string]fsm.Callback, deps NFAppStateMachineWrapperDeps) {
	callbackMap[beforeEventKey+asmenums.EventPanic.String()] = beforeEventPanicCallbackFunc(deps)
	callbackMap[afterEventKey+asmenums.EventRollbackAppReview.String()] = afterRollbackAppReviewCallbackFunc(deps)
	callbackMap[beforeEventKey+asmenums.EventBindQuote.String()] = beforeEventBindQuoteCallbackFunc(deps)
}

func enterUnderUWReviewStateCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of enterUnderUWReviewStateCallbackFunc for nonfleet state machine")

			args, err := getArgs(event)
			if err != nil {
				return err
			}
			appID, _, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}

			ctx = log.ContextWithFields(ctx, log.AppID(appID.String()))

			if args.HasSubmissionID() {
				// We already check for valid subID above, so suppressing the error
				subID, _ := args.SubID()
				originalAppReview, err := deps.NFApplicationReviewWrapper.GetAppReviewByAppID(ctx, appID)
				if err != nil {
					return errors.Wrapf(err, "failed to get original appReview for appID %s", appID)
				}

				if originalAppReview != nil {
					err = deps.NFApplicationReviewWrapper.UpdateAppReview(
						ctx,
						originalAppReview.GetID().String(),
						func(review application_review.ApplicationReview) (application_review.ApplicationReview, error) {
							review.UpdateAppState(application_review.AppReviewStateStale)
							return review, nil
						},
					)
					if err != nil {
						return errors.Wrapf(err, "couldn't mark original app review: %s as stale",
							originalAppReview.GetID().String())
					}
					log.Debug(ctx, "marked original app review as stale",
						log.Any("originalAppReviewID", originalAppReview.GetID().String()))
				}

				progTyp, err := deps.ProgramTypeIdentifier.GetProgramTypeByAppID(ctx, appID)
				if err != nil {
					return errors.Wrapf(err, "unable to get program type for appID %s", appID)
				}

				var appType *constants.ApplicationType

				//nolint:exhaustive
				switch progTyp {
				case policyenums.ProgramTypeNonFleetAdmitted:
					{
						appType = pointer_utils.ToPointer(constants.ApplicationTypeNonFleetAdmitted)
						appReviewId, err := insertAppReviewForAdmitted(ctx, deps, appID, subID, originalAppReview)
						if err != nil {
							return errors.Wrapf(err, "failed to insert app review for admitted app with id %v", appID)
						}
						// TODO - Confirm if rateml has been run before this.
						if _, err := quoting_jobs.TriggerGenerateNonFleetAuthoritiesJob(ctx, deps.Jobber, appReviewId.String()); err != nil {
							log.Error(ctx, "failed to trigger TriggerGenerateNonFleetAuthoritiesJob job", log.Err(err))
							return errors.Wrapf(err, "failed to trigger TriggerGenerateNonFleetAuthoritiesJob job for app review %s", appReviewId)
						}

						// If the app is eligible for express lane, then we update the express lane status.
						if err = updateExpressLaneStatusOnSubmission(ctx, deps, appID); err != nil {
							return errors.Wrapf(err, "failed to update express lane status for app with id %v", appID)
						}

					}
				default:
					log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
				}

				err = triggerAppPDF(ctx, deps, appType, subID)
				if err != nil {
					return errors.Wrapf(err, "failed to trigger app pdf for app with id %v", appID)
				}
			}

			log.Info(ctx, "enterUnderUWReviewStateCallbackFunc executed successfully for nonfleet state machine")
			return nil
		},
	)
}

func enterApprovedStateCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of enterApprovedStateCallbackFunc for nonfleet state machine")

			args, err := getArgs(event)
			if err != nil {
				return err
			}

			appID, progTyp, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}

			subID, err := args.SubID()
			if err != nil {
				return ErrSubIDNotFound
			}

			//nolint:exhaustive
			switch progTyp {
			case policyenums.ProgramTypeNonFleetAdmitted:
				err = deps.AdmittedAppWrapper.UpdateApp(
					ctx,
					appID,
					func(app application.Application[*admitted_app.AdmittedApp]) (application.Application[*admitted_app.AdmittedApp], error) {
						app.BindableSubmissionID = subID
						return app, nil
					},
				)
				if err != nil {
					return errors.Wrapf(err, "failed to update appID: %s with bindable subID: %s",
						appID.String(), subID.String())
				}
			default:
				log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
			}

			log.Info(ctx, "enterApprovedStateCallbackFunc executed successfully for nonfleet state machine")
			return nil
		},
	)
}

// enterStateDefaultCallbackFunc is the default callback function called before entering
// any state during the state transition
func enterStateDefaultCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of enterStateDefaultCallbackFunc for nonfleet state machine")

			destState, err := nfenums.AppStateString(event.Dst)
			if err != nil {
				return errors.Wrapf(err, "failed to convert state to string: %v", event.Dst)
			}

			args, err := getArgs(event)
			if err != nil {
				return err
			}

			appID, progTyp, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}

			ctx = log.ContextWithFields(ctx, log.AppID(appID.String()))

			//nolint:exhaustive
			switch progTyp {
			case policyenums.ProgramTypeNonFleetAdmitted:
				{
					if err := deps.AdmittedAppWrapper.UpdateApp(
						ctx, appID,
						func(app application.Application[*admitted_app.AdmittedApp]) (
							application.Application[*admitted_app.AdmittedApp], error,
						) {
							app.State = destState
							// nolint:exhaustive
							switch destState {
							case enums.AppStateQuoteGenerating, enums.AppStateComplete:
								app.PageState = enums.PageStateSubmitted
							case enums.AppStateDeclined:
								reason, err := args.Reason()
								if err != nil {
									return app, errors.New("failed to type assert arg reason")
								}
								app.DeclineReason = reason.Text
								app.Info.ClosureInfo = application.ClosureInfo{
									PrimaryReason:            reason.PrimaryReason,
									SecondaryReason:          reason.SecondaryReason,
									GuidelineBasedReasons:    reason.GuidelineBasedReasons,
									NonGuidelineBasedReasons: reason.NonGuidelineBasedReasons,
									Comments:                 reason.Text,
								}
							case enums.AppStateClosed:
								reason, err := args.Reason()
								if err != nil {
									return app, errors.New("failed to type assert arg reason")
								}
								app.Info.ClosureInfo = application.ClosureInfo{
									PrimaryReason:   reason.PrimaryReason,
									SecondaryReason: reason.SecondaryReason,
									Comments:        reason.Text,
									WinCarrier:      args.WinCarrier(),
								}
							}

							return app, nil
						},
					); err != nil {
						return errors.Wrapf(err, "failed to update application: %v", appID)
					}
				}
			default:
				log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
			}

			log.Info(ctx, "enterStateDefaultCallbackFunc executed successfully for nonfleet state machine")
			return nil
		},
	)
}

// beforeEventDefaultCallbackFunc is the default callback function called before any event
// is triggered in the state machine
func beforeEventDefaultCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of beforeEventDefaultCallbackFunc for non fleet state machine")

			eventType, err := asmenums.EventString(event.Event)
			if err != nil {
				return errors.Wrapf(err, "invalid event %s", event.Event)
			}

			if isEventInAppUpdateReviewEvents(eventType) {
				err = updateAppReviewState(ctx, application_review.AppReviewStatePending, event, deps)
				if err != nil {
					return errors.Wrapf(err, "failed to transition appReview for event %s", event.Event)
				}
			}

			log.Info(
				ctx, "beforeEventDefaultCallbackFunc executed successfully for nonfleet state machine",
				log.String("event", event.Event), log.String("source", event.Src),
				log.String("destination", event.Dst),
			)
			return nil
		},
	)
}

// afterEventDefaultCallbackFunc is the default callback function called after any event
// is triggered in the state machine
func afterEventDefaultCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of afterEventDefaultCallbackFunc for nonfleet state machine")

			args, err := getArgs(event)
			if err != nil {
				return err
			}

			if args.isIBBackfillEnabled {
				// This is a backfill event, we don't need to do anything like push segment events or run rules
				return nil
			}

			appID, progTyp, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}

			// Push segment events
			err = PushSegmentEvents(ctx, event, deps)
			if err != nil {
				err := deps.MetricsClient.Inc("NFFailedSegmentEvents", 1, 1)
				if err != nil {
					log.Error(ctx, "failed to increment failed segment upload count metric", log.Err(err))
				}
				return errors.Wrapf(err, "failed to push segment events for appID: %v", appID)
			}

			nfEvent, err := asmenums.EventString(event.Event)
			if err != nil {
				return errors.Wrapf(err, "invalid event %s", event.Event)
			}

			err = pushAdmittedSalesforceEvent(ctx, deps, nfEvent, args, appID)
			if err != nil {
				// just log the error, we don't want to fail the event for now
				log.Error(ctx, "failed to push admitted salesforce event", log.Err(err))
			}

			var subID *uuid.UUID
			if args.HasSubmissionID() {
				sID, _ := args.SubID()
				subID = &sID
			}

			//nolint:exhaustive
			switch progTyp {
			case policyenums.ProgramTypeNonFleetAdmitted:
				{
					app, submission, err := fetchAppAndSubmission(ctx, appID, subID, deps.AdmittedAppWrapper)
					if err != nil {
						return errors.Wrapf(err, "failed to fetch app and submission for appID: %v", appID)
					}
					sm, err := New(ctx, app.State, deps)
					if err != nil {
						return errors.Wrapf(err, "failed to create state machine for appID: %v", appID)
					}

					// Run rules for the new state
					rulesOutcome := deps.AdmittedRuleEngine.RunRules(
						ctx,
						registry.NewRuleInput[*admitted_app.AdmittedApp](app, submission),
					)
					switch rulesOutcome.Result {
					case enums.HardErrored:
						return errors.Newf("failed to run rules for appID: %v with reason: %s", appID, rulesOutcome.JoinReasons())
					case enums.Pass:
					case enums.Fail:
						if err := sm.Event(ctx, asmenums.EventDeclined.String(), EventArguments{
							appID: pointer_utils.UUID(app.ID),
						}); err != nil {
							return errors.Wrapf(err,
								"failed to transition to declined state for appID: %v with reason: %s",
								appID, rulesOutcome.JoinReasons(),
							)
						}
					case enums.Referral:
						if err := sm.Event(ctx, asmenums.EventReferralCheckFailed.String(), EventArguments{
							appID: pointer_utils.UUID(app.ID),
							subID: pointer_utils.UUID(submission.ID),
						}); err != nil {
							return errors.Wrapf(err,
								"failed to transition to refer state for appID: %v with reason: %s",
								appID, rulesOutcome.JoinReasons(),
							)
						}
						err = deps.AdmittedAppWrapper.UpdateApp(
							ctx,
							appID,
							func(app application.Application[*admitted_app.AdmittedApp]) (application.Application[*admitted_app.AdmittedApp], error) {
								app.PageState = enums.PageStateReferral
								return app, nil
							},
						)
						if err != nil {
							return errors.Wrapf(err, "failed to update page state as referral for appID: %v", appID)
						}

					case enums.Flagged:
					case enums.SoftErrored:
						// TODO(kaavee): Handle errors correctly
					default:
						return errors.Errorf("invalid result from rule engine: %s", rulesOutcome.Result.String())
					}
				}
			default:
				log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
			}

			return nil
		},
	)
}

// beforeEventPanicCallbackFunc is a callback function that is called before the Panic event is triggered.
func beforeEventPanicCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of beforeEventPanicCallbackFunc for non fleet state machine")

			args, err := getArgs(event)
			if err != nil {
				return err
			}

			appID, progTyp, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}
			pErr := args.pErr

			//nolint:exhaustive
			switch progTyp {
			case policyenums.ProgramTypeNonFleetAdmitted:
				{
					err = deps.AdmittedAppWrapper.UpdateApp(
						ctx,
						appID,
						func(app application.Application[*admitted_app.AdmittedApp]) (application.Application[*admitted_app.AdmittedApp], error) {
							app.StateMetadata = application.StateMetadata{
								Description:   fmt.Sprintf("RateML Panicked With Error: %s", pErr.Error()),
								Time:          time.Now(),
								PreviousState: event.Src,
							}
							return app, nil
						},
					)
					if err != nil {
						return errors.Wrapf(err, "failed to update app StateMetadata for appID: %v", appID)
					}
				}
			default:
				log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
			}

			log.Info(ctx, "beforeEventPanicCallbackFunc executed successfully for non fleet state machine")
			return nil
		},
	)
}

// afterRollbackAppReviewCallbackFunc is a callback function that is called after the RollbackAppReview event is triggered.
func afterRollbackAppReviewCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of afterRollbackAppReviewCallbackFunc for non fleet state machine")

			args, err := getArgs(event)
			if err != nil {
				return err
			}

			appID, progTyp, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}

			//nolint:exhaustive
			switch progTyp {
			case policyenums.ProgramTypeNonFleetAdmitted:
				{
					err = deps.AdmittedAppWrapper.UpdateApp(
						ctx,
						appID,
						func(app application.Application[*admitted_app.AdmittedApp]) (application.Application[*admitted_app.AdmittedApp], error) {
							app.State = nfenums.AppStateUnderUWReview
							return app, nil
						},
					)
					if err != nil {
						return errors.Wrapf(err, "failed to update app StateMetadata for appID: %v", appID)
					}
				}
			default:
				log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
			}

			log.Info(ctx, "afterRollbackAppReviewCallbackFunc executed successfully for non fleet state machine")
			return nil
		},
	)
}

// beforeEventBindQuoteCallbackFunc is a callback function that is called before the BindQuote event is triggered.
func beforeEventBindQuoteCallbackFunc(deps NFAppStateMachineWrapperDeps) fsm.Callback {
	return fsmutil.DecorateCallbackWithErrorHandling(
		func(ctx context.Context, event *fsm.Event) error {
			ctx = addEventFieldsToLoggerCtx(ctx, event)
			log.Info(ctx, "starting execution of beforeEventBindQuoteCallbackFunc for non fleet state machine")

			args, err := getArgs(event)
			if err != nil {
				return err
			}

			appID, progTyp, err := args.AppIDWithProgramType(ctx, deps.ProgramTypeIdentifier)
			if err != nil {
				return ErrAppIDWithProgramTypeNotFound
			}

			//nolint:exhaustive
			switch progTyp {
			case policyenums.ProgramTypeNonFleetAdmitted:
				createPolicyArgs, err := non_fleet.GetAdmittedCreatePolicyArgs(
					ctx,
					deps.AdmittedAppWrapper,
					deps.AgencyWrapper,
					deps.FormsWrapper,
					deps.FMCSAWrapper,
					appID,
				)
				if err != nil {
					return errors.Wrapf(err, "failed to get createPolicy args for appID: %v", appID)
				}

				migrationState, err := nfutils.GetMigrationState(ctx, deps.FeatureFlagClient)
				if err != nil {
					return errors.Wrap(err, "failed to get migration state")
				}

				// Creates IB with PolicyV2, put the older policy creation behind this feature flag later.
				if args.isIBBackfillEnabled || migrationState.CanWriteToNewMigratedStore() {
					err = createIB(ctx, createPolicyArgs, deps)
					if err != nil {
						log.Warn(ctx, "failed to create IB for appID", log.AppID(appID.String()),
							log.SubID(createPolicyArgs.SubObj.ID.String()), log.Err(err))
						return errors.Wrapf(errors.Mark(errors.WithStack(err), ErrIBCreationFailed),
							"failed to create IB for appID: %v", appID)
					}
				}

				if !args.isIBBackfillEnabled && migrationState.CanWriteToOldStore() {
					policySetId, err := non_fleet.CreateAdmittedPolicySet(
						ctx,
						*createPolicyArgs.AppObj,
						*createPolicyArgs.SubObj,
						createPolicyArgs.IndOpt,
						createPolicyArgs.PolicySetIdentifier,
						deps.PolicySetWrapper,
						progTyp,
					)
					if err != nil {
						return errors.Wrapf(err, "failed to create policy set for appID: %v", appID)
					}

					createPolicyArgs.PolicySetId = *policySetId

					var policies []oapiapp.PolicyDetails
					for _, cov := range createPolicyArgs.SubObj.Info.CoverageInfo.PrimaryCovs {
						if cov.CoverageType == appenums.CoverageAutoPhysicalDamage {
							continue
						}
						createPolicyArgs.Coverage = cov.CoverageType
						policyDetails, err := non_fleet.CreateAdmittedPolicy(ctx, createPolicyArgs, deps.PolicyWrapper, deps.FormsWrapper, deps.AdmittedAppWrapper)
						if err != nil {
							return errors.Wrapf(err, "failed to create policy for appID: %v with coverage: %s", appID, cov.CoverageType.String())
						}
						policies = append(policies, *policyDetails)
					}

					if len(policies) == 0 {
						return errors.Wrapf(err, "no valid coverages found for appID: %v", appID)
					}
				}

				if !args.isIBBackfillEnabled {
					// Update the state of the signature packet associated with the app
					sigPacket := createPolicyArgs.SubObj.SignaturePacketFormID
					if sigPacket != nil {
						err = deps.FormsWrapper.UpdateFormCompilation(ctx, *sigPacket,
							func(formComp compilation.FormsCompilationImpl) (*compilation.FormsCompilationImpl, error) {
								// check if the form compilation which is being updated is a signature packet
								if formComp.FormType == nil {
									return nil, errors.Newf("form compilation with id %v has type nil", *sigPacket)
								}
								if *formComp.FormType != compilation.CompilationTypeSignaturePacket {
									return nil, errors.Newf("form compilation with id %v is not a signature packet, type %s",
										*sigPacket,
										formComp.FormType.String(),
									)
								}
								// we can't bind a signature packet to a policy if it's not in the released state
								if !formComp.State.Matches(formenums.FormCompilationStateReleased) {
									return nil,
										errors.Newf("signature packet is not in released state, state %s id %s",
											formComp.State.String(),
											formComp.ID,
										)
								}
								formComp.State = formenums.FormCompilationStateBound
								return &formComp, nil
							},
						)
						if err != nil {
							return errors.Wrapf(err, "failed to update form compilation with id: %v", *sigPacket)
						}
					}
				}
			default:
				log.DPanic(ctx, "invalid program type for appID", log.AppID(appID.String()))
			}

			return nil
		},
	)
}

func createIB(ctx context.Context, args *non_fleet.CreateAdmittedPolicyArgs, deps NFAppStateMachineWrapperDeps) error {
	upsertIBRequest, err := NewUpsertIBRequest(ctx, args, deps.FormsWrapper, deps.AdmittedAppWrapper)
	if err != nil {
		return errors.Wrap(err, "failed to create IB request")
	}

	resp, err := deps.IBService.UpsertInsuranceBundle(ctx, upsertIBRequest)
	log.Debug(ctx,
		"createIB response",
		log.Any("response", resp),
		log.AppID(args.AppObj.ID.String()),
		log.Err(err),
	)
	return err
}

func NewUpsertIBRequest(
	ctx context.Context,
	args *non_fleet.CreateAdmittedPolicyArgs,
	formsWrapper forms.FormWrapper,
	nfApWrapper application.Wrapper[*admitted_app.AdmittedApp],
) (*service.UpsertInsuranceBundleRequest, error) {
	if args.PricingContext() == nil {
		return nil, errors.New("pricing context is nil")
	}
	if args.GetSignaturePacketFormsCompilation() == nil {
		return nil, errors.Newf("signature packet forms compilation is nil")
	}

	subObj := args.SubObj
	appObj := args.AppObj
	provider := subObj.Info.GetModelPinConfigInfo().RateML.Provider
	insuranceCarrier := common_policy_utils.GetFronterFromProvider(provider)
	protoInsuranceCarrier := insurance_core.GetProtoCommonInsuranceCarrier(insuranceCarrier)
	if protoInsuranceCarrier == insurance_core.InsuranceCarrier_InsuranceCarrier_Empty {
		return nil, errors.Newf("failed to get insurance carrier for provider %s", provider)
	}

	spFormCompilationID := args.GetSignaturePacketFormsCompilation().Id()
	spFormCompilation, err := formsWrapper.GetFormCompilationById(ctx, spFormCompilationID)
	if err != nil {
		return nil, errors.Wrapf(
			err,
			"failed to get signature packet form compilation with id %s",
			spFormCompilationID,
		)
	}

	return &service.UpsertInsuranceBundleRequest{
		InsuranceBundle: &model.InsuranceBundle{
			DefaultCarrier: protoInsuranceCarrier,
			DefaultSeller: &insurance_core.SellerInfo{
				AgencyID: appObj.AgencyID.String(),
			},
			DefaultEffectiveDuration: &proto.Interval{
				Start: timestamppb.New(subObj.Info.GetEffectiveDates().Start),
				End:   timestamppb.New(subObj.Info.GetEffectiveDates().End),
			},
			ProgramType: insurance_core.ProgramType_ProgramType_NonFleetAdmitted,
			Metadata: &model.InsuranceBundleMetadata{
				RootBindableSubmissionId: subObj.ID.String(),
				RootApplicationId:        appObj.ID.String(),
			},
			FormInfo: getFormInfo(ctx, subObj, spFormCompilation),
			Segments: []*model.InsuranceBundleSegment{
				getIBSegment(ctx, args, formsWrapper, nfApWrapper),
			},
			CarrierAdmittedType: getCarrierType(appObj.FilingType),
		},
		Insured: &insuredmodel.Insured{
			Name: &insurance_core.InsuredName{
				BusinessName: appObj.Info.CompanyInfo.Name,
			},
			Address: getProtoAddress(subObj.Info.GetBusinessOwnerDetails().Address),
			ContactInfo: &insuredmodel.ContactInfo{
				Email: subObj.Info.TSPInfo.InsuredInfo.Email,
				Phone: "", // Not captured today, hence empty.
			},
			ExternalIdentifier: &insurance_core.InsuredIdentifier{
				Type:  insurance_core.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
				Value: []string{strconv.Itoa(subObj.Info.CompanyInfo.DOTNumber)},
			},
		},
	}, nil
}

func getCarrierType(filingType *constants.FilingType) insurance_core.CarrierAdmittedType {
	if filingType == nil {
		return insurance_core.CarrierAdmittedType_CarrierAdmittedType_Unspecified
	}

	switch *filingType {
	case constants.FilingTypeAdmitted:
		return insurance_core.CarrierAdmittedType_CarrierAdmittedType_Admitted
	case constants.FilingTypeNonAdmitted:
		return insurance_core.CarrierAdmittedType_CarrierAdmittedType_NonAdmitted
	}

	return insurance_core.CarrierAdmittedType_CarrierAdmittedType_Unspecified
}

func getIBSegment(
	ctx context.Context,
	args *non_fleet.CreateAdmittedPolicyArgs,
	formsWrapper forms.FormWrapper,
	nfApWrapper application.Wrapper[*admitted_app.AdmittedApp],
) *model.InsuranceBundleSegment {
	policies, err := getPolicies(ctx, args, formsWrapper, nfApWrapper)
	if err != nil {
		log.Error(ctx, "failed to get policies", log.Err(err), log.AppID(args.AppObj.ID.String()))
		return nil
	}
	return &model.InsuranceBundleSegment{
		Id: uuid.NewString(),
		Interval: &proto.Interval{
			Start: timestamppb.New(args.SubObj.Info.GetEffectiveDates().Start),
			End:   timestamppb.New(args.SubObj.Info.GetEffectiveDates().End),
		},
		PrimaryInsured: &insurance_core.Insured{
			Type: insurance_core.InsuredType_InsuredType_PrimaryInsured,
			Name: &insurance_core.InsuredName{
				BusinessName: args.SubObj.Info.CompanyInfo.Name,
			},
			// TODO Confirm if we need to use mailing address here
			Address: getProtoAddress(args.SubObj.Info.GetBusinessOwnerDetails().Address),
			ExternalIdentifier: &insurance_core.InsuredIdentifier{
				Type:  insurance_core.InsuredIdentifierType_InsuredIdentifierType_DOTNumber,
				Value: []string{strconv.Itoa(args.SubObj.Info.CompanyInfo.DOTNumber)},
			},
		},
		Policies: policies,
		CoverageCriteria: &model.CoverageCriteria{
			Limits:              getLimits(&args.SubObj.Info.CoverageInfo),
			Deductibles:         getDeductibles(&args.SubObj.Info.CoverageInfo),
			CombinedDeductibles: getCombinedDeductibles(&args.SubObj.Info.CoverageInfo),
		},
	}
}

func getPolicies(
	ctx context.Context,
	args *non_fleet.CreateAdmittedPolicyArgs,
	formsWrapper forms.FormWrapper,
	nfApWrapper application.Wrapper[*admitted_app.AdmittedApp],
) (map[string]*model.Policy, error) {
	policyMap := make(map[string]*model.Policy, 0)
	fcCoverageIdMap := args.GetSignaturePacketFormsCompilation().Metadata().CoverageIdsMap
	if fcCoverageIdMap == nil {
		return nil, errors.Newf("form compilation coverage ids map is nil")
	}

	for _, cov := range args.SubObj.Info.CoverageInfo.PrimaryCovs {
		if cov.CoverageType == appenums.CoverageAutoPhysicalDamage {
			// Skipping APD coverage as it is part of the AL policy
			continue
		}

		policyFormCompilationID := fcCoverageIdMap.GetFormCompilationID(cov.CoverageType)
		if policyFormCompilationID == nil {
			return nil, errors.Newf("form compilation id not found for coverage type %s",
				cov.CoverageType)
		}
		policyFormCompilation, err := formsWrapper.GetFormCompilationById(ctx, *policyFormCompilationID)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to get policy form compilation with id %s",
				policyFormCompilationID)
		}

		policyNumber, err := non_fleet.GeneratePolicyNumber(
			ctx,
			nfApWrapper,
			*args.AppObj,
			cov.CoverageType,
			policydbenums.ProgramTypeNonFleetAdmitted,
			args.SubObj.Info.GetEffectiveDates().Start,
		)
		if err != nil {
			log.Error(ctx, "failed to generate policy number", log.Err(err))
			return nil, errors.Wrap(err, "failed to generate policy number")
		}

		programData, nonPrimaryInsureds, clauses, err := getNFProgramData(ctx, args)
		if err != nil {
			log.Error(ctx, "failed to get program data", log.Err(err))
			return nil, errors.Wrap(err, "failed to get program data")
		}

		var chgs *charges.ChargeList
		if args.PricingContext().Charges.PolicyCharges != nil {
			chgs = args.PricingContext().Charges.PolicyCharges[policyNumber.String()]
		}

		policyDetails := &model.Policy{
			PolicyNumber: policyNumber.String(),
			State:        model.PolicyState_PolicyState_Created,
			Coverages:    getCoverages(ctx, cov, args.SubObj.Info.CoverageInfo),
			EffectiveInterval: &proto.Interval{
				Start: timestamppb.New(args.SubObj.Info.GetEffectiveDates().Start),
				End:   timestamppb.New(args.SubObj.Info.GetEffectiveDates().End),
			},
			Metadata: &model.PolicyMetadata{
				BindableSubmissionId: args.SubObj.ID.String(),
				ApplicationId:        args.SubObj.ApplicationID.String(),
			},
			NonPrimaryInsureds: nonPrimaryInsureds,
			ProgramData:        programData,
			WrittenExposure:    nil,
			FormInfo: insurance_core.GetFormInfoFromFormCompilation(
				ctx,
				*policyFormCompilation,
				insurance_core.FormCompilationType_FormCompilationTypePolicy,
				"", // TODO: Discuss forms flow with PMs, possible to keep this now in contract
			),
			Charges: chgs,
			Clauses: clauses,
		}
		policyMap[policyDetails.PolicyNumber] = policyDetails
	}
	return policyMap, nil
}

// This and all other coverage related functions should be moved to a coverage package. This is a temporary solution.
// Right now, info is NF specific, so keeping it here.
func getCoverages(
	ctx context.Context,
	cov application.CoverageDetails,
	info application.CoverageInfo,
) []*model.Coverage {
	coverages := []*model.Coverage{
		{
			Id:           cov.CoverageType.String(),
			DisplayName:  cov.Label,
			SubCoverages: getSubCoveragesForCoverage(ctx, cov.CoverageType, info),
		},
	}

	if cov.CoverageType == appenums.CoverageAutoLiability {
		apdCov := info.GetCoverage(appenums.CoverageAutoPhysicalDamage)
		if apdCov != nil {
			coverages = append(coverages, &model.Coverage{
				Id:           apdCov.CoverageType.String(),
				DisplayName:  apdCov.Label,
				SubCoverages: getSubCoveragesForCoverage(ctx, apdCov.CoverageType, info),
			})
		}
	}
	return coverages
}

func getSubCoveragesForCoverage(
	ctx context.Context,
	cov appenums.Coverage,
	info application.CoverageInfo,
) []*model.SubCoverage {
	subCoverages := make([]*model.SubCoverage, 0)

	ancCovFromPrimaryCoverage := coverage.GetSubCoverageFromPrimaryCoverage(cov)
	for _, ancCoverage := range ancCovFromPrimaryCoverage {
		subCov := &model.SubCoverage{
			Id:          ancCoverage.String(),
			DisplayName: appenums.GetCoverageLabel(ancCoverage),
		}
		subCoverages = append(subCoverages, subCov)
	}

	for _, ancCoverage := range info.AncillaryCovs {
		if coverage.IsAppCoverageAPolicyModifier(ancCoverage.CoverageType) {
			continue
		}
		pCovFromAncCoverage, ok := appenums.AncCoverageToPrimaryCoverage[ancCoverage.CoverageType]
		if !ok {
			log.Error(ctx, "sub coverage not found in AncCoverageToPrimaryCoverage map",
				log.String("ancCoverageType", ancCoverage.CoverageType.String()))
			continue
			// TODO: Uncomment this when we are ready to fail the IB creation because of a missing mapping
			// return nil, errors.Wrap(err, "failed to find primary coverage for sub coverage")
		}
		if pCovFromAncCoverage != cov {
			continue
		}
		subCoverage := &model.SubCoverage{
			Id:          ancCoverage.CoverageType.String(),
			DisplayName: appenums.GetCoverageLabel(ancCoverage.CoverageType),
			// DisplayName: ancCoverage.Label, // TODO: ancCoverage.Label is not getting populated today, fix this
		}
		subCoverages = append(subCoverages, subCoverage)
	}
	return subCoverages
}

func getNFProgramData(
	ctx context.Context,
	args *non_fleet.CreateAdmittedPolicyArgs,
) (*model.ProgramData, []*insurance_core.Insured, *insurance_core.ClauseList, error) {
	// TO be moved to not use legacy program data and directly create ProgramData from AppInfo
	legacyProgramData, err := non_fleet.GetAdmittedProgramData(args)
	if err != nil {
		log.Error(ctx, "failed to get legacy program data", log.Err(err))
		return nil, nil, nil, errors.Wrap(err, "failed to get legacy program data")
	}

	nonPrimaryInsureds := getNonPrimaryInsureds(legacyProgramData.CompanyInfo)
	clauses := getNFAdditionalInfoV1(args.SubObj.Info.CoverageInfo)
	programData := &model.ProgramData{
		Data: &model.ProgramData_NonFleetData{
			NonFleetData: &nfmodel.NFAdmittedProgramDataV1{
				Operations: &nfmodel.NFAdmittedOperationDataV1{
					MaxRadiusOfOperation: nfmodel.MaxRadiusOfOperation(legacyProgramData.ClassInfo.MaxRadiusOfOperation),
					NumberOfPowerUnits:   int64(legacyProgramData.CompanyInfo.NumberOfPowerUnits),
				},
				Drivers:          getDrivers(legacyProgramData.DriverInfo, legacyProgramData.UnderwriterInfo.DriverViolations),
				Vehicles:         getVehicles(legacyProgramData.EquipmentInfo),
				CommodityDetails: getCommodityDetails(legacyProgramData.CommodityInfo),
				UnderwriterInput: getUnderwriterInput(legacyProgramData.UnderwriterInfo),
				CompanyInfo:      getCompanyInfo(legacyProgramData.CompanyInfo),
			},
		},
	}

	return programData, nonPrimaryInsureds, clauses, nil
}

func getNonPrimaryInsureds(info *admitted.CompanyInfo) []*insurance_core.Insured {
	insureds := make([]*insurance_core.Insured, 0)
	if info == nil {
		return insureds
	}
	for _, owner := range info.DesignatedInsureds {
		insureds = append(insureds, GetInsCoreInsuredFromPDInsured(owner,
			insurance_core.InsuredType_InsuredType_DesignatedInsured))
	}

	for _, owner := range info.AdditionalNamedInsureds {
		insureds = append(insureds, GetInsCoreInsuredFromPDInsured(owner,
			insurance_core.InsuredType_InsuredType_AdditionalNamedInsured))
	}

	for _, owner := range info.AdditionalInsureds {
		insureds = append(insureds, GetInsCoreInsuredFromPDInsured(owner,
			insurance_core.InsuredType_InsuredType_AdditionalInsured))
	}

	for _, owner := range info.AdditionalInsuredsAndLossPayees {
		insureds = append(insureds, GetInsCoreInsuredFromPDInsured(owner,
			insurance_core.InsuredType_InsuredType_AdditionalInsuredAndLossPayee))
	}

	for _, owner := range info.SpecifiedAdditionalInsureds {
		insureds = append(insureds, GetInsCoreInsuredFromPDInsured(owner,
			insurance_core.InsuredType_InsuredType_SpecifiedAdditionalInsured))
	}

	return insureds
}

func GetInsCoreInsuredFromPDInsured(
	owner pdshared.Insured,
	insuredType insurance_core.InsuredType,
) *insurance_core.Insured {
	return &insurance_core.Insured{
		Type:    insuredType,
		Name:    &insurance_core.InsuredName{BusinessName: owner.Name},
		Address: getProtoAddressFromAddressV1(owner.Address),
	}
}

func getCompanyInfo(info *admitted.CompanyInfo) *nfmodel.NFCompanyInfoV1 {
	if info == nil {
		return nil
	}
	return &nfmodel.NFCompanyInfoV1{
		Name:             info.Name,
		DotNumber:        int32(info.DOTNumber),
		UsState:          info.USState.ToCode(),
		BusinessOwner:    getBusinessOwner(info.BusinessOwner),
		TerminalAddress:  getProtoAddressFromTerminalLocation(info.TerminalLocation),
		MailingAddress:   getProtoAddressFromApplicationAddress(info.MailingAddress),
		AnnualCostOfHire: info.AnnualCostOfHire,
	}
}

func getBusinessOwner(owner admitted.BusinessOwner) *nfmodel.NFBusinessOwnerV1 {
	return &nfmodel.NFBusinessOwnerV1{
		FirstName:            owner.Firstname,
		LastName:             owner.Lastname,
		DateOfBirth:          timestamppb.New(owner.DateOfBirth),
		Address:              getProtoAddressFromAdmittedAddress(owner.Address),
		DriverOnPolicy:       owner.DriverOnPolicy,
		EncryptedSSN:         owner.EncryptedSSN,
		EncryptedSSNLastFour: owner.EncryptedSSNLastFour,
	}
}

func getUnderwriterInput(info *admitted_app.UnderwriterInput) *nfmodel.NFAdmittedUnderwriterInputDataV1 {
	if info == nil {
		return nil
	}
	return &nfmodel.NFAdmittedUnderwriterInputDataV1{
		UsDotScore:   nfenums.GetNFUSDOTProtoEnumFromNFUSDot(info.USDOTScore),
		CreditScore:  admittedEnums.GetNFCreditScoreProtoEnumFromCreditScore(info.CreditScore),
		PaymentPlan:  nfenums.GetNFPaymentPlanProtoEnumFromNFPaymentPlan(info.PaymentPlan),
		ScheduleMods: getScheduleMods(info),
	}
}

func getScheduleMods(info *admitted_app.UnderwriterInput) *nfmodel.NFAdmittedScheduleModDataV1 {
	if info == nil {
		return nil
	}
	return &nfmodel.NFAdmittedScheduleModDataV1{
		LiabScheduleMod: info.LiabScheduleMod,
		ApdScheduleMod:  info.ApdScheduleMod,
		GlScheduleMod:   info.GlScheduleMod,
		MtcScheduleMod:  info.MtcScheduleMod,
		AllCovSafetyMod: info.AllCovSafetyMod,
	}
}

func getCommodityDetails(info *admitted_app.CommodityInfo) *nfmodel.NFAdmittedCommodityDetailsV1 {
	var pCommodity nfmodel.CommodityCategory
	if info.PrimaryCommodity != nil {
		pCommodity = admittedEnums.GetNFCommodityCategoryProtoEnumFromNFAdmittedCommodity(*info.PrimaryCommodity)
	}

	return &nfmodel.NFAdmittedCommodityDetailsV1{
		Commodities:      getAdmittedCommodities(info.CommodityDistribution),
		PrimaryCategory:  info.PrimaryCategory,
		PrimaryCommodity: pCommodity,
	}
}

func getAdmittedCommodities(distribution []admitted_app.CommodityRecord) []*nfmodel.NFAdmittedCommodityDataV1 {
	commodities := make([]*nfmodel.NFAdmittedCommodityDataV1, 0)
	for _, record := range distribution {
		commodities = append(commodities, &nfmodel.NFAdmittedCommodityDataV1{
			CategoryType: record.CategoryLabel,
			CategoryName: record.Name,
			Commodity:    admittedEnums.GetNFCommodityCategoryProtoEnumFromNFAdmittedCommodity(record.Category),
			Percentage:   float32(record.Percentage),
		})
	}
	return commodities
}

func getVehicles(info *admitted.EquipmentInfo) []*nfmodel.NFAdmittedVehicleDataV1 {
	vehicles := make([]*nfmodel.NFAdmittedVehicleDataV1, 0)
	if info == nil {
		return vehicles
	}
	for _, vehicle := range info.Vehicles {
		var lossPayee *insurance_core.Insured
		if vehicle.LossPayee != nil {
			lossPayee = &insurance_core.Insured{
				Type: insurance_core.InsuredType_InsuredType_LossPayee,
				Name: &insurance_core.InsuredName{
					BusinessName: vehicle.LossPayee.Name,
				},
				Address: getProtoAddressFromAddressV1(vehicle.LossPayee.Address),
			}
		}
		var statedValue *float64
		if vehicle.StatedValue != nil {
			statedValue = pointer_utils.ToPointer(float64(*vehicle.StatedValue))
		}

		vehicles = append(vehicles, &nfmodel.NFAdmittedVehicleDataV1{
			Vin:                vehicle.VIN,
			Make:               vehicle.Make,
			Model:              vehicle.Model,
			Year:               int32(vehicle.Year),
			VehicleClass:       admittedEnums.GetNFVehicleClassProtoEnumFromNFVehicleClass(vehicle.Class),
			VehicleType:        admittedEnums.GetNFVehicleTypeProtoEnumFromNFVehicleType(vehicle.Type),
			VehicleWeightClass: admittedEnums.GetNFVehicleWeightClassProtoEnumFromNFWeightClass(vehicle.WeightClass),
			StatedValue:        statedValue,
			LossPayee:          lossPayee,
		})
	}
	return vehicles
}

func getDrivers(
	info *admitted.DriverInfo,
	driverViolations []admitted_app.DriverViolation,
) []*nfmodel.NFAdmittedDriverDataV1 {
	drivers := make([]*nfmodel.NFAdmittedDriverDataV1, 0)
	if info == nil {
		return drivers
	}

	driverViolationMap := slice_utils.ToMap(driverViolations, func(v admitted_app.DriverViolation) string {
		return v.LicenseNumber
	}, func(v admitted_app.DriverViolation) admitted_app.DriverViolation {
		return v
	})

	for _, driver := range info.Drivers {
		admittedDriverViolation, ok := driverViolationMap[driver.LicenseNumber]
		driverViolation := &nfmodel.NFAdmittedViolationDataV1{}
		// If driver has no violations, then set it to empty struct. Check if this is correct thing to do
		if ok {
			classCount := map_utils.Transform(admittedDriverViolation.ClassCounts, func(k string, v int64) int32 {
				return int32(v)
			})
			driverViolation = &nfmodel.NFAdmittedViolationDataV1{
				ViolationPoints: int32(admittedDriverViolation.ViolationPoints),
				ClassCounts:     classCount,
			}
		}

		drivers = append(drivers, &nfmodel.NFAdmittedDriverDataV1{
			FirstName:                 driver.FirstName,
			LastName:                  driver.LastName,
			DateOfBirth:               timestamppb.New(driver.DateOfBirth),
			DateOfHire:                timestamppb.New(driver.DateOfHire),
			YearsOfExperience:         int32(driver.YearsOfExp),
			LicenseNumber:             driver.LicenseNumber,
			LicenseState:              driver.LicenseState,
			IsOutOfState:              driver.IsOutOfState,
			IsIncludedInPolicy:        driver.IsIncluded,
			HasViolationsInLast3Years: driver.ViolationInLastThreeYears,
			Violations:                driverViolation,
		})
	}
	return drivers
}

func getCombinedDeductibles(info *application.CoverageInfo) []*model.CombinedDeductible {
	// Only APD and MIC can be combined at the moment.
	// TODO: Update the combined deductible to accept a slice of group deductible.
	combinedDeductibles := make([]*model.CombinedDeductible, 0)
	if info.IsAPDMTCDeductibleCombined {
		subCovIds := make([]string, 0)
		subCovIds = append(subCovIds, coverage.GetSubCoverageStringFromPrimaryCoverage(appenums.CoverageAutoPhysicalDamage)...)
		subCovIds = append(subCovIds, coverage.GetSubCoverageStringFromPrimaryCoverage(appenums.CoverageMotorTruckCargo)...)
		combinedDeductibles = append(combinedDeductibles, &model.CombinedDeductible{
			SubCoverageIds: subCovIds,
		})
	}
	return combinedDeductibles
}

func getDeductibles(info *application.CoverageInfo) []*model.Deductible {
	deductibles := make([]*model.Deductible, 0)
	if info == nil {
		return deductibles
	}
	for _, cov := range info.PrimaryCovs {
		subCovIds := coverage.GetSubCoverageStringFromPrimaryCoverage(cov.CoverageType)
		deductibles = updateDeductibleForCoverages(deductibles, cov, subCovIds)
	}

	for _, cov := range info.AncillaryCovs {
		deductibles = updateDeductibleForCoverages(deductibles, cov, []string{cov.CoverageType.String()})
	}
	return deductibles
}

func updateDeductibleForCoverages(
	deductibles []*model.Deductible,
	cov application.CoverageDetails,
	subCovs []string,
) []*model.Deductible {
	if coverage.IsAppCoverageAPolicyModifier(cov.CoverageType) || cov.Deductible == nil {
		return deductibles
	}
	if cov.CoverageType == appenums.CoverageAutoLiability {
		// For Auto Liability, BI PD is getting treated as a group deductible.
		deductibles = append(deductibles, &model.Deductible{
			Amount:         float64(*cov.Deductible),
			SubCoverageIds: subCovs,
		})
	} else {
		for _, subCov := range subCovs {
			deductibles = append(deductibles, &model.Deductible{
				Amount:         float64(*cov.Deductible),
				SubCoverageIds: []string{subCov},
			})
		}
	}
	return deductibles
}

func getLimits(info *application.CoverageInfo) []*model.Limit {
	limits := make([]*model.Limit, 0)
	if info == nil {
		return limits
	}
	for _, cov := range info.PrimaryCovs {
		if coverage.IsAppCoverageAPolicyModifier(cov.CoverageType) {
			continue
		}
		if cov.Limit == nil || *cov.Limit == 0 {
			continue
		}
		subCovIds := coverage.GetSubCoverageStringFromPrimaryCoverage(cov.CoverageType)
		if cov.CoverageType == appenums.CoverageAutoLiability {
			limits = append(limits, getLimit(cov, subCovIds, model.LimitGrouping_LimitGrouping_Combined))
		} else {
			for _, subCov := range subCovIds {
				limits = append(limits, getLimit(cov, []string{subCov}, model.LimitGrouping_LimitGrouping_Single))
			}
		}
	}

	for _, cov := range info.AncillaryCovs {
		if coverage.IsAppCoverageAPolicyModifier(cov.CoverageType) {
			continue
		}
		if cov.Limit == nil || *cov.Limit == 0 {
			continue
		}
		limits = append(limits, getLimit(cov, []string{cov.CoverageType.String()}, model.LimitGrouping_LimitGrouping_Single))
	}
	return limits
}

func getLimit(cov application.CoverageDetails, subCov []string, limitGrouping model.LimitGrouping) *model.Limit {
	// TODO: Update this to use the right coverage Id, type and display name when we solve for sub coverages completely
	covId := cov.CoverageType.String()
	if limitGrouping == model.LimitGrouping_LimitGrouping_Single && len(subCov) == 1 {
		// For APD, which has 2 single limits for Coll and Comp, we use the coll and comp as ids.
		covId = subCov[0]
	}
	return &model.Limit{
		Id:             covId,
		DisplayName:    appenums.GetCoverageLabel(cov.CoverageType),
		SubCoverageIds: subCov,
		Amount:         float64(*cov.Limit),
		Grouping:       limitGrouping,
	}
}

func getProtoAddress(address nonfleet.Address) *proto.Address {
	return &proto.Address{
		Nation:  pointer_utils.ToPointer(us_states.DefaultCountry),
		State:   pointer_utils.ToPointer(address.State),
		City:    pointer_utils.ToPointer(address.City),
		Street:  pointer_utils.ToPointer(address.Street),
		ZipCode: pointer_utils.ToPointer(address.Zip),
	}
}

func getProtoAddressFromAddressV1(address *pdshared.AddressV1) *proto.Address {
	return &proto.Address{
		Nation:  pointer_utils.ToPointer(us_states.DefaultCountry),
		State:   pointer_utils.ToPointer(address.State.ToCode()),
		City:    pointer_utils.ToPointer(address.City),
		Street:  pointer_utils.ToPointer(address.Street),
		ZipCode: pointer_utils.ToPointer(address.ZipCode),
	}
}

func getProtoAddressFromTerminalLocation(location *admitted.TerminalLocation) *proto.Address {
	address := getProtoAddressFromAdmittedAddress(location.Address)
	return address
}

func getProtoAddressFromAdmittedAddress(address admitted.Address) *proto.Address {
	return &proto.Address{
		Nation:  pointer_utils.ToPointer(us_states.DefaultCountry),
		State:   pointer_utils.ToPointer(address.State),
		City:    pointer_utils.ToPointer(address.City),
		Street:  pointer_utils.ToPointer(address.Street),
		ZipCode: pointer_utils.ToPointer(address.ZipCode),
	}
}

func getProtoAddressFromApplicationAddress(address application.Address) *proto.Address {
	return &proto.Address{
		Nation:  pointer_utils.ToPointer(us_states.DefaultCountry),
		State:   pointer_utils.ToPointer(address.State),
		City:    pointer_utils.ToPointer(address.City),
		Street:  pointer_utils.ToPointer(address.Street),
		ZipCode: pointer_utils.ToPointer(address.ZipCode),
	}
}

func getFormInfo(
	ctx context.Context,
	subObj *application.Submission[*admitted_app.AdmittedApp],
	formCompilation *compilation.FormsCompilation,
) *insurance_core.FormInfo {
	docHandleId := ""
	// Failing migration for 1 case because of this being nil, shouldn't be nil ideally
	if subObj.QuotePDFHandleID != nil {
		docHandleId = subObj.QuotePDFHandleID.String()
	}
	return insurance_core.GetFormInfoFromFormCompilation(
		ctx,
		*formCompilation,
		insurance_core.FormCompilationType_FormCompilationTypeSignaturePacket,
		docHandleId,
	)
}

func getNFAdditionalInfoV1(info application.CoverageInfo) *insurance_core.ClauseList {
	clauses := insurance_core.ClauseList{}
	for _, ancCoverage := range info.AncillaryCovs {
		if ancCoverage.CoverageType == appenums.CoverageBlanketWaiverOfSubrogation {
			clauses.Clauses = append(clauses.Clauses,
				getInsuranceClause(insurance_core.ClauseType_ClauseTypeWaiverOfSubrogation))
		} else if ancCoverage.CoverageType == appenums.CoverageBlanketAdditional {
			clauses.Clauses = append(clauses.Clauses,
				getInsuranceClause(insurance_core.ClauseType_ClauseTypeAdditionalInsured))
		} else if ancCoverage.CoverageType == appenums.CoverageHiredAuto {
			clauses.Clauses = append(clauses.Clauses,
				getInsuranceClause(insurance_core.ClauseType_ClauseTypeUIIA),
			)
		}
	}
	return &clauses
}

func getInsuranceClause(clauseType insurance_core.ClauseType) *insurance_core.Clause {
	return &insurance_core.Clause{
		Id: &insurance_core.ClauseId{
			Id: clauseType.String(),
		},
		Type: clauseType,
		ParticipantScope: &insurance_core.ParticipantScope{
			Type: insurance_core.ParticipantScopeApplicabilityType_ParticipantScopeApplicabilityTypeBlanket,
		},
	}
}

func getArgs(event *fsm.Event) (EventArguments, error) {
	if len(event.Args) == 0 {
		return EventArguments{}, ErrEmptyArgument
	}
	args, ok := (event.Args[0]).(EventArguments)
	if !ok {
		return EventArguments{}, errors.Wrapf(ErrTypeAssertion, "", log.Any("EventArguments", event.Args[0]))
	}
	return args, nil
}

func insertAppReviewForAdmitted(
	ctx context.Context,
	deps NFAppStateMachineWrapperDeps,
	appID uuid.UUID,
	subID uuid.UUID,
	originalAppReview application_review.ApplicationReview,
) (*uuid.UUID, error) {
	app, err := deps.AdmittedAppWrapper.GetAppById(ctx, appID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get app for appID: %v", appID)
	}
	sub, err := deps.AdmittedAppWrapper.GetSubmissionById(ctx, subID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get submission for subID: %v", subID)
	}

	// We wait till the UW Submission Job has reached a terminal state before we proceed
	err = deps.Jobber.WaitForJobRunCompletion(ctx, jtypes.JobRunId{JobId: sub.JobRunInfo.JobId, RunId: sub.JobRunInfo.RunId})
	if err != nil {
		return nil, errors.Wrapf(err, "failed to wait for job run completion for subID: %v", subID)
	}

	// Set the selected Indication id for the UW Sub
	err = setSelectedIndicationID(ctx, deps, app.QuoteSubmissionID, subID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to set selected indication id for subID: %v", subID)
	}

	// Re-fetch submission to get the updated selected indication id
	sub, err = deps.AdmittedAppWrapper.GetSubmissionById(ctx, subID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get submission for subID: %v", subID)
	}

	if sub.SelectedIndicationID == uuid.Nil {
		return nil, errors.Newf("submission does not have a selected indication for subID: %v", subID)
	}
	indicationOption, err := deps.AdmittedAppWrapper.GetIndOptionById(ctx, sub.SelectedIndicationID)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get selected indication option for subID: %v", subID)
	}

	var (
		overrides           application_review.Overrides
		mvrPulled           bool
		additionalDocuments application_review.ApplicationReviewDocuments
		notes               *application_review.ApplicationReviewNote
	)

	// Compile and persist Ancillary Coverages for the app review
	ancillaryCoverages, err := generateAppReviewAncillaryCoverages(sub, policyenums.ProgramTypeNonFleetAdmitted, indicationOption.GetPackage())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to compile ancillary coverages for subID: %v", sub.ID)
	}

	// Set scraperInfo for the app review by copying from the app
	// Also, set the USDOTScore and CreditScore from the scraperInfo
	scraperInfo := app.Info.ScraperInfo
	if scraperInfo.USDOTVariance != nil && math.Abs(float64(*scraperInfo.USDOTVariance)) <= admitted_app.AllowedVarianceInScores {
		overrides.USDotScore = scraperInfo.USDOTScore
	}
	if scraperInfo.CreditVariance != nil && math.Abs(float64(*scraperInfo.CreditVariance)) <= admitted_app.AllowedVarianceInScores {
		overrides.BizOwnerCreditScore = scraperInfo.CreditScore
	}

	var driverInfo *application_review.DriversInfo
	// We copy over the overrides, mvrPulled, additionalDocuments and notes from the original app review
	// which gets marked stale & a new app review is created
	if originalAppReview != nil {
		overrides = originalAppReview.GetOverrides()
		mvrPulled = originalAppReview.IsMVRPulled()
		additionalDocuments = originalAppReview.GetReviewDocuments()
		notes = originalAppReview.GetReviewNotes()

		// Schedule Mods are not copied over from the original app review
		overrides.ALPercent = nil
		overrides.GLPercent = nil
		overrides.APDPercent = nil
		overrides.MTCPercent = nil

		// Safety Credit is copied over only if selected safety score for review is not nil
		// i.e. we want to copy over safety credit wherever a safety score was selected for review
		if overrides.SafetyScoreSelectedForReview == nil {
			overrides.SafetyCredit = nil
		}

		originalScraperInfo := originalAppReview.GetScraperInfo()
		if originalScraperInfo != nil && isBusinessOwnerSame(ctx, deps, originalAppReview.GetSubmissionID(), app) {
			// Copying over the scraper info from the original app review if the business owner is the same
			scraperInfo = *originalScraperInfo
		}
		driverInfo = originalAppReview.GetDriversInfo()
	}

	// Ancillary coverages are recomputed & not copied over from the original app review
	overrides.AncillaryCoverages = ancillaryCoverages

	driversInfo := getAppReviewDriversInfo(app, driverInfo)
	appReviewId := uuid.New()

	if err := deps.NFApplicationReviewWrapper.InsertApplicationReview(
		ctx,
		&application_review.AdmittedApplicationReview{
			ID:                  appReviewId,
			State:               application_review.AppReviewStatePending,
			ApplicationID:       appID,
			SubmissionID:        subID,
			UnderwriterID:       app.UnderwriterID,
			CreatedAt:           time.Now(),
			UpdatedAt:           time.Now(),
			Notes:               notes,
			Overrides:           overrides,
			AdditionalDocuments: additionalDocuments,
			MVRPulled:           mvrPulled,
			EffectiveDate:       app.EffectiveDate,
			EffectiveDateTo:     app.EffectiveDateTo,
			ScraperInfo:         scraperInfo,
			DriversInfo:         &driversInfo,
		},
	); err != nil {
		return nil, errors.Wrapf(err, "failed to insert application review for admitted app")
	}
	return &appReviewId, nil
}

func isBusinessOwnerSame(
	ctx context.Context,
	deps NFAppStateMachineWrapperDeps,
	subID uuid.UUID,
	appObj *application.Application[*admitted_app.AdmittedApp],
) bool {
	originalSub, err := deps.AdmittedAppWrapper.GetSubmissionById(ctx, subID)
	if err != nil {
		log.Error(ctx, "failed to get submission", log.Err(err))
		return false
	}

	if cmp.Equal(
		originalSub.Info.CompanyInfo.BusinessOwner,
		appObj.Info.CompanyInfo.BusinessOwner,
		cmpopts.IgnoreFields(admitted_app.BusinessOwner{}, "DriverOnPolicy"),
	) {
		return true
	}

	return false
}

func setSelectedIndicationID(
	ctx context.Context,
	deps NFAppStateMachineWrapperDeps,
	quoteSubID uuid.UUID,
	subID uuid.UUID,
) error {
	indSubObj, err := deps.AdmittedAppWrapper.GetSubmissionById(ctx, quoteSubID)
	if err != nil {
		return errors.Wrapf(err, "failed to get submission: %v", quoteSubID)
	}

	originalIndication, err := deps.AdmittedAppWrapper.GetIndOptionById(ctx, indSubObj.SelectedIndicationID)
	if err != nil {
		return errors.Wrapf(err, "failed to get indication: %v", indSubObj.SelectedIndicationID)
	}
	indicationPackageType := originalIndication.GetPackage()

	err = deps.AdmittedAppWrapper.UpdateSubmission(
		ctx,
		subID,
		func(sub application.Submission[*admitted_app.AdmittedApp]) (*application.Submission[*admitted_app.AdmittedApp], error) {
			for _, indID := range sub.IndicationIDs {
				ind, err := deps.AdmittedAppWrapper.GetIndOptionById(ctx, indID)
				if err != nil {
					return nil, errors.Wrap(err, "failed to get indication")
				}
				if ind.GetPackage() == indicationPackageType {
					sub.SelectedIndicationID = indID
					break
				}
			}

			selectedQuotingPricingContextID, err := quoting.GetQuotingPricingContextIdForAPackage(ctx,
				sub.QuotingPricingContextIDs, deps.AdmittedAppWrapper, indicationPackageType)
			if err != nil {
				return nil, errors.Wrapf(err, "unable to get quoting pricing context id for package %s",
					indicationPackageType)
			}
			sub.SelectedQuotingPricingContextID = selectedQuotingPricingContextID
			return &sub, nil
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update submission: %v", subID)
	}
	return nil
}

func triggerAppPDF(
	ctx context.Context,
	deps NFAppStateMachineWrapperDeps,
	appType *constants.ApplicationType,
	subID uuid.UUID,
) error {
	if appType != nil {
		jobRunID, err := deps.Jobber.AddJobRun(ctx,
			jobber.NewAddJobRunParams(
				pdfjobs.GenerateNFApplicationPDF,
				&pdfjobs.GenerateNFAppPDFArgs{
					SubmissionID:    subID,
					ApplicationType: *appType,
				},
				jtypes.NewMetadata(jtypes.Immediate)),
		)
		if err != nil {
			return errors.Wrapf(err, "failed to add job: %v for subID: %v", pdfjobs.GenerateNFApplicationPDF, subID)
		}
		log.Info(ctx, "added job",
			log.String("Job", pdfjobs.GenerateNFApplicationPDF), log.String("jobRunID", jobRunID.String()))
	}
	return nil
}

func getAppReviewDriversInfo(
	app *application.Application[*admitted_app.AdmittedApp],
	originalDriversInfo *application_review.DriversInfo,
) application_review.DriversInfo {
	originalDriversMap := make(map[string]application_review.Driver)
	if originalDriversInfo != nil {
		originalDriversMap = originalDriversInfo.Drivers
	}
	driversMap := make(map[string]application_review.Driver)
	for _, d := range app.Info.DriverInfo.Drivers {
		violations := originalDriversMap[d.LicenseNumber].Violations
		driversMap[d.LicenseNumber] = application_review.Driver{
			IsExcluded:   !d.IsIncluded,
			IsOutOfState: d.IsOutOfState,
			Violations:   violations,
		}
	}
	return application_review.DriversInfo{
		Drivers: driversMap,
	}
}

func updateExpressLaneStatusOnSubmission(
	ctx context.Context,
	deps NFAppStateMachineWrapperDeps,
	appId uuid.UUID,
) error {
	// STEP 1 - Check if the app is eligible for Express Lane
	isApplied, err := deps.NFApplicationExperimentManager.IsExperimentAppliedToSubject(ctx, express_lane.ExpressLaneV1ExperimentId, appId)
	if err != nil {
		return errors.Wrapf(err, "failed to check if experiment is applicable to app %s", appId)
	}
	if isApplied == nil || !*isApplied {
		return nil
	}

	// STEP 2 - Update the express lane status on the submission
	app, err := deps.AdmittedAppWrapper.GetAppById(ctx, appId)
	if err != nil {
		return errors.Wrapf(err, "failed to get app for appID: %v", appId)
	}
	var (
		fromState *string
		jobRunIds []jtypes.JobRunId
	)

	if app.ExpressLaneMetadata != nil {
		stateStr := app.ExpressLaneMetadata.State.String()
		fromState = pointer_utils.ToPointer(stateStr)
		jobRunIds = app.ExpressLaneMetadata.JobRunIds
	}

	err = deps.AdmittedAppWrapper.UpdateApp(ctx, appId,
		func(appObj application.Application[*admitted_app.AdmittedApp]) (application.Application[*admitted_app.AdmittedApp], error) {
			expressLaneMetadata := application.ExpressLaneMetadata{
				State:                    nfenums.ExpressLaneStateWaitingForTelematics,
				LastStateUpdateTimeStamp: deps.Clock.Now(),
				JobRunIds:                jobRunIds,
			}
			appObj.ExpressLaneMetadata = pointer_utils.ToPointer(expressLaneMetadata)
			return appObj, nil
		})
	if err != nil {
		return errors.Wrap(err, "failed to update express lane status")
	}

	// STEP 3 - Trigger the express lane state change event
	payload := express_lane.ExpressLaneStateChangeEvent{
		FromState: fromState,
		ToState:   pointer_utils.ToPointer(nfenums.ExpressLaneStateWaitingForTelematics.String()),
	}

	err = deps.NFApplicationExperimentManager.RecordExperimentStateChangeEvent(ctx, express_lane.ExpressLaneV1ExperimentId, appId, payload)
	if err != nil {
		// Just log and move, since this is non-breaking, and if it fails, it won't harm a lot.
		log.Error(ctx, "failed to record experiment state change event for express lane",
			log.Stringer("app_id", app.ID),
			log.Stringer("experiment_id", express_lane.ExpressLaneV1ExperimentId),
			log.String("old_status", *fromState),
			log.Stringer("new_status", nfenums.ExpressLaneStateWaitingForTelematics),
			log.Err(err),
		)
	}

	return nil
}
