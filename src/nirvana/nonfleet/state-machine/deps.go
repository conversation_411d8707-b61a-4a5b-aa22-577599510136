package statemachine

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cactus/go-statsd-client/v5/statsd"
	"go.uber.org/fx"
	"gopkg.in/segmentio/analytics-go.v3"
	"nirvanatech.com/nirvana/application/experiments/non_fleet"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/jobber/event"

	"nirvanatech.com/nirvana/insurance-bundle/service"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/db-api/db_wrappers/agency"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/db-api/db_wrappers/forms"
	nf_application "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_app_review "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application_review"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy_set"
	"nirvanatech.com/nirvana/db-api/db_wrappers/prog_type_identifier"
	"nirvanatech.com/nirvana/nonfleet/quote_generator"
	"nirvanatech.com/nirvana/nonfleet/rule_engine"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
)

type NFAppStateMachineWrapperDeps struct {
	fx.In
	AdmittedAppWrapper             nf_application.Wrapper[*admitted_app.AdmittedApp]
	ProgramTypeIdentifier          prog_type_identifier.ProgramTypeIdentifier
	NFApplicationReviewWrapper     nf_app_review.Wrapper
	AdmittedRuleEngine             *rule_engine.NFRuleEngine[*admitted_app.AdmittedApp]
	PDFGenDeps                     quote_generator.PDFGenDeps
	SegmentClient                  analytics.Client
	Jobber                         quoting_jobber.Client
	AuthWrapper                    auth.DataWrapper
	AgencyWrapper                  agency.DataWrapper
	PolicySetWrapper               policy_set.DataWrapper
	PolicyWrapper                  policy.DataWrapper
	FormsWrapper                   forms.FormWrapper
	FMCSAWrapper                   fmcsa.DataWrapper
	MetricsClient                  statsd.Statter
	TSPConnManager                 *tsp_connections.TSPConnManager
	IBService                      service.InsuranceBundleManagerClient
	FeatureFlagClient              feature_flag_lib.Client
	EventClient                    event.Client
	Clock                          clock.Clock
	NFApplicationExperimentManager *non_fleet.Manager
}
