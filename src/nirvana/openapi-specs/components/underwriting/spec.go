// Package underwriting provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.0.0 DO NOT EDIT.
package underwriting

import (
	"encoding/json"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
	externalRef0 "nirvanatech.com/nirvana/openapi-specs/components/common"
)

// Defines values for AppetiteScoreEnum.
const (
	AppetiteScoreEnumAcceptable AppetiteScoreEnum = "AppetiteScoreEnumAcceptable"
	AppetiteScoreEnumHighRisk   AppetiteScoreEnum = "AppetiteScoreEnumHighRisk"
	AppetiteScoreEnumMarginal   AppetiteScoreEnum = "AppetiteScoreEnumMarginal"
	AppetiteScoreEnumPreferred  AppetiteScoreEnum = "AppetiteScoreEnumPreferred"
)

// Defines values for ApplicationEndState.
const (
	ApplicationEndStateBound    ApplicationEndState = "Bound"
	ApplicationEndStateClosed   ApplicationEndState = "Closed"
	ApplicationEndStateDeclined ApplicationEndState = "Declined"
	ApplicationEndStateQuoted   ApplicationEndState = "Quoted"
)

// Defines values for ApplicationQuoteArtifactPackageType.
const (
	BasicPackage    ApplicationQuoteArtifactPackageType = "BasicPackage"
	CompletePackage ApplicationQuoteArtifactPackageType = "CompletePackage"
	CustomPackage   ApplicationQuoteArtifactPackageType = "CustomPackage"
	StandardPackage ApplicationQuoteArtifactPackageType = "StandardPackage"
)

// Defines values for ApplicationReviewAccountGradeProperty.
const (
	ApplicationReviewAccountGradePropertyA     ApplicationReviewAccountGradeProperty = "A"
	ApplicationReviewAccountGradePropertyB     ApplicationReviewAccountGradeProperty = "B"
	ApplicationReviewAccountGradePropertyC     ApplicationReviewAccountGradeProperty = "C"
	ApplicationReviewAccountGradePropertyD     ApplicationReviewAccountGradeProperty = "D"
	ApplicationReviewAccountGradePropertyE     ApplicationReviewAccountGradeProperty = "E"
	ApplicationReviewAccountGradePropertyEmpty ApplicationReviewAccountGradeProperty = ""
	ApplicationReviewAccountGradePropertyF     ApplicationReviewAccountGradeProperty = "F"
)

// Defines values for ApplicationReviewActionDisableReasonCategory.
const (
	ApplicationReviewActionDisableReasonCategoryAuthority          ApplicationReviewActionDisableReasonCategory = "Authority"
	ApplicationReviewActionDisableReasonCategoryCameraDetails      ApplicationReviewActionDisableReasonCategory = "CameraDetails"
	ApplicationReviewActionDisableReasonCategoryClosedApplication  ApplicationReviewActionDisableReasonCategory = "ClosedApplication"
	ApplicationReviewActionDisableReasonCategoryCreditLimitBreach  ApplicationReviewActionDisableReasonCategory = "CreditLimitBreach"
	ApplicationReviewActionDisableReasonCategoryMiscellaneous      ApplicationReviewActionDisableReasonCategory = "Miscellaneous"
	ApplicationReviewActionDisableReasonCategoryMstReferral        ApplicationReviewActionDisableReasonCategory = "MstReferral"
	ApplicationReviewActionDisableReasonCategoryRecommendations    ApplicationReviewActionDisableReasonCategory = "Recommendations"
	ApplicationReviewActionDisableReasonCategoryUnreviewedProblems ApplicationReviewActionDisableReasonCategory = "UnreviewedProblems"
	ApplicationReviewActionDisableReasonCategoryVinVisibility      ApplicationReviewActionDisableReasonCategory = "VinVisibility"
)

// Defines values for ApplicationReviewActionDisableReasonDataType.
const (
	ApplicationReviewActionDisableReasonDataTypeMarkdown ApplicationReviewActionDisableReasonDataType = "ApplicationReviewActionDisableReasonDataTypeMarkdown"
)

// Defines values for ApplicationReviewCoverageInfoState.
const (
	ApplicationReviewCoverageInfoStateApproved ApplicationReviewCoverageInfoState = "Approved"
	ApplicationReviewCoverageInfoStateDeclined ApplicationReviewCoverageInfoState = "Declined"
)

// Defines values for ApplicationReviewCurrentStatus.
const (
	AwaitingAgent           ApplicationReviewCurrentStatus = "AwaitingAgent"
	AwaitingManagerApproval ApplicationReviewCurrentStatus = "AwaitingManagerApproval"
	BORRequested            ApplicationReviewCurrentStatus = "BORRequested"
	BlockedMST              ApplicationReviewCurrentStatus = "BlockedMST"
	InReview                ApplicationReviewCurrentStatus = "InReview"
	NotStarted              ApplicationReviewCurrentStatus = "NotStarted"
)

// Defines values for ApplicationReviewDataCompletionTab.
const (
	ApplicationReviewDataCompletionTabInternal       ApplicationReviewDataCompletionTab = "ApplicationReviewDataCompletionTabInternal"
	ApplicationReviewDataCompletionTabOpen           ApplicationReviewDataCompletionTab = "ApplicationReviewDataCompletionTabOpen"
	ApplicationReviewDataCompletionTabReadyForReview ApplicationReviewDataCompletionTab = "ApplicationReviewDataCompletionTabReadyForReview"
	ApplicationReviewDataCompletionTabReviewComplete ApplicationReviewDataCompletionTab = "ApplicationReviewDataCompletionTabReviewComplete"
)

// Defines values for ApplicationReviewDeclineReasonObjectReasonCategory.
const (
	Guideline    ApplicationReviewDeclineReasonObjectReasonCategory = "Guideline"
	NonGuideline ApplicationReviewDeclineReasonObjectReasonCategory = "NonGuideline"
)

// Defines values for ApplicationReviewDriversListItemMvrStatus.
const (
	ApplicationReviewDriversListItemMvrStatusFailure     ApplicationReviewDriversListItemMvrStatus = "failure"
	ApplicationReviewDriversListItemMvrStatusInProgress  ApplicationReviewDriversListItemMvrStatus = "in_progress"
	ApplicationReviewDriversListItemMvrStatusSuccess     ApplicationReviewDriversListItemMvrStatus = "success"
	ApplicationReviewDriversListItemMvrStatusUninitiated ApplicationReviewDriversListItemMvrStatus = "uninitiated"
)

// Defines values for ApplicationReviewLargeLossesItemCoverageType.
const (
	ApplicationReviewLargeLossesItemCoverageTypeAutoLiability      ApplicationReviewLargeLossesItemCoverageType = "Auto Liability"
	ApplicationReviewLargeLossesItemCoverageTypeAutoPhysicalDamage ApplicationReviewLargeLossesItemCoverageType = "Auto Physical Damage"
)

// Defines values for ApplicationReviewLossSummaryItemRecordV2Tags.
const (
	FileMissing   ApplicationReviewLossSummaryItemRecordV2Tags = "FileMissing"
	FileOutOfDate ApplicationReviewLossSummaryItemRecordV2Tags = "FileOutOfDate"
)

// Defines values for ApplicationReviewMstReferralReviewRuleDecision.
const (
	ApplicationReviewMstReferralReviewRuleDecisionActive   ApplicationReviewMstReferralReviewRuleDecision = "Active"
	ApplicationReviewMstReferralReviewRuleDecisionInactive ApplicationReviewMstReferralReviewRuleDecision = "Inactive"
)

// Defines values for ApplicationReviewMstReferralReviewRuleType.
const (
	ApplicationReviewMstReferralReviewRuleTypeAutomatic     ApplicationReviewMstReferralReviewRuleType = "Automatic"
	ApplicationReviewMstReferralReviewRuleTypeSemiAutomatic ApplicationReviewMstReferralReviewRuleType = "SemiAutomatic"
)

// Defines values for ApplicationReviewMstReferralReviewStatus.
const (
	ApplicationReviewMstReferralReviewStatusDecisionSubmitted ApplicationReviewMstReferralReviewStatus = "DecisionSubmitted"
	ApplicationReviewMstReferralReviewStatusNotEligible       ApplicationReviewMstReferralReviewStatus = "NotEligible"
	ApplicationReviewMstReferralReviewStatusNotEligibleByUW   ApplicationReviewMstReferralReviewStatus = "NotEligibleByUW"
	ApplicationReviewMstReferralReviewStatusPending           ApplicationReviewMstReferralReviewStatus = "Pending"
	ApplicationReviewMstReferralReviewStatusReferralEmailSent ApplicationReviewMstReferralReviewStatus = "ReferralEmailSent"
)

// Defines values for ApplicationReviewMstReferralRuleWidgetEnum.
const (
	CrashRecords ApplicationReviewMstReferralRuleWidgetEnum = "CrashRecords"
	LossSummary  ApplicationReviewMstReferralRuleWidgetEnum = "LossSummary"
)

// Defines values for ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass.
const (
	ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClassA ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass = "A"
	ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClassB ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass = "B"
	ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClassC ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass = "C"
	ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClassD ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass = "D"
	ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClassE ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass = "E"
	ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClassF ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass = "F"
)

// Defines values for ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType.
const (
	OLD     ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType = "OLD"
	OVERALL ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType = "OVERALL"
	RECENT  ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType = "RECENT"
)

// Defines values for ApplicationReviewOperationsGaragingLocationFormItemType.
const (
	ApplicationReviewOperationsGaragingLocationFormItemTypeGaraging ApplicationReviewOperationsGaragingLocationFormItemType = "garaging"
	ApplicationReviewOperationsGaragingLocationFormItemTypeParking  ApplicationReviewOperationsGaragingLocationFormItemType = "parking"
)

// Defines values for ApplicationReviewOperationsGaragingLocationItemType.
const (
	ApplicationReviewOperationsGaragingLocationItemTypeGaraging ApplicationReviewOperationsGaragingLocationItemType = "garaging"
	ApplicationReviewOperationsGaragingLocationItemTypeParking  ApplicationReviewOperationsGaragingLocationItemType = "parking"
)

// Defines values for ApplicationReviewOperationsTerminalLocationItemType.
const (
	ApplicationReviewOperationsTerminalLocationItemTypeDock     ApplicationReviewOperationsTerminalLocationItemType = "Dock"
	ApplicationReviewOperationsTerminalLocationItemTypeDropLot  ApplicationReviewOperationsTerminalLocationItemType = "DropLot"
	ApplicationReviewOperationsTerminalLocationItemTypeOffice   ApplicationReviewOperationsTerminalLocationItemType = "Office"
	ApplicationReviewOperationsTerminalLocationItemTypeTerminal ApplicationReviewOperationsTerminalLocationItemType = "Terminal"
)

// Defines values for ApplicationReviewPackageTypeValue.
const (
	Basic    ApplicationReviewPackageTypeValue = "Basic"
	Complete ApplicationReviewPackageTypeValue = "Complete"
	Standard ApplicationReviewPackageTypeValue = "Standard"
)

// Defines values for ApplicationReviewQuoteCombinedDeductibleCoverages.
const (
	ApplicationReviewQuoteCombinedDeductibleCoveragesAutoLiability      ApplicationReviewQuoteCombinedDeductibleCoverages = "Auto Liability"
	ApplicationReviewQuoteCombinedDeductibleCoveragesAutoPhysicalDamage ApplicationReviewQuoteCombinedDeductibleCoverages = "Auto Physical Damage"
	ApplicationReviewQuoteCombinedDeductibleCoveragesGeneralLiability   ApplicationReviewQuoteCombinedDeductibleCoverages = "General Liability"
	ApplicationReviewQuoteCombinedDeductibleCoveragesMotorTruckCargo    ApplicationReviewQuoteCombinedDeductibleCoverages = "Motor Truck Cargo"
)

// Defines values for ApplicationReviewQuoteStatus.
const (
	ApplicationReviewQuoteStatusFailure ApplicationReviewQuoteStatus = "failure"
	ApplicationReviewQuoteStatusRunning ApplicationReviewQuoteStatus = "running"
	ApplicationReviewQuoteStatusSuccess ApplicationReviewQuoteStatus = "success"
	ApplicationReviewQuoteStatusUnknown ApplicationReviewQuoteStatus = "unknown"
)

// Defines values for ApplicationReviewRecommendationConclusion.
const (
	ApplicationReviewRecommendationConclusionAccept          ApplicationReviewRecommendationConclusion = "Accept"
	ApplicationReviewRecommendationConclusionDecline         ApplicationReviewRecommendationConclusion = "Decline"
	ApplicationReviewRecommendationConclusionPartiallyAccept ApplicationReviewRecommendationConclusion = "Partially Accept"
)

// Defines values for ApplicationReviewRecommendationRecommendedActionPrimaryAction.
const (
	ApplySubsidy               ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Apply Subsidy"
	BindLess                   ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Bind Less"
	BindMore                   ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Bind More"
	DecreaseMargin             ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Decrease Margin"
	DelightAgent               ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Delight Agent"
	DontQuote                  ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Don't Quote"
	IncreaseMargin             ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Increase Margin"
	InspectFinancialStatements ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Inspect Financial Statements"
	QuoteLess                  ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Quote Less"
	QuoteMore                  ApplicationReviewRecommendationRecommendedActionPrimaryAction = "Quote More"
)

// Defines values for ApplicationReviewReportType.
const (
	MSTReferral ApplicationReviewReportType = "MSTReferral"
)

// Defines values for ApplicationReviewSafetyDotRatingSafetyRating.
const (
	Conditional    ApplicationReviewSafetyDotRatingSafetyRating = "Conditional"
	Satisfactory   ApplicationReviewSafetyDotRatingSafetyRating = "Satisfactory"
	Unrated        ApplicationReviewSafetyDotRatingSafetyRating = "Unrated"
	Unsatisfactory ApplicationReviewSafetyDotRatingSafetyRating = "Unsatisfactory"
)

// Defines values for ApplicationReviewState.
const (
	ApplicationReviewStateApproved           ApplicationReviewState = "ApplicationReviewStateApproved"
	ApplicationReviewStateClosed             ApplicationReviewState = "ApplicationReviewStateClosed"
	ApplicationReviewStateDeclined           ApplicationReviewState = "ApplicationReviewStateDeclined"
	ApplicationReviewStatePending            ApplicationReviewState = "ApplicationReviewStatePending"
	ApplicationReviewStateReadyForReview     ApplicationReviewState = "ApplicationReviewStateReadyForReview"
	ApplicationReviewStateRefreshingPremiums ApplicationReviewState = "ApplicationReviewStateRefreshingPremiums"
	ApplicationReviewStateStale              ApplicationReviewState = "ApplicationReviewStateStale"
)

// Defines values for ApplicationReviewTab.
const (
	ApplicationReviewTabAll                     ApplicationReviewTab = "ApplicationReviewTabAll"
	ApplicationReviewTabApproved                ApplicationReviewTab = "ApplicationReviewTabApproved"
	ApplicationReviewTabClosed                  ApplicationReviewTab = "ApplicationReviewTabClosed"
	ApplicationReviewTabDeclined                ApplicationReviewTab = "ApplicationReviewTabDeclined"
	ApplicationReviewTabExpressLane             ApplicationReviewTab = "ApplicationReviewTabExpressLane"
	ApplicationReviewTabIncomplete              ApplicationReviewTab = "ApplicationReviewTabIncomplete"
	ApplicationReviewTabInternal                ApplicationReviewTab = "ApplicationReviewTabInternal"
	ApplicationReviewTabPending                 ApplicationReviewTab = "ApplicationReviewTabPending"
	ApplicationReviewTabPreTelematicsExperiment ApplicationReviewTab = "ApplicationReviewTabPreTelematicsExperiment"
	ApplicationReviewTabReadyForReview          ApplicationReviewTab = "ApplicationReviewTabReadyForReview"
	ApplicationReviewTabReadyToBind             ApplicationReviewTab = "ApplicationReviewTabReadyToBind"
	ApplicationReviewTabReferral                ApplicationReviewTab = "ApplicationReviewTabReferral"
	ApplicationReviewTabStale                   ApplicationReviewTab = "ApplicationReviewTabStale"
)

// Defines values for ApplicationReviewTabName.
const (
	ApplicationReviewTabNameApproved       ApplicationReviewTabName = "Approved"
	ApplicationReviewTabNameClosed         ApplicationReviewTabName = "Closed"
	ApplicationReviewTabNameDeclined       ApplicationReviewTabName = "Declined"
	ApplicationReviewTabNameIncomplete     ApplicationReviewTabName = "Incomplete"
	ApplicationReviewTabNameInternal       ApplicationReviewTabName = "Internal"
	ApplicationReviewTabNameReadyForReview ApplicationReviewTabName = "Ready for Review"
	ApplicationReviewTabNameStale          ApplicationReviewTabName = "Stale"
)

// Defines values for ApplicationReviewTabStatus.
const (
	ApplicationReviewTabStatusApproved       ApplicationReviewTabStatus = "Approved"
	ApplicationReviewTabStatusClosed         ApplicationReviewTabStatus = "Closed"
	ApplicationReviewTabStatusDeclined       ApplicationReviewTabStatus = "Declined"
	ApplicationReviewTabStatusIncomplete     ApplicationReviewTabStatus = "Incomplete"
	ApplicationReviewTabStatusNeedsAttention ApplicationReviewTabStatus = "Needs Attention"
	ApplicationReviewTabStatusReadyForReview ApplicationReviewTabStatus = "Ready for Review"
	ApplicationReviewTabStatusStale          ApplicationReviewTabStatus = "Stale"
)

// Defines values for ApplicationReviewUserPermissionType.
const (
	ApplicationReviewUserPermissionTypeALPerUnitPremium               ApplicationReviewUserPermissionType = "ALPerUnitPremium"
	ApplicationReviewUserPermissionTypeAddedCoverageLines             ApplicationReviewUserPermissionType = "AddedCoverageLines"
	ApplicationReviewUserPermissionTypeAlDeductible                   ApplicationReviewUserPermissionType = "AlDeductible"
	ApplicationReviewUserPermissionTypeAlRate                         ApplicationReviewUserPermissionType = "AlRate"
	ApplicationReviewUserPermissionTypeAppetiteFactorRecommendation   ApplicationReviewUserPermissionType = "AppetiteFactorRecommendation"
	ApplicationReviewUserPermissionTypeAppetiteScore                  ApplicationReviewUserPermissionType = "AppetiteScore"
	ApplicationReviewUserPermissionTypeApprovalPermission             ApplicationReviewUserPermissionType = "ApprovalPermission"
	ApplicationReviewUserPermissionTypeAuthorityPermission            ApplicationReviewUserPermissionType = "AuthorityPermission"
	ApplicationReviewUserPermissionTypeBasicAlerts                    ApplicationReviewUserPermissionType = "BasicAlerts"
	ApplicationReviewUserPermissionTypeCurrentTermGrowth              ApplicationReviewUserPermissionType = "CurrentTermGrowth"
	ApplicationReviewUserPermissionTypeDotRating                      ApplicationReviewUserPermissionType = "DotRating"
	ApplicationReviewUserPermissionTypeGWP                            ApplicationReviewUserPermissionType = "GWP"
	ApplicationReviewUserPermissionTypeHeavyHaul                      ApplicationReviewUserPermissionType = "HeavyHaul"
	ApplicationReviewUserPermissionTypeIsMinimumMileageGuaranteed     ApplicationReviewUserPermissionType = "IsMinimumMileageGuaranteed"
	ApplicationReviewUserPermissionTypeIsRenewalApp                   ApplicationReviewUserPermissionType = "IsRenewalApp"
	ApplicationReviewUserPermissionTypeLossPerUnitPerYear             ApplicationReviewUserPermissionType = "LossPerUnitPerYear"
	ApplicationReviewUserPermissionTypeMileageProjection              ApplicationReviewUserPermissionType = "MileageProjection"
	ApplicationReviewUserPermissionTypeNegotiatedRates                ApplicationReviewUserPermissionType = "NegotiatedRates"
	ApplicationReviewUserPermissionTypeNonStandardDrivers             ApplicationReviewUserPermissionType = "NonStandardDrivers"
	ApplicationReviewUserPermissionTypeRateChange                     ApplicationReviewUserPermissionType = "RateChange"
	ApplicationReviewUserPermissionTypeRecommendedActionQuote         ApplicationReviewUserPermissionType = "RecommendedActionQuote"
	ApplicationReviewUserPermissionTypeRecommendedActionStronglyQuote ApplicationReviewUserPermissionType = "RecommendedActionStronglyQuote"
	ApplicationReviewUserPermissionTypeSafetyScores                   ApplicationReviewUserPermissionType = "SafetyScores"
	ApplicationReviewUserPermissionTypeScheduleMod                    ApplicationReviewUserPermissionType = "ScheduleMod"
	ApplicationReviewUserPermissionTypeTelematicsConnectionPremier    ApplicationReviewUserPermissionType = "TelematicsConnectionPremier"
	ApplicationReviewUserPermissionTypeTotalClaims                    ApplicationReviewUserPermissionType = "TotalClaims"
	ApplicationReviewUserPermissionTypeYIB                            ApplicationReviewUserPermissionType = "YIB"
)

// Defines values for ApplicationState.
const (
	AppStateApproved            ApplicationState = "AppStateApproved"
	AppStateClosed              ApplicationState = "AppStateClosed"
	AppStateDeclined            ApplicationState = "AppStateDeclined"
	AppStateIndicationGenerated ApplicationState = "AppStateIndicationGenerated"
	AppStatePanic               ApplicationState = "AppStatePanic"
	AppStatePolicyCreated       ApplicationState = "AppStatePolicyCreated"
	AppStateQuoteGenerated      ApplicationState = "AppStateQuoteGenerated"
	AppStateUnderUWReview       ApplicationState = "AppStateUnderUWReview"
	AppStateUnsubmitted         ApplicationState = "AppStateUnsubmitted"
)

// Defines values for ApplicationUserActionType.
const (
	UserActionApproval             ApplicationUserActionType = "UserActionApproval"
	UserActionClose                ApplicationUserActionType = "UserActionClose"
	UserActionDecline              ApplicationUserActionType = "UserActionDecline"
	UserActionReopenReview         ApplicationUserActionType = "UserActionReopenReview"
	UserActionViewReleaseDocuments ApplicationUserActionType = "UserActionViewReleaseDocuments"
)

// Defines values for AuthorityRequestState.
const (
	AuthorityRequestStateApproved             AuthorityRequestState = "approved"
	AuthorityRequestStateDeclined             AuthorityRequestState = "declined"
	AuthorityRequestStatePending              AuthorityRequestState = "pending"
	AuthorityRequestStateUnderUwManagerReview AuthorityRequestState = "under_uw_manager_review"
)

// Defines values for AuthorityRequestType.
const (
	RequestTypeDecline AuthorityRequestType = "RequestTypeDecline"
)

// Defines values for AuthorityReviewAction.
const (
	ReviewActionApprove AuthorityReviewAction = "ReviewActionApprove"
	ReviewActionReject  AuthorityReviewAction = "ReviewActionReject"
)

// Defines values for Category.
const (
	DRIVERS             Category = "DRIVERS"
	EQUIPMENTS          Category = "EQUIPMENTS"
	FINANCIALS          Category = "FINANCIALS"
	LOSSES              Category = "LOSSES"
	OPERATIONS          Category = "OPERATIONS"
	SAFETY              Category = "SAFETY"
	UNSPECIFIEDCATEGORY Category = "UNSPECIFIED_CATEGORY"
)

// Defines values for EmailPreference.
const (
	EmailPreferenceActive  EmailPreference = "Active"
	EmailPreferenceStopped EmailPreference = "Stopped"
)

// Defines values for EmailStatus.
const (
	EmailStatusActive                 EmailStatus = "Active"
	EmailStatusCancelledAutomatically EmailStatus = "CancelledAutomatically"
	EmailStatusCancelledByUser        EmailStatus = "CancelledByUser"
	EmailStatusCompleted              EmailStatus = "Completed"
	EmailStatusUnscheduled            EmailStatus = "Unscheduled"
)

// Defines values for FeatureStatus.
const (
	FeatureStatusNoDataAvailable FeatureStatus = "NoDataAvailable"
	FeatureStatusProcessing      FeatureStatus = "Processing"
	FeatureStatusSuccess         FeatureStatus = "Success"
)

// Defines values for ISOVehicleType.
const (
	ISOVehicleTypeSemiTrailer ISOVehicleType = "semi_trailer"
	ISOVehicleTypeTractor     ISOVehicleType = "tractor"
	ISOVehicleTypeTruck       ISOVehicleType = "truck"
)

// Defines values for ISOVehicleWeightGroup.
const (
	ISOVehicleWeightGroupHeavy       ISOVehicleWeightGroup = "heavy"
	ISOVehicleWeightGroupLight       ISOVehicleWeightGroup = "light"
	ISOVehicleWeightGroupMedium      ISOVehicleWeightGroup = "medium"
	ISOVehicleWeightGroupSemiTrailer ISOVehicleWeightGroup = "semi_trailer"
	ISOVehicleWeightGroupXheavy      ISOVehicleWeightGroup = "xheavy"
)

// Defines values for LossRunFileErrorCode.
const (
	IncompleteFile LossRunFileErrorCode = "Incomplete File"
	InvalidFile    LossRunFileErrorCode = "Invalid File"
	NoRowsPresent  LossRunFileErrorCode = "No Rows Present"
	UnparsableFile LossRunFileErrorCode = "Unparsable File"
)

// Defines values for LossRunFileParsedStatus.
const (
	Failed     LossRunFileParsedStatus = "Failed"
	InProgress LossRunFileParsedStatus = "In Progress"
	Processed  LossRunFileParsedStatus = "Processed"
)

// Defines values for LossRunValueSource.
const (
	Agent  LossRunValueSource = "Agent"
	Parsed LossRunValueSource = "Parsed"
)

// Defines values for LossSummaryVersion.
const (
	V1 LossSummaryVersion = "V1"
	V2 LossSummaryVersion = "V2"
)

// Defines values for MileageEstimateReasonsCategory.
const (
	MileageEstimateReasonsCategoryHigher          MileageEstimateReasonsCategory = "Higher"
	MileageEstimateReasonsCategoryLower           MileageEstimateReasonsCategory = "Lower"
	MileageEstimateReasonsCategoryNoDataAvailable MileageEstimateReasonsCategory = "NoDataAvailable"
	MileageEstimateReasonsCategorySame            MileageEstimateReasonsCategory = "Same"
)

// Defines values for MileageEstimateReasonsData.
const (
	AgentIsConservative MileageEstimateReasonsData = "Agent is conservative"
	Conservative        MileageEstimateReasonsData = "Conservative"
	DecliningFleet      MileageEstimateReasonsData = "Declining fleet"
	GrowingFleet        MileageEstimateReasonsData = "Growing fleet"
	IFTAs               MileageEstimateReasonsData = "IFTAs"
	Negotiation         MileageEstimateReasonsData = "Negotiation"
	NewContract         MileageEstimateReasonsData = "New contract"
	Other               MileageEstimateReasonsData = "Other"
	SeasonalBusiness    MileageEstimateReasonsData = "Seasonal Business"
)

// Defines values for MstReferralRuleType.
const (
	MstReferralRuleTypeAutomatic MstReferralRuleType = "Automatic"
	MstReferralRuleTypeManual    MstReferralRuleType = "Manual"
)

// Defines values for NBasicScore.
const (
	NBasicScoreA NBasicScore = "NBasicScoreA"
	NBasicScoreB NBasicScore = "NBasicScoreB"
	NBasicScoreC NBasicScore = "NBasicScoreC"
	NBasicScoreD NBasicScore = "NBasicScoreD"
	NBasicScoreE NBasicScore = "NBasicScoreE"
	NBasicScoreF NBasicScore = "NBasicScoreF"
	Undefined    NBasicScore = "Undefined"
)

// Defines values for OptionalValueValueType.
const (
	Bool    OptionalValueValueType = "bool"
	Integer OptionalValueValueType = "integer"
	String  OptionalValueValueType = "string"
)

// Defines values for PaginatedListSortBy.
const (
	DaysConnected PaginatedListSortBy = "DaysConnected"
	EffectiveDate PaginatedListSortBy = "EffectiveDate"
)

// Defines values for PaginatedListSortDirection.
const (
	Ascending  PaginatedListSortDirection = "Ascending"
	Descending PaginatedListSortDirection = "Descending"
)

// Defines values for ParsedLossState.
const (
	CheckWithAgent ParsedLossState = "Check with Agent"
	Match          ParsedLossState = "Match"
	Mismatch       ParsedLossState = "Mismatch"
	NoAgentFile    ParsedLossState = "No Agent File"
	NotValidated   ParsedLossState = "Not Validated"
)

// Defines values for ParsedLossStateV2.
const (
	Processing ParsedLossStateV2 = "Processing"
	Validated  ParsedLossStateV2 = "Validated"
)

// Defines values for PricingType.
const (
	ALREADYPRICED          PricingType = "ALREADY_PRICED"
	MANUALLYPRICED         PricingType = "MANUALLY_PRICED"
	UNSPECIFIEDPRICINGTYPE PricingType = "UNSPECIFIED_PRICING_TYPE"
)

// Defines values for RecommendationInfoState.
const (
	RecommendationInfoStateActive RecommendationInfoState = "Active"
	RecommendationInfoStateStale  RecommendationInfoState = "Stale"
)

// Defines values for RecommendationInfoPanelType.
const (
	Drivers    RecommendationInfoPanelType = "Drivers"
	Equipments RecommendationInfoPanelType = "Equipments"
	Financials RecommendationInfoPanelType = "Financials"
	Losses     RecommendationInfoPanelType = "Losses"
	Operations RecommendationInfoPanelType = "Operations"
	Overview   RecommendationInfoPanelType = "Overview"
	Packages   RecommendationInfoPanelType = "Packages"
	Safety     RecommendationInfoPanelType = "Safety"
)

// Defines values for RecommendedAction.
const (
	RecommendedActionDecline                  RecommendedAction = "Decline"
	RecommendedActionNeutral                  RecommendedAction = "Neutral"
	RecommendedActionNotApplicableForNonFleet RecommendedAction = "Not Applicable For Non Fleet"
	RecommendedActionNotApplicableForRenewal  RecommendedAction = "Not Applicable For Renewal"
	RecommendedActionPending                  RecommendedAction = "Pending"
	RecommendedActionQuote                    RecommendedAction = "Quote"
	RecommendedActionStronglyQuote            RecommendedAction = "Strongly Quote"
)

// Defines values for RecommendedActionReason.
const (
	DriverCountIsLessThanThreshold                                 RecommendedActionReason = "Driver count is less than threshold"
	DriverTurnoverIsGreaterThanThreshold                           RecommendedActionReason = "Driver Turnover is greater than threshold"
	HalfYearlyUtilizationIsGreaterThanThreshold                    RecommendedActionReason = "Half-yearly Utilization is greater than threshold"
	HazardZoneDistancePercentageForNewJerseyIsGreaterThanThreshold RecommendedActionReason = "Hazard Zone Distance Percentage (for New Jersey) is greater than threshold"
	HazardZoneDistancePercentageIsGreaterThanThreshold             RecommendedActionReason = "Hazard Zone Distance Percentage is greater than threshold"
	HazardZoneDurationPercentageIsGreaterThanThreshold             RecommendedActionReason = "Hazard Zone Duration Percentage is greater than threshold"
	HighHazardZoneExposureDetected                                 RecommendedActionReason = "High hazard zone exposure detected"
	LossesBurnRateIsGreaterThan20K                                 RecommendedActionReason = "Losses Burn Rate is greater than $20K"
	N100UnitAccountWithANonPremierTSP                              RecommendedActionReason = "100+ unit account with a non-premier TSP"
	N50100UnitAccountWithANonPremierTSP                            RecommendedActionReason = "50-100 unit account with a non-premier TSP"
	QuarterlyUtilizationIsGreaterThanThreshold                     RecommendedActionReason = "Quarterly Utilization is greater than threshold"
	RecentConditionalDOTRating                                     RecommendedActionReason = "Recent Conditional DOT Rating"
	SafetyScoreIsUnavailableFromDataScience                        RecommendedActionReason = "Safety Score is unavailable from Data Science"
	TRSMarketCategoryIsDecline                                     RecommendedActionReason = "TRS Market Category is Decline"
	UnsupportedTSP                                                 RecommendedActionReason = "Unsupported TSP"
	VINVisibilityIsLessThan60AfterUWAction                         RecommendedActionReason = "VIN Visibility is less than 60% after UW Action"
	VINVisibilityIsLessThanThreshold                               RecommendedActionReason = "VIN Visibility is less than threshold"
	VehicleCountIsLessThanThreshold                                RecommendedActionReason = "Vehicle count is less than threshold"
	YearsInBusinessIsLessThan2Years                                RecommendedActionReason = "Years in business is less than 2 years"
)

// Defines values for ReviewReadinessTaskCompletionMethod.
const (
	TaskCompletionMethodAutomatic ReviewReadinessTaskCompletionMethod = "TaskCompletionMethodAutomatic"
	TaskCompletionMethodManual    ReviewReadinessTaskCompletionMethod = "TaskCompletionMethodManual"
)

// Defines values for ReviewReadinessTaskName.
const (
	TaskEquipment                      ReviewReadinessTaskName = "TaskEquipment"
	TaskSuccessfulTelematicsConnection ReviewReadinessTaskName = "TaskSuccessfulTelematicsConnection"
	TaskVINVisibility                  ReviewReadinessTaskName = "TaskVINVisibility"
)

// Defines values for ReviewReadinessTaskStatus.
const (
	TaskStatusDone    ReviewReadinessTaskStatus = "TaskStatusDone"
	TaskStatusPending ReviewReadinessTaskStatus = "TaskStatusPending"
	TaskStatusSkipped ReviewReadinessTaskStatus = "TaskStatusSkipped"
)

// Defines values for RiskFactorState.
const (
	ACTIVE                     RiskFactorState = "ACTIVE"
	DRAFT                      RiskFactorState = "DRAFT"
	INACTIVE                   RiskFactorState = "INACTIVE"
	UNSPECIFIEDRISKFACTORSTATE RiskFactorState = "UNSPECIFIED_RISK_FACTOR_STATE"
)

// Defines values for Sentiment.
const (
	NEGATIVE             Sentiment = "NEGATIVE"
	NEUTRAL              Sentiment = "NEUTRAL"
	POSITIVE             Sentiment = "POSITIVE"
	UNSPECIFIEDSENTIMENT Sentiment = "UNSPECIFIED_SENTIMENT"
)

// Defines values for TelematicsConnectionState.
const (
	TelematicsConnectionStateConnected    TelematicsConnectionState = "TelematicsConnectionStateConnected"
	TelematicsConnectionStateNotInitiated TelematicsConnectionState = "TelematicsConnectionStateNotInitiated"
	TelematicsConnectionStateRequestSent  TelematicsConnectionState = "TelematicsConnectionStateRequestSent"
)

// Defines values for TelematicsDataButtonStatus.
const (
	TelematicsDataButtonStatusDisabled     TelematicsDataButtonStatus = "Disabled"
	TelematicsDataButtonStatusEnabled      TelematicsDataButtonStatus = "Enabled"
	TelematicsDataButtonStatusNotConnected TelematicsDataButtonStatus = "NotConnected"
)

// Defines values for TelematicsStatus.
const (
	TelematicsStatusConnected    TelematicsStatus = "Connected"
	TelematicsStatusErrored      TelematicsStatus = "Errored"
	TelematicsStatusNotConnected TelematicsStatus = "NotConnected"
)

// Defines values for TraditionalRiskFactorsAppetiteFlag.
const (
	HighRisk     TraditionalRiskFactorsAppetiteFlag = "High Risk"
	LowRisk      TraditionalRiskFactorsAppetiteFlag = "Low Risk"
	ModerateRisk TraditionalRiskFactorsAppetiteFlag = "Moderate Risk"
	Unknown      TraditionalRiskFactorsAppetiteFlag = "Unknown"
)

// Defines values for UpdateApplicationReviewOperationsTerminalLocationItemType.
const (
	UpdateApplicationReviewOperationsTerminalLocationItemTypeDock     UpdateApplicationReviewOperationsTerminalLocationItemType = "Dock"
	UpdateApplicationReviewOperationsTerminalLocationItemTypeDropLot  UpdateApplicationReviewOperationsTerminalLocationItemType = "DropLot"
	UpdateApplicationReviewOperationsTerminalLocationItemTypeOffice   UpdateApplicationReviewOperationsTerminalLocationItemType = "Office"
	UpdateApplicationReviewOperationsTerminalLocationItemTypeTerminal UpdateApplicationReviewOperationsTerminalLocationItemType = "Terminal"
)

// Defines values for VehicleBodyClass.
const (
	TRAILER      VehicleBodyClass = "TRAILER"
	TruckTractor VehicleBodyClass = "Truck-Tractor"
)

// Defines values for VehicleType.
const (
	VehicleTypeIncompleteVehicle VehicleType = "VehicleTypeIncompleteVehicle"
	VehicleTypeMotorcycle        VehicleType = "VehicleTypeMotorcycle"
	VehicleTypeNil               VehicleType = "VehicleTypeNil"
	VehicleTypeTrailer           VehicleType = "VehicleTypeTrailer"
	VehicleTypeTruck             VehicleType = "VehicleTypeTruck"
)

// Defines values for WorksheetState.
const (
	APPROVED                  WorksheetState = "APPROVED"
	INPROGRESS                WorksheetState = "IN_PROGRESS"
	STALE                     WorksheetState = "STALE"
	UNSPECIFIEDWORKSHEETSTATE WorksheetState = "UNSPECIFIED_WORKSHEET_STATE"
)

// AppReviewEquipmentAddRequest defines model for AppReviewEquipmentAddRequest.
type AppReviewEquipmentAddRequest struct {
	StatedValue int    `json:"statedValue"`
	Vin         string `json:"vin"`
}

// AppReviewEquipmentUpdateFormItem defines model for AppReviewEquipmentUpdateFormItem.
type AppReviewEquipmentUpdateFormItem struct {
	Fixes         *EquipmentListVinProblemFixes `json:"fixes,omitempty"`
	IsDeletedByUW bool                          `json:"isDeletedByUW"`
	StatedValue   int32                         `json:"statedValue"`
	Vin           string                        `json:"vin"`
}

// AppReviewForList defines model for AppReviewForList.
type AppReviewForList struct {
	AgencyName                       *string                                  `json:"agencyName,omitempty"`
	AppReviewID                      string                                   `json:"appReviewID"`
	Assignees                        ApplicationReviewAssignees               `json:"assignees"`
	ClearanceStatus                  *externalRef0.ApplicationClearanceStatus `json:"clearanceStatus,omitempty"`
	CompanyName                      string                                   `json:"companyName"`
	CurrentStatus                    *ApplicationReviewCurrentStatus          `json:"currentStatus,omitempty"`
	DaysConnected                    *int                                     `json:"daysConnected,omitempty"`
	EffectiveDate                    openapi_types.Date                       `json:"effectiveDate"`
	IsInternal                       *bool                                    `json:"isInternal,omitempty"`
	IsVinVisibilityChecklistComplete *bool                                    `json:"isVinVisibilityChecklistComplete,omitempty"`
	PuCount                          int                                      `json:"puCount"`
	RecommendedAction                RecommendedAction                        `json:"recommendedAction"`
	TabDetails                       *ApplicationReviewTabDetails             `json:"tabDetails,omitempty"`
	TspName                          *string                                  `json:"tspName,omitempty"`
	VinVisibilityPercentage          *float32                                 `json:"vinVisibilityPercentage,omitempty"`
}

// AppetiteScoreEnum defines model for AppetiteScoreEnum.
type AppetiteScoreEnum string

// ApplicationEndState defines model for ApplicationEndState.
type ApplicationEndState string

// ApplicationIdBoundReasonObject defines model for ApplicationIdBoundReasonObject.
type ApplicationIdBoundReasonObject struct {
	IsActive         bool   `json:"isActive"`
	IsRenewal        bool   `json:"isRenewal"`
	PrimaryWinNote   string `json:"primaryWinNote"`
	Reason           string `json:"reason"`
	ReasonId         string `json:"reasonId"`
	SecondaryWinNote string `json:"secondaryWinNote"`
}

// ApplicationIdBoundReasons defines model for ApplicationIdBoundReasons.
type ApplicationIdBoundReasons struct {
	Reasons *[]ApplicationIdBoundReasonObject `json:"reasons,omitempty"`
}

// ApplicationMvrProblemRecord defines model for ApplicationMvrProblemRecord.
type ApplicationMvrProblemRecord struct {
	DateOfBirth               openapi_types.Date `json:"dateOfBirth"`
	DlNumber                  string             `json:"dlNumber"`
	Error                     string             `json:"error"`
	FixedMovingViolationCount *int               `json:"fixedMovingViolationCount,omitempty"`
	IsResolved                bool               `json:"isResolved"`
	IsReviewed                bool               `json:"isReviewed"`
	Name                      string             `json:"name"`
	ShouldSkip                *bool              `json:"shouldSkip,omitempty"`
	UsState                   string             `json:"usState"`
}

// ApplicationMvrProblems defines model for ApplicationMvrProblems.
type ApplicationMvrProblems struct {
	Problems []ApplicationMvrProblemRecord `json:"problems"`
}

// ApplicationQuoteArtifact defines model for ApplicationQuoteArtifact.
type ApplicationQuoteArtifact struct {
	ApplicationID      openapi_types.UUID                  `json:"applicationID"`
	ArtifactID         int64                               `json:"artifactID"`
	CreatedAt          time.Time                           `json:"createdAt"`
	IndicationOptionID openapi_types.UUID                  `json:"indicationOptionID"`
	PackageType        ApplicationQuoteArtifactPackageType `json:"packageType"`
	Premium            int32                               `json:"premium"`
	S3Link             string                              `json:"s3Link"`
	SubmissionID       openapi_types.UUID                  `json:"submissionID"`
}

// ApplicationQuoteArtifactPackageType defines model for ApplicationQuoteArtifact.PackageType.
type ApplicationQuoteArtifactPackageType string

// ApplicationReviewAccountGrade defines model for ApplicationReviewAccountGrade.
type ApplicationReviewAccountGrade struct {
	Grade ApplicationReviewAccountGradeProperty `json:"grade"`
}

// ApplicationReviewAccountGradeProperty defines model for ApplicationReviewAccountGradeProperty.
type ApplicationReviewAccountGradeProperty string

// ApplicationReviewAction defines model for ApplicationReviewAction.
type ApplicationReviewAction struct {
	ActionType     ApplicationUserActionType               `json:"actionType"`
	DisableReasons *[]ApplicationReviewActionDisableReason `json:"disableReasons,omitempty"`
	IsEnabled      bool                                    `json:"isEnabled"`
}

// ApplicationReviewActionDisableReason defines model for ApplicationReviewActionDisableReason.
type ApplicationReviewActionDisableReason struct {
	Category ApplicationReviewActionDisableReasonCategory `json:"category"`
	Data     ApplicationReviewActionDisableReasonData     `json:"data"`
	DataType ApplicationReviewActionDisableReasonDataType `json:"dataType"`
}

// ApplicationReviewActionDisableReasonCategory defines model for ApplicationReviewActionDisableReasonCategory.
type ApplicationReviewActionDisableReasonCategory string

// ApplicationReviewActionDisableReasonData defines model for ApplicationReviewActionDisableReasonData.
type ApplicationReviewActionDisableReasonData struct {
	MarkdownData *ApplicationReviewActionDisableReasonMarkdownData `json:"markdownData,omitempty"`
}

// ApplicationReviewActionDisableReasonDataType defines model for ApplicationReviewActionDisableReasonDataType.
type ApplicationReviewActionDisableReasonDataType string

// ApplicationReviewActionDisableReasonMarkdownData defines model for ApplicationReviewActionDisableReasonMarkdownData.
type ApplicationReviewActionDisableReasonMarkdownData struct {
	Markdown string `json:"markdown"`
}

// ApplicationReviewAggregateCreditByCoverage defines model for ApplicationReviewAggregateCreditByCoverage.
type ApplicationReviewAggregateCreditByCoverage struct {
	AutoLiability      *float32 `json:"autoLiability,omitempty"`
	AutoPhysicalDamage *float32 `json:"autoPhysicalDamage,omitempty"`
	GeneralLiability   *float32 `json:"generalLiability,omitempty"`
	MotorTruckCargo    *float32 `json:"motorTruckCargo,omitempty"`
}

// ApplicationReviewAggregateCreditLimitsByCoverage defines model for ApplicationReviewAggregateCreditLimitsByCoverage.
type ApplicationReviewAggregateCreditLimitsByCoverage struct {
	AutoLiability      *ApplicationReviewAggregateCreditLimitsByCoverageData `json:"autoLiability,omitempty"`
	AutoPhysicalDamage *ApplicationReviewAggregateCreditLimitsByCoverageData `json:"autoPhysicalDamage,omitempty"`
	MotorTruckCargo    *ApplicationReviewAggregateCreditLimitsByCoverageData `json:"motorTruckCargo,omitempty"`
}

// ApplicationReviewAggregateCreditLimitsByCoverageData defines model for ApplicationReviewAggregateCreditLimitsByCoverageData.
type ApplicationReviewAggregateCreditLimitsByCoverageData struct {
	Maximum *float32 `json:"maximum,omitempty"`
	Minimum *float32 `json:"minimum,omitempty"`
}

// ApplicationReviewAssignee defines model for ApplicationReviewAssignee.
type ApplicationReviewAssignee struct {
	Current *ApplicationReviewUser   `json:"current,omitempty"`
	Options *[]ApplicationReviewUser `json:"options,omitempty"`
}

// ApplicationReviewAssignees defines model for ApplicationReviewAssignees.
type ApplicationReviewAssignees struct {
	Underwriter *ApplicationReviewAssignee `json:"underwriter,omitempty"`
}

// ApplicationReviewAssigneesForm defines model for ApplicationReviewAssigneesForm.
type ApplicationReviewAssigneesForm struct {
	UnderwriterID *string `json:"underwriterID,omitempty"`
}

// ApplicationReviewBoardsInfo defines model for ApplicationReviewBoardsInfo.
type ApplicationReviewBoardsInfo struct {
	CommitTimestamp *time.Time `json:"commitTimestamp,omitempty"`
	Version         *string    `json:"version,omitempty"`
}

// ApplicationReviewClaimHistory defines model for ApplicationReviewClaimHistory.
type ApplicationReviewClaimHistory struct {
	ClaimHistoryTable *ApplicationReviewClaimHistoryTable `json:"claimHistoryTable,omitempty"`
	Meta              *ApplicationReviewWidgetMeta        `json:"meta,omitempty"`
}

// ApplicationReviewClaimHistoryDescription defines model for ApplicationReviewClaimHistoryDescription.
type ApplicationReviewClaimHistoryDescription struct {
	LossLocationCityCounty *string    `json:"LossLocationCityCounty,omitempty"`
	LossLocationState      *string    `json:"LossLocationState,omitempty"`
	LossLocationStreet     *string    `json:"LossLocationStreet,omitempty"`
	LossLocationZipCode    *int32     `json:"LossLocationZipCode,omitempty"`
	Agent                  *string    `json:"agent,omitempty"`
	CancelDate             *time.Time `json:"cancelDate,omitempty"`
	CauseOfLossDescription *string    `json:"causeOfLossDescription,omitempty"`
	CauseOfLossSummary     *string    `json:"causeOfLossSummary,omitempty"`
	ClaimId                *string    `json:"claimId,omitempty"`
	ClaimSn                *int32     `json:"claimSn,omitempty"`
	DateOfLoss             *time.Time `json:"dateOfLoss,omitempty"`
	DateReported           *time.Time `json:"dateReported,omitempty"`
	DocumentId             *string    `json:"documentId,omitempty"`
	DriverAge              *int32     `json:"driverAge,omitempty"`
	DriverDob              *time.Time `json:"driverDob,omitempty"`
	DriverGender           *string    `json:"driverGender,omitempty"`
	DriverHiringDate       *time.Time `json:"driverHiringDate,omitempty"`
	DriverId               *string    `json:"driverId,omitempty"`
	DriverName             *string    `json:"driverName,omitempty"`
	EffectiveDate          *time.Time `json:"effectiveDate,omitempty"`
	ExpiryDate             *time.Time `json:"expiryDate,omitempty"`
	Insured                *string    `json:"insured,omitempty"`
	Insurer                *string    `json:"insurer,omitempty"`
	NumberOfLosses         *int32     `json:"numberOfLosses,omitempty"`
	OtherPolicyData        *string    `json:"otherPolicyData,omitempty"`
	OtherVehicle           *string    `json:"otherVehicle,omitempty"`
	PolicyNo               *string    `json:"policyNo,omitempty"`
	PolicySn               *int32     `json:"policySn,omitempty"`
	ReportGenerationDate   *time.Time `json:"reportGenerationDate,omitempty"`
	TimeOfLoss             *time.Time `json:"timeOfLoss,omitempty"`
	TotalIncurred          *float64   `json:"totalIncurred,omitempty"`
	TotalPaid              *float64   `json:"totalPaid,omitempty"`
	TotalRecovered         *float64   `json:"totalRecovered,omitempty"`
	TotalReserve           *float64   `json:"totalReserve,omitempty"`
	VehicleType            *string    `json:"vehicleType,omitempty"`
	Vin                    *string    `json:"vin,omitempty"`
}

// ApplicationReviewClaimHistoryItemRecord defines model for ApplicationReviewClaimHistoryItemRecord.
type ApplicationReviewClaimHistoryItemRecord struct {
	CauseOfLossSummary *string    `json:"causeOfLossSummary,omitempty"`
	ClaimId            *string    `json:"claimId,omitempty"`
	ClaimSn            *int32     `json:"claimSn,omitempty"`
	DateOfLoss         *time.Time `json:"dateOfLoss,omitempty"`
	DocumentId         *string    `json:"documentId,omitempty"`
	DriverName         *string    `json:"driverName,omitempty"`
	Insurer            *string    `json:"insurer,omitempty"`
	OtherVehicle       *string    `json:"otherVehicle,omitempty"`
	PolicyNo           *string    `json:"policyNo,omitempty"`
	PolicySn           *int32     `json:"policySn,omitempty"`
	Vin                *string    `json:"vin,omitempty"`
}

// ApplicationReviewClaimHistoryTable defines model for ApplicationReviewClaimHistoryTable.
type ApplicationReviewClaimHistoryTable struct {
	Claim *[]ApplicationReviewClaimHistoryItemRecord `json:"claim,omitempty"`
}

// ApplicationReviewCloseReasonObject defines model for ApplicationReviewCloseReasonObject.
type ApplicationReviewCloseReasonObject struct {
	Reason      string `json:"reason"`
	ReasonId    string `json:"reasonId"`
	SubReason   string `json:"subReason"`
	SubReasonId string `json:"subReasonId"`
}

// ApplicationReviewCloseReasons defines model for ApplicationReviewCloseReasons.
type ApplicationReviewCloseReasons struct {
	Reasons     *[]ApplicationReviewCloseReasonObject `json:"reasons,omitempty"`
	WinCarriers *[]ApplicationWinCarriersObject       `json:"winCarriers,omitempty"`
}

// ApplicationReviewCloseReasonsForm defines model for ApplicationReviewCloseReasonsForm.
type ApplicationReviewCloseReasonsForm struct {
	Comments                  string                               `json:"comments"`
	IntentionToQuote          *bool                                `json:"intentionToQuote,omitempty"`
	ReasonsArray              []ApplicationReviewCloseReasonObject `json:"reasonsArray"`
	RecommendedActionFeedback *string                              `json:"recommendedActionFeedback,omitempty"`
	WinCarrier                *int32                               `json:"winCarrier,omitempty"`

	// WinCarrierPricing Pricing information from winning carrier
	WinCarrierPricing *WinCarrierPricing `json:"winCarrierPricing,omitempty"`
}

// ApplicationReviewConfirmBindableQuoteForm defines model for ApplicationReviewConfirmBindableQuoteForm.
type ApplicationReviewConfirmBindableQuoteForm struct {
	DepositAmount             *ApplicationReviewDepositAmount `json:"depositAmount,omitempty"`
	RecommendedActionFeedback *string                         `json:"recommendedActionFeedback,omitempty"`
}

// ApplicationReviewCoverageInfo defines model for ApplicationReviewCoverageInfo.
type ApplicationReviewCoverageInfo struct {
	Collateral *int                                         `json:"collateral,omitempty"`
	Deductible *externalRef0.CoverageVariablesOptionNumeric `json:"deductible,omitempty"`
	Limit      *externalRef0.CoverageVariablesOptionNumeric `json:"limit,omitempty"`
	State      ApplicationReviewCoverageInfoState           `json:"state"`
}

// ApplicationReviewCoverageInfoState defines model for ApplicationReviewCoverageInfo.State.
type ApplicationReviewCoverageInfoState string

// ApplicationReviewCoverages defines model for ApplicationReviewCoverages.
type ApplicationReviewCoverages struct {
	AutoLiability      *ApplicationReviewCoverageInfo    `json:"autoLiability,omitempty"`
	AutoPhysicalDamage *ApplicationReviewCoverageInfo    `json:"autoPhysicalDamage,omitempty"`
	CombinedCoverages  *[]externalRef0.CombinedCoverages `json:"combinedCoverages,omitempty"`
	GeneralLiability   *ApplicationReviewCoverageInfo    `json:"generalLiability,omitempty"`
	Meta               *ApplicationReviewWidgetMeta      `json:"meta,omitempty"`
	MotorTruckCargo    *ApplicationReviewCoverageInfo    `json:"motorTruckCargo,omitempty"`
}

// ApplicationReviewCurrentStatus defines model for ApplicationReviewCurrentStatus.
type ApplicationReviewCurrentStatus string

// ApplicationReviewCurrentStatusForm defines model for ApplicationReviewCurrentStatusForm.
type ApplicationReviewCurrentStatusForm struct {
	Data ApplicationReviewCurrentStatusFormData `json:"data"`
}

// ApplicationReviewCurrentStatusFormData defines model for ApplicationReviewCurrentStatusFormData.
type ApplicationReviewCurrentStatusFormData struct {
	Status ApplicationReviewCurrentStatus `json:"status"`
}

// ApplicationReviewCurrentStatusRequest defines model for ApplicationReviewCurrentStatusRequest.
type ApplicationReviewCurrentStatusRequest struct {
	Status ApplicationReviewCurrentStatus `json:"status"`
}

// ApplicationReviewDataCompletionTab defines model for ApplicationReviewDataCompletionTab.
type ApplicationReviewDataCompletionTab string

// ApplicationReviewDeclineReasonObject defines model for ApplicationReviewDeclineReasonObject.
type ApplicationReviewDeclineReasonObject struct {
	ExternalNote   string                                              `json:"externalNote"`
	Reason         string                                              `json:"reason"`
	ReasonCategory *ApplicationReviewDeclineReasonObjectReasonCategory `json:"reasonCategory,omitempty"`
	ReasonId       string                                              `json:"reasonId"`
	SubReason      string                                              `json:"subReason"`
	SubReasonId    string                                              `json:"subReasonId"`
}

// ApplicationReviewDeclineReasonObjectReasonCategory defines model for ApplicationReviewDeclineReasonObject.ReasonCategory.
type ApplicationReviewDeclineReasonObjectReasonCategory string

// ApplicationReviewDeclineReasons defines model for ApplicationReviewDeclineReasons.
type ApplicationReviewDeclineReasons struct {
	Reasons *[]ApplicationReviewDeclineReasonObject `json:"reasons,omitempty"`
}

// ApplicationReviewDeclineReasonsForm defines model for ApplicationReviewDeclineReasonsForm.
type ApplicationReviewDeclineReasonsForm struct {
	Comments                         string                                 `json:"comments"`
	IsApplicationAgainstUwGuidelines *bool                                  `json:"isApplicationAgainstUwGuidelines,omitempty"`
	ReasonsArray                     []ApplicationReviewDeclineReasonObject `json:"reasonsArray"`
	RecommendedActionFeedback        *string                                `json:"recommendedActionFeedback,omitempty"`
}

// ApplicationReviewDepositAmount defines model for ApplicationReviewDepositAmount.
type ApplicationReviewDepositAmount struct {
	MonthlyPaymentDeposit *float32 `json:"monthlyPaymentDeposit,omitempty"`
}

// ApplicationReviewDetail defines model for ApplicationReviewDetail.
type ApplicationReviewDetail struct {
	AggregateCredit                 float32                                           `json:"aggregateCredit"`
	AggregateCreditByCoverage       ApplicationReviewAggregateCreditByCoverage        `json:"aggregateCreditByCoverage"`
	AggregateCreditLimitsByCoverage *ApplicationReviewAggregateCreditLimitsByCoverage `json:"aggregateCreditLimitsByCoverage,omitempty"`
	AggregateMerit                  int32                                             `json:"aggregateMerit"`
	PanelWiseReviewInfo             ApplicationReviewPanelWiseInfo                    `json:"panelWiseReviewInfo"`
	Summary                         ApplicationReviewSummary                          `json:"summary"`
	TelematicsDataButtonStatus      *TelematicsDataButtonStatus                       `json:"telematicsDataButtonStatus,omitempty"`
}

// ApplicationReviewDocuments defines model for ApplicationReviewDocuments.
type ApplicationReviewDocuments struct {
	Files []externalRef0.FileMetadata `json:"files"`
}

// ApplicationReviewDriverCreateRequest defines model for ApplicationReviewDriverCreateRequest.
type ApplicationReviewDriverCreateRequest struct {
	DateHired           openapi_types.Date  `json:"dateHired"`
	DateOfBirth         *openapi_types.Date `json:"dateOfBirth,omitempty"`
	DlNumber            string              `json:"dlNumber"`
	ExperienceStartDate *openapi_types.Date `json:"experienceStartDate,omitempty"`
	FirstName           *string             `json:"firstName,omitempty"`
	LastName            *string             `json:"lastName,omitempty"`
	UsStateCode         string              `json:"usStateCode"`
}

// ApplicationReviewDriverMvrDetails defines model for ApplicationReviewDriverMvrDetails.
type ApplicationReviewDriverMvrDetails struct {
	DateExpires          *openapi_types.Date                    `json:"dateExpires,omitempty"`
	DateIssued           *openapi_types.Date                    `json:"dateIssued,omitempty"`
	DateOfBirth          *openapi_types.Date                    `json:"dateOfBirth,omitempty"`
	DlState              *string                                `json:"dlState,omitempty"`
	MovingViolationCount *float32                               `json:"movingViolationCount,omitempty"`
	Violations           *[]ApplicationReviewDriverMvrViolation `json:"violations,omitempty"`
}

// ApplicationReviewDriverMvrProblem defines model for ApplicationReviewDriverMvrProblem.
type ApplicationReviewDriverMvrProblem struct {
	Error      *string `json:"error,omitempty"`
	IsResolved *bool   `json:"isResolved,omitempty"`
	IsReviewed *bool   `json:"isReviewed,omitempty"`
}

// ApplicationReviewDriverMvrViolation defines model for ApplicationReviewDriverMvrViolation.
type ApplicationReviewDriverMvrViolation struct {
	Code              *string             `json:"code,omitempty"`
	Date              *openapi_types.Date `json:"date,omitempty"`
	Description       *string             `json:"description,omitempty"`
	Details           *string             `json:"details,omitempty"`
	IsMovingViolation *bool               `json:"isMovingViolation,omitempty"`
	Points            *int32              `json:"points,omitempty"`
	Type              *string             `json:"type,omitempty"`
}

// ApplicationReviewDriverUpdateRequest defines model for ApplicationReviewDriverUpdateRequest.
type ApplicationReviewDriverUpdateRequest struct {
	DlNumber      string `json:"dlNumber"`
	IsDeletedByUW *bool  `json:"isDeletedByUW,omitempty"`
	UsStateCode   string `json:"usStateCode"`
}

// ApplicationReviewDriversList defines model for ApplicationReviewDriversList.
type ApplicationReviewDriversList struct {
	Drivers               []ApplicationReviewDriversListItem          `json:"drivers"`
	EffectiveDate         openapi_types.Date                          `json:"effectiveDate"`
	ExperienceDataFromMVR bool                                        `json:"experienceDataFromMVR"`
	Meta                  *ApplicationReviewWidgetMeta                `json:"meta,omitempty"`
	Summary               ApplicationReviewDriversListSummary         `json:"summary"`
	WidgetStatus          *externalRef0.ApplicationReviewWidgetStatus `json:"widgetStatus,omitempty"`
}

// ApplicationReviewDriversListForm defines model for ApplicationReviewDriversListForm.
type ApplicationReviewDriversListForm struct {
	Data *ApplicationReviewDriversListFormData `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta          `json:"meta,omitempty"`
}

// ApplicationReviewDriversListFormData defines model for ApplicationReviewDriversListFormData.
type ApplicationReviewDriversListFormData struct {
	Drivers []ApplicationReviewDriversListFormItem `json:"drivers"`
}

// ApplicationReviewDriversListFormItem defines model for ApplicationReviewDriversListFormItem.
type ApplicationReviewDriversListFormItem struct {
	DateHired           openapi_types.Date  `json:"dateHired"`
	DateOfBirth         *openapi_types.Date `json:"dateOfBirth,omitempty"`
	DlNumber            string              `json:"dlNumber"`
	ExperienceStartDate *openapi_types.Date `json:"experienceStartDate,omitempty"`
	FirstName           *string             `json:"firstName,omitempty"`
	LastName            *string             `json:"lastName,omitempty"`
	UsState             string              `json:"usState"`
}

// ApplicationReviewDriversListItem defines model for ApplicationReviewDriversListItem.
type ApplicationReviewDriversListItem struct {
	AttractScore                *int32                                    `json:"attractScore,omitempty"`
	DateHired                   openapi_types.Date                        `json:"dateHired"`
	DateHiredOverride           *openapi_types.Date                       `json:"dateHiredOverride,omitempty"`
	DateOfBirth                 *openapi_types.Date                       `json:"dateOfBirth,omitempty"`
	DateOfBirthOverride         *openapi_types.Date                       `json:"dateOfBirthOverride,omitempty"`
	DlNumber                    string                                    `json:"dlNumber"`
	ExperienceStartDate         *openapi_types.Date                       `json:"experienceStartDate,omitempty"`
	ExperienceStartDateOverride *openapi_types.Date                       `json:"experienceStartDateOverride,omitempty"`
	FirstName                   *string                                   `json:"firstName,omitempty"`
	FirstNameOverride           *string                                   `json:"firstNameOverride,omitempty"`
	IsCreatedByUW               bool                                      `json:"isCreatedByUW"`
	IsDeletedByUW               bool                                      `json:"isDeletedByUW"`
	IsExperienceMismatch        *bool                                     `json:"isExperienceMismatch,omitempty"`
	IsMexicanDriver             bool                                      `json:"isMexicanDriver"`
	LastName                    *string                                   `json:"lastName,omitempty"`
	LastNameOverride            *string                                   `json:"lastNameOverride,omitempty"`
	MvrDetails                  *ApplicationReviewDriverMvrDetails        `json:"mvrDetails,omitempty"`
	MvrProblem                  *ApplicationReviewDriverMvrProblem        `json:"mvrProblem,omitempty"`
	MvrScore                    *int32                                    `json:"mvrScore,omitempty"`
	MvrStatus                   ApplicationReviewDriversListItemMvrStatus `json:"mvrStatus"`
	Name                        *string                                   `json:"name,omitempty"`
	NameOverride                *string                                   `json:"nameOverride,omitempty"`
	SafetyScore                 *int32                                    `json:"safetyScore,omitempty"`
	UsState                     string                                    `json:"usState"`
	YearsOfExperienceAgentInput *string                                   `json:"yearsOfExperienceAgentInput,omitempty"`
	YearsOfExperienceMVR        *string                                   `json:"yearsOfExperienceMVR,omitempty"`
}

// ApplicationReviewDriversListItemMvrStatus defines model for ApplicationReviewDriversListItem.MvrStatus.
type ApplicationReviewDriversListItemMvrStatus string

// ApplicationReviewDriversListSummary defines model for ApplicationReviewDriversListSummary.
type ApplicationReviewDriversListSummary struct {
	AverageTenure         float32  `json:"averageTenure"`
	NumberOfDrivers       int32    `json:"numberOfDrivers"`
	PercentageOfB1Drivers *float32 `json:"percentageOfB1Drivers,omitempty"`
	TenureTurnover        float32  `json:"tenureTurnover"`
}

// ApplicationReviewEquipmentsAdditionalInfoUnits defines model for ApplicationReviewEquipmentsAdditionalInfoUnits.
type ApplicationReviewEquipmentsAdditionalInfoUnits struct {
	PercentageOfSubhaul *float32 `json:"percentageOfSubhaul,omitempty"`
	TotalOwnerOperators int32    `json:"totalOwnerOperators"`
}

// ApplicationReviewEquipmentsOwnerOperators defines model for ApplicationReviewEquipmentsOwnerOperators.
type ApplicationReviewEquipmentsOwnerOperators struct {
	Total int32 `json:"total"`
}

// ApplicationReviewEquipmentsSafetyUsage defines model for ApplicationReviewEquipmentsSafetyUsage.
type ApplicationReviewEquipmentsSafetyUsage struct {
	Data []ApplicationReviewEquipmentsSafetyUsageItem `json:"data"`
	Meta *ApplicationReviewWidgetMeta                 `json:"meta,omitempty"`
}

// ApplicationReviewEquipmentsSafetyUsageItem defines model for ApplicationReviewEquipmentsSafetyUsageItem.
type ApplicationReviewEquipmentsSafetyUsageItem struct {
	Camera string  `json:"camera"`
	Usage  float32 `json:"usage"`
}

// ApplicationReviewEquipmentsSummaryItem defines model for ApplicationReviewEquipmentsSummaryItem.
type ApplicationReviewEquipmentsSummaryItem struct {
	AverageYear          *int32   `json:"averageYear,omitempty"`
	OwnerOperatedPercent *float32 `json:"ownerOperatedPercent,omitempty"`
	TelematicsUnitCount  *int32   `json:"telematicsUnitCount,omitempty"`
	Tiv                  float32  `json:"tiv"`
	UnitCount            int32    `json:"unitCount"`
	UnitType             string   `json:"unitType"`
}

// ApplicationReviewEquipmentsUnits defines model for ApplicationReviewEquipmentsUnits.
type ApplicationReviewEquipmentsUnits struct {
	Meta         *ApplicationReviewWidgetMeta                `json:"meta,omitempty"`
	Summary      []ApplicationReviewEquipmentsSummaryItem    `json:"summary"`
	Units        []ApplicationReviewEquipmentsUnitsItem      `json:"units"`
	WidgetStatus *externalRef0.ApplicationReviewWidgetStatus `json:"widgetStatus,omitempty"`
}

// ApplicationReviewEquipmentsUnitsForm defines model for ApplicationReviewEquipmentsUnitsForm.
type ApplicationReviewEquipmentsUnitsForm struct {
	Data *UpdateAppReviewEquipmentListFormData `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta          `json:"meta,omitempty"`
}

// ApplicationReviewEquipmentsUnitsItem defines model for ApplicationReviewEquipmentsUnitsItem.
type ApplicationReviewEquipmentsUnitsItem struct {
	BodyClass              *string                      `json:"bodyClass,omitempty"`
	IsCreatedByUW          bool                         `json:"isCreatedByUW"`
	IsDeletedByUW          bool                         `json:"isDeletedByUW"`
	IsSkipped              bool                         `json:"isSkipped"`
	IsoVehicleTypeOverride *ISOVehicleType              `json:"isoVehicleTypeOverride,omitempty"`
	IsoWeightGroupOverride *ISOVehicleWeightGroup       `json:"isoWeightGroupOverride,omitempty"`
	Make                   *string                      `json:"make,omitempty"`
	Manufacturer           *string                      `json:"manufacturer,omitempty"`
	Model                  *string                      `json:"model,omitempty"`
	ModelYear              *string                      `json:"modelYear,omitempty"`
	StatedValue            int32                        `json:"statedValue"`
	StatedValueOverride    *int32                       `json:"statedValueOverride,omitempty"`
	Trim                   *string                      `json:"trim,omitempty"`
	VehicleType            *string                      `json:"vehicleType,omitempty"`
	Vin                    string                       `json:"vin"`
	VinProblem             *ApplicationReviewVinProblem `json:"vinProblem,omitempty"`
}

// ApplicationReviewFinancialsData defines model for ApplicationReviewFinancialsData.
type ApplicationReviewFinancialsData = ApplicationReviewWidgetBase

// ApplicationReviewFlags defines model for ApplicationReviewFlags.
type ApplicationReviewFlags struct {
	LossSummaryVersion *LossSummaryVersion `json:"lossSummaryVersion,omitempty"`
}

// ApplicationReviewForDataCompletion defines model for ApplicationReviewForDataCompletion.
type ApplicationReviewForDataCompletion struct {
	Agency                        string                                   `json:"agency"`
	AppReviewID                   string                                   `json:"appReviewID"`
	Assignee                      ApplicationReviewUser                    `json:"assignee"`
	ClearanceStatus               *externalRef0.ApplicationClearanceStatus `json:"clearanceStatus,omitempty"`
	CompanyName                   string                                   `json:"companyName"`
	CompletedReviewReadinessTasks int32                                    `json:"completedReviewReadinessTasks"`
	DotNumber                     int64                                    `json:"dotNumber"`
	EffectiveDate                 openapi_types.Date                       `json:"effectiveDate"`
	Producer                      string                                   `json:"producer"`
	State                         ApplicationReviewState                   `json:"state"`
	TelematicsConnectedDays       *int32                                   `json:"telematicsConnectedDays,omitempty"`
	TelematicsStatus              *TelematicsStatus                        `json:"telematicsStatus,omitempty"`
	TotalReviewReadinessTasks     int32                                    `json:"totalReviewReadinessTasks"`
	TspName                       *string                                  `json:"tspName,omitempty"`
}

// ApplicationReviewGetActionsResponse defines model for ApplicationReviewGetActionsResponse.
type ApplicationReviewGetActionsResponse struct {
	AssignedUwActions []ApplicationReviewAction `json:"assignedUwActions"`
	VisibleActions    []ApplicationReviewAction `json:"visibleActions"`
}

// ApplicationReviewLargeLosses defines model for ApplicationReviewLargeLosses.
type ApplicationReviewLargeLosses struct {
	Meta                    *ApplicationReviewWidgetMeta                        `json:"meta,omitempty"`
	ParsedLossRunStatusInfo ApplicationReviewLossSummaryParsedLossRunStatusInfo `json:"parsedLossRunStatusInfo"`
	Value                   []ApplicationReviewLargeLossesItem                  `json:"value"`
}

// ApplicationReviewLargeLossesForm defines model for ApplicationReviewLargeLossesForm.
type ApplicationReviewLargeLossesForm struct {
	Data *struct {
		Value []ApplicationReviewLargeLossesItem `json:"value"`
	} `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewLargeLossesItem defines model for ApplicationReviewLargeLossesItem.
type ApplicationReviewLargeLossesItem struct {
	CoverageType       ApplicationReviewLargeLossesItemCoverageType `json:"coverageType"`
	Date               openapi_types.Date                           `json:"date"`
	Description        *string                                      `json:"description,omitempty"`
	LossIncurred       int32                                        `json:"lossIncurred"`
	ParsedLossIncurred *int32                                       `json:"parsedLossIncurred,omitempty"`
}

// ApplicationReviewLargeLossesItemCoverageType defines model for ApplicationReviewLargeLossesItem.CoverageType.
type ApplicationReviewLargeLossesItemCoverageType string

// ApplicationReviewListCountResponse defines model for ApplicationReviewListCountResponse.
type ApplicationReviewListCountResponse struct {
	Counts              []ApplicationReviewTabCount `json:"counts"`
	NeedsAttentionCount int32                       `json:"needsAttentionCount"`
}

// ApplicationReviewListForDataCompletionResponse defines model for ApplicationReviewListForDataCompletionResponse.
type ApplicationReviewListForDataCompletionResponse struct {
	Cursor                 *string                                      `json:"cursor,omitempty"`
	OrderedAppReviewGroups []GroupOfApplicationReviewsForDataCompletion `json:"orderedAppReviewGroups"`
}

// ApplicationReviewListResponse defines model for ApplicationReviewListResponse.
type ApplicationReviewListResponse struct {
	AppReviews []ApplicationReviewSummary `json:"appReviews"`
	Cursor     *string                    `json:"cursor,omitempty"`
}

// ApplicationReviewListResponseV2 defines model for ApplicationReviewListResponseV2.
type ApplicationReviewListResponseV2 struct {
	AppReviews []AppReviewForList `json:"appReviews"`
	Cursor     *string            `json:"cursor,omitempty"`
}

// ApplicationReviewLossAverages defines model for ApplicationReviewLossAverages.
type ApplicationReviewLossAverages struct {
	AutoLiability      []ApplicationReviewLossAveragesItem    `json:"autoLiability"`
	AutoPhysicalDamage []ApplicationReviewLossAveragesItem    `json:"autoPhysicalDamage"`
	Data               []ApplicationReviewLossAveragesPerType `json:"data"`
	Meta               *ApplicationReviewWidgetMeta           `json:"meta,omitempty"`
	MotorTruckCargo    []ApplicationReviewLossAveragesItem    `json:"motorTruckCargo"`
}

// ApplicationReviewLossAveragesForm defines model for ApplicationReviewLossAveragesForm.
type ApplicationReviewLossAveragesForm = ApplicationReviewWidgetBase

// ApplicationReviewLossAveragesItem defines model for ApplicationReviewLossAveragesItem.
type ApplicationReviewLossAveragesItem struct {
	Value     string `json:"value"`
	Violation string `json:"violation"`
}

// ApplicationReviewLossAveragesPerType defines model for ApplicationReviewLossAveragesPerType.
type ApplicationReviewLossAveragesPerType struct {
	AutoLiability      *float32 `json:"autoLiability,omitempty"`
	AutoPhysicalDamage *float32 `json:"autoPhysicalDamage,omitempty"`
	AverageType        string   `json:"averageType"`
	MotorTruckCargo    *float32 `json:"motorTruckCargo,omitempty"`
}

// ApplicationReviewLossSummary defines model for ApplicationReviewLossSummary.
type ApplicationReviewLossSummary struct {
	FmcsaLastFetchDate       *openapi_types.Date                                 `json:"fmcsaLastFetchDate,omitempty"`
	IsFurtherReviewRequired  *bool                                               `json:"isFurtherReviewRequired,omitempty"`
	IsPUCountOverrideApplied *bool                                               `json:"isPUCountOverrideApplied,omitempty"`
	Meta                     *ApplicationReviewWidgetMeta                        `json:"meta,omitempty"`
	ParsedLossRunStatusInfo  ApplicationReviewLossSummaryParsedLossRunStatusInfo `json:"parsedLossRunStatusInfo"`
	Value                    []struct {
		CoverageType string                                   `json:"coverageType"`
		Summary      []ApplicationReviewLossSummaryItemRecord `json:"summary"`
	} `json:"value"`
}

// ApplicationReviewLossSummaryForm defines model for ApplicationReviewLossSummaryForm.
type ApplicationReviewLossSummaryForm struct {
	Data *struct {
		Value []struct {
			CoverageType string                                       `json:"coverageType"`
			Summary      []ApplicationReviewLossSummaryFormItemRecord `json:"summary"`
		} `json:"value"`
	} `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewLossSummaryFormItemRecord defines model for ApplicationReviewLossSummaryFormItemRecord.
type ApplicationReviewLossSummaryFormItemRecord struct {
	LossIncurredOverride       *int32             `json:"lossIncurredOverride,omitempty"`
	NumberOfClaimsOverride     *int32             `json:"numberOfClaimsOverride,omitempty"`
	NumberOfPowerUnitsOverride *int32             `json:"numberOfPowerUnitsOverride,omitempty"`
	PolicyPeriodEndDate        openapi_types.Date `json:"policyPeriodEndDate"`
	PolicyPeriodStartDate      openapi_types.Date `json:"policyPeriodStartDate"`
}

// ApplicationReviewLossSummaryItemRecord defines model for ApplicationReviewLossSummaryItemRecord.
type ApplicationReviewLossSummaryItemRecord struct {
	LossIncurred               int32              `json:"lossIncurred"`
	LossIncurredOverride       *int32             `json:"lossIncurredOverride,omitempty"`
	NumberOfClaims             int32              `json:"numberOfClaims"`
	NumberOfClaimsOverride     *int32             `json:"numberOfClaimsOverride,omitempty"`
	NumberOfPowerUnits         int32              `json:"numberOfPowerUnits"`
	NumberOfPowerUnitsOverride *int32             `json:"numberOfPowerUnitsOverride,omitempty"`
	ParsedLossIncurred         *int32             `json:"parsedLossIncurred,omitempty"`
	ParsedLossState            *ParsedLossState   `json:"parsedLossState,omitempty"`
	ParsedLossStateLNISource   *[]string          `json:"parsedLossStateLNISource,omitempty"`
	ParsedLossStateOverride    *ParsedLossState   `json:"parsedLossStateOverride,omitempty"`
	PolicyPeriodEndDate        openapi_types.Date `json:"policyPeriodEndDate"`
	PolicyPeriodStartDate      openapi_types.Date `json:"policyPeriodStartDate"`
}

// ApplicationReviewLossSummaryItemRecordV2 defines model for ApplicationReviewLossSummaryItemRecordV2.
type ApplicationReviewLossSummaryItemRecordV2 struct {
	GrossLoss          LossValueWithOverride                           `json:"grossLoss"`
	LossRatio          float32                                         `json:"lossRatio"`
	NumberOfClaims     LossValueWithOverride                           `json:"numberOfClaims"`
	NumberOfPowerUnits LossValueWithOverride                           `json:"numberOfPowerUnits"`
	PeriodEndDate      openapi_types.Date                              `json:"periodEndDate"`
	PeriodStartDate    openapi_types.Date                              `json:"periodStartDate"`
	Tags               *[]ApplicationReviewLossSummaryItemRecordV2Tags `json:"tags,omitempty"`
}

// ApplicationReviewLossSummaryItemRecordV2Tags defines model for ApplicationReviewLossSummaryItemRecordV2.Tags.
type ApplicationReviewLossSummaryItemRecordV2Tags string

// ApplicationReviewLossSummaryParsedLossRunStatusInfo defines model for ApplicationReviewLossSummaryParsedLossRunStatusInfo.
type ApplicationReviewLossSummaryParsedLossRunStatusInfo struct {
	AreAllDocumentsProcessed bool                   `json:"areAllDocumentsProcessed"`
	DocumentList             *[]LossRunFileMetadata `json:"documentList,omitempty"`
	ProcessingEndDate        *openapi_types.Date    `json:"processingEndDate,omitempty"`
	ProcessingStartDate      *openapi_types.Date    `json:"processingStartDate,omitempty"`
}

// ApplicationReviewLossSummaryV2 defines model for ApplicationReviewLossSummaryV2.
type ApplicationReviewLossSummaryV2 struct {
	LatestCoverageSummaries []LatestCoverageSummary      `json:"latestCoverageSummaries"`
	Meta                    *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
	ParsingStatus           ParsedLossStateV2            `json:"parsingStatus"`
}

// ApplicationReviewLossSummaryV2Form defines model for ApplicationReviewLossSummaryV2Form.
type ApplicationReviewLossSummaryV2Form struct {
	Data *ApplicationReviewLossSummaryV2FormData `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta            `json:"meta,omitempty"`
}

// ApplicationReviewLossSummaryV2FormData defines model for ApplicationReviewLossSummaryV2FormData.
type ApplicationReviewLossSummaryV2FormData struct {
	CoverageType externalRef0.CoverageType                `json:"coverageType"`
	Summary      []ApplicationReviewLossSummaryV2FormItem `json:"summary"`
}

// ApplicationReviewLossSummaryV2FormItem defines model for ApplicationReviewLossSummaryV2FormItem.
type ApplicationReviewLossSummaryV2FormItem struct {
	GrossLossOverride          *float32           `json:"grossLossOverride,omitempty"`
	NumberOfClaimsOverride     *float32           `json:"numberOfClaimsOverride,omitempty"`
	NumberOfPowerUnitsOverride *float32           `json:"numberOfPowerUnitsOverride,omitempty"`
	PeriodEndDate              openapi_types.Date `json:"periodEndDate"`
	PeriodStartDate            openapi_types.Date `json:"periodStartDate"`
}

// ApplicationReviewLossSummaryV2Request defines model for ApplicationReviewLossSummaryV2Request.
type ApplicationReviewLossSummaryV2Request struct {
	CoverageParams []LossSummaryCoverageParams `json:"coverageParams"`
}

// ApplicationReviewMstReferralReview defines model for ApplicationReviewMstReferralReview.
type ApplicationReviewMstReferralReview struct {
	MarkAsSentDate       *time.Time                               `json:"markAsSentDate,omitempty"`
	ReferralPacketUrl    *string                                  `json:"referralPacketUrl,omitempty"`
	ReferralRules        []ApplicationReviewMstReferralReviewRule `json:"referralRules"`
	ReferralStatus       ApplicationReviewMstReferralReviewStatus `json:"referralStatus"`
	ShouldCollapseWidget bool                                     `json:"shouldCollapseWidget"`
	Summary              *string                                  `json:"summary,omitempty"`
	SummaryPoints        *string                                  `json:"summaryPoints,omitempty"`
}

// ApplicationReviewMstReferralReviewForm defines model for ApplicationReviewMstReferralReviewForm.
type ApplicationReviewMstReferralReviewForm struct {
	Data ApplicationReviewMstReferralReviewFormData `json:"data"`
}

// ApplicationReviewMstReferralReviewFormData defines model for ApplicationReviewMstReferralReviewFormData.
type ApplicationReviewMstReferralReviewFormData struct {
	ReferralRules  *[]ApplicationReviewMstReferralReviewRule `json:"referralRules,omitempty"`
	ReferralStatus ApplicationReviewMstReferralReviewStatus  `json:"referralStatus"`
}

// ApplicationReviewMstReferralReviewRule defines model for ApplicationReviewMstReferralReviewRule.
type ApplicationReviewMstReferralReviewRule struct {
	Category         string                                          `json:"category"`
	Decision         ApplicationReviewMstReferralReviewRuleDecision  `json:"decision"`
	DecisionOverride *ApplicationReviewMstReferralReviewRuleDecision `json:"decisionOverride,omitempty"`
	Description      string                                          `json:"description"`
	Id               string                                          `json:"id"`
	IsPrimary        bool                                            `json:"isPrimary"`
	RuleType         ApplicationReviewMstReferralReviewRuleType      `json:"ruleType"`
	ShouldPrefill    bool                                            `json:"shouldPrefill"`
}

// ApplicationReviewMstReferralReviewRuleDecision defines model for ApplicationReviewMstReferralReviewRuleDecision.
type ApplicationReviewMstReferralReviewRuleDecision string

// ApplicationReviewMstReferralReviewRuleType defines model for ApplicationReviewMstReferralReviewRuleType.
type ApplicationReviewMstReferralReviewRuleType string

// ApplicationReviewMstReferralReviewStatus defines model for ApplicationReviewMstReferralReviewStatus.
type ApplicationReviewMstReferralReviewStatus string

// ApplicationReviewMstReferralRule defines model for ApplicationReviewMstReferralRule.
type ApplicationReviewMstReferralRule struct {
	Description             string                                      `json:"description"`
	Id                      int                                         `json:"id"`
	IsFurtherReviewRequired *bool                                       `json:"isFurtherReviewRequired,omitempty"`
	IsReferralRequired      *bool                                       `json:"isReferralRequired,omitempty"`
	IsReviewed              *bool                                       `json:"isReviewed,omitempty"`
	RuleType                MstReferralRuleType                         `json:"ruleType"`
	WidgetEnum              *ApplicationReviewMstReferralRuleWidgetEnum `json:"widgetEnum,omitempty"`
}

// ApplicationReviewMstReferralRuleWidgetEnum defines model for ApplicationReviewMstReferralRule.WidgetEnum.
type ApplicationReviewMstReferralRuleWidgetEnum string

// ApplicationReviewNegotiatedRates defines model for ApplicationReviewNegotiatedRates.
type ApplicationReviewNegotiatedRates struct {
	// BaseLimitPremium Tells us if what is the base limit premium
	BaseLimitPremium *int64                                  `json:"baseLimitPremium,omitempty"`
	Coverages        []externalRef0.CoverageType             `json:"coverages"`
	Details          ApplicationReviewNegotiatedRatesDetails `json:"details"`

	// IsNegotiatedRatesApplicable Tells us if negotiated rates can be applied or not
	IsNegotiatedRatesApplicable bool `json:"isNegotiatedRatesApplicable"`

	// IsNegotiatedRatesApplied Tells us if UW applied negotiated rates or not
	IsNegotiatedRatesApplied bool                                          `json:"isNegotiatedRatesApplied"`
	Rules                    []ApplicationReviewNegotiatedRatesRuleDetails `json:"rules"`

	// ThresholdPremium Tells us if what is the threshold premium. If base premium > threshold premium, NR is applicable
	ThresholdPremium *int64 `json:"thresholdPremium,omitempty"`
}

// ApplicationReviewNegotiatedRatesDetails defines model for ApplicationReviewNegotiatedRatesDetails.
type ApplicationReviewNegotiatedRatesDetails struct {
	AlNegotiatedRate   int64                                `json:"alNegotiatedRate"`
	AlTraditionalRate  *int64                               `json:"alTraditionalRate,omitempty"`
	ApdNegotiatedRate  *int64                               `json:"apdNegotiatedRate,omitempty"`
	ApdTraditionalRate *int64                               `json:"apdTraditionalRate,omitempty"`
	CaseDescription    string                               `json:"caseDescription"`
	Exemption          externalRef0.NegotiatedRateRulesEnum `json:"exemption"`
}

// ApplicationReviewNegotiatedRatesRuleDetails defines model for ApplicationReviewNegotiatedRatesRuleDetails.
type ApplicationReviewNegotiatedRatesRuleDetails struct {
	IsApplicable bool                                 `json:"isApplicable"`
	RuleLabel    string                               `json:"ruleLabel"`
	RuleType     externalRef0.NegotiatedRateRulesEnum `json:"ruleType"`
}

// ApplicationReviewNotes defines model for ApplicationReviewNotes.
type ApplicationReviewNotes struct {
	Notes string `json:"notes"`
}

// ApplicationReviewOperationsCommodities defines model for ApplicationReviewOperationsCommodities.
type ApplicationReviewOperationsCommodities struct {
	Data ApplicationReviewOperationsCommoditiesData `json:"data"`
	Meta *ApplicationReviewWidgetMeta               `json:"meta,omitempty"`
}

// ApplicationReviewOperationsCommoditiesData defines model for ApplicationReviewOperationsCommoditiesData.
type ApplicationReviewOperationsCommoditiesData struct {
	Additional struct {
		Commodities       string  `json:"commodities"`
		PercentageOfHauls float32 `json:"percentageOfHauls"`
	} `json:"additional"`
	Primary []ApplicationReviewOperationsCommoditiesDataPrimaryItem `json:"primary"`
}

// ApplicationReviewOperationsCommoditiesDataPrimaryItem defines model for ApplicationReviewOperationsCommoditiesDataPrimaryItem.
type ApplicationReviewOperationsCommoditiesDataPrimaryItem struct {
	AvgDollarValueHauled int64                                                               `json:"avgDollarValueHauled"`
	Category             string                                                              `json:"category"`
	CategoryLabel        string                                                              `json:"categoryLabel"`
	Commodity            *string                                                             `json:"commodity,omitempty"`
	CommodityClass       ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass `json:"commodityClass"`
	CommodityEnum        *string                                                             `json:"commodityEnum,omitempty"`
	CommodityEnumLabel   *string                                                             `json:"commodityEnumLabel,omitempty"`
	CommodityLabel       string                                                              `json:"commodityLabel"`
	MaxDollarValueHauled int64                                                               `json:"maxDollarValueHauled"`
	PercentageOfHauls    float32                                                             `json:"percentageOfHauls"`
}

// ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass defines model for ApplicationReviewOperationsCommoditiesDataPrimaryItem.CommodityClass.
type ApplicationReviewOperationsCommoditiesDataPrimaryItemCommodityClass string

// ApplicationReviewOperationsCommoditiesForm defines model for ApplicationReviewOperationsCommoditiesForm.
type ApplicationReviewOperationsCommoditiesForm struct {
	Data *ApplicationReviewOperationsCommoditiesData `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta                `json:"meta,omitempty"`
}

// ApplicationReviewOperationsCommoditiesSupportedOperations defines model for ApplicationReviewOperationsCommoditiesSupportedOperations.
type ApplicationReviewOperationsCommoditiesSupportedOperations = externalRef0.SupportedOperationsRecordV2

// ApplicationReviewOperationsCustomers defines model for ApplicationReviewOperationsCustomers.
type ApplicationReviewOperationsCustomers struct {
	Data []ApplicationReviewOperationsCustomersDataItem `json:"data"`
	Meta *ApplicationReviewWidgetMeta                   `json:"meta,omitempty"`
}

// ApplicationReviewOperationsCustomersDataItem defines model for ApplicationReviewOperationsCustomersDataItem.
type ApplicationReviewOperationsCustomersDataItem struct {
	Frequency int32  `json:"frequency"`
	Name      string `json:"name"`
}

// ApplicationReviewOperationsFleetHistory defines model for ApplicationReviewOperationsFleetHistory.
type ApplicationReviewOperationsFleetHistory struct {
	CarrierLoyaltyInsight *ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummary `json:"carrierLoyaltyInsight,omitempty"`
	InsuranceCurrent      []ApplicationReviewOperationsFleetHistoryInsuranceCurrentItem `json:"insuranceCurrent"`
	InsuranceHistory      []ApplicationReviewOperationsFleetHistoryInsuranceHistoryItem `json:"insuranceHistory"`
	InsuranceLimits       []ApplicationReviewOperationsFleetHistoryInsuranceLimitItem   `json:"insuranceLimits"`
	Meta                  *ApplicationReviewWidgetMeta                                  `json:"meta,omitempty"`
	PowerUnitsTrend       []ApplicationReviewOperationsFleetHistoryPowerUnitsTrendItem  `json:"powerUnitsTrend"`
	WidgetStatus          *externalRef0.ApplicationReviewWidgetStatus                   `json:"widgetStatus,omitempty"`
}

// ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummary defines model for ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummary.
type ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummary struct {
	CarrierLoyaltySummary *[]ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem `json:"carrierLoyaltySummary,omitempty"`
	OverallScore          *string                                                             `json:"overallScore,omitempty"`
}

// ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem defines model for ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem.
type ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem struct {
	CarrierSwitches int32                                                                        `json:"carrierSwitches"`
	DurationType    ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType `json:"durationType"`
	NumberOfYears   int32                                                                        `json:"numberOfYears"`
	TotalGapDays    int32                                                                        `json:"totalGapDays"`
}

// ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType defines model for ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItem.DurationType.
type ApplicationReviewOperationsFleetHistoryCarrierLoyaltySummaryItemDurationType string

// ApplicationReviewOperationsFleetHistoryForm defines model for ApplicationReviewOperationsFleetHistoryForm.
type ApplicationReviewOperationsFleetHistoryForm = ApplicationReviewWidgetBase

// ApplicationReviewOperationsFleetHistoryInsuranceCurrentItem defines model for ApplicationReviewOperationsFleetHistoryInsuranceCurrentItem.
type ApplicationReviewOperationsFleetHistoryInsuranceCurrentItem struct {
	CancellationDate *openapi_types.Date `json:"cancellationDate,omitempty"`
	Carrier          string              `json:"carrier"`
	EffectiveDate    openapi_types.Date  `json:"effectiveDate"`
	InsuranceType    *string             `json:"insuranceType,omitempty"`
}

// ApplicationReviewOperationsFleetHistoryInsuranceHistoryItem defines model for ApplicationReviewOperationsFleetHistoryInsuranceHistoryItem.
type ApplicationReviewOperationsFleetHistoryInsuranceHistoryItem struct {
	Carrier       string             `json:"carrier"`
	EndDate       openapi_types.Date `json:"endDate"`
	InsuranceType *string            `json:"insuranceType,omitempty"`
	StartDate     openapi_types.Date `json:"startDate"`
}

// ApplicationReviewOperationsFleetHistoryInsuranceLimitItem defines model for ApplicationReviewOperationsFleetHistoryInsuranceLimitItem.
type ApplicationReviewOperationsFleetHistoryInsuranceLimitItem struct {
	Carrier                string              `json:"carrier"`
	CoverageType           string              `json:"coverageType"`
	EffectiveDate          *openapi_types.Date `json:"effectiveDate,omitempty"`
	InsuranceLimit         *float32            `json:"insuranceLimit,omitempty"`
	ShouldFlagCoverageType bool                `json:"shouldFlagCoverageType"`
}

// ApplicationReviewOperationsFleetHistoryPowerUnitsTrendItem defines model for ApplicationReviewOperationsFleetHistoryPowerUnitsTrendItem.
type ApplicationReviewOperationsFleetHistoryPowerUnitsTrendItem struct {
	Date      openapi_types.Date `json:"date"`
	UnitCount int32              `json:"unitCount"`
}

// ApplicationReviewOperationsGaragingLocation defines model for ApplicationReviewOperationsGaragingLocation.
type ApplicationReviewOperationsGaragingLocation struct {
	Locations struct {
		Override *[]ApplicationReviewOperationsGaragingLocationItem `json:"override,omitempty"`
		Value    []ApplicationReviewOperationsGaragingLocationItem  `json:"value"`
	} `json:"locations"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewOperationsGaragingLocationForm defines model for ApplicationReviewOperationsGaragingLocationForm.
type ApplicationReviewOperationsGaragingLocationForm struct {
	Data *struct {
		Locations []ApplicationReviewOperationsGaragingLocationFormItem `json:"locations"`
	} `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewOperationsGaragingLocationFormItem defines model for ApplicationReviewOperationsGaragingLocationFormItem.
type ApplicationReviewOperationsGaragingLocationFormItem struct {
	Address     *string `json:"address,omitempty"`
	Coordinates *struct {
		Latitude  *float64 `json:"latitude,omitempty"`
		Longitude *float64 `json:"longitude,omitempty"`
	} `json:"coordinates,omitempty"`
	Type *ApplicationReviewOperationsGaragingLocationFormItemType `json:"type,omitempty"`
}

// ApplicationReviewOperationsGaragingLocationFormItemType defines model for ApplicationReviewOperationsGaragingLocationFormItem.Type.
type ApplicationReviewOperationsGaragingLocationFormItemType string

// ApplicationReviewOperationsGaragingLocationItem defines model for ApplicationReviewOperationsGaragingLocationItem.
type ApplicationReviewOperationsGaragingLocationItem struct {
	Address        *string                                              `json:"address,omitempty"`
	GeojsonFeature *map[string]interface{}                              `json:"geojsonFeature,omitempty"`
	Type           *ApplicationReviewOperationsGaragingLocationItemType `json:"type,omitempty"`
}

// ApplicationReviewOperationsGaragingLocationItemType defines model for ApplicationReviewOperationsGaragingLocationItem.Type.
type ApplicationReviewOperationsGaragingLocationItemType string

// ApplicationReviewOperationsHazardZoneState defines model for ApplicationReviewOperationsHazardZoneState.
type ApplicationReviewOperationsHazardZoneState struct {
	Miles      *float32 `json:"miles,omitempty"`
	Percentage *float32 `json:"percentage,omitempty"`

	// State Two character short code for the US state the driver is licensed in.
	State *externalRef0.USState `json:"state,omitempty"`
}

// ApplicationReviewOperationsHazardZones defines model for ApplicationReviewOperationsHazardZones.
type ApplicationReviewOperationsHazardZones struct {
	HazardZoneDistance struct {
		Miles      *float32 `json:"miles,omitempty"`
		Percentage *float32 `json:"percentage,omitempty"`
	} `json:"hazardZoneDistance"`
	HazardZoneDuration struct {
		Hours      *float32 `json:"hours,omitempty"`
		Percentage *float32 `json:"percentage,omitempty"`
	} `json:"hazardZoneDuration"`
	HazardZoneStates []ApplicationReviewOperationsHazardZoneState `json:"hazardZoneStates"`
	Meta             *ApplicationReviewWidgetMeta                 `json:"meta,omitempty"`
}

// ApplicationReviewOperationsHazardZonesForm defines model for ApplicationReviewOperationsHazardZonesForm.
type ApplicationReviewOperationsHazardZonesForm = ApplicationReviewWidgetBase

// ApplicationReviewOperationsOperatingClasses defines model for ApplicationReviewOperationsOperatingClasses.
type ApplicationReviewOperationsOperatingClasses struct {
	Comment       *string                                                  `json:"comment,omitempty"`
	EquipmentInfo ApplicationReviewOperationsOperatingClassesEquipmentInfo `json:"equipmentInfo"`
	MarginalOps   *[]string                                                `json:"marginalOps,omitempty"`
	Meta          *ApplicationReviewWidgetMeta                             `json:"meta,omitempty"`
}

// ApplicationReviewOperationsOperatingClassesEquipmentInfo defines model for ApplicationReviewOperationsOperatingClassesEquipmentInfo.
type ApplicationReviewOperationsOperatingClassesEquipmentInfo struct {
	Distribution          []ApplicationReviewOperationsOperatingClassesEquipmentInfoDistributionItem `json:"distribution"`
	PrimaryCommodity      string                                                                     `json:"primaryCommodity"`
	PrimaryOperatingClass string                                                                     `json:"primaryOperatingClass"`
}

// ApplicationReviewOperationsOperatingClassesEquipmentInfoDistributionItem defines model for ApplicationReviewOperationsOperatingClassesEquipmentInfoDistributionItem.
type ApplicationReviewOperationsOperatingClassesEquipmentInfoDistributionItem struct {
	OperatingClass string `json:"operatingClass"`
	Value          int32  `json:"value"`
}

// ApplicationReviewOperationsOperatingClassesForm defines model for ApplicationReviewOperationsOperatingClassesForm.
type ApplicationReviewOperationsOperatingClassesForm = ApplicationReviewWidgetBase

// ApplicationReviewOperationsProjectedInformation defines model for ApplicationReviewOperationsProjectedInformation.
type ApplicationReviewOperationsProjectedInformation struct {
	IsMinimumMileageGuaranteed *bool                                           `json:"isMinimumMileageGuaranteed,omitempty"`
	Meta                       *ApplicationReviewWidgetMeta                    `json:"meta,omitempty"`
	Mileage                    ApplicationReviewProjectedInformationMileage    `json:"mileage"`
	PowerUnits                 ApplicationReviewProjectedInformationPowerUnits `json:"powerUnits"`
	Units                      int                                             `json:"units"`
	Version                    externalRef0.ApplicationReviewWidgetVersion     `json:"version"`
	VinVisibility              *ApplicationReviewVinVisibility                 `json:"vinVisibility,omitempty"`
}

// ApplicationReviewOperationsProjectedInformationForm defines model for ApplicationReviewOperationsProjectedInformationForm.
type ApplicationReviewOperationsProjectedInformationForm struct {
	Data *struct {
		IsMinimumMileageGuaranteed *bool                  `json:"isMinimumMileageGuaranteed,omitempty"`
		Mileage                    *int                   `json:"mileage,omitempty"`
		Reason                     *MileageEstimateReason `json:"reason,omitempty"`
		Units                      *int                   `json:"units,omitempty"`
	} `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewOperationsRadiusOfOperation defines model for ApplicationReviewOperationsRadiusOfOperation.
type ApplicationReviewOperationsRadiusOfOperation struct {
	BucketedData struct {
		MileageRadiusBucketFiftyToTwoHundred       ApplicationReviewOperationsRadiusOfOperationBucketedDataItem `json:"MileageRadiusBucketFiftyToTwoHundred"`
		MileageRadiusBucketFiveHundredPlus         ApplicationReviewOperationsRadiusOfOperationBucketedDataItem `json:"MileageRadiusBucketFiveHundredPlus"`
		MileageRadiusBucketTwoHundredToFiveHundred ApplicationReviewOperationsRadiusOfOperationBucketedDataItem `json:"MileageRadiusBucketTwoHundredToFiveHundred"`
		MileageRadiusBucketUnknown                 ApplicationReviewOperationsRadiusOfOperationBucketedDataItem `json:"MileageRadiusBucketUnknown"`
		MileageRadiusBucketZeroToFifty             ApplicationReviewOperationsRadiusOfOperationBucketedDataItem `json:"MileageRadiusBucketZeroToFifty"`
	} `json:"bucketedData"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewOperationsRadiusOfOperationBucketedDataItem defines model for ApplicationReviewOperationsRadiusOfOperationBucketedDataItem.
type ApplicationReviewOperationsRadiusOfOperationBucketedDataItem struct {
	Override   *int32 `json:"override,omitempty"`
	Telematics *int32 `json:"telematics,omitempty"`
	Value      int32  `json:"value"`
}

// ApplicationReviewOperationsRadiusOfOperationForm defines model for ApplicationReviewOperationsRadiusOfOperationForm.
type ApplicationReviewOperationsRadiusOfOperationForm struct {
	Data *struct {
		BucketedData struct {
			MileageRadiusBucketFiftyToTwoHundred       ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem `json:"MileageRadiusBucketFiftyToTwoHundred"`
			MileageRadiusBucketFiveHundredPlus         ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem `json:"MileageRadiusBucketFiveHundredPlus"`
			MileageRadiusBucketTwoHundredToFiveHundred ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem `json:"MileageRadiusBucketTwoHundredToFiveHundred"`
			MileageRadiusBucketZeroToFifty             ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem `json:"MileageRadiusBucketZeroToFifty"`
		} `json:"bucketedData"`
	} `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem defines model for ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem.
type ApplicationReviewOperationsRadiusOfOperationFormBucketedDataItem struct {
	Override int32 `json:"override"`
}

// ApplicationReviewOperationsTerminalLocationItem defines model for ApplicationReviewOperationsTerminalLocationItem.
type ApplicationReviewOperationsTerminalLocationItem struct {
	Address               string                                              `json:"address"`
	AddressLineOne        string                                              `json:"addressLineOne"`
	AddressLineTwo        *string                                             `json:"addressLineTwo,omitempty"`
	CargoTerminalSchedule *externalRef0.CargoTerminalSchedule                 `json:"cargoTerminalSchedule,omitempty"`
	IsCreatedByUW         bool                                                `json:"isCreatedByUW"`
	IsDeletedByUW         bool                                                `json:"isDeletedByUW"`
	IsGated               bool                                                `json:"isGated"`
	IsGuarded             bool                                                `json:"isGuarded"`
	IsSelectedForRating   bool                                                `json:"isSelectedForRating"`
	IsValidForRating      bool                                                `json:"isValidForRating"`
	Type                  ApplicationReviewOperationsTerminalLocationItemType `json:"type"`
	UsState               string                                              `json:"usState"`
	ZipCode               string                                              `json:"zipCode"`
	ZipCodeDetail         *ZipCodeDetails                                     `json:"zipCodeDetail,omitempty"`
}

// ApplicationReviewOperationsTerminalLocationItemType defines model for ApplicationReviewOperationsTerminalLocationItem.Type.
type ApplicationReviewOperationsTerminalLocationItemType string

// ApplicationReviewOperationsTerminalLocations defines model for ApplicationReviewOperationsTerminalLocations.
type ApplicationReviewOperationsTerminalLocations struct {
	Locations []ApplicationReviewOperationsTerminalLocationItem `json:"locations"`
}

// ApplicationReviewOperationsVehicleZones defines model for ApplicationReviewOperationsVehicleZones.
type ApplicationReviewOperationsVehicleZones struct {
	DefaultZones struct {
		EndZone   VehicleZone `json:"endZone"`
		StartZone VehicleZone `json:"startZone"`
	} `json:"defaultZones"`
	Options struct {
		EndZones   []VehicleZone `json:"endZones"`
		StartZones []VehicleZone `json:"startZones"`
	} `json:"options"`
	VehicleZones []VehicleZoneRecord `json:"vehicleZones"`
}

// ApplicationReviewOperationsVehicleZonesForm defines model for ApplicationReviewOperationsVehicleZonesForm.
type ApplicationReviewOperationsVehicleZonesForm struct {
	Data struct {
		VehicleZones []VehicleZoneRecord `json:"vehicleZones"`
	} `json:"data"`
}

// ApplicationReviewOperationsYearsInBusiness defines model for ApplicationReviewOperationsYearsInBusiness.
type ApplicationReviewOperationsYearsInBusiness struct {
	Meta         *ApplicationReviewWidgetMeta                `json:"meta,omitempty"`
	Months       int                                         `json:"months"`
	Value        int                                         `json:"value"`
	WidgetStatus *externalRef0.ApplicationReviewWidgetStatus `json:"widgetStatus,omitempty"`
	Years        int                                         `json:"years"`
}

// ApplicationReviewOperationsYearsInBusinessForm defines model for ApplicationReviewOperationsYearsInBusinessForm.
type ApplicationReviewOperationsYearsInBusinessForm = ApplicationReviewWidgetBase

// ApplicationReviewOverviewRecommendedAction defines model for ApplicationReviewOverviewRecommendedAction.
type ApplicationReviewOverviewRecommendedAction struct {
	Action        RecommendedAction        `json:"action"`
	Reason        *RecommendedActionReason `json:"reason,omitempty"`
	RefreshDate   openapi_types.Date       `json:"refreshDate"`
	RefreshReason *RefreshReason           `json:"refreshReason,omitempty"`
}

// ApplicationReviewOverviewRecommendedActionDetails defines model for ApplicationReviewOverviewRecommendedActionDetails.
type ApplicationReviewOverviewRecommendedActionDetails struct {
	AppetiteScore          *AppetiteScoreEnum                            `json:"appetiteScore,omitempty"`
	IsRenewalApp           *bool                                         `json:"isRenewalApp,omitempty"`
	IsTspException         *bool                                         `json:"isTspException,omitempty"`
	RecommendedAction      *ApplicationReviewOverviewRecommendedAction   `json:"recommendedAction,omitempty"`
	TelematicsRiskScore    *ApplicationReviewOverviewTelematicsRiskScore `json:"telematicsRiskScore,omitempty"`
	TraditionalRiskFactors *ApplicationReviewOverviewRiskFactors         `json:"traditionalRiskFactors,omitempty"`
	UpdatedAt              *openapi_types.Date                           `json:"updatedAt,omitempty"`
	VinVisibility          *ApplicationReviewVinVisibility               `json:"vinVisibility,omitempty"`
}

// ApplicationReviewOverviewRiskFactors defines model for ApplicationReviewOverviewRiskFactors.
type ApplicationReviewOverviewRiskFactors = []TraditionalRiskFactors

// ApplicationReviewOverviewTelematicsRiskScore defines model for ApplicationReviewOverviewTelematicsRiskScore.
type ApplicationReviewOverviewTelematicsRiskScore struct {
	ActualScore     *int32              `json:"actualScore,omitempty"`
	Category        *string             `json:"category,omitempty"`
	IsShortHaul     *bool               `json:"isShortHaul,omitempty"`
	MaxScore        *int32              `json:"maxScore,omitempty"`
	ScoreType       string              `json:"scoreType"`
	VinCount        *int32              `json:"vinCount,omitempty"`
	WindowEndDate   *openapi_types.Date `json:"windowEndDate,omitempty"`
	WindowStartDate *openapi_types.Date `json:"windowStartDate,omitempty"`
}

// ApplicationReviewPackageType defines model for ApplicationReviewPackageType.
type ApplicationReviewPackageType struct {
	PackageType ApplicationReviewPackageTypeValue `json:"packageType"`
}

// ApplicationReviewPackageTypeValue defines model for ApplicationReviewPackageTypeValue.
type ApplicationReviewPackageTypeValue string

// ApplicationReviewPanelInfo defines model for ApplicationReviewPanelInfo.
type ApplicationReviewPanelInfo struct {
	Comments         string                                     `json:"comments"`
	Credit           float32                                    `json:"credit"`
	CreditByCoverage ApplicationReviewAggregateCreditByCoverage `json:"creditByCoverage"`
	IsReviewed       bool                                       `json:"isReviewed"`
	Merit            int32                                      `json:"merit"`
}

// ApplicationReviewPanelInfoPatch defines model for ApplicationReviewPanelInfoPatch.
type ApplicationReviewPanelInfoPatch struct {
	Comments   *string  `json:"comments,omitempty"`
	Credit     *float32 `json:"credit,omitempty"`
	IsReviewed *bool    `json:"isReviewed,omitempty"`
}

// ApplicationReviewPanelNotes defines model for ApplicationReviewPanelNotes.
type ApplicationReviewPanelNotes struct {
	Notes ApplicationReviewPanelNotesData `json:"notes"`
}

// ApplicationReviewPanelNotesData defines model for ApplicationReviewPanelNotesData.
type ApplicationReviewPanelNotesData struct {
	// Drivers Driver panel notes for an application review.
	Drivers string `json:"drivers"`
}

// ApplicationReviewPanelWiseInfo defines model for ApplicationReviewPanelWiseInfo.
type ApplicationReviewPanelWiseInfo struct {
	Drivers    ApplicationReviewPanelInfo  `json:"drivers"`
	Equipments ApplicationReviewPanelInfo  `json:"equipments"`
	Financials ApplicationReviewPanelInfo  `json:"financials"`
	Losses     ApplicationReviewPanelInfo  `json:"losses"`
	Operations ApplicationReviewPanelInfo  `json:"operations"`
	Overview   *ApplicationReviewPanelInfo `json:"overview,omitempty"`
	Packages   ApplicationReviewPanelInfo  `json:"packages"`
	Safety     ApplicationReviewPanelInfo  `json:"safety"`
}

// ApplicationReviewProjectedInformationMileage defines model for ApplicationReviewProjectedInformationMileage.
type ApplicationReviewProjectedInformationMileage struct {
	MileageEstimate *MileageEstimate       `json:"mileageEstimate,omitempty"`
	Override        *int                   `json:"override,omitempty"`
	Reasons         *MileageEstimateReason `json:"reasons,omitempty"`
	Telematics      int                    `json:"telematics"`
	Value           int                    `json:"value"`
}

// ApplicationReviewProjectedInformationPowerUnits defines model for ApplicationReviewProjectedInformationPowerUnits.
type ApplicationReviewProjectedInformationPowerUnits struct {
	Override *int `json:"override,omitempty"`
	Value    int  `json:"value"`
}

// ApplicationReviewQuote defines model for ApplicationReviewQuote.
type ApplicationReviewQuote struct {
	AutoLiabilityPremium        *ApplicationReviewQuoteCoverageType                  `json:"autoLiabilityPremium,omitempty"`
	AutoPhysicalDamagePremium   *ApplicationReviewQuoteCoverageType                  `json:"autoPhysicalDamagePremium,omitempty"`
	CombinedDeductibleCoverages *[]ApplicationReviewQuoteCombinedDeductibleCoverages `json:"combinedDeductibleCoverages,omitempty"`
	ErrorMessage                *string                                              `json:"errorMessage,omitempty"`
	FlatCharges                 *int32                                               `json:"flatCharges,omitempty"`
	GeneralLiabilityPremium     *ApplicationReviewQuoteCoverageType                  `json:"generalLiabilityPremium,omitempty"`
	MotorTruckCargoPremium      *ApplicationReviewQuoteCoverageType                  `json:"motorTruckCargoPremium,omitempty"`
	PackageType                 ApplicationReviewPackageTypeValue                    `json:"packageType"`
	PreDiscountTotalPremium     *int32                                               `json:"preDiscountTotalPremium,omitempty"`
	PremiumRange                *PremiumRange                                        `json:"premiumRange,omitempty"`
	SafetyDiscount              *int32                                               `json:"safetyDiscount,omitempty"`
	SafetyDiscountPercentage    *int32                                               `json:"safetyDiscountPercentage,omitempty"`
	Status                      ApplicationReviewQuoteStatus                         `json:"status"`
	SubtotalPremium             *int32                                               `json:"subtotalPremium,omitempty"`
	TotalPremium                int32                                                `json:"totalPremium"`
	TotalSurchargePremium       *int32                                               `json:"totalSurchargePremium,omitempty"`
}

// ApplicationReviewQuoteCombinedDeductibleCoverages defines model for ApplicationReviewQuote.CombinedDeductibleCoverages.
type ApplicationReviewQuoteCombinedDeductibleCoverages string

// ApplicationReviewQuoteStatus defines model for ApplicationReviewQuote.Status.
type ApplicationReviewQuoteStatus string

// ApplicationReviewQuoteCoverageType defines model for ApplicationReviewQuoteCoverageType.
type ApplicationReviewQuoteCoverageType struct {
	Deductible             *int32        `json:"deductible,omitempty"`
	Limit                  *int32        `json:"limit,omitempty"`
	NegotiatedPremium      *int32        `json:"negotiatedPremium,omitempty"`
	Premium                int32         `json:"premium"`
	PremiumPerHundredMiles *float32      `json:"premiumPerHundredMiles,omitempty"`
	PremiumPerUnit         *int32        `json:"premiumPerUnit,omitempty"`
	PremiumRange           *PremiumRange `json:"premiumRange,omitempty"`
	TivPercentage          *float32      `json:"tivPercentage,omitempty"`
	TivRange               *TivRange     `json:"tivRange,omitempty"`
	TotalTargetPremium     *float32      `json:"totalTargetPremium,omitempty"`
	TraditionalPremium     *int32        `json:"traditionalPremium,omitempty"`
}

// ApplicationReviewRecommendationConclusion defines model for ApplicationReviewRecommendationConclusion.
type ApplicationReviewRecommendationConclusion string

// ApplicationReviewRecommendationConclusionReason defines model for ApplicationReviewRecommendationConclusionReason.
type ApplicationReviewRecommendationConclusionReason = string

// ApplicationReviewRecommendationForm defines model for ApplicationReviewRecommendationForm.
type ApplicationReviewRecommendationForm struct {
	Conclusion       *ApplicationReviewRecommendationConclusion       `json:"conclusion,omitempty"`
	ConclusionReason *ApplicationReviewRecommendationConclusionReason `json:"conclusionReason,omitempty"`
}

// ApplicationReviewRecommendationRecommendedAction defines model for ApplicationReviewRecommendationRecommendedAction.
type ApplicationReviewRecommendationRecommendedAction struct {
	AdditionalAction *int32                                                        `json:"additionalAction,omitempty"`
	PrimaryAction    ApplicationReviewRecommendationRecommendedActionPrimaryAction `json:"primaryAction"`
}

// ApplicationReviewRecommendationRecommendedActionPrimaryAction defines model for ApplicationReviewRecommendationRecommendedAction.PrimaryAction.
type ApplicationReviewRecommendationRecommendedActionPrimaryAction string

// ApplicationReviewRecommendedActionTrail defines model for ApplicationReviewRecommendedActionTrail.
type ApplicationReviewRecommendedActionTrail struct {
	TrailItems []ApplicationReviewRecommendedActionTrailItem `json:"trailItems"`
}

// ApplicationReviewRecommendedActionTrailItem defines model for ApplicationReviewRecommendedActionTrailItem.
type ApplicationReviewRecommendedActionTrailItem struct {
	Date           openapi_types.Date      `json:"date"`
	Recommendation TrailItemRecommendation `json:"recommendation"`
	Updated        *TrailItemUpdate        `json:"updated,omitempty"`
}

// ApplicationReviewReportData defines model for ApplicationReviewReportData.
type ApplicationReviewReportData struct {
	ReportType ApplicationReviewReportType `json:"reportType"`
}

// ApplicationReviewReportForm defines model for ApplicationReviewReportForm.
type ApplicationReviewReportForm struct {
	Data ApplicationReviewReportData `json:"data"`
}

// ApplicationReviewReportType defines model for ApplicationReviewReportType.
type ApplicationReviewReportType string

// ApplicationReviewRepullWidgetResponse defines model for ApplicationReviewRepullWidgetResponse.
type ApplicationReviewRepullWidgetResponse = JobRunId

// ApplicationReviewSafetyBasicScoreThreshold defines model for ApplicationReviewSafetyBasicScoreThreshold.
type ApplicationReviewSafetyBasicScoreThreshold struct {
	Data externalRef0.BasicScoreThresholds `json:"data"`
	Meta *ApplicationReviewWidgetMeta      `json:"meta,omitempty"`
}

// ApplicationReviewSafetyBasicScoreThresholdForm defines model for ApplicationReviewSafetyBasicScoreThresholdForm.
type ApplicationReviewSafetyBasicScoreThresholdForm = ApplicationReviewWidgetBase

// ApplicationReviewSafetyBasicScoreTrend defines model for ApplicationReviewSafetyBasicScoreTrend.
type ApplicationReviewSafetyBasicScoreTrend struct {
	Data []ApplicationReviewSafetyBasicScoreTrendItem `json:"data"`
	Meta *ApplicationReviewWidgetMeta                 `json:"meta,omitempty"`
}

// ApplicationReviewSafetyBasicScoreTrendForm defines model for ApplicationReviewSafetyBasicScoreTrendForm.
type ApplicationReviewSafetyBasicScoreTrendForm = ApplicationReviewWidgetBase

// ApplicationReviewSafetyBasicScoreTrendItem defines model for ApplicationReviewSafetyBasicScoreTrendItem.
type ApplicationReviewSafetyBasicScoreTrendItem struct {
	ControlledSubstance *float32           `json:"controlledSubstance,omitempty"`
	CrashIndicator      *float32           `json:"crashIndicator,omitempty"`
	Date                openapi_types.Date `json:"date"`
	DriverFitness       *float32           `json:"driverFitness,omitempty"`
	HmCompliance        *float32           `json:"hmCompliance,omitempty"`
	HosCompliance       *float32           `json:"hosCompliance,omitempty"`
	UnsafeDriving       *float32           `json:"unsafeDriving,omitempty"`
	VehicleMaintenance  *float32           `json:"vehicleMaintenance,omitempty"`
}

// ApplicationReviewSafetyCrashRecord defines model for ApplicationReviewSafetyCrashRecord.
type ApplicationReviewSafetyCrashRecord struct {
	CrashRecordHistory      externalRef0.CrashRecordHistory `json:"crashRecordHistory"`
	CrashRecordSummary      externalRef0.CrashRecordSummary `json:"crashRecordSummary"`
	IsFurtherReviewRequired *bool                           `json:"isFurtherReviewRequired,omitempty"`
	Meta                    *ApplicationReviewWidgetMeta    `json:"meta,omitempty"`
}

// ApplicationReviewSafetyCrashRecordForm defines model for ApplicationReviewSafetyCrashRecordForm.
type ApplicationReviewSafetyCrashRecordForm = ApplicationReviewWidgetBase

// ApplicationReviewSafetyDotRating defines model for ApplicationReviewSafetyDotRating.
type ApplicationReviewSafetyDotRating struct {
	EffectiveDate *openapi_types.Date                           `json:"effectiveDate,omitempty"`
	Meta          *ApplicationReviewWidgetMeta                  `json:"meta,omitempty"`
	SafetyRating  *ApplicationReviewSafetyDotRatingSafetyRating `json:"safetyRating,omitempty"`
}

// ApplicationReviewSafetyDotRatingSafetyRating defines model for ApplicationReviewSafetyDotRating.SafetyRating.
type ApplicationReviewSafetyDotRatingSafetyRating string

// ApplicationReviewSafetyDotRatingForm defines model for ApplicationReviewSafetyDotRatingForm.
type ApplicationReviewSafetyDotRatingForm = ApplicationReviewWidgetBase

// ApplicationReviewSafetyISSScoreTrend defines model for ApplicationReviewSafetyISSScoreTrend.
type ApplicationReviewSafetyISSScoreTrend struct {
	Meta  *ApplicationReviewWidgetMeta               `json:"meta,omitempty"`
	Trend []ApplicationReviewSafetyISSScoreTrendItem `json:"trend"`
}

// ApplicationReviewSafetyISSScoreTrendForm defines model for ApplicationReviewSafetyISSScoreTrendForm.
type ApplicationReviewSafetyISSScoreTrendForm = ApplicationReviewWidgetBase

// ApplicationReviewSafetyISSScoreTrendItem defines model for ApplicationReviewSafetyISSScoreTrendItem.
type ApplicationReviewSafetyISSScoreTrendItem struct {
	Score     float32            `json:"score"`
	Timestamp openapi_types.Date `json:"timestamp"`
}

// ApplicationReviewSafetyOOSViolations defines model for ApplicationReviewSafetyOOSViolations.
type ApplicationReviewSafetyOOSViolations struct {
	Data      externalRef0.OOSViolations   `json:"data"`
	EndDate   openapi_types.Date           `json:"endDate"`
	Meta      *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
	StartDate openapi_types.Date           `json:"startDate"`
}

// ApplicationReviewSafetyOOSViolationsForm defines model for ApplicationReviewSafetyOOSViolationsForm.
type ApplicationReviewSafetyOOSViolationsForm = ApplicationReviewWidgetBase

// ApplicationReviewSafetyScore defines model for ApplicationReviewSafetyScore.
type ApplicationReviewSafetyScore struct {
	IsSafetyScoreEditable  bool                                        `json:"isSafetyScoreEditable"`
	IsShortHaul            *bool                                       `json:"isShortHaul,omitempty"`
	Meta                   *ApplicationReviewWidgetMeta                `json:"meta,omitempty"`
	RiskScore              *externalRef0.RiskScore                     `json:"riskScore,omitempty"`
	Score                  float32                                     `json:"score"`
	ScoreSelectedForRating *float32                                    `json:"scoreSelectedForRating,omitempty"`
	ScoreSelectedForReview *float32                                    `json:"scoreSelectedForReview,omitempty"`
	Status                 *FeatureStatus                              `json:"status,omitempty"`
	Threshold              float32                                     `json:"threshold"`
	Trend                  []ApplicationReviewSafetyScoreTrendItem     `json:"trend"`
	Version                externalRef0.ApplicationReviewWidgetVersion `json:"version"`
}

// ApplicationReviewSafetyScoreForm defines model for ApplicationReviewSafetyScoreForm.
type ApplicationReviewSafetyScoreForm struct {
	Form *ApplicationReviewSafetyScoreFormData `json:"form,omitempty"`
	Meta *ApplicationReviewWidgetMeta          `json:"meta,omitempty"`
}

// ApplicationReviewSafetyScoreFormData defines model for ApplicationReviewSafetyScoreFormData.
type ApplicationReviewSafetyScoreFormData struct {
	ScoreSelectedForRating *float32                                       `json:"scoreSelectedForRating,omitempty"`
	ScoreSelectedForReview *ApplicationReviewSafetyScoreSelectedForRating `json:"scoreSelectedForReview,omitempty"`
}

// ApplicationReviewSafetyScoreFormV2 defines model for ApplicationReviewSafetyScoreFormV2.
type ApplicationReviewSafetyScoreFormV2 struct {
	Form *UpdateSafetyScoreRequest    `json:"form,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewSafetyScoreSelectedForRating defines model for ApplicationReviewSafetyScoreSelectedForRating.
type ApplicationReviewSafetyScoreSelectedForRating struct {
	Score        *float32                `json:"score,omitempty"`
	ScoreType    externalRef0.ScoreType  `json:"scoreType"`
	ScoreVersion externalRef0.Version    `json:"scoreVersion"`
	Timestamp    time.Time               `json:"timestamp"`
	VinCount     *float32                `json:"vinCount,omitempty"`
	WindowEnd    time.Time               `json:"windowEnd"`
	WindowStart  time.Time               `json:"windowStart"`
	WindowType   externalRef0.WindowType `json:"windowType"`
}

// ApplicationReviewSafetyScoreTrendItem defines model for ApplicationReviewSafetyScoreTrendItem.
type ApplicationReviewSafetyScoreTrendItem struct {
	HarshDriving *float32           `json:"harshDriving,omitempty"`
	Score        float32            `json:"score"`
	Speeding     *float32           `json:"speeding,omitempty"`
	Timestamp    openapi_types.Date `json:"timestamp"`
}

// ApplicationReviewSafetyScoreV2 defines model for ApplicationReviewSafetyScoreV2.
type ApplicationReviewSafetyScoreV2 struct {
	CanEdit       bool                                        `json:"canEdit"`
	IsShortHaul   *bool                                       `json:"isShortHaul,omitempty"`
	Meta          *ApplicationReviewWidgetMeta                `json:"meta,omitempty"`
	RiskScore     externalRef0.RiskScore                      `json:"riskScore"`
	Status        FeatureStatus                               `json:"status"`
	TotalVinCount int                                         `json:"totalVinCount"`
	Version       externalRef0.ApplicationReviewWidgetVersion `json:"version"`
}

// ApplicationReviewSafetySevereViolations defines model for ApplicationReviewSafetySevereViolations.
type ApplicationReviewSafetySevereViolations struct {
	Meta       *ApplicationReviewWidgetMeta  `json:"meta,omitempty"`
	Violations externalRef0.SevereViolations `json:"violations"`
}

// ApplicationReviewSafetySevereViolationsForm defines model for ApplicationReviewSafetySevereViolationsForm.
type ApplicationReviewSafetySevereViolationsForm = ApplicationReviewWidgetBase

// ApplicationReviewState defines model for ApplicationReviewState.
type ApplicationReviewState string

// ApplicationReviewSummary defines model for ApplicationReviewSummary.
type ApplicationReviewSummary struct {
	AccountGrade           ApplicationReviewAccountGradeProperty    `json:"accountGrade"`
	AppReviewID            string                                   `json:"appReviewID"`
	ApplicationShortID     string                                   `json:"applicationShortID"`
	Assignees              ApplicationReviewAssignees               `json:"assignees"`
	ClearanceStatus        *externalRef0.ApplicationClearanceStatus `json:"clearanceStatus,omitempty"`
	CompanyName            string                                   `json:"companyName"`
	CreatedAt              time.Time                                `json:"createdAt"`
	DotNumber              int64                                    `json:"dotNumber"`
	EffectiveDate          openapi_types.Date                       `json:"effectiveDate"`
	EndStateReasons        *EndStateReasons                         `json:"endStateReasons,omitempty"`
	IsInternal             *bool                                    `json:"isInternal,omitempty"`
	IsRiskWorksheetEnabled bool                                     `json:"isRiskWorksheetEnabled"`

	// MtcVersion MTC Rating Model Version - None (Not Applicable), V1 (Sentry Model) or V2 (GWCC Model)
	MtcVersion                externalRef0.MTCVersion      `json:"mtcVersion"`
	NumDeclineCriteria        *int                         `json:"numDeclineCriteria,omitempty"`
	PuCount                   int                          `json:"puCount"`
	RecommendedAction         RecommendedAction            `json:"recommendedAction"`
	State                     ApplicationReviewState       `json:"state"`
	TabDetails                *ApplicationReviewTabDetails `json:"tabDetails,omitempty"`
	TelematicsConnectionState TelematicsConnectionState    `json:"telematicsConnectionState"`
	TspConnectionHandleId     *string                      `json:"tspConnectionHandleId,omitempty"`
	TspName                   *string                      `json:"tspName,omitempty"`
	UnderwriterEmail          openapi_types.Email          `json:"underwriterEmail"`
	UnderwriterName           string                       `json:"underwriterName"`
	UnresolvedProblems        []Problems                   `json:"unresolvedProblems"`
	UnreviewedProblems        []Problems                   `json:"unreviewedProblems"`
	UpdatedAt                 time.Time                    `json:"updatedAt"`
}

// ApplicationReviewSummaryForm defines model for ApplicationReviewSummaryForm.
type ApplicationReviewSummaryForm struct {
	EffectiveDate          *openapi_types.Date `json:"effectiveDate,omitempty"`
	NumberOfVehicleCameras *int                `json:"numberOfVehicleCameras,omitempty"`
	SubsidyAmount          *float64            `json:"subsidyAmount,omitempty"`
}

// ApplicationReviewTab defines model for ApplicationReviewTab.
type ApplicationReviewTab string

// ApplicationReviewTabCount defines model for ApplicationReviewTabCount.
type ApplicationReviewTabCount struct {
	Count int32                `json:"count"`
	Tab   ApplicationReviewTab `json:"tab"`
}

// ApplicationReviewTabDetails defines model for ApplicationReviewTabDetails.
type ApplicationReviewTabDetails struct {
	TabName   ApplicationReviewTabName   `json:"tabName"`
	TabStatus ApplicationReviewTabStatus `json:"tabStatus"`
}

// ApplicationReviewTabName defines model for ApplicationReviewTabName.
type ApplicationReviewTabName string

// ApplicationReviewTabStatus defines model for ApplicationReviewTabStatus.
type ApplicationReviewTabStatus string

// ApplicationReviewTargetPrice defines model for ApplicationReviewTargetPrice.
type ApplicationReviewTargetPrice struct {
	PercentOfTIV        *float32                           `json:"percentOfTIV,omitempty"`
	Range               *ApplicationReviewTargetPriceRange `json:"range,omitempty"`
	RatePerHundredMiles *float32                           `json:"ratePerHundredMiles,omitempty"`
}

// ApplicationReviewTargetPriceOverride defines model for ApplicationReviewTargetPriceOverride.
type ApplicationReviewTargetPriceOverride struct {
	AlTargetPrice          *ApplicationReviewTargetPrice `json:"alTargetPrice,omitempty"`
	ApdTargetPrice         *ApplicationReviewTargetPrice `json:"apdTargetPrice,omitempty"`
	IsTargetPriceAvailable *bool                         `json:"isTargetPriceAvailable,omitempty"`
	MtcTargetPrice         *ApplicationReviewTargetPrice `json:"mtcTargetPrice,omitempty"`
	TotalTargetPrice       *ApplicationReviewTargetPrice `json:"totalTargetPrice,omitempty"`
}

// ApplicationReviewTargetPriceRange defines model for ApplicationReviewTargetPriceRange.
type ApplicationReviewTargetPriceRange struct {
	Max int32 `json:"max"`
	Min int32 `json:"min"`
}

// ApplicationReviewUser defines model for ApplicationReviewUser.
type ApplicationReviewUser struct {
	Email   openapi_types.Email `json:"email"`
	IconUrl *string             `json:"iconUrl,omitempty"`
	Id      string              `json:"id"`
	Name    string              `json:"name"`
}

// ApplicationReviewUserAction defines model for ApplicationReviewUserAction.
type ApplicationReviewUserAction struct {
	ActionType ApplicationUserActionType         `json:"actionType"`
	RiskTypes  []ApplicationReviewUserPermission `json:"riskTypes"`
	Status     bool                              `json:"status"`
}

// ApplicationReviewUserPermission defines model for ApplicationReviewUserPermission.
type ApplicationReviewUserPermission struct {
	MinLevelRequired int32                               `json:"minLevelRequired"`
	Status           bool                                `json:"status"`
	Type             ApplicationReviewUserPermissionType `json:"type"`
	Value            *string                             `json:"value,omitempty"`
}

// ApplicationReviewUserPermissionType defines model for ApplicationReviewUserPermissionType.
type ApplicationReviewUserPermissionType string

// ApplicationReviewUserPermissions defines model for ApplicationReviewUserPermissions.
type ApplicationReviewUserPermissions struct {
	MinLevelRequired int32                             `json:"minLevelRequired"`
	Permissions      []ApplicationReviewUserPermission `json:"permissions"`
	Status           bool                              `json:"status"`
	UserActions      []ApplicationReviewUserAction     `json:"userActions"`
}

// ApplicationReviewVinProblem defines model for ApplicationReviewVinProblem.
type ApplicationReviewVinProblem struct {
	Error          *string                       `json:"error,omitempty"`
	Fixes          *EquipmentListVinProblemFixes `json:"fixes,omitempty"`
	IsResolved     *bool                         `json:"isResolved,omitempty"`
	IsReviewed     *bool                         `json:"isReviewed,omitempty"`
	SystemReviewed *bool                         `json:"systemReviewed,omitempty"`
}

// ApplicationReviewVinVisibility defines model for ApplicationReviewVinVisibility.
type ApplicationReviewVinVisibility struct {
	EquipmentListVinCount                   int32    `json:"equipmentListVinCount"`
	NonVisibleAgentSubmittedVinList         []string `json:"nonVisibleAgentSubmittedVinList"`
	NonVisibleAgentSubmittedVinListCount    int32    `json:"nonVisibleAgentSubmittedVinListCount"`
	VisibleAndOverlappingVinCount           int32    `json:"visibleAndOverlappingVinCount"`
	VisibleAndOverlappingVinCountPercentage float32  `json:"visibleAndOverlappingVinCountPercentage"`
	VisibleVinsNotInAgentSubmittedList      []string `json:"visibleVinsNotInAgentSubmittedList"`
	VisibleVinsNotInAgentSubmittedVinCount  int32    `json:"visibleVinsNotInAgentSubmittedVinCount"`
}

// ApplicationReviewVinVisibilityCheckList defines model for ApplicationReviewVinVisibilityCheckList.
type ApplicationReviewVinVisibilityCheckList struct {
	Status ApplicationReviewVinVisibilityCheckListStatus `json:"status"`
	Tasks  []ApplicationReviewVinVisibilityCheckListItem `json:"tasks"`
}

// ApplicationReviewVinVisibilityCheckListItem defines model for ApplicationReviewVinVisibilityCheckListItem.
type ApplicationReviewVinVisibilityCheckListItem struct {
	Description string        `json:"description"`
	Id          int32         `json:"id"`
	IsCompleted bool          `json:"isCompleted"`
	Value       OptionalValue `json:"value"`
}

// ApplicationReviewVinVisibilityCheckListResponse defines model for ApplicationReviewVinVisibilityCheckListResponse.
type ApplicationReviewVinVisibilityCheckListResponse struct {
	PreviousReviewChecklist *ApplicationReviewVinVisibilityCheckList      `json:"previousReviewChecklist,omitempty"`
	Status                  ApplicationReviewVinVisibilityCheckListStatus `json:"status"`
	Tasks                   []ApplicationReviewVinVisibilityCheckListItem `json:"tasks"`
}

// ApplicationReviewVinVisibilityCheckListStatus defines model for ApplicationReviewVinVisibilityCheckListStatus.
type ApplicationReviewVinVisibilityCheckListStatus struct {
	CompletedTasks int32 `json:"completedTasks"`
	TotalTasks     int32 `json:"totalTasks"`
}

// ApplicationReviewVinVisibilityChecklistPut defines model for ApplicationReviewVinVisibilityChecklistPut.
type ApplicationReviewVinVisibilityChecklistPut struct {
	Tasks []ApplicationReviewVinVisibilityCheckListItem `json:"tasks"`
}

// ApplicationReviewWidgetBackFillForm defines model for ApplicationReviewWidgetBackFillForm.
type ApplicationReviewWidgetBackFillForm struct {
	ApplicationReviewIDs []ApplicationReviewIDs                     `json:"applicationReviewIDs"`
	BackFillAll          bool                                       `json:"backFillAll"`
	WidgetEnums          []externalRef0.ApplicationReviewWidgetEnum `json:"widgetEnums"`
}

// ApplicationReviewWidgetBackfillResponse defines model for ApplicationReviewWidgetBackfillResponse.
type ApplicationReviewWidgetBackfillResponse = JobRunId

// ApplicationReviewWidgetBase defines model for ApplicationReviewWidgetBase.
type ApplicationReviewWidgetBase struct {
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// ApplicationReviewWidgetCoverageMeta defines model for ApplicationReviewWidgetCoverageMeta.
type ApplicationReviewWidgetCoverageMeta struct {
	Credit *float32 `json:"credit,omitempty"`
	Merit  *int32   `json:"merit,omitempty"`
}

// ApplicationReviewWidgetMeta defines model for ApplicationReviewWidgetMeta.
type ApplicationReviewWidgetMeta struct {
	AutoLiability      *ApplicationReviewWidgetCoverageMeta `json:"autoLiability,omitempty"`
	AutoPhysicalDamage *ApplicationReviewWidgetCoverageMeta `json:"autoPhysicalDamage,omitempty"`
	Credit             *float32                             `json:"credit,omitempty"`
	Flags              *[]ApplicationReviewWidgetMetaFlag   `json:"flags,omitempty"`
	GeneralLiability   *ApplicationReviewWidgetCoverageMeta `json:"generalLiability,omitempty"`
	Merit              *int32                               `json:"merit,omitempty"`
	MotorTruckCargo    *ApplicationReviewWidgetCoverageMeta `json:"motorTruckCargo,omitempty"`
}

// ApplicationReviewWidgetMetaFlag defines model for ApplicationReviewWidgetMetaFlag.
type ApplicationReviewWidgetMetaFlag struct {
	Description *string `json:"description,omitempty"`
	Title       string  `json:"title"`
	Weight      int32   `json:"weight"`
}

// ApplicationReviewWidgetRepull defines model for ApplicationReviewWidgetRepull.
type ApplicationReviewWidgetRepull struct {
	WidgetEnums *[]externalRef0.ApplicationReviewWidgetEnum `json:"widgetEnums,omitempty"`
}

// ApplicationReviewWidgetSummary defines model for ApplicationReviewWidgetSummary.
type ApplicationReviewWidgetSummary struct {
	AccountGrade ApplicationReviewAccountGradeProperty `json:"accountGrade"`
	AccountName  string                                `json:"accountName"`

	// AdditionalInfo Additional information about the application added by the agents.
	AdditionalInfo                string                  `json:"additionalInfo"`
	Address                       string                  `json:"address"`
	AgencyName                    string                  `json:"agencyName"`
	AgentSelectedIndicationOption *ApplicationReviewQuote `json:"agentSelectedIndicationOption,omitempty"`
	ApplicationId                 string                  `json:"applicationId"`
	AreCamerasRequired            *bool                   `json:"areCamerasRequired,omitempty"`
	AssignedBD                    *string                 `json:"assignedBD,omitempty"`
	CameraSubsidyDetails          *CameraSubsidyDetails   `json:"cameraSubsidyDetails,omitempty"`
	DotNumber                     int64                   `json:"dotNumber"`
	EffectiveDate                 openapi_types.Date      `json:"effectiveDate"`
	FetchAttractScore             bool                    `json:"fetchAttractScore"`
	Fronter                       *string                 `json:"fronter,omitempty"`
	IsALIncumbent                 bool                    `json:"isALIncumbent"`
	IsEligibleForCameraProgram    *bool                   `json:"isEligibleForCameraProgram,omitempty"`
	IsRenewal                     bool                    `json:"isRenewal"`
	MarketerEmail                 string                  `json:"marketerEmail"`
	MarketerName                  string                  `json:"marketerName"`
	OriginalEffectiveDate         *openapi_types.Date     `json:"originalEffectiveDate,omitempty"`
	ProducerName                  string                  `json:"producerName"`
	PullMvrs                      externalRef0.MVRFlag    `json:"pullMvrs"`
	SalesforceLink                *string                 `json:"salesforceLink,omitempty"`
	TotalPolicyPremiumUnmodified  *int32                  `json:"totalPolicyPremiumUnmodified,omitempty"`
	TspDetails                    *TspDetails             `json:"tspDetails,omitempty"`
	UnitCount                     int                     `json:"unitCount"`
	UsState                       string                  `json:"usState"`
}

// ApplicationState defines model for ApplicationState.
type ApplicationState string

// ApplicationUserActionType defines model for ApplicationUserActionType.
type ApplicationUserActionType string

// ApplicationVinProblemRecord defines model for ApplicationVinProblemRecord.
type ApplicationVinProblemRecord struct {
	Errors                 []string               `json:"errors"`
	FixedBodyClass         *VehicleBodyClass      `json:"fixedBodyClass,omitempty"`
	FixedMake              *string                `json:"fixedMake,omitempty"`
	FixedModel             *string                `json:"fixedModel,omitempty"`
	FixedModelYear         *string                `json:"fixedModelYear,omitempty"`
	FixedVehicleType       *VehicleType           `json:"fixedVehicleType,omitempty"`
	FixedVin               *string                `json:"fixedVin,omitempty"`
	FixedWeightClass       *string                `json:"fixedWeightClass,omitempty"`
	IsResolved             bool                   `json:"isResolved"`
	IsReviewed             bool                   `json:"isReviewed"`
	IsoVehicleTypeOverride *ISOVehicleType        `json:"isoVehicleTypeOverride,omitempty"`
	IsoWeightGroupOverride *ISOVehicleWeightGroup `json:"isoWeightGroupOverride,omitempty"`
	NhtsaDecodeError       *string                `json:"nhtsaDecodeError,omitempty"`
	ShouldSkip             *bool                  `json:"shouldSkip,omitempty"`
	SystemReviewed         *bool                  `json:"systemReviewed,omitempty"`
	Vin                    string                 `json:"vin"`
}

// ApplicationVinProblems defines model for ApplicationVinProblems.
type ApplicationVinProblems struct {
	Problems []ApplicationVinProblemRecord `json:"problems"`
}

// ApplicationWinCarriersObject defines model for ApplicationWinCarriersObject.
type ApplicationWinCarriersObject struct {
	CarrierName string  `json:"CarrierName"`
	Id          float32 `json:"Id"`
}

// AuthorityDeclineRequestPayload Payload for decline authority requests
type AuthorityDeclineRequestPayload struct {
	// DeclineReasons Reasons for decline (required when has_hard_rule_violations is false)
	DeclineReasons *[]ApplicationReviewDeclineReasonObject `json:"decline_reasons,omitempty"`

	// HardRuleViolations List of violated hard rules (required when has_hard_rule_violations is true)
	HardRuleViolations *[]HardRuleViolation `json:"hard_rule_violations,omitempty"`

	// HasHardRuleViolations Indicates if hard rules were violated that prevent quoting
	HasHardRuleViolations bool `json:"has_hard_rule_violations"`

	// NoQuoteExplanation Explanation for no quote (required when has_hard_rule_violations is false)
	NoQuoteExplanation *string `json:"no_quote_explanation,omitempty"`

	// TargetQuotePrice Target quote price (required when has_hard_rule_violations is false)
	TargetQuotePrice *float64 `json:"target_quote_price,omitempty"`

	// ViolationReason Explanation of why data differs and violates guidelines (required when has_hard_rule_violations is true)
	ViolationReason *string `json:"violation_reason,omitempty"`
}

// AuthorityRequest Request to create an authority request
type AuthorityRequest struct {
	ApplicationReviewId openapi_types.UUID           `json:"application_review_id"`
	RequestData         AuthorityRequest_RequestData `json:"request_data"`

	// RequestType Type of authority request
	RequestType AuthorityRequestType `json:"request_type"`
}

// AuthorityRequest_RequestData defines model for AuthorityRequest.RequestData.
type AuthorityRequest_RequestData struct {
	union json.RawMessage
}

// AuthorityRequestDetails Detailed information about an authority request
type AuthorityRequestDetails struct {
	ApplicationReviewId openapi_types.UUID `json:"application_review_id"`
	CreatedAt           time.Time          `json:"created_at"`
	ExecutedAt          *time.Time         `json:"executed_at,omitempty"`

	// IsUnderwritingManager Indicates if the current user has underwriting manager role
	IsUnderwritingManager *bool               `json:"is_underwriting_manager,omitempty"`
	LastReviewedAt        *time.Time          `json:"last_reviewed_at,omitempty"`
	LastReviewedBy        *openapi_types.UUID `json:"last_reviewed_by,omitempty"`

	// ManagerNote Note from UW manager when sending request back to UW
	ManagerNote *string `json:"manager_note,omitempty"`

	// RequestData The original request data, typed based on request_type
	RequestData AuthorityRequestDetails_RequestData `json:"request_data"`
	RequestId   AuthorityRequestID                  `json:"request_id"`

	// RequestType Type of authority request
	RequestType   AuthorityRequestType `json:"request_type"`
	RequesterId   openapi_types.UUID   `json:"requester_id"`
	RequesterName *string              `json:"requester_name,omitempty"`
	ReviewerName  *string              `json:"reviewer_name,omitempty"`

	// State Current state of the authority request
	State       AuthorityRequestState `json:"state"`
	SubmittedAt *time.Time            `json:"submitted_at,omitempty"`
	UpdatedAt   time.Time             `json:"updated_at"`
}

// AuthorityRequestDetails_RequestData The original request data, typed based on request_type
type AuthorityRequestDetails_RequestData struct {
	union json.RawMessage
}

// AuthorityRequestID defines model for AuthorityRequestID.
type AuthorityRequestID = openapi_types.UUID

// AuthorityRequestList List of authority requests for an application
type AuthorityRequestList struct {
	Requests []AuthorityRequestDetails `json:"requests"`

	// Total Total number of requests
	Total int `json:"total"`
}

// AuthorityRequestResponse Response after creating an authority request
type AuthorityRequestResponse struct {
	ApplicationReviewId openapi_types.UUID `json:"application_review_id"`
	CreatedAt           time.Time          `json:"created_at"`
	RequestId           AuthorityRequestID `json:"request_id"`

	// RequestType Type of authority request
	RequestType AuthorityRequestType `json:"request_type"`

	// State Current state of the authority request
	State AuthorityRequestState `json:"state"`
}

// AuthorityRequestState Current state of the authority request
type AuthorityRequestState string

// AuthorityRequestType Type of authority request
type AuthorityRequestType string

// AuthorityReviewAction Action to be taken on an authority request
type AuthorityReviewAction string

// BaselineFactor Represents the baseline value of a specific factor. It includes the sample size used in its calculation.
type BaselineFactor struct {
	SampleSize int32   `json:"sampleSize"`
	Value      float32 `json:"value"`
}

// BulkUpdateReviewReadinessTaskRequest defines model for BulkUpdateReviewReadinessTaskRequest.
type BulkUpdateReviewReadinessTaskRequest struct {
	Tasks *[]UpdateReviewReadinessTaskRequest `json:"tasks,omitempty"`
}

// CameraSubsidyDetails defines model for CameraSubsidyDetails.
type CameraSubsidyDetails struct {
	NumberOfCameras int      `json:"numberOfCameras"`
	SubsidyAmount   *float64 `json:"subsidyAmount,omitempty"`
}

// Category defines model for Category.
type Category string

// CategoryPricingDetails defines model for CategoryPricingDetails.
type CategoryPricingDetails struct {
	Category              Category                `json:"category"`
	CoveragePricingDetail []CoveragePricingDetail `json:"coverage_pricing_detail"`
	IsEditable            bool                    `json:"isEditable"`
}

// ClearedApplicationResponse defines model for ClearedApplicationResponse.
type ClearedApplicationResponse struct {
	AgencyName         string `json:"agencyName"`
	ApplicationID      string `json:"applicationID"`
	ApplicationShortID string `json:"applicationShortID"`
	CompanyName        string `json:"companyName"`
	ProducerName       string `json:"producerName"`
}

// CompanyInfo defines model for CompanyInfo.
type CompanyInfo struct {
	CompanyName   string  `json:"companyName"`
	PhoneNumber   *string `json:"phoneNumber,omitempty"`
	ProducerEmail string  `json:"producerEmail"`
	ProducerName  string  `json:"producerName"`
}

// CoveragePricingDetail defines model for CoveragePricingDetail.
type CoveragePricingDetail struct {
	Coverage externalRef0.CoverageType `json:"coverage"`
	Credit   int32                     `json:"credit"`
}

// EmailPreference defines model for EmailPreference.
type EmailPreference string

// EmailPreferenceInfo defines model for EmailPreferenceInfo.
type EmailPreferenceInfo struct {
	Preference *EmailPreference `json:"preference,omitempty"`
	UpdatedAt  *time.Time       `json:"updatedAt,omitempty"`
}

// EmailStatus defines model for EmailStatus.
type EmailStatus string

// EmailStatusInfo defines model for EmailStatusInfo.
type EmailStatusInfo struct {
	Status    *EmailStatus `json:"status,omitempty"`
	UpdatedAt *time.Time   `json:"updatedAt,omitempty"`
}

// EndStateReasons defines model for EndStateReasons.
type EndStateReasons struct {
	Comments         *string              `json:"comments,omitempty"`
	Notes            *string              `json:"notes,omitempty"`
	PolicyIdentifier *string              `json:"policyIdentifier,omitempty"`
	Reason           *string              `json:"reason,omitempty"`
	State            *ApplicationEndState `json:"state,omitempty"`
	SubReason        *string              `json:"subReason,omitempty"`
	WinCarrier       *string              `json:"winCarrier,omitempty"`
}

// EquipmentListVinProblemFixes defines model for EquipmentListVinProblemFixes.
type EquipmentListVinProblemFixes struct {
	FixedBodyClass         *VehicleBodyClass      `json:"fixedBodyClass,omitempty"`
	FixedMake              *string                `json:"fixedMake,omitempty"`
	FixedModel             *string                `json:"fixedModel,omitempty"`
	FixedModelYear         *string                `json:"fixedModelYear,omitempty"`
	FixedVehicleType       *VehicleType           `json:"fixedVehicleType,omitempty"`
	FixedWeightClass       *string                `json:"fixedWeightClass,omitempty"`
	IsoVehicleTypeOverride *ISOVehicleType        `json:"isoVehicleTypeOverride,omitempty"`
	IsoWeightGroupOverride *ISOVehicleWeightGroup `json:"isoWeightGroupOverride,omitempty"`
}

// FactorRanking defines model for FactorRanking.
type FactorRanking struct {
	RankedFactors []FactorRankingItem `json:"rankedFactors"`
	RaterType     string              `json:"raterType"`
}

// FactorRankingItem defines model for FactorRankingItem.
type FactorRankingItem struct {
	AboveBaseline *float32 `json:"aboveBaseline,omitempty"`

	// Baseline Represents the baseline value of a specific factor. It includes the sample size used in its calculation.
	Baseline        BaselineFactor `json:"baseline"`
	Description     string         `json:"description"`
	FactorName      string         `json:"factorName"`
	WeightedAverage float32        `json:"weightedAverage"`
}

// FactorRankingsResponse defines model for FactorRankingsResponse.
type FactorRankingsResponse = []FactorRanking

// FeatureStatus defines model for FeatureStatus.
type FeatureStatus string

// GetApplicationReviewMstReferral defines model for GetApplicationReviewMstReferral.
type GetApplicationReviewMstReferral struct {
	IsReferralRequired bool                               `json:"isReferralRequired"`
	IsReferred         *bool                              `json:"isReferred,omitempty"`
	IsReviewed         bool                               `json:"isReviewed"`
	ReferralRules      []ApplicationReviewMstReferralRule `json:"referralRules"`
	UpdatedAt          *time.Time                         `json:"updatedAt,omitempty"`
}

// GetSaferResponse defines model for GetSaferResponse.
type GetSaferResponse struct {
	DotRating     *string `json:"dotRating,omitempty"`
	EffectiveDate *string `json:"effectiveDate,omitempty"`
}

// GroupOfApplicationReviewsForDataCompletion defines model for GroupOfApplicationReviewsForDataCompletion.
type GroupOfApplicationReviewsForDataCompletion = []ApplicationReviewForDataCompletion

// HardRuleViolation Details about a hard rule violation
type HardRuleViolation struct {
	// Description Detailed description of the rule violation
	Description string `json:"description"`

	// RuleId Unique identifier for the violated rule
	RuleId string `json:"rule_id"`

	// RuleName Human-readable name of the violated rule
	RuleName string `json:"rule_name"`
}

// ISOVehicleType defines model for ISOVehicleType.
type ISOVehicleType string

// ISOVehicleWeightGroup defines model for ISOVehicleWeightGroup.
type ISOVehicleWeightGroup string

// JobRunId defines model for JobRunId.
type JobRunId struct {
	JobId string `json:"job_id"`
	RunId int    `json:"run_id"`
}

// LatestCoverageSummary defines model for LatestCoverageSummary.
type LatestCoverageSummary struct {
	Averages            LossRunAverages                            `json:"averages"`
	CoverageType        externalRef0.CoverageType                  `json:"coverageType"`
	LossRatioAggregates LossRatioAggregates                        `json:"lossRatioAggregates"`
	Summary             []ApplicationReviewLossSummaryItemRecordV2 `json:"summary"`
}

// LossFrequency defines model for LossFrequency.
type LossFrequency struct {
	PerMillionMiles float32 `json:"perMillionMiles"`
	PerUnit         float32 `json:"perUnit"`
}

// LossRatioAggregates defines model for LossRatioAggregates.
type LossRatioAggregates = []struct {
	PeriodLabel  string  `json:"periodLabel"`
	ValuePercent float32 `json:"valuePercent"`
}

// LossRunAverages defines model for LossRunAverages.
type LossRunAverages struct {
	AverageBurnRate  float32       `json:"averageBurnRate"`
	AverageClaimSize float32       `json:"averageClaimSize"`
	LossFrequency    LossFrequency `json:"lossFrequency"`
}

// LossRunFileErrorCode defines model for LossRunFileErrorCode.
type LossRunFileErrorCode string

// LossRunFileMetadata defines model for LossRunFileMetadata.
type LossRunFileMetadata struct {
	ErrorCode    *LossRunFileErrorCode   `json:"errorCode,omitempty"`
	FileHandle   string                  `json:"fileHandle"`
	FileName     string                  `json:"fileName"`
	ParsedStatus LossRunFileParsedStatus `json:"parsedStatus"`
}

// LossRunFileParsedStatus defines model for LossRunFileParsedStatus.
type LossRunFileParsedStatus string

// LossRunValueSource defines model for LossRunValueSource.
type LossRunValueSource string

// LossSummaryCoverageParams defines model for LossSummaryCoverageParams.
type LossSummaryCoverageParams struct {
	CoverageType          externalRef0.CoverageType `json:"coverageType"`
	PreClaimDeductible    float32                   `json:"preClaimDeductible"`
	RequestedPremiumPerPU float32                   `json:"requestedPremiumPerPU"`
}

// LossSummaryVersion defines model for LossSummaryVersion.
type LossSummaryVersion string

// LossValueWithOverride defines model for LossValueWithOverride.
type LossValueWithOverride struct {
	Override    *float32           `json:"override,omitempty"`
	Value       float32            `json:"value"`
	ValueSource LossRunValueSource `json:"valueSource"`
}

// MileageEstimate defines model for MileageEstimate.
type MileageEstimate struct {
	Data   *MileageEstimateData `json:"data,omitempty"`
	Status FeatureStatus        `json:"status"`
}

// MileageEstimateData defines model for MileageEstimateData.
type MileageEstimateData struct {
	EstimatedMileage       float32                             `json:"estimatedMileage"`
	MileageEstimateFeature externalRef0.MileageEstimateFeature `json:"mileageEstimateFeature"`
	MileageRange           MileageRange                        `json:"mileageRange"`
	TrackedEquipment       TrackedEquipment                    `json:"trackedEquipment"`
}

// MileageEstimateReason defines model for MileageEstimateReason.
type MileageEstimateReason struct {
	AdditionalDetails *string                        `json:"additionalDetails,omitempty"`
	Category          MileageEstimateReasonsCategory `json:"category"`
	Reason            *MileageEstimateReasonsData    `json:"reason,omitempty"`
}

// MileageEstimateReasons defines model for MileageEstimateReasons.
type MileageEstimateReasons = []MileageEstimateReason

// MileageEstimateReasonsCategory defines model for MileageEstimateReasonsCategory.
type MileageEstimateReasonsCategory string

// MileageEstimateReasonsData defines model for MileageEstimateReasonsData.
type MileageEstimateReasonsData string

// MileageRange defines model for MileageRange.
type MileageRange struct {
	LowerBoundMileage float32 `json:"lowerBoundMileage"`
	UpperBoundMileage float32 `json:"upperBoundMileage"`
}

// MstReferralRuleType defines model for MstReferralRuleType.
type MstReferralRuleType string

// NBasicScore defines model for NBasicScore.
type NBasicScore string

// OptionalValue defines model for OptionalValue.
type OptionalValue struct {
	Value struct {
		BoolValue    *bool   `json:"boolValue,omitempty"`
		IntegerValue *int    `json:"integerValue,omitempty"`
		StringValue  *string `json:"stringValue,omitempty"`
	} `json:"value"`
	ValueType OptionalValueValueType `json:"valueType"`
}

// OptionalValueValueType defines model for OptionalValue.ValueType.
type OptionalValueValueType string

// PaginatedListSortBy defines model for PaginatedListSortBy.
type PaginatedListSortBy string

// PaginatedListSortDirection defines model for PaginatedListSortDirection.
type PaginatedListSortDirection string

// ParsedLossState defines model for ParsedLossState.
type ParsedLossState string

// ParsedLossStateV2 defines model for ParsedLossStateV2.
type ParsedLossStateV2 string

// PatchApplicationReviewMstReferralRule defines model for PatchApplicationReviewMstReferralRule.
type PatchApplicationReviewMstReferralRule struct {
	IsReferred *bool                             `json:"isReferred,omitempty"`
	Rule       *ApplicationReviewMstReferralRule `json:"rule,omitempty"`
}

// PremiumRange defines model for PremiumRange.
type PremiumRange struct {
	Max int32 `json:"max"`
	Min int32 `json:"min"`
}

// PricingType defines model for PricingType.
type PricingType string

// Problems defines model for Problems.
type Problems struct {
	Id  string `json:"id"`
	Tag string `json:"tag"`
}

// ProcessAuthorityRequest Request to process (approve/reject) an authority request
type ProcessAuthorityRequest struct {
	// Action Action to be taken on an authority request
	Action AuthorityReviewAction `json:"action"`

	// Notes Optional notes explaining the decision
	Notes *string `json:"notes,omitempty"`
}

// RecalculateTelematicsConnectionStateResponse defines model for RecalculateTelematicsConnectionStateResponse.
type RecalculateTelematicsConnectionStateResponse struct {
	ApplicationReviewId string `json:"applicationReviewId"`

	// CurrentReviewReadinessState Current review readiness state stored in DB
	CurrentReviewReadinessState string `json:"currentReviewReadinessState"`

	// CurrentTelematicsConnectionState Current telematics connection state stored in DB
	CurrentTelematicsConnectionState string `json:"currentTelematicsConnectionState"`

	// RecalculatedReviewReadinessState Recalculated review readiness state
	RecalculatedReviewReadinessState string `json:"recalculatedReviewReadinessState"`

	// RecalculatedTelematicsConnectionState Recalculated telematics connection state
	RecalculatedTelematicsConnectionState string `json:"recalculatedTelematicsConnectionState"`
}

// RecommendationInfo defines model for RecommendationInfo.
type RecommendationInfo struct {
	Conclusion        *ApplicationReviewRecommendationConclusion       `json:"conclusion,omitempty"`
	ConclusionReason  *ApplicationReviewRecommendationConclusionReason `json:"conclusionReason,omitempty"`
	Description       *string                                          `json:"description,omitempty"`
	ExperimentId      string                                           `json:"experimentId"`
	Name              string                                           `json:"name"`
	PanelTypes        []RecommendationInfoPanelType                    `json:"panelTypes"`
	RecommendedAction ApplicationReviewRecommendationRecommendedAction `json:"recommendedAction"`
	State             RecommendationInfoState                          `json:"state"`
}

// RecommendationInfoState defines model for RecommendationInfo.State.
type RecommendationInfoState string

// RecommendationInfoPanelType defines model for RecommendationInfoPanelType.
type RecommendationInfoPanelType string

// Recommendations defines model for Recommendations.
type Recommendations = []RecommendationInfo

// RecommendedAction defines model for RecommendedAction.
type RecommendedAction string

// RecommendedActionNotification defines model for RecommendedActionNotification.
type RecommendedActionNotification struct {
	HasTRSUpdated       bool   `json:"hasTRSUpdated"`
	NotificationId      string `json:"notificationId"`
	NumOfFactorsUpdated int32  `json:"numOfFactorsUpdated"`
}

// RecommendedActionNotificationInfo defines model for RecommendedActionNotificationInfo.
type RecommendedActionNotificationInfo struct {
	IsNotificationAvailable bool                           `json:"isNotificationAvailable"`
	Notification            *RecommendedActionNotification `json:"notification,omitempty"`
}

// RecommendedActionReason defines model for RecommendedActionReason.
type RecommendedActionReason string

// RefreshReason defines model for RefreshReason.
type RefreshReason = []RefreshReasonItem

// RefreshReasonItem defines model for RefreshReasonItem.
type RefreshReasonItem = string

// ReviewReadinessTask defines model for ReviewReadinessTask.
type ReviewReadinessTask struct {
	Assignee         ApplicationReviewUser               `json:"assignee"`
	CompletionMethod ReviewReadinessTaskCompletionMethod `json:"completionMethod"`

	// DisableReason Reason if task is disabled, reason should always be present if isEnabled is false
	DisableReason *string                   `json:"disableReason,omitempty"`
	IsEnabled     bool                      `json:"isEnabled"`
	Name          ReviewReadinessTaskName   `json:"name"`
	Notes         *string                   `json:"notes,omitempty"`
	Status        ReviewReadinessTaskStatus `json:"status"`
}

// ReviewReadinessTaskCompletionMethod defines model for ReviewReadinessTaskCompletionMethod.
type ReviewReadinessTaskCompletionMethod string

// ReviewReadinessTaskListResponse defines model for ReviewReadinessTaskListResponse.
type ReviewReadinessTaskListResponse struct {
	AppReviewState ApplicationReviewState `json:"appReviewState"`
	CompletedTasks int32                  `json:"completedTasks"`
	Tasks          []ReviewReadinessTask  `json:"tasks"`
	TotalTasks     int32                  `json:"totalTasks"`
}

// ReviewReadinessTaskName defines model for ReviewReadinessTaskName.
type ReviewReadinessTaskName string

// ReviewReadinessTaskStatus defines model for ReviewReadinessTaskStatus.
type ReviewReadinessTaskStatus string

// RiskFactor defines model for RiskFactor.
type RiskFactor struct {
	Category Category `json:"category"`

	// CompositeName Name of the composite risk factor if applicable
	CompositeName *string   `json:"composite_name,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	CreatedBy     *string   `json:"created_by,omitempty"`
	Description   string    `json:"description"`
	Id            string    `json:"id"`

	// IsSystemGenerated Indicates if the risk factor is system generated or user created
	IsSystemGenerated bool            `json:"is_system_generated"`
	LastUpdatedBy     *string         `json:"last_updated_by,omitempty"`
	Name              string          `json:"name"`
	PricingType       PricingType     `json:"pricing_type"`
	State             RiskFactorState `json:"state"`
	UpdatedAt         time.Time       `json:"updated_at"`
	Version           int32           `json:"version"`
}

// RiskFactorState defines model for RiskFactorState.
type RiskFactorState string

// RiskFactors defines model for RiskFactors.
type RiskFactors struct {
	Factors []RiskFactor `json:"factors"`
}

// RollbackMetadata defines model for RollbackMetadata.
type RollbackMetadata struct {
	Comment string `json:"comment"`
}

// SelectApplicationReviewOperationsTerminalLocationForm defines model for SelectApplicationReviewOperationsTerminalLocationForm.
type SelectApplicationReviewOperationsTerminalLocationForm struct {
	SelectedLocation *ApplicationReviewOperationsTerminalLocationItem `json:"selectedLocation,omitempty"`
}

// Sentiment defines model for Sentiment.
type Sentiment string

// SuggestedRiskFactor defines model for SuggestedRiskFactor.
type SuggestedRiskFactor struct {
	Category  Category  `json:"category"`
	Comment   *string   `json:"comment,omitempty"`
	Name      string    `json:"name"`
	Notes     string    `json:"notes"`
	Sentiment Sentiment `json:"sentiment"`
	Value     string    `json:"value"`
}

// TSPConnectionInfo defines model for TSPConnectionInfo.
type TSPConnectionInfo struct {
	CompanyInfo                 CompanyInfo                       `json:"CompanyInfo"`
	TSPConnectionHandleId       *string                           `json:"TSPConnectionHandleId,omitempty"`
	TelematicsDataStatus        externalRef0.TelematicsDataStatus `json:"TelematicsDataStatus"`
	TelematicsPipelineStatus    *TelematicsPipelineStatus         `json:"TelematicsPipelineStatus,omitempty"`
	TelematicsReminderEmailInfo *TelematicsReminderEmailInfo      `json:"TelematicsReminderEmailInfo,omitempty"`
}

// TelematicsConnectionState defines model for TelematicsConnectionState.
type TelematicsConnectionState string

// TelematicsDataButtonStatus defines model for TelematicsDataButtonStatus.
type TelematicsDataButtonStatus string

// TelematicsPipelineStatus defines model for TelematicsPipelineStatus.
type TelematicsPipelineStatus struct {
	ProcessingDate *time.Time `json:"processingDate,omitempty"`
	Tsp            string     `json:"tsp"`
}

// TelematicsReminderEmailInfo defines model for TelematicsReminderEmailInfo.
type TelematicsReminderEmailInfo struct {
	EmailsSent   *int32               `json:"emailsSent,omitempty"`
	LastReminder *time.Time           `json:"lastReminder,omitempty"`
	NextReminder *time.Time           `json:"nextReminder,omitempty"`
	Preference   *EmailPreferenceInfo `json:"preference,omitempty"`
	Status       *EmailStatusInfo     `json:"status,omitempty"`
}

// TelematicsStatus defines model for TelematicsStatus.
type TelematicsStatus string

// TivRange defines model for TivRange.
type TivRange struct {
	Max float32 `json:"max"`
	Min float32 `json:"min"`
}

// TrackedEquipment defines model for TrackedEquipment.
type TrackedEquipment struct {
	AgentsEquipmentsCount     int `json:"agentsEquipmentsCount"`
	TelematicsEquipmentsCount int `json:"telematicsEquipmentsCount"`
}

// TraditionalRiskFactor defines model for TraditionalRiskFactor.
type TraditionalRiskFactor struct {
	Description         *string `json:"description,omitempty"`
	Name                string  `json:"name"`
	PreviousDescription *string `json:"previousDescription,omitempty"`
}

// TraditionalRiskFactors defines model for TraditionalRiskFactors.
type TraditionalRiskFactors struct {
	AppetiteFlag TraditionalRiskFactorsAppetiteFlag `json:"appetiteFlag"`
	RiskFactors  *[]TraditionalRiskFactor           `json:"riskFactors,omitempty"`
}

// TraditionalRiskFactorsAppetiteFlag defines model for TraditionalRiskFactors.AppetiteFlag.
type TraditionalRiskFactorsAppetiteFlag string

// TrailItemRecommendation defines model for TrailItemRecommendation.
type TrailItemRecommendation struct {
	HasRecommendationChanged bool               `json:"hasRecommendationChanged"`
	IsFirstRecommendation    bool               `json:"isFirstRecommendation"`
	PreviousRecommendation   *RecommendedAction `json:"previousRecommendation,omitempty"`
}

// TrailItemUpdate defines model for TrailItemUpdate.
type TrailItemUpdate struct {
	HasTRSUpdated           bool                     `json:"hasTRSUpdated"`
	HasVinVisibilityUpdated *bool                    `json:"hasVinVisibilityUpdated,omitempty"`
	RiskFactors             *[]TraditionalRiskFactor `json:"riskFactors,omitempty"`
}

// TspDetails defines model for TspDetails.
type TspDetails struct {
	IsException bool   `json:"isException"`
	Tsp         string `json:"tsp"`
}

// Underwriters defines model for Underwriters.
type Underwriters struct {
	SeniorUnderwriters []ApplicationReviewUser `json:"seniorUnderwriters"`
}

// UpdateAppReviewEquipmentListFormData defines model for UpdateAppReviewEquipmentListFormData.
type UpdateAppReviewEquipmentListFormData struct {
	Units []AppReviewEquipmentUpdateFormItem `json:"units"`
}

// UpdateApplicationReviewOperationsTerminalLocationItem defines model for UpdateApplicationReviewOperationsTerminalLocationItem.
type UpdateApplicationReviewOperationsTerminalLocationItem struct {
	AddressLineOne        string                                                    `json:"addressLineOne"`
	AddressLineTwo        *string                                                   `json:"addressLineTwo,omitempty"`
	CargoTerminalSchedule *externalRef0.CargoTerminalSchedule                       `json:"cargoTerminalSchedule,omitempty"`
	IsCreatedByUW         *bool                                                     `json:"isCreatedByUW,omitempty"`
	IsDeletedByUW         *bool                                                     `json:"isDeletedByUW,omitempty"`
	IsGated               bool                                                      `json:"isGated"`
	IsGuarded             bool                                                      `json:"isGuarded"`
	Type                  UpdateApplicationReviewOperationsTerminalLocationItemType `json:"type"`
	UsState               string                                                    `json:"usState"`
	ZipCode               string                                                    `json:"zipCode"`
}

// UpdateApplicationReviewOperationsTerminalLocationItemType defines model for UpdateApplicationReviewOperationsTerminalLocationItem.Type.
type UpdateApplicationReviewOperationsTerminalLocationItemType string

// UpdateApplicationReviewOperationsTerminalLocations defines model for UpdateApplicationReviewOperationsTerminalLocations.
type UpdateApplicationReviewOperationsTerminalLocations struct {
	Data *struct {
		Value []UpdateApplicationReviewOperationsTerminalLocationItem `json:"value"`
	} `json:"data,omitempty"`
	Meta *ApplicationReviewWidgetMeta `json:"meta,omitempty"`
}

// UpdateApplicationReviewRequest defines model for UpdateApplicationReviewRequest.
type UpdateApplicationReviewRequest struct {
	Drivers    *ApplicationReviewPanelInfoPatch `json:"drivers,omitempty"`
	Equipments *ApplicationReviewPanelInfoPatch `json:"equipments,omitempty"`
	Financials *ApplicationReviewPanelInfoPatch `json:"financials,omitempty"`
	Losses     *ApplicationReviewPanelInfoPatch `json:"losses,omitempty"`
	Operations *ApplicationReviewPanelInfoPatch `json:"operations,omitempty"`
	Overview   *ApplicationReviewPanelInfoPatch `json:"overview,omitempty"`
	Packages   *ApplicationReviewPanelInfoPatch `json:"packages,omitempty"`
	Safety     *ApplicationReviewPanelInfoPatch `json:"safety,omitempty"`
}

// UpdatePricingRequest defines model for UpdatePricingRequest.
type UpdatePricingRequest struct {
	Category              Category                `json:"category"`
	CoveragePricingDetail []CoveragePricingDetail `json:"coverage_pricing_detail"`
	RiskFactorId          *string                 `json:"riskFactorId,omitempty"`
}

// UpdateReviewReadinessTaskRequest defines model for UpdateReviewReadinessTaskRequest.
type UpdateReviewReadinessTaskRequest struct {
	AssigneeID *string                    `json:"assigneeID,omitempty"`
	Name       ReviewReadinessTaskName    `json:"name"`
	Notes      *string                    `json:"notes,omitempty"`
	Status     *ReviewReadinessTaskStatus `json:"status,omitempty"`
}

// UpdateSafetyScoreRequest defines model for UpdateSafetyScoreRequest.
type UpdateSafetyScoreRequest struct {
	ChangeReason      string    `json:"changeReason"`
	NewScoreTimestamp time.Time `json:"newScoreTimestamp"`
}

// UpdateWorksheetRequest defines model for UpdateWorksheetRequest.
type UpdateWorksheetRequest struct {
	Notes *string `json:"notes,omitempty"`
}

// VehicleBodyClass defines model for VehicleBodyClass.
type VehicleBodyClass string

// VehicleType defines model for VehicleType.
type VehicleType string

// VehicleZone defines model for VehicleZone.
type VehicleZone struct {
	Description *string `json:"description,omitempty"`
	Id          int32   `json:"id"`
}

// VehicleZoneRecord defines model for VehicleZoneRecord.
type VehicleZoneRecord struct {
	EndZone              VehicleZone `json:"endZone"`
	PercentageOfVehicles int32       `json:"percentageOfVehicles"`
	StartZone            VehicleZone `json:"startZone"`
}

// WinCarrierPricing Pricing information from winning carrier
type WinCarrierPricing struct {
	// AutoLiability Auto Liability price from winning carrier
	AutoLiability *int32 `json:"autoLiability,omitempty"`

	// FullPrice Full price from winning carrier
	FullPrice *int32 `json:"fullPrice,omitempty"`
}

// WorksheetResponse defines model for WorksheetResponse.
type WorksheetResponse struct {
	CreatedAt            time.Time                `json:"created_at"`
	Id                   string                   `json:"id"`
	LastUpdatedBy        *string                  `json:"last_updated_by,omitempty"`
	Notes                *string                  `json:"notes,omitempty"`
	PricingDetails       []CategoryPricingDetails `json:"pricing_details"`
	ReviewId             string                   `json:"review_id"`
	State                WorksheetState           `json:"state"`
	UpdatedAt            time.Time                `json:"updated_at"`
	Version              int32                    `json:"version"`
	WorksheetRiskFactors []WorksheetRiskFactor    `json:"worksheet_risk_factors"`
}

// WorksheetRiskFactor defines model for WorksheetRiskFactor.
type WorksheetRiskFactor struct {
	CreatedAt      time.Time                `json:"created_at"`
	CreatedBy      string                   `json:"created_by"`
	Id             string                   `json:"id"`
	LastUpdatedBy  string                   `json:"last_updated_by"`
	Notes          *string                  `json:"notes,omitempty"`
	PricingDetails *[]CoveragePricingDetail `json:"pricing_details,omitempty"`
	RiskFactor     RiskFactor               `json:"risk_factor"`
	Sentiment      Sentiment                `json:"sentiment"`
	UpdatedAt      time.Time                `json:"updated_at"`
	Value          *string                  `json:"value,omitempty"`
	Version        *int32                   `json:"version,omitempty"`
}

// WorksheetRiskFactorAddRequest defines model for WorksheetRiskFactorAddRequest.
type WorksheetRiskFactorAddRequest struct {
	Notes        *string   `json:"notes,omitempty"`
	RiskFactorId string    `json:"risk_factor_id"`
	Sentiment    Sentiment `json:"sentiment"`
	Value        string    `json:"value"`
}

// WorksheetRiskFactorUpdate defines model for WorksheetRiskFactorUpdate.
type WorksheetRiskFactorUpdate struct {
	Notes        *string `json:"notes,omitempty"`
	RiskFactorId string  `json:"risk_factor_id"`
	Value        string  `json:"value"`
}

// WorksheetRiskFactorUpdateRequest defines model for WorksheetRiskFactorUpdateRequest.
type WorksheetRiskFactorUpdateRequest struct {
	Notes     *string    `json:"notes,omitempty"`
	Sentiment *Sentiment `json:"sentiment,omitempty"`
	Value     *string    `json:"value,omitempty"`
}

// WorksheetState defines model for WorksheetState.
type WorksheetState string

// ZipCodeDetails defines model for ZipCodeDetails.
type ZipCodeDetails struct {
	City  *string `json:"city,omitempty"`
	State *string `json:"state,omitempty"`
}

// ApplicationReviewIDs defines model for applicationReviewIDs.
type ApplicationReviewIDs = string

// ApplicationID defines model for ApplicationID.
type ApplicationID = string

// ApplicationReviewID defines model for ApplicationReviewID.
type ApplicationReviewID = string

// ClaimSn defines model for ClaimSn.
type ClaimSn = int32

// DocumentId defines model for DocumentId.
type DocumentId = string

// PolicySn defines model for PolicySn.
type PolicySn = int32

// AsAuthorityDeclineRequestPayload returns the union data inside the AuthorityRequest_RequestData as a AuthorityDeclineRequestPayload
func (t AuthorityRequest_RequestData) AsAuthorityDeclineRequestPayload() (AuthorityDeclineRequestPayload, error) {
	var body AuthorityDeclineRequestPayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromAuthorityDeclineRequestPayload overwrites any union data inside the AuthorityRequest_RequestData as the provided AuthorityDeclineRequestPayload
func (t *AuthorityRequest_RequestData) FromAuthorityDeclineRequestPayload(v AuthorityDeclineRequestPayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeAuthorityDeclineRequestPayload performs a merge with any union data inside the AuthorityRequest_RequestData, using the provided AuthorityDeclineRequestPayload
func (t *AuthorityRequest_RequestData) MergeAuthorityDeclineRequestPayload(v AuthorityDeclineRequestPayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

func (t AuthorityRequest_RequestData) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *AuthorityRequest_RequestData) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsAuthorityDeclineRequestPayload returns the union data inside the AuthorityRequestDetails_RequestData as a AuthorityDeclineRequestPayload
func (t AuthorityRequestDetails_RequestData) AsAuthorityDeclineRequestPayload() (AuthorityDeclineRequestPayload, error) {
	var body AuthorityDeclineRequestPayload
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromAuthorityDeclineRequestPayload overwrites any union data inside the AuthorityRequestDetails_RequestData as the provided AuthorityDeclineRequestPayload
func (t *AuthorityRequestDetails_RequestData) FromAuthorityDeclineRequestPayload(v AuthorityDeclineRequestPayload) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeAuthorityDeclineRequestPayload performs a merge with any union data inside the AuthorityRequestDetails_RequestData, using the provided AuthorityDeclineRequestPayload
func (t *AuthorityRequestDetails_RequestData) MergeAuthorityDeclineRequestPayload(v AuthorityDeclineRequestPayload) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JsonMerge(t.union, b)
	t.union = merged
	return err
}

func (t AuthorityRequestDetails_RequestData) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *AuthorityRequestDetails_RequestData) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}
