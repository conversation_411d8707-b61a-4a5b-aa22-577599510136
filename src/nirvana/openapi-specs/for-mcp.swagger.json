{"openapi": "3.0.3", "info": {"description": "Nirvana APIs for MCP Experiments", "title": "Nirvana APIs for MCP Experiments", "version": "1.0.0"}, "servers": [{"url": "https://mcp-experiments-api.prod.nirvanatech.com/"}], "security": [{"sessionIdAuth": []}], "tags": [{"description": "MCP Experiments APIs", "name": "mcp-experiments"}, {"description": "Policy-related operations", "name": "policies"}, {"description": "DOT number related operations", "name": "dot"}, {"description": "For Model Context Protocol Servers", "name": "for-mcp"}, {"description": "Auth related APIs", "name": "auth"}], "paths": {"/mcp-experiments/dot/{dotNumber}/v0-policies/": {"get": {"description": "Retrieves all V0 policies for a DOT number. A V0 policy is the **first** policy\nrow in the policy table for a given policy number.\n", "operationId": "MCPExperimentsGetV0PoliciesForDOT", "parameters": [{"explode": false, "in": "path", "name": "dotNumber", "required": true, "schema": {"example": 1002125, "format": "int64", "type": "integer"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_200_response_inner"}, "type": "array"}}}, "description": "List of policies for the DOT number"}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "DOT number not found"}}, "summary": "Get the V0 policies for a DOT number", "tags": ["mcp-experiments", "policies", "dot", "for-mcp"]}}, "/mcp-experiments/dot/{dotNumber}/policies/effective/{effectiveDate}": {"get": {"description": "Retrieves all active policies and their associated documents for a DOT number as of a specific date.\nThe response includes summary information for each policy and its documents.\n\n### Notes\n- The date parameter must be in YYYY-MM-DD format\n- Policies are returned in chronological order by effective date\n- Documents for each policy are included in the response\n", "operationId": "MCPExperimentsGetActivePoliciesForDOTAsOn", "parameters": [{"explode": false, "in": "path", "name": "dotNumber", "required": true, "schema": {"example": 1002125, "format": "int64", "type": "integer"}, "style": "simple"}, {"description": "The date as of which to retrieve policies (YYYY-MM-DD format)", "explode": false, "in": "path", "name": "effectiveDate", "required": true, "schema": {"format": "date", "type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_200_response_inner"}, "type": "array"}}}, "description": "Returns all active policies and their documents for DOT as of the given date"}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "DOT number not found"}}, "summary": "Get active policies and their documents for DOT as of the given date", "tags": ["mcp-experiments", "policies", "dot", "for-mcp"]}}, "/mcp-experiments/policy/{policyNumber}/effective/{effectiveDate}": {"get": {"description": "Retrieves the policy information and its documents as of the given effective date.\n\n### Notes\n- The date parameter must be in YYYY-MM-DD format\n- Endorsements are returned in chronological order by effective date, starting with the earliest endorsement\n", "operationId": "MCPExperimentsGetPolicyAndDocsAsOn", "parameters": [{"explode": false, "in": "path", "name": "policyNumber", "required": true, "schema": {"example": "NISTK0017619-23", "type": "string"}, "style": "simple"}, {"description": "The date as of which to retrieve documents (YYYY-MM-DD format)", "explode": false, "in": "path", "name": "effectiveDate", "required": true, "schema": {"format": "date", "type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetPolicyAndDocsAsOn_200_response"}}}, "description": "Returns all documents for the policy as of the given date"}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Policy not found"}}, "summary": "Get policy information and its documents as of a specific date", "tags": ["mcp-experiments", "policies", "documents", "for-mcp"]}}, "/mcp-experiments/policy/{policyNumber}/documents/{documentId}": {"get": {"description": "Returns a pre-signed URL to access a specific policy document. The URL is time-limited and will expire after a specified duration.\n\n### Notes\n- The pre-signed URL is valid for 1 hour by default\n- The URL can be used to directly download the document\n- Document access is subject to the user's permissions\n- Documents may be available in multiple formats\n", "operationId": "MCPExperimentsDownloadPolicyDocument", "parameters": [{"explode": false, "in": "path", "name": "policyNumber", "required": true, "schema": {"example": "NISTK0017619-23", "type": "string"}, "style": "simple"}, {"description": "The document ID", "explode": false, "in": "path", "name": "documentId", "required": true, "schema": {"format": "uuid", "type": "string"}, "style": "simple"}, {"description": "The desired document format", "explode": true, "in": "query", "name": "format", "required": false, "schema": {"default": "original", "enum": ["original", "markdown"], "type": "string"}, "style": "form"}, {"description": "Number of minutes until the pre-signed URL expires", "explode": true, "in": "query", "name": "expirationMinutes", "required": false, "schema": {"default": 60, "maximum": 1440, "minimum": 1, "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsDownloadPolicyDocument_200_response"}}}, "description": "Returns the document details and access URL"}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Invalid request parameters"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "User is not authorized to access this document"}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Policy or document not found"}}, "summary": "Download a specific policy document", "tags": ["mcp-experiments", "policies", "documents", "for-mcp"]}}, "/mcp-experiments/policy/{policyNumber}/claims": {"get": {"description": "Retrieves claims associated with a specific policy number.\n", "operationId": "MCPExperimentsGetClaimsForPolicy", "parameters": [{"explode": false, "in": "path", "name": "policyNumber", "required": true, "schema": {"example": "NISTK0017619-23", "type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetClaimsForPolicy_200_response_inner"}, "type": "array"}}}, "description": "Returns claims for the policy."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Policy not found."}}, "summary": "Get claims for a policy", "tags": ["mcp-experiments", "claims", "policies", "for-mcp"]}}, "/mcp-experiments/claim/{claimExternalId}": {"get": {"description": "Retrieves the core details for a specific claim, identified by its external ID.\nThis endpoint returns summary information including the most recent status.\n", "operationId": "MCPExperimentsGetClaimInfo", "parameters": [{"description": "The external identifier of the claim (e.g., C-ABCD-12345).", "explode": false, "in": "path", "name": "claimExternalId", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetClaimInfo_200_response"}}}, "description": "Returns the core claim information."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "<PERSON><PERSON><PERSON> not found."}}, "summary": "Get core information for a specific claim", "tags": ["mcp-experiments", "claims", "for-mcp"]}}, "/mcp-experiments/claim/{claimExternalId}/notes": {"get": {"description": "Retrieves notes associated with a specific claim, identified by its external ID.\n", "operationId": "MCPExperimentsGetClaimNotes", "parameters": [{"description": "The external identifier of the claim (e.g., C-ABCD-12345).", "explode": false, "in": "path", "name": "claimExternalId", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetClaimNotes_200_response_inner"}, "type": "array"}}}, "description": "Returns a list of claim notes."}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "<PERSON><PERSON><PERSON> not found."}}, "summary": "Get notes for a specific claim", "tags": ["mcp-experiments", "claims", "notes", "for-mcp"]}}, "/mcp-experiments/claim/{claimExternalId}/status-log": {"get": {"description": "Retrieves status log observations associated with a specific claim, identified by its external ID.\n\nNote that this can return duplicate values as it can contain periodic observations, as\nwell as status change events.\n", "operationId": "MCPExperimentsGetClaimStatusLog", "parameters": [{"description": "The external identifier of the claim (e.g., C-ABCD-12345).", "explode": false, "in": "path", "name": "claimExternalId", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetClaimStatusLog_200_response_inner"}, "type": "array"}}}, "description": "Returns a list of claim status log observations."}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Invalid query parameters"}, "404": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "<PERSON><PERSON><PERSON> not found."}}, "summary": "Get status log for a specific claim", "tags": ["mcp-experiments", "claims", "for-mcp"]}}, "/mcp-experiments/claim/{claimExternalId}/sync": {"post": {"description": "Synchronously updates claim notes from the source database (NARS C3).\nThis endpoint calls the NARS API to get the latest notes for the claim and\npersists them to the database.\n", "operationId": "MCPExperimentsTriggerClaimSync", "parameters": [{"explode": false, "in": "path", "name": "claimExternalId", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"201": {"description": "Claim data has been successfully synchronized and persisted to the database"}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Invalid request parameters"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MCPExperimentsGetV0PoliciesForDOT_404_response"}}}, "description": "Sync operation failed"}}, "summary": "Synchronously update claim notes from source database", "tags": ["mcp-experiments", "claims", "sync", "for-mcp"]}}, "/auth/login": {"post": {"requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "description": "A JSON object containing the login and password.", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}, "description": "A JSON Object containing the sessionId. All subsequent requests should contain a header JSESSIONID with the returned value"}}, "security": [], "summary": "Logs the user in and returns the sessionId", "tags": ["auth", "for-mcp"]}}, "/auth/logout": {"post": {"responses": {"200": {"description": "Succesfully logged out the user"}}, "summary": "Logs the user out", "tags": ["auth", "for-mcp"]}}, "/me": {"get": {"responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileResponse"}}}, "description": "User's Profile"}}, "summary": "Get user profile for the authenticated user", "tags": ["auth", "for-mcp"]}}}, "components": {"schemas": {"MCPExperimentsGetV0PoliciesForDOT_404_response": {"properties": {"message": {"example": "Unexpected error", "type": "string"}, "code": {"example": 400, "type": "integer"}}, "required": ["code", "message"]}, "MCPExperimentsGetPolicyAndDocsAsOn_200_response": {"allOf": [{"properties": {"policyNumber": {"example": "NITSK00123456-22", "type": "string"}, "status": {"enum": ["Generated", "Created", "Active", "Expired", "CancellationFiled", "Cancelled", "Stale"], "type": "string"}, "programType": {"enum": ["Fleet", "NonFleetAdmitted", "NonFleetCanopiusNRB"], "type": "string"}, "insuredName": {"example": "MNM Trucking", "type": "string"}, "dotNumber": {"example": 1234567, "type": "string"}, "effectiveStartDate": {"description": "Policy effective start date (inclusive). The policy is active starting from this date.", "format": "date", "type": "string"}, "effectiveEndDate": {"description": "Policy effective end date (inclusive). The policy is active up to and including this date.", "format": "date", "type": "string"}, "insuranceCarrier": {"description": "Name of the insurance carrier", "enum": ["InsuranceCarrierEmpty", "InsuranceCarrierFallsLake", "InsuranceCarrierSiriusPoint", "InsuranceCarrierFronterX", "InsuranceCarrierMSTransverse", "InsuranceCarrierTransverse", "InsuranceCarrierMSTSpeciality", "InsuranceCarrierSiriusPointSpeciality"], "type": "string"}, "miscellaneousInfo": {"additionalProperties": {}, "description": "Miscellaneous information about the policy, sort of a dump-all object", "nullable": true}, "createdAt": {"description": "Timestamp when the policy record was created", "format": "date-time", "type": "string"}, "updatedAt": {"description": "Timestamp when the policy record was last updated", "format": "date-time", "type": "string"}}, "required": ["createdAt", "dotNumber", "effectiveEndDate", "effectiveStartDate", "insuranceCarrier", "insuredName", "policyNumber", "programType", "status", "updatedAt"]}, {"properties": {"policyDocument": {"$ref": "#/components/schemas/MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_policyDocument"}, "endorsements": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_endorsements_inner"}, "type": "array"}}, "required": ["endorsements", "policyDocument"]}]}, "MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_policyDocument_metadata": {"description": "Additional metadata about the document", "properties": {"fileSize": {"description": "Size of the document in bytes", "format": "int32", "type": "integer"}, "mimeType": {"description": "MIME type of the document", "type": "string"}, "createdAt": {"description": "When the document was created", "format": "date-time", "type": "string"}, "updatedAt": {"description": "When the document was last updated", "format": "date-time", "type": "string"}}}, "MCPExperimentsGetClaimsForPolicy_200_response_inner": {"description": "Summary information about a claim.", "properties": {"id": {"description": "Unique identifier for the claim.", "format": "uuid", "type": "string"}, "externalId": {"description": "The external identifier for the claim.", "type": "string"}, "policyNumber": {"description": "The policy number associated with the claim.", "type": "string"}, "reportedAt": {"description": "Timestamp when the claim was reported.", "format": "date-time", "type": "string"}, "lossOccurredAt": {"description": "Timestamp when the loss occurred.", "format": "date-time", "type": "string"}, "currentStatus": {"enum": ["Open", "Closed", "Reopen", "CreatedInError"], "type": "string"}}, "required": ["createdAt", "currentStatus", "externalId", "id", "policyNumber", "reportedAt", "updatedAt"]}, "MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_endorsements_inner": {"properties": {"endorsementId": {"description": "Unique identifier for the endorsement", "format": "uuid", "type": "string"}, "effectiveDate": {"description": "Effective date of the endorsement", "format": "date", "type": "string"}, "approvedAt": {"description": "Date and time the endorsement was approved", "format": "date-time", "type": "string"}, "description": {"description": "Description of the endorsement", "type": "string"}, "documents": {"items": {"$ref": "#/components/schemas/MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_policyDocument"}, "type": "array"}}, "required": ["endorsementId"]}, "MCPExperimentsGetClaimStatusLog_200_response_inner": {"description": "Represents a status log item for a claim.", "properties": {"id": {"description": "Unique identifier for the status log item.", "format": "uuid", "type": "string"}, "createdAt": {"description": "Timestamp when the status was recorded.", "format": "date-time", "type": "string"}, "value": {"enum": ["Open", "Closed", "Reopen", "CreatedInError"], "type": "string"}}, "required": ["createdAt", "id", "value"]}, "MCPExperimentsDownloadPolicyDocument_200_response": {"allOf": [{"properties": {"documentId": {"description": "Unique identifier for the document", "format": "uuid", "type": "string"}, "fileName": {"description": "Original name of the document file", "type": "string"}, "metadata": {"$ref": "#/components/schemas/MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_policyDocument_metadata"}}, "required": ["documentId", "fileName"]}, {"properties": {"preSignedUrl": {"description": "Time-limited URL to access the document", "format": "uri", "type": "string"}, "urlExpiresAt": {"description": "Timestamp when the URL will expire", "format": "date-time", "type": "string"}, "metadata": {"$ref": "#/components/schemas/MCPExperimentsDownloadPolicyDocument_200_response_allOf_metadata"}}, "required": ["preSignedUrl", "urlExpiresAt"]}]}, "MCPExperimentsGetClaimInfo_200_response": {"description": "Summary information about a claim.", "properties": {"id": {"description": "Unique identifier for the claim.", "format": "uuid", "type": "string"}, "externalId": {"description": "The external identifier for the claim.", "type": "string"}, "policyNumber": {"description": "The policy number associated with the claim.", "type": "string"}, "reportedAt": {"description": "Timestamp when the claim was reported.", "format": "date-time", "type": "string"}, "lossOccurredAt": {"description": "Timestamp when the loss occurred.", "format": "date-time", "type": "string"}, "currentStatus": {"enum": ["Open", "Closed", "Reopen", "CreatedInError"], "type": "string"}}, "required": ["currentStatus", "externalId", "id", "policyNumber", "reportedAt"]}, "MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_policyDocument": {"properties": {"documentId": {"description": "Unique identifier for the document", "format": "uuid", "type": "string"}, "fileName": {"description": "Original name of the document file", "type": "string"}, "metadata": {"$ref": "#/components/schemas/MCPExperimentsGetPolicyAndDocsAsOn_200_response_allOf_policyDocument_metadata"}}, "required": ["documentId", "fileName"]}, "MCPExperimentsGetClaimNotes_200_response_inner": {"description": "Represents a note associated with a claim.", "properties": {"noteId": {"description": "Unique identifier for the note.", "format": "uuid", "type": "string"}, "createdAt": {"description": "Timestamp when the note was created.", "format": "date-time", "type": "string"}, "text": {"description": "The content of the note.", "type": "string"}, "category": {"description": "The category of the note.", "type": "string"}}, "required": ["createdAt", "noteId", "text"]}, "MCPExperimentsGetV0PoliciesForDOT_200_response_inner": {"properties": {"policyNumber": {"example": "NITSK00123456-22", "type": "string"}, "status": {"enum": ["Generated", "Created", "Active", "Expired", "CancellationFiled", "Cancelled", "Stale"], "type": "string"}, "programType": {"enum": ["Fleet", "NonFleetAdmitted", "NonFleetCanopiusNRB"], "type": "string"}, "insuredName": {"example": "MNM Trucking", "type": "string"}, "dotNumber": {"example": 1234567, "type": "string"}, "effectiveStartDate": {"description": "Policy effective start date (inclusive). The policy is active starting from this date.", "format": "date", "type": "string"}, "effectiveEndDate": {"description": "Policy effective end date (inclusive). The policy is active up to and including this date.", "format": "date", "type": "string"}, "insuranceCarrier": {"description": "Name of the insurance carrier", "enum": ["InsuranceCarrierEmpty", "InsuranceCarrierFallsLake", "InsuranceCarrierSiriusPoint", "InsuranceCarrierFronterX", "InsuranceCarrierMSTransverse", "InsuranceCarrierTransverse", "InsuranceCarrierMSTSpeciality", "InsuranceCarrierSiriusPointSpeciality"], "type": "string"}, "miscellaneousInfo": {"additionalProperties": {}, "description": "Miscellaneous information about the policy, sort of a dump-all object", "nullable": true}, "createdAt": {"description": "Timestamp when the policy record was created", "format": "date-time", "type": "string"}, "updatedAt": {"description": "Timestamp when the policy record was last updated", "format": "date-time", "type": "string"}}, "required": ["createdAt", "dotNumber", "effectiveEndDate", "effectiveStartDate", "insuranceCarrier", "insuredName", "policyNumber", "programType", "status", "updatedAt"]}, "MCPExperimentsDownloadPolicyDocument_200_response_allOf_metadata": {"description": "Additional metadata about the document", "properties": {"pageCount": {"description": "Number of pages in the document", "format": "int32", "type": "integer"}, "version": {"description": "Version of the document", "type": "string"}}}, "LoginRequest": {"properties": {"email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "password": {"example": "T5daYm!wE&2D&uy!", "format": "password", "type": "string"}, "utmTags": {"$ref": "#/components/schemas/UTMTags"}, "source": {"description": "The source of the login request.", "enum": ["Agent", "Underwriter", "Safety", "Unknown"], "type": "string"}}, "required": ["email", "password"], "type": "object"}, "UTMTags": {"properties": {"utm_source": {"example": "telematics_consent", "type": "string"}, "utm_medium": {"example": "quoting_app", "type": "string"}, "utm_campaign": {"example": "nirvana", "type": "string"}, "utm_adgroup": {"example": "nirvana", "type": "string"}, "utm_keyword": {"example": "nirvana", "type": "string"}}, "type": "object"}, "LoginResponse": {"properties": {"sessionId": {"example": "abcde12345", "type": "string"}, "expiration": {"format": "date-time", "type": "string"}}, "required": ["expiration", "sessionId"], "type": "object"}, "UserProfileResponse": {"properties": {"id": {"example": "a81bc81b-dead-4e5d-abff-90865d1e13b1", "type": "string"}, "reportId": {"example": "a81bc81b-dead-4e5d-abff-90865d1e13b1", "type": "string"}, "name": {"example": "<PERSON>", "type": "string"}, "email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "userType": {"description": "Type of the user's roles:\n * `nirvana` - Internal Nirvana user, such as `superuser` or `support` users.\n * `agent` - User belongs to one or more Agency roles.\n * `fleet` - User belongs to one or more Fleet roles, and no Agency or Nirvana roles. This user should only have access to the safety app.\n * `shared_link` - Shared link user that is anonymously viewing a Safety App shared URL.\n * `unprivileged` - User has login credentials but does not belong to any agencies or fleets.\n", "enum": ["nirvana", "agent", "fleet", "shared_link", "unprivileged"], "type": "string"}, "roles": {"$ref": "#/components/schemas/Roles"}, "defaultAgencyId": {"example": "d754a727-80fd-468d-b2d0-8c8659eac8f3", "type": "string"}}, "required": ["email", "id", "name", "reportId", "userType"], "type": "object"}, "Roles": {"properties": {"nirvanaRoles": {"description": "List of authz internal `Nirvana` roles for this user.\n", "items": {"$ref": "#/components/schemas/NirvanaRole"}, "type": "array"}, "agencyRoles": {"description": "List of authz `Agency` roles for this user. If a user has an `agency` role, then they are typically either an `Agent` or an internal `Nirvana` user.\n", "items": {"$ref": "#/components/schemas/AgencyRole"}, "type": "array"}, "fleetRoles": {"description": "List of authz `Fleet` roles for this user. If a user has `Fleet` roles but no `Agency` or `Nirvana` roles, then this user can only view their own DOT(s) in the Safety App.\n", "items": {"$ref": "#/components/schemas/FleetRole"}, "type": "array"}}, "type": "object"}, "NirvanaRole": {"properties": {"role": {"example": "SuperuserRole", "type": "string"}}, "required": ["role"], "type": "object"}, "AgencyRole": {"properties": {"role": {"example": "AgencyAdminRole", "type": "string"}, "agency": {"$ref": "#/components/schemas/Agency"}}, "required": ["agency", "role"], "type": "object"}, "Agency": {"properties": {"id": {"example": "0ec2372b-d6e7-4aa4-9e3e-8efd378fb83f", "format": "uuid", "type": "string"}, "name": {"example": "Trucks R US", "type": "string"}}, "required": ["id", "name"], "type": "object"}, "FleetRole": {"properties": {"role": {"example": "FleetAdminRole", "type": "string"}, "fleet": {"$ref": "#/components/schemas/Fleet"}}, "required": ["fleet", "role"], "type": "object"}, "Fleet": {"properties": {"id": {"example": "0eb441d4-4e6a-409a-94b0-0af97bc1296e", "format": "uuid", "type": "string"}, "name": {"example": "Give2Get", "type": "string"}, "dotNumber": {"example": 2834206, "format": "int64", "minimum": 1, "type": "integer"}}, "required": ["dotNumber", "id", "name"], "type": "object"}}, "securitySchemes": {"sessionIdAuth": {"in": "header", "name": "JSESSIONID", "type": "<PERSON><PERSON><PERSON><PERSON>"}}}}