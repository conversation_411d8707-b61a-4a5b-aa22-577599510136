#!/bin/bash
# This file script merges all module-level OpenAPI specs into a unified OpenAPI JSON

set -euo pipefail

TARGET_DIR="target"

script_dir=$(cd -P -- "$(dirname -- "$0")" && pwd -P)
cd "$script_dir"

spec_dirs=(
    "api_server_auth"
    "api_server_app/fleet_app"
    "api_server_app/nonfleet_app"
    "api_server_uw/fleet_uw"
    "api_server_uw/nonfleet_uw"
    "api_server_academy"
    "api_server_billing"
    "api_server_knock_webhook"
    "api_server_telematics"
    "api_server_forms"
    "api_server_external"
    "api_server_dp"
    "api_server_jobber"
    "api_server_app/nirvana_app"
    "api_server_mcp_experiments"
)

# Recreate target directory
if [ -d "$TARGET_DIR" ]; then rm -Rf $TARGET_DIR; fi
mkdir -p $TARGET_DIR/

# First, convert YAMLs to JSONs, resolving references
for spec_dir in "${spec_dirs[@]}"; do
    docker run --rm -v "${PWD}:/local" openapitools/openapi-generator-cli generate \
         -i "/local/${spec_dir}/spec.yaml" \
         -g openapi \
         -o "/local/${TARGET_DIR}/${spec_dir}"
done

echo "Done resolving OpenAPI references and converting to JSON"

# Then merge all the JSONs at ./swagger.json
npx --yes openapi-merge-cli -c merge.conf

# Separate swagger JSON for telematics API(s)
npx --yes openapi-merge-cli -c telematics.merge.conf

# Separate swagger JSON for FLI NARS API(s)
npx --yes openapi-merge-cli -c fli_nars.merge.conf

# Separate swagger JSON for API INTEGRATION RELIANCE API(s)
npx --yes openapi-merge-cli -c sample_api_integration.merge.conf

# Separate swagger JSON for for-mcp API(s)
# 
# This step filters OpenAPI specs to include only endpoints tagged with "for-mcp".
# These endpoints are consumed by MCP (Model Context Protocol) servers that provide
# AI agents with tools to access backend APIs. See: src/llm_agents/docs/mcp_server_development_guide.md
npx --yes openapi-merge-cli -c for-mcp.merge.conf

# Post-process the MCP spec with Redocly for optimization
#
# WHY: openapi-merge-cli doesn't support removing unused components, leading to bloated
# Python client generation. As we add more endpoints from larger specs (e.g., underwriting),
# the generated clients become unwieldy with thousands of unused model classes.
#
# WHAT: Redocly's bundle command with --remove-unused-components:
# - Removes unused schemas, parameters, responses, etc. from the final spec
# - Keeps only components actually referenced by the filtered endpoints
# - Reduces Python client size and improves maintainability
#
# HOW: Takes the already-filtered for-mcp.swagger.json (from openapi-merge-cli) and
# optimizes it while preserving all functionality. The input and output file are the same,
# so this is a pure optimization step.
#
# DOCKER: Uses redocly/cli container to avoid local installation dependencies
# PATHS: Working directory is mounted as /spec, so paths are relative to container root
docker run --rm -v "${PWD}:/spec" redocly/cli:2.0.5 bundle for-mcp.swagger.json --remove-unused-components -o for-mcp.swagger.json
