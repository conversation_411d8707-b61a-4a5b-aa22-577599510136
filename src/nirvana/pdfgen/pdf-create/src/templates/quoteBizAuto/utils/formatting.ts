import { getFormattedDate } from '../../../helpers/date';

// ===========================
// CURRENCY FORMATTING
// ===========================

/**
 * Format currency values - handles both numbers and special strings
 */
export const formatCurrency = (value: number | string | null | undefined): string => {
  // Handle special strings like "Incl.", "N/A", "Included"
  if (typeof value === 'string' && isNaN(Number(value))) {
    return value;
  }

  const amount = typeof value === 'string' ? Number(value) : value;
  if (amount == null || isNaN(amount)) return 'N/A';

  return amount.toLocaleString();
};

/**
 * Format limit values with optional metadata
 */
export const formatLimit = (limit: number | string | null, limitMeta?: string): string => {
  // Handle string limits like "See Specific Unit"
  if (typeof limit === 'string' && isNaN(Number(limit))) {
    return limit;
  }

  const amount = typeof limit === 'string' ? Number(limit) : limit;
  
  let formatted: string;
  if (amount == null) {
    formatted = '';
  } else if (isNaN(amount)) {
    formatted = '0';
  } else {
    formatted = amount.toLocaleString();
  }

  const withSign = formatted === '' ? '' : `$${formatted}`;

  return limitMeta ? `${withSign} ${limitMeta}` : withSign;
};

// ===========================
// TEXT FORMATTING
// ===========================

/**
 * Format vehicle display name consistently
 */
export const formatVehicleName = (
  year: string | number,
  make: string,
  model: string,
  vin?: string
): string => {
  const baseString = `${year} ${make.toUpperCase()} ${model.toUpperCase()}`;
  return vin ? `${baseString} (${vin})` : baseString;
};

/**
 * Generate padded unit numbers
 */
export const generateUnitNumber = (index: number): string => {
  return (index + 1).toString().padStart(2, '0');
};

// ===========================
// DATE FORMATTING
// ===========================

/**
 * Create consistent page footer data
 */
export const createFooterData = (
  policyStartDate: string,
  policyNumber: string,
  pageNumber: string,
  timestamp?: string
) => {
  // Use timestamp if available, otherwise use policy start date
  const dateToFormat = timestamp || policyStartDate;
  const dateFormatted = getFormattedDate(dateToFormat, 'MM.dd.yyyy');

  return {
    left: `${dateFormatted} | #${policyNumber.toUpperCase()}`,
    right: 'nirvanatech.com',
    pageNumber: pageNumber.padStart(2, '0'),
  };
};
