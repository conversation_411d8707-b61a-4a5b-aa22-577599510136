package manual_forms

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/pdffill/fields"
	"nirvanatech.com/nirvana/pdffill/requests/fleet/manual_forms/MST_TIC_Cover_GA_112023"
	"nirvanatech.com/nirvana/policy_common/forms_generator/forms/generated/fleet/auto_liability"
)

func genMSTTICCoverGA112023Req(formCode string, inputs ManualFormsInput) (*MST_TIC_Cover_GA_112023.Request, error) {
	if formCode != auto_liability.MSTTICCoverGA112023.Code {
		return nil, errors.Newf(
			"unexpected form code received, supported code %s",
			auto_liability.MSTTICCoverGA112023.Code,
		)
	}
	return &MST_TIC_Cover_GA_112023.Request{
		CompanyOnForm: fields.InsuranceCarrier(inputs.CompanyOnForm),
	}, nil
}
