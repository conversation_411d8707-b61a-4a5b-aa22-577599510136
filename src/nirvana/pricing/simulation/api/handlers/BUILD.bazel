load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "handlers",
    srcs = [
        "get_simulation_output_download_url.go",
        "get_simulation_runs.go",
        "get_simulation_state_models.go",
        "post_simulation_trigger.go",
    ],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/api/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/common",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/slice_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_models/pricing",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/external_client/file-upload-client",
        "//nirvana/infra/authz",
        "//nirvana/jobber/jtypes",
        "//nirvana/openapi-specs/api_server_simulation",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/simulation",
        "//nirvana/policy/enums",
        "//nirvana/pricing/simulation/db",
        "//nirvana/pricing/simulation/engine/common/files",
        "//nirvana/pricing/simulation/enums",
        "//nirvana/pricing/simulation/jobber",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator/messages",
        "//nirvana/pricing/simulation/models",
        "//nirvana/rating/models/models_release",
        "//nirvana/rating/rtypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_sqlboiler_v4//queries/qm",
        "@org_uber_go_fx//:fx",
    ],
)
