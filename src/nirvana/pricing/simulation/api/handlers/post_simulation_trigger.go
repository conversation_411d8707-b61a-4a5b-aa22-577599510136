package handlers

import (
	"bytes"
	"context"
	"encoding/csv"
	"io"
	"net/http"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/api-server/common"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	file_upload_client "nirvanatech.com/nirvana/external_client/file-upload-client"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/jobber/jtypes"
	common_components "nirvanatech.com/nirvana/openapi-specs/components/common"
	simulation_components "nirvanatech.com/nirvana/openapi-specs/components/simulation"
	"nirvanatech.com/nirvana/policy/enums"
	simulation_common_files "nirvanatech.com/nirvana/pricing/simulation/engine/common/files"
	"nirvanatech.com/nirvana/pricing/simulation/jobber"
	"nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator"
	"nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator/messages"
)

type HandlePostSimulationTriggerDeps struct {
	fx.In

	JobberClient  jobber.Client
	PolicyWrapper policy.DataWrapper
	S3Client      s3_utils.Client
	ImplerClient  file_upload_client.Client `name:"impler"`
}

// HandlePostSimulationTrigger handles the POST /simulation/trigger endpoint.
// Currently, it only works for Fleet policies.
func HandlePostSimulationTrigger(
	ctx context.Context,
	deps *HandlePostSimulationTriggerDeps,
	request *simulation_components.PostSimulationTriggerRequest,
) *common.HttpHandlerError {
	simulationRunID := uuid.New()

	if err := validatePostSimulationTriggerRequest(request); err != nil {
		return common.NewHttpErrorf(
			http.StatusBadRequest,
			"invalid request: %v",
			err,
		)
	}

	var modificationsFileKey string
	if request.ModificationsFileId != nil && *request.ModificationsFileId != "" {
		fileKey, err := uploadModificationsFileToS3(ctx, deps.ImplerClient, deps.S3Client, simulationRunID, *request.ModificationsFileId)
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusInternalServerError,
				"failed to upload modifications file to S3: %v",
				err,
			)
		}
		modificationsFileKey = fileKey
	}

	var simulationPairs []messages.SimulationPair
	if request.ModelKeyPairsFileId != nil && *request.ModelKeyPairsFileId != "" {
		content, err := deps.ImplerClient.DownloadFile(*request.ModelKeyPairsFileId)
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusInternalServerError,
				"failed to download file from Impler (ID: %s): %v",
				*request.ModelKeyPairsFileId,
				err,
			)
		}

		simulationPairs, err = parseModelKeyPairsFile(content)
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusBadRequest,
				"failed to parse model key pairs file: %v",
				err,
			)
		}
	} else {
		// Existing logic when UsState, ModelKey, and Statuses are provided
		usState, err := us_states.StrToUSState(string(*request.UsState))
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusBadRequest,
				"failed to convert US state: %v",
				err,
			)
		}

		policyStates, err := slice_utils.MapErr(
			*request.Statuses,
			func(status common_components.PolicyStatus) (enums.PolicyState, error) {
				return enums.TransformOapiPolicyStatusToPolicyState(status)
			},
		)
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusBadRequest,
				"failed to convert policy status: %v",
				err,
			)
		}

		submissionIDs, err := getSubmissionIDsFromPolicies(ctx, deps.PolicyWrapper, usState, policyStates)
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusInternalServerError,
				"failed to get active policies submission IDs: %v",
				err,
			)
		}

		rateMLModelKey, err := messages.ModelKeyFromString(*request.ModelKey)
		if err != nil {
			return common.NewHttpErrorf(
				http.StatusBadRequest,
				"failed to parse model key: %v",
				err,
			)
		}

		simulationPairs = slice_utils.Map(
			submissionIDs,
			func(submissionID uuid.UUID) messages.SimulationPair {
				return messages.SimulationPair{
					SubmissionID: submissionID,
					ModelKey:     *rateMLModelKey,
				}
			},
		)
	}

	enableMVRFetching := false
	if request.EnableMVRFetching != nil {
		enableMVRFetching = *request.EnableMVRFetching
	}

	log.Info(ctx, "Sending simulation pairs to simulation orchestrator", log.Int("count", len(simulationPairs)))
	user := authz.UserFromContext(ctx)
	message := &messages.SimulationOrchestratorMessage{
		SimulationID:              simulationRunID,
		SimulationPairs:           simulationPairs,
		CreatedBy:                 user.ID,
		ModificationInputsFileKey: modificationsFileKey,
		EnableMVRFetching:         enableMVRFetching,
	}
	jobRunId, err := deps.JobberClient.AddJobRun(
		ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			message,
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return common.NewHttpErrorf(
			http.StatusInternalServerError,
			"failed to send message to simulation jobber: %v",
			err,
		)
	}
	log.Info(ctx, "simulation run added to jobber", log.String("jobRunId", jobRunId.String()))

	return nil
}

func validatePostSimulationTriggerRequest(request *simulation_components.PostSimulationTriggerRequest) error {
	if request.ModelKeyPairsFileId != nil && *request.ModelKeyPairsFileId != "" {
		return nil
	}

	if request.UsState == nil || request.ModelKey == nil || request.Statuses == nil || len(*request.Statuses) == 0 {
		return errors.New("UsState, ModelKey, and Statuses are required when ModelKeyPairsFileId is not provided")
	}

	return nil
}

func getSubmissionIDsFromPolicies(
	ctx context.Context,
	policyWrapper policy.DataWrapper,
	usState us_states.USState,
	policyStates []enums.PolicyState,
) ([]uuid.UUID, error) {
	policies, err := policyWrapper.GetAllPolicies(
		ctx,
		&policy.GetRequest{
			Filters: []policy.Filter{
				policy.SkipTestAgencies,
				policy.ProgramTypeIs(policy_enums.ProgramTypeFleet),
				policy.StateOneOf(policyStates...),
				policy.PolicyUSStateIs(usState.ToCode()),
			},
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get active policies")
	}

	submissionIDs := make(map[uuid.UUID]struct{})
	for _, pol := range policies {
		submissionIDs[pol.SubmissionId] = struct{}{}
	}

	return map_utils.Keys(submissionIDs), nil
}

func parseModelKeyPairsFile(fileContent []byte) ([]messages.SimulationPair, error) {
	if len(fileContent) == 0 {
		return nil, errors.New("model key pairs file content is empty")
	}

	pairs := []messages.SimulationPair{}

	reader := csv.NewReader(bytes.NewReader(fileContent))
	records, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse CSV file")
	}

	startIdx := 1 // Skip the header row
	for i := startIdx; i < len(records); i++ {
		record := records[i]
		if len(record) < 2 {
			return nil, errors.Newf("invalid record at line %d: expected at least 2 columns", i+1)
		}

		submissionID, err := uuid.Parse(record[0])
		if err != nil {
			return nil, errors.Wrapf(err, "invalid submission ID at line %d", i+1)
		}

		modelKey, err := messages.ModelKeyFromString(record[1])
		if err != nil {
			return nil, errors.Wrapf(err, "invalid model key at line %d", i+1)
		}

		pairs = append(pairs, messages.SimulationPair{
			SubmissionID: submissionID,
			ModelKey:     *modelKey,
		})
	}

	return pairs, nil
}

func uploadModificationsFileToS3(
	ctx context.Context,
	implerClient file_upload_client.Client,
	s3Client s3_utils.Client,
	simulationID uuid.UUID,
	modificationsFileID string,
) (string, error) {
	modificationsFileBytes, err := implerClient.DownloadFile(modificationsFileID)
	if err != nil {
		return "", errors.Wrap(err, "failed to download modifications file from Impler")
	}
	if len(modificationsFileBytes) == 0 {
		return "", errors.New("modifications file downloaded from Impler is empty")
	}

	reader := csv.NewReader(bytes.NewReader(modificationsFileBytes))

	// Read and discard the header row
	if _, err := reader.Read(); err != nil {
		if err == io.EOF {
			return "", errors.New("modifications file is empty (no header found)")
		}
		return "", errors.Wrap(err, "failed to read header row from modifications CSV")
	}

	var modifications []simulation_common_files.FileModificationRow
	rowNum := 2 // Start at 2 since we've read the header (row 1)
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", errors.Wrapf(err, "failed to read CSV row %d", rowNum)
		}

		// Parse submission ID
		submissionID, err := uuid.Parse(record[0])
		if err != nil {
			return "", errors.Newf("invalid submission ID in row %d: %v. Error: %v", rowNum, record, err)
		}

		// Validate modification type
		if record[1] == "" {
			return "", errors.Newf("empty modification type in row %d: %v", rowNum, record)
		}

		// Validate modifier value
		if record[2] == "" {
			return "", errors.Newf("empty modifier value in row %d: %v", rowNum, record)
		}

		// Create modification row
		modification := simulation_common_files.FileModificationRow{
			SubmissionID:      submissionID,
			ModificationType:  record[1],
			ModificationValue: record[2],
		}
		modifications = append(modifications, modification)
		rowNum++
	}

	// Validate that we have at least one modification
	if len(modifications) == 0 {
		return "", errors.New("no modifications found in file")
	}

	fileKey, err := simulation_common_files.UploadModificationsFile(ctx, s3Client, simulationID, modifications)
	if err != nil {
		return "", errors.Wrap(err, "failed to upload modifications file to S3")
	}

	return fileKey, nil
}
