load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "tests_test",
    srcs = [
        "get_simulation_output_download_url_test.go",
        "get_simulation_runs_test.go",
        "get_simulation_state_models_test.go",
        "post_simulation_trigger_test.go",
    ],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models/pricing",
        "//nirvana/external_client/file-upload-client",
        "//nirvana/infra/fx/testfixtures/api_server_fixture",
        "//nirvana/infra/fx/testfixtures/fixture_utils",
        "//nirvana/infra/fx/testfixtures/users_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/openapi-specs/api_server_simulation",
        "//nirvana/openapi-specs/components/common",
        "//nirvana/openapi-specs/components/simulation",
        "//nirvana/pricing/simulation/engine/common/files",
        "//nirvana/pricing/simulation/enums",
        "//nirvana/pricing/simulation/jobber/imports",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@com_github_volatiletech_null_v8//:null",
        "@com_github_volatiletech_sqlboiler_v4//boil",
        "@org_uber_go_fx//:fx",
    ],
)
