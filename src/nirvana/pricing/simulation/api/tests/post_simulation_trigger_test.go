package tests

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	file_upload_client "nirvanatech.com/nirvana/external_client/file-upload-client"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/api_server_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/users_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	common_components "nirvanatech.com/nirvana/openapi-specs/components/common"
	simulation_components "nirvanatech.com/nirvana/openapi-specs/components/simulation"
	_ "nirvanatech.com/nirvana/pricing/simulation/jobber/imports"
)

func TestPostSimulationTrigger(t *testing.T) {
	var env struct {
		fx.In

		ApiServer      api_server_fixture.ApiServer
		UsersFixture   *users_fixture.UsersFixture
		FlatFileClient file_upload_client.Client `name:"flatfile"`
	}

	defer testloader.RequireStart(t, &env).RequireStop()

	t.Run("should return 401 when unauthenticated", func(t *testing.T) {
		simulationClient := env.ApiServer.SimulationClient(nil)
		response, err := simulationClient.GetSimulationRuns(context.Background())
		require.NoError(t, err)
		require.Equal(t, 401, response.StatusCode)
	})

	t.Run("should return 403 when user is not superadmin", func(t *testing.T) {
		// selecting any user except superuser
		simulationClient := env.ApiServer.SimulationClient(&env.UsersFixture.AgencyViewer)
		response, err := simulationClient.GetSimulationRuns(context.Background())
		require.NoError(t, err)
		require.Equal(t, 403, response.StatusCode)
	})

	authorizedClient := env.ApiServer.SimulationClient(&env.UsersFixture.Superuser)
	t.Run("should return 400 when US state is not valid", func(t *testing.T) {
		request := simulation_components.PostSimulationTriggerRequest{
			ModelKey: pointer_utils.ToPointer("sentry-IL-v0.0.1"),
			UsState:  pointer_utils.ToPointer(common_components.IL),
		}
		response, err := authorizedClient.PostSimulationTrigger(
			context.Background(),
			request,
		)
		require.NoError(t, err)
		require.Equal(t, 400, response.StatusCode)
	})

	t.Run("should return 400 when ModelKey is not valid", func(t *testing.T) {
		request := simulation_components.PostSimulationTriggerRequest{
			ModelKey: pointer_utils.ToPointer("Invalid"),
			UsState:  pointer_utils.ToPointer(common_components.OH),
		}
		response, err := authorizedClient.PostSimulationTrigger(
			context.Background(),
			request,
		)
		require.NoError(t, err)
		require.Equal(t, 400, response.StatusCode)
	})

	// Test cases for missing required fields when ModelKeyPairsFileId is not provided
	baseValidRequest := func() simulation_components.PostSimulationTriggerRequest {
		return simulation_components.PostSimulationTriggerRequest{
			ModelKey: pointer_utils.ToPointer("sentry-OH-v0.8.1"),
			UsState:  pointer_utils.ToPointer(common_components.OH),
			Statuses: pointer_utils.ToPointer([]common_components.PolicyStatus{
				common_components.PolicyStatusActive,
			}),
		}
	}

	t.Run("should return 400 when ModelKeyPairsFileId is nil and UsState is missing", func(t *testing.T) {
		request := baseValidRequest()
		request.UsState = nil
		response, err := authorizedClient.PostSimulationTrigger(context.Background(), request)
		require.NoError(t, err)
		require.Equal(t, 400, response.StatusCode)
	})

	t.Run("should return 400 when ModelKeyPairsFileId is nil and ModelKey is missing", func(t *testing.T) {
		request := baseValidRequest()
		request.ModelKey = nil
		response, err := authorizedClient.PostSimulationTrigger(context.Background(), request)
		require.NoError(t, err)
		require.Equal(t, 400, response.StatusCode)
	})

	t.Run("should return 400 when ModelKeyPairsFileId is nil and Statuses are missing", func(t *testing.T) {
		request := baseValidRequest()
		request.Statuses = nil
		response, err := authorizedClient.PostSimulationTrigger(context.Background(), request)
		require.NoError(t, err)
		require.Equal(t, 400, response.StatusCode)
	})

	t.Run("should return 400 when ModelKeyPairsFileId is nil and Statuses are empty", func(t *testing.T) {
		request := baseValidRequest()
		request.Statuses = pointer_utils.ToPointer([]common_components.PolicyStatus{})
		response, err := authorizedClient.PostSimulationTrigger(context.Background(), request)
		require.NoError(t, err)
		require.Equal(t, 400, response.StatusCode)
	})

	t.Run("should return 201 when trigger has been successful", func(t *testing.T) {
		request := simulation_components.PostSimulationTriggerRequest{
			ModelKey: pointer_utils.ToPointer("sentry-OH-v0.8.1"),
			UsState:  pointer_utils.ToPointer(common_components.OH),
			Statuses: pointer_utils.ToPointer([]common_components.PolicyStatus{
				common_components.PolicyStatusActive,
			}),
		}
		response, err := authorizedClient.PostSimulationTrigger(
			context.Background(),
			request,
		)
		require.NoError(t, err)
		require.Equal(t, 201, response.StatusCode)
	})

	t.Run("should return 201 when ModelKeyPairsFileId is provided", func(t *testing.T) {
		mockClient, ok := env.FlatFileClient.(*file_upload_client.InMemoryClient)
		require.True(t, ok, "Failed to cast FlatFileClient to *fileuploadclient.InMemoryClient")

		handleID := "test-handle-id-123"
		submissionID1 := uuid.New()
		submissionID2 := uuid.New()
		mockCSVData := []byte(fmt.Sprintf("SubmissionID,ModelKey\n%s,sentry-FL-v0.0.1\n%s,sentry-IL-v0.8.1", submissionID1, submissionID2))

		mockClient.SetMockFile(handleID, mockCSVData, nil)
		defer mockClient.SetMockFile(handleID, nil, nil)

		request := simulation_components.PostSimulationTriggerRequest{
			ModelKeyPairsFileId: pointer_utils.ToPointer(handleID),
		}
		response, err := authorizedClient.PostSimulationTrigger(
			context.Background(),
			request,
		)
		require.NoError(t, err)
		require.Equal(t, 201, response.StatusCode)
	})
}
