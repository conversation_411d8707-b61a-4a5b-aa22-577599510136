load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "run_lib",
    srcs = [
        "command.go",
        "main.go",
    ],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/cmd/run",
    visibility = ["//visibility:private"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/policy",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/appfx/cobrafx",
        "//nirvana/jobber/jtypes",
        "//nirvana/policy/enums",
        "//nirvana/pricing/simulation/db",
        "//nirvana/pricing/simulation/engine/common/files",
        "//nirvana/pricing/simulation/jobber",
        "//nirvana/pricing/simulation/jobber/imports",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator/messages",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_spf13_cobra//:cobra",
        "@org_uber_go_fx//:fx",
    ],
)

go_binary(
    name = "run",
    embed = [":run_lib"],
    visibility = ["//visibility:public"],
)
