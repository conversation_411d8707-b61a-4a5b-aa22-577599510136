package main

import (
	"context"
	"encoding/csv"
	"io"
	"os"
	"reflect"
	"strings"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/spf13/cobra"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/auth"
	"nirvanatech.com/nirvana/db-api/db_wrappers/policy"
	policy_enums "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/infra/fx/appfx/cobrafx"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/policy/enums"
	"nirvanatech.com/nirvana/pricing/simulation/db"
	simulation_common_files "nirvanatech.com/nirvana/pricing/simulation/engine/common/files"
	jobber "nirvanatech.com/nirvana/pricing/simulation/jobber"
	_ "nirvanatech.com/nirvana/pricing/simulation/jobber/imports"
	"nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator"
	"nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator/messages"
)

var (
	USState             string
	ModelKey            string
	SimulationPairsFile string
	OutputFilePath      string
	ModificationsFile   string
	UserID              string
	EnableMVRFetching   bool
)

type commandEnv struct {
	fx.In

	Config        *config.Config
	DBWrapper     db.Wrapper
	PolicyWrapper policy.DataWrapper
	JobberClient  jobber.Client
	S3Client      s3_utils.Client
	AuthWrapper   auth.DataWrapper
}

var command = cobrafx.NewCLI(
	&cobra.Command{
		Use:   "run",
		Short: "Trigger a pricing simulation run",
		Long: "The run command triggers pricing simulation runs either:\n" +
			"1. Over all existing fleet policies for the US state provided (using --us_state and --model_key)\n" +
			"2. For specific submission ID and model key pairs from a CSV file (using --pairs_file)\n\n" +
			"Note: The --user_id flag is required to identify who is running the simulation.",
		Example: "bazel run //nirvana/pricing/simulation/cmd/run:run -- --user_id <uuid> [--us_state <state> --model_key <model_key> | --pairs_file <path>] [--modifications_file <path>]",
	},
	execute,
)

func execute(cmd *cobra.Command, _ []string, env commandEnv) error {
	ctx := cmd.Context()

	// Verify user exists
	userID, err := uuid.Parse(UserID)
	if err != nil {
		return errors.Wrap(err, "invalid user ID format")
	}

	user, err := env.AuthWrapper.FetchUserInfo(ctx, userID)
	if err != nil {
		return errors.Wrap(err, "failed to get user")
	}
	if user == nil {
		return errors.Newf("user with ID %s not found", UserID)
	}

	var simulationPairs []messages.SimulationPair
	// Validate input parameters
	switch {
	case SimulationPairsFile != "" && (USState != "" || ModelKey != ""):
		return errors.New("cannot specify both pairs_file and us_state/model_key")
	case SimulationPairsFile == "" && (USState == "" || ModelKey == ""):
		return errors.New("must specify either pairs_file or both us_state and model_key")
	}

	// Get simulation pairs based on input parameters
	if SimulationPairsFile != "" {
		simulationPairs, err = readSimulationPairs(SimulationPairsFile)
		if err != nil {
			return errors.Wrap(err, "failed to read simulation pairs")
		}
	} else {
		simulationPairs, err = getStateWidePairs(ctx, env.PolicyWrapper, ModelKey)
		if err != nil {
			return errors.Wrap(err, "failed to get state-wide pairs")
		}
	}

	simulationID := uuid.New()
	jobMessage := &messages.SimulationOrchestratorMessage{
		SimulationID:      simulationID,
		SimulationPairs:   simulationPairs,
		CreatedBy:         user.ID,
		EnableMVRFetching: EnableMVRFetching,
	}

	if ModificationsFile != "" {
		modificationsFileKey, err := ingestModificationsFile(ctx, env.S3Client, ModificationsFile, simulationID)
		if err != nil {
			return errors.Wrap(err, "failed to ingest modifications file")
		}
		jobMessage.ModificationInputsFileKey = modificationsFileKey
	}

	jobRunId, err := env.JobberClient.AddJobRun(
		ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			jobMessage,
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return errors.Wrap(err, "failed to add job run")
	}
	log.Info(ctx, "Job run added", log.String("JobRunId", jobRunId.String()))

	err = env.JobberClient.WaitForJobRunCompletion(ctx, jobRunId)
	if err != nil {
		return errors.Wrap(err, "failed to wait for job run completion")
	}

	if OutputFilePath != "" {
		err = downloadResults(ctx, env.S3Client, simulationID)
		if err != nil {
			return errors.Wrap(err, "failed to download results")
		}
	}

	log.Info(ctx, "Finished all simulation runs")

	return nil
}

func readSimulationPairs(filepath string) ([]messages.SimulationPair, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, errors.Wrap(err, "failed to open pairs file")
	}
	defer func(file *os.File) {
		fErr := file.Close()
		if fErr != nil {
			log.Error(context.Background(), "failed to close pairs file", log.Err(fErr))
		}
	}(file)

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "failed to read CSV")
	}

	var pairs []messages.SimulationPair
	for i, record := range records {
		if i == 0 { // Skip header row
			continue
		}
		if len(record) != 2 {
			return nil, errors.Newf("invalid record format at line %d", i+1)
		}

		submissionID, err := uuid.Parse(strings.TrimSpace(record[0]))
		if err != nil {
			return nil, errors.Wrapf(err, "invalid submission ID at line %d", i+1)
		}

		modelKey, err := messages.ModelKeyFromString(strings.TrimSpace(record[1]))
		if err != nil {
			return nil, errors.Wrapf(err, "invalid model key at line %d", i+1)
		}

		pairs = append(pairs, messages.SimulationPair{
			SubmissionID: submissionID,
			ModelKey:     *modelKey,
		})
	}

	return pairs, nil
}

func getStateWidePairs(ctx context.Context, policyWrapper policy.DataWrapper, modelKeyStr string) ([]messages.SimulationPair, error) {
	modelKey, err := messages.ModelKeyFromString(modelKeyStr)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse model key")
	}

	usState, err := us_states.StrToUSState(modelKey.USState)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse model key")
	}

	submissionIDs, err := getActivePoliciesSubmissionIDs(ctx, policyWrapper, usState)
	if err != nil {
		return nil, err
	}

	pairs := make([]messages.SimulationPair, len(submissionIDs))
	for i, id := range submissionIDs {
		pairs[i] = messages.SimulationPair{
			SubmissionID: id,
			ModelKey:     *modelKey,
		}
	}

	return pairs, nil
}

func getActivePoliciesSubmissionIDs(
	ctx context.Context,
	policyWrapper policy.DataWrapper,
	usState us_states.USState,
) ([]uuid.UUID, error) {
	policies, err := policyWrapper.GetAllPolicies(
		ctx,
		&policy.GetRequest{
			Filters: []policy.Filter{
				policy.SkipTestAgencies,
				// TODO(@cirey1): remove this once we have implemented multi-program simulation
				policy.ProgramTypeIs(policy_enums.ProgramTypeFleet),
				policy.StateOneOf(enums.PolicyStateActive),
				policy.PolicyUSStateIs(usState.ToCode()),
			},
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get active policies")
	}

	submissionIDs := make(map[uuid.UUID]struct{})
	for _, pol := range policies {
		submissionIDs[pol.SubmissionId] = struct{}{}
	}

	return map_utils.Keys(submissionIDs), nil
}

func downloadResults(ctx context.Context, s3Client s3_utils.Client, simulationID uuid.UUID) error {
	file, err := os.Create(OutputFilePath)
	if err != nil {
		return errors.Wrapf(err, "failed to create local file: %s", OutputFilePath)
	}

	// Defer closing the file and capture potential close error.
	var closeFileErr error
	defer func() {
		err := file.Close()
		if err != nil {
			closeFileErr = errors.Wrapf(err, "failed to close output file: %s", OutputFilePath)
			log.Error(ctx, "Deferred file close failed", log.Err(closeFileErr), log.String("LocalFile", OutputFilePath))
		}
	}()

	downloadBuff, err := simulation_common_files.DownloadSimulationOutputFile(ctx, s3Client, simulationID)
	if err != nil {
		return errors.Wrap(err, "failed to download simulation output file")
	}

	_, err = file.Write(downloadBuff.Bytes())
	if err != nil {
		return errors.Wrapf(err, "failed to write combined results to local file: %s", OutputFilePath)
	}

	if closeFileErr != nil {
		return closeFileErr
	}

	log.Info(ctx, "Successfully downloaded and combined simulation results",
		log.String("LocalFile", OutputFilePath),
	)

	return nil
}

func ingestModificationsFile(ctx context.Context, s3Client s3_utils.Client, localFilePath string, simulationID uuid.UUID) (string, error) {
	// Read and parse the CSV file
	file, err := os.Open(localFilePath)
	if err != nil {
		return "", errors.Wrap(err, "failed to open modifications file")
	}
	defer file.Close()

	// Create CSV reader
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = 3 // We expect 3 fields: SubmissionID, ModificationType, ModifierValue

	// Read header
	header, err := reader.Read()
	if err != nil {
		return "", errors.Wrap(err, "failed to read CSV header")
	}

	// Validate header
	expectedHeader := []string{"SubmissionID", "ModificationType", "ModifierValue"}
	if !reflect.DeepEqual(header, expectedHeader) {
		return "", errors.Newf("invalid header. Expected %v, got %v", expectedHeader, header)
	}

	// Read and parse rows
	var modifications []simulation_common_files.FileModificationRow
	rowNum := 2 // Start at 2 since we've read the header (row 1)
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return "", errors.Wrapf(err, "failed to read CSV row %d", rowNum)
		}

		// Parse submission ID
		submissionID, err := uuid.Parse(record[0])
		if err != nil {
			return "", errors.Newf("invalid submission ID in row %d: %v. Error: %v", rowNum, record, err)
		}

		// Validate modification type
		if record[1] == "" {
			return "", errors.Newf("empty modification type in row %d: %v", rowNum, record)
		}

		// Validate modifier value
		if record[2] == "" {
			return "", errors.Newf("empty modifier value in row %d: %v", rowNum, record)
		}

		// Create modification row
		modification := simulation_common_files.FileModificationRow{
			SubmissionID:      submissionID,
			ModificationType:  record[1],
			ModificationValue: record[2],
		}
		modifications = append(modifications, modification)
		rowNum++
	}

	// Validate that we have at least one modification
	if len(modifications) == 0 {
		return "", errors.New("no modifications found in file")
	}

	fileKey, err := simulation_common_files.UploadModificationsFile(ctx, s3Client, simulationID, modifications)
	if err != nil {
		return "", errors.Wrap(err, "failed to upload modifications file to S3")
	}

	return fileKey, nil
}

func init() {
	command.Flags().StringVarP(&USState, "us_state", "s", "", "State to trigger the run for")
	command.Flags().StringVarP(&SimulationPairsFile, "simulation_pairs_file", "p", "", "CSV file containing submission ID and model key pairs")
	command.Flags().StringVarP(&ModelKey, "model_key", "m", "", "Model to trigger the run for (when using us_state)")
	command.Flags().StringVarP(&OutputFilePath, "output_file_path", "o", "", "Path to save the output file")
	command.Flags().StringVarP(&ModificationsFile, "modifications_file", "", "", "CSV file containing modifications to apply to submissions")
	command.Flags().StringVarP(&UserID, "user_id", "u", "", "User ID of the person running the simulation (required)")
	command.Flags().BoolVarP(&EnableMVRFetching, "enable-mvr-fetching", "", false, "Enable MVR fetching during simulation")
	_ = command.MarkFlagRequired("user_id")
}
