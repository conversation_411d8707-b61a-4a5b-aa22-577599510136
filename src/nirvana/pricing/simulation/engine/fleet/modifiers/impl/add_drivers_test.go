package impl

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/pricing/simulation/engine/fleet/types"
)

func Test_NewAddDriversModifierFromString(t *testing.T) {
	testCases := []struct {
		name          string
		modifierValue string
		expected      *AddDriversModifier
		expectError   bool
		errorContains string
	}{
		{
			name:          "valid multiple drivers",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,5.5,<PERSON>,<PERSON>|XYZ789,TX,2019-03-10,1990-12-05,3.2,<PERSON>,<PERSON>",
			expected: &AddDriversModifier{
				Drivers: []*application.DriverListRecord{
					application.NewDriverListRecord(
						"ABC123",
						"CA",
						time.Date(2020, 1, 15, 0, 0, 0, 0, time.UTC),
						null.TimeFrom(time.Date(1985, 6, 20, 0, 0, 0, 0, time.UTC)),
						pointer_utils.ToPointer(float32(5.5)),
						null.StringFrom("<PERSON>"),
						null.StringFrom("Doe"),
					),
					application.NewDriverListRecord(
						"XYZ789",
						"TX",
						time.Date(2019, 3, 10, 0, 0, 0, 0, time.UTC),
						null.TimeFrom(time.Date(1990, 12, 5, 0, 0, 0, 0, time.UTC)),
						pointer_utils.ToPointer(float32(3.2)),
						null.StringFrom("Jane"),
						null.StringFrom("Smith"),
					),
				},
			},
			expectError: false,
		},
		{
			name:          "valid driver with spaces",
			modifierValue: "  ABC123  ,CA  ,  2020-01-15,  1985-06-20  ,  5.5 ,  John  ,  Doe  ",
			expected: &AddDriversModifier{
				Drivers: []*application.DriverListRecord{
					application.NewDriverListRecord(
						"ABC123",
						"CA",
						time.Date(2020, 1, 15, 0, 0, 0, 0, time.UTC),
						null.TimeFrom(time.Date(1985, 6, 20, 0, 0, 0, 0, time.UTC)),
						pointer_utils.ToPointer(float32(5.5)),
						null.StringFrom("John"),
						null.StringFrom("Doe"),
					),
				},
			},
			expectError: false,
		},
		{
			name:          "empty string results in empty drivers",
			modifierValue: "",
			expected: &AddDriversModifier{
				Drivers: []*application.DriverListRecord{},
			},
			expectError: false,
		},
		// Required field tests
		{
			name:          "missing driver license number",
			modifierValue: ",CA,2020-01-15,1985-06-20,5.5,John,Doe",
			expectError:   true,
			errorContains: "driver license number is required",
		},
		{
			name:          "missing state",
			modifierValue: "ABC123,,2020-01-15,1985-06-20,5.5,John,Doe",
			expectError:   true,
			errorContains: "state is required",
		},
		{
			name:          "missing first name",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,5.5,,Doe",
			expectError:   true,
			errorContains: "first name is required",
		},
		{
			name:          "missing last name",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,5.5,John,",
			expectError:   true,
			errorContains: "last name is required",
		},
		{
			name:          "invalid driver format - too few parts",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,5.5,John",
			expectError:   true,
			errorContains: "invalid driver format: expected 7 parts separated by",
		},
		{
			name:          "invalid driver format - too many parts",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,5.5,John,Doe,Extra",
			expectError:   true,
			errorContains: "invalid driver format: expected 7 parts separated by",
		},
		{
			name:          "invalid date hired format",
			modifierValue: "ABC123,CA,invalid-date,1985-06-20,5.5,John,Doe",
			expectError:   true,
			errorContains: "failed to parse date hired",
		},
		{
			name:          "invalid date of birth format",
			modifierValue: "ABC123,CA,2020-01-15,invalid-date,5.5,John,Doe",
			expectError:   true,
			errorContains: "failed to parse date of birth",
		},
		{
			name:          "invalid years of experience format",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,invalid,John,Doe",
			expectError:   true,
			errorContains: "failed to parse years of experience",
		},
		{
			name:          "invalid driver in multiple drivers",
			modifierValue: "ABC123,CA,2020-01-15,1985-06-20,5.5,John,Doe|XYZ789,TX,invalid-date,1990-12-05,3.2,Jane,Smith",
			expectError:   true,
			errorContains: "failed to parse driver list record at index 1",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			modifier, err := NewAddDriversModifierFromString(tc.modifierValue)

			if tc.expectError {
				require.Error(t, err)
				if tc.errorContains != "" {
					require.Contains(t, err.Error(), tc.errorContains)
				}
				require.Nil(t, modifier)
			} else {
				require.NoError(t, err)
				require.NotNil(t, modifier)
				// Type assert to get the concrete type
				addDriversModifier, ok := modifier.(*AddDriversModifier)
				require.True(t, ok, "Expected modifier to be of type *AddDriversModifier")
				require.Equal(t, len(tc.expected.Drivers), len(addDriversModifier.Drivers))

				for i, expectedDriver := range tc.expected.Drivers {
					actualDriver := addDriversModifier.Drivers[i]
					require.Equal(t, expectedDriver.DriverLicenseNumber, actualDriver.DriverLicenseNumber)
					require.Equal(t, expectedDriver.UsState, actualDriver.UsState)
					require.Equal(t, expectedDriver.DateHired, actualDriver.DateHired)
					require.Equal(t, expectedDriver.DateOfBirth, actualDriver.DateOfBirth)
					require.Equal(t, expectedDriver.YearsOfExperience, actualDriver.YearsOfExperience)
					require.Equal(t, expectedDriver.FirstName, actualDriver.FirstName)
					require.Equal(t, expectedDriver.LastName, actualDriver.LastName)
				}
			}
		})
	}
}

func Test_AddDriversModifier_Apply(t *testing.T) {
	testCases := []struct {
		name           string
		setupModifier  func(t *testing.T) *AddDriversModifier
		setupFunc      func(t *testing.T) *types.PricingInput
		expectError    bool
		errorContains  string
		validationFunc func(t *testing.T, initial, modified *types.PricingInput, modifier *AddDriversModifier)
	}{
		{
			name: "add drivers to existing drivers",
			setupModifier: func(t *testing.T) *AddDriversModifier {
				return &AddDriversModifier{
					Drivers: []*application.DriverListRecord{
						application.NewDriverListRecord(
							"NEW123",
							"NY",
							time.Date(2021, 5, 10, 0, 0, 0, 0, time.UTC),
							null.TimeFrom(time.Date(1988, 3, 15, 0, 0, 0, 0, time.UTC)),
							pointer_utils.ToPointer(float32(4.0)),
							null.StringFrom("Alice"),
							null.StringFrom("Johnson"),
						),
					},
				}
			},
			setupFunc: func(t *testing.T) *types.PricingInput {
				return &types.PricingInput{
					ModelInput: &application.ModelInput{
						DriversInfo: &application.DriversInfo{
							Drivers: []application.DriverListRecord{
								*application.NewDriverListRecord(
									"EXISTING123",
									"CA",
									time.Date(2020, 1, 15, 0, 0, 0, 0, time.UTC),
									null.TimeFrom(time.Date(1985, 6, 20, 0, 0, 0, 0, time.UTC)),
									pointer_utils.ToPointer(float32(5.5)),
									null.StringFrom("John"),
									null.StringFrom("Doe"),
								),
							},
						},
					},
				}
			},
			expectError: false,
			validationFunc: func(t *testing.T, initial, modified *types.PricingInput, modifier *AddDriversModifier) {
				require.Equal(t, 2, len(modified.ModelInput.DriversInfo.Drivers))

				// Verify existing driver is preserved
				existingDriver := modified.ModelInput.DriversInfo.Drivers[0]
				require.Equal(t, "EXISTING123", existingDriver.DriverLicenseNumber)
				require.Equal(t, "CA", existingDriver.UsState)

				// Verify new driver is added
				newDriver := modified.ModelInput.DriversInfo.Drivers[1]
				require.Equal(t, "NEW123", newDriver.DriverLicenseNumber)
				require.Equal(t, "NY", newDriver.UsState)
				require.Equal(t, "Alice", newDriver.FirstName.String)
				require.Equal(t, "Johnson", newDriver.LastName.String)
			},
		},
		{
			name: "add drivers when drivers info is nil",
			setupModifier: func(t *testing.T) *AddDriversModifier {
				return &AddDriversModifier{
					Drivers: []*application.DriverListRecord{
						application.NewDriverListRecord(
							"NEW123",
							"NY",
							time.Date(2021, 5, 10, 0, 0, 0, 0, time.UTC),
							null.TimeFrom(time.Date(1988, 3, 15, 0, 0, 0, 0, time.UTC)),
							pointer_utils.ToPointer(float32(4.0)),
							null.StringFrom("Alice"),
							null.StringFrom("Johnson"),
						),
					},
				}
			},
			setupFunc: func(t *testing.T) *types.PricingInput {
				return &types.PricingInput{
					ModelInput: &application.ModelInput{
						DriversInfo: nil,
					},
				}
			},
			expectError: false,
			validationFunc: func(t *testing.T, initial, modified *types.PricingInput, modifier *AddDriversModifier) {
				require.NotNil(t, modified.ModelInput.DriversInfo)
				require.Equal(t, 1, len(modified.ModelInput.DriversInfo.Drivers))

				newDriver := modified.ModelInput.DriversInfo.Drivers[0]
				require.Equal(t, "NEW123", newDriver.DriverLicenseNumber)
				require.Equal(t, "NY", newDriver.UsState)
				require.Equal(t, "Alice", newDriver.FirstName.String)
				require.Equal(t, "Johnson", newDriver.LastName.String)
			},
		},
		{
			name: "error when pricing input is nil",
			setupModifier: func(t *testing.T) *AddDriversModifier {
				return &AddDriversModifier{
					Drivers: []*application.DriverListRecord{
						application.NewDriverListRecord(
							"NEW123",
							"NY",
							time.Date(2021, 5, 10, 0, 0, 0, 0, time.UTC),
							null.TimeFrom(time.Date(1988, 3, 15, 0, 0, 0, 0, time.UTC)),
							pointer_utils.ToPointer(float32(4.0)),
							null.StringFrom("Alice"),
							null.StringFrom("Johnson"),
						),
					},
				}
			},
			setupFunc: func(t *testing.T) *types.PricingInput {
				return nil
			},
			expectError:   true,
			errorContains: "pricing input is nil",
		},
		{
			name: "error when model input is nil",
			setupModifier: func(t *testing.T) *AddDriversModifier {
				return &AddDriversModifier{
					Drivers: []*application.DriverListRecord{
						application.NewDriverListRecord(
							"NEW123",
							"NY",
							time.Date(2021, 5, 10, 0, 0, 0, 0, time.UTC),
							null.TimeFrom(time.Date(1988, 3, 15, 0, 0, 0, 0, time.UTC)),
							pointer_utils.ToPointer(float32(4.0)),
							null.StringFrom("Alice"),
							null.StringFrom("Johnson"),
						),
					},
				}
			},
			setupFunc: func(t *testing.T) *types.PricingInput {
				return &types.PricingInput{
					ModelInput: nil,
				}
			},
			expectError:   true,
			errorContains: "model input is nil",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			modifier := tc.setupModifier(t)
			pricingInput := tc.setupFunc(t)

			initialPricingInput := testonly_deepCopyPricingInput(pricingInput)

			err := modifier.Apply(pricingInput)

			if tc.expectError {
				require.Error(t, err)
				if tc.errorContains != "" {
					require.Contains(t, err.Error(), tc.errorContains)
				}
			} else {
				require.NoError(t, err)
				require.NotNil(t, pricingInput)
				if tc.validationFunc != nil {
					tc.validationFunc(t, initialPricingInput, pricingInput, modifier)
				}
			}
		})
	}
}
