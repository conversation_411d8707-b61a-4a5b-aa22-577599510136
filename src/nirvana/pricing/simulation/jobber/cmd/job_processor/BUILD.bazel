load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_library(
    name = "job_processor_lib",
    srcs = ["main.go"],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/jobber/cmd/job_processor",
    visibility = ["//visibility:private"],
    deps = [
        "//nirvana/common-go/aws_utils",
        "//nirvana/infra/config",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/appfx",
        "//nirvana/jobber/clusterops",
        "//nirvana/pricing/simulation/jobber",
        "//nirvana/pricing/simulation/jobber/imports",
        "@org_uber_go_fx//:fx",
    ],
)

go_binary(
    name = "job_processor",
    embed = [":job_processor_lib"],
    visibility = ["//visibility:public"],
)

go_test(
    name = "job_processor_test",
    srcs = ["config_test.go"],
    embed = [":job_processor_lib"],
    deps = [
        "//nirvana/infra/config",
        "//nirvana/infra/constants",
        "//nirvana/infra/fx/appfx",
        "@com_github_stretchr_testify//require",
    ],
)
