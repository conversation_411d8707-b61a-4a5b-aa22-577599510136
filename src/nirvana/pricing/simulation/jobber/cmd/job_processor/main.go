package main

import (
	"flag"
	"fmt"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/aws_utils"
	"nirvanatech.com/nirvana/infra/config"
	"nirvanatech.com/nirvana/infra/constants"
	"nirvanatech.com/nirvana/infra/fx/appfx"
	"nirvanatech.com/nirvana/jobber/clusterops"
	"nirvanatech.com/nirvana/pricing/simulation/jobber"
	_ "nirvanatech.com/nirvana/pricing/simulation/jobber/imports"
)

const (
	servicePort = 56227
)

var (
	host string
	port int
)

func main() {
	flag.StringVar(
		&host, "host", "",
		"Host on which to start listening. Defaults to listening on all interfaces"+
			"in dev and test environments and on container private ip in prod environment.",
	)
	flag.IntVar(
		&port, "port", servicePort, "Port on which to start grpc server",
	)

	cfg := config.LoadOnce()
	appfx.NewGRPCAppBuilderWithInjectedAddress(
		constants.SimulationJobProcessor, cfg.GetEnv(), nil, options(cfg)...,
	).Build().Run()
}

func options(cfg *config.Config) []fx.Option {
	options := []fx.Option{
		jobber.FXModule(cfg),
		clusterops.JobberServerFxOptions(cfg.JobberProcessors.Simulation),
	}

	if cfg.Env == config.Env_PROD {
		options = append(options, appfx.FxGrpcServerAddrFn(
			func(ip aws_utils.ContainerPrivateIP) (appfx.GRPCServerAddr, error) {
				return appfx.GRPCServerAddr(fmt.Sprintf("%s:%d", ip, port)), nil
			}))
	} else {
		options = append(options, appfx.FxGrpcServerAddr(
			appfx.GRPCServerAddr(fmt.Sprintf("%s:%d", host, port))),
		)
	}

	return options
}
