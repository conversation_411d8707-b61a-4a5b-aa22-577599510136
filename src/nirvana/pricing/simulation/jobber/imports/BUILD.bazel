load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "imports",
    srcs = ["imports.go"],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/jobber/imports",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/pricing/simulation/jobber",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator/impl",
        "@org_uber_go_fx//:fx",
    ],
)
