load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "impl",
    srcs = [
        "add_jobs.go",
        "persist_simulation_run_task.go",
        "simulation_orchestrator_job.go",
        "simulation_runner_job.go",
        "simulation_runner_task.go",
        "spawn_simulation_runner_tasks.go",
        "sync_simulation_runner_tasks.go",
    ],
    importpath = "nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator/impl",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/log",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_models/pricing",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/pricing/simulation/builders",
        "//nirvana/pricing/simulation/db",
        "//nirvana/pricing/simulation/engine/common/files",
        "//nirvana/pricing/simulation/engine/common/orchestrator",
        "//nirvana/pricing/simulation/engine/fleet/types",
        "//nirvana/pricing/simulation/enums",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator/messages",
        "//nirvana/rating/rtypes",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "impl_test",
    srcs = ["simulation_orchestrator_job_test.go"],
    deps = [
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/math_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/s3_utils",
        "//nirvana/common-go/test_utils/builders/application",
        "//nirvana/common-go/test_utils/builders/submission",
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/db-api/db_models/pricing",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/application/enums/old_enums",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/external_data_management/common",
        "//nirvana/external_data_management/context_management",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/data_processing/vin",
        "//nirvana/infra/fx/testfixtures/fmcsa_fixture",
        "//nirvana/infra/fx/testfixtures/lni_fixture",
        "//nirvana/infra/fx/testloader",
        "//nirvana/jobber/jtypes",
        "//nirvana/policy_common/constants",
        "//nirvana/pricing/simulation/db",
        "//nirvana/pricing/simulation/engine/common/files",
        "//nirvana/pricing/simulation/engine/fleet/types",
        "//nirvana/pricing/simulation/enums",
        "//nirvana/pricing/simulation/jobber",
        "//nirvana/pricing/simulation/jobber/imports",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator",
        "//nirvana/pricing/simulation/jobs/simulation_orchestrator/messages",
        "//nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/vehicles",
        "//nirvana/rating/rtypes",
        "//nirvana/rating/utils",
        "//nirvana/telematics",
        "@com_github_aws_aws_sdk_go//aws",
        "@com_github_aws_aws_sdk_go//service/s3",
        "@com_github_aws_aws_sdk_go//service/s3/s3manager",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//suite",
        "@com_github_volatiletech_null_v8//:null",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
        "@org_uber_go_mock//gomock",
    ],
)
