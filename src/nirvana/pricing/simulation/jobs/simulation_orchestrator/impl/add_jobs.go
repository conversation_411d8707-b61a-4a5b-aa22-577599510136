package impl

import (
	"go.uber.org/fx"
	"go.uber.org/multierr"

	"nirvanatech.com/nirvana/jobber/registry"
)

type deps struct {
	fx.In

	OrchestratorJobDeps simulationOrchestratorJobDeps
	RunnerJobDeps       simulationRunnerJobDeps
}

func AddJobsToRegistry[R registry.RegistryInterface](r R, deps deps) (combinedErr error) {
	simulationOrchestratorJob, err := newSimulationOrchestratorJob(
		&deps.OrchestratorJobDeps,
	)
	combinedErr = multierr.Append(combinedErr, err)

	simulationRunnerJob, err := newSimulationRunnerJob(
		&deps.RunnerJobDeps,
	)
	combinedErr = multierr.Append(combinedErr, err)

	return multierr.Combine(
		combinedErr,
		registry.AddJob(r.Registry(), simulationOrchestratorJob),
		registry.AddJob(r.Registry(), simulationRunnerJob),
	)
}
