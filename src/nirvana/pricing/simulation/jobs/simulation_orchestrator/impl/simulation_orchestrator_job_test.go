package impl_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/types/known/timestamppb"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/math_utils"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/s3_utils"
	application_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/application"
	submission_builder "nirvanatech.com/nirvana/common-go/test_utils/builders/submission"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_models/pricing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums/old_enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fmcsa"
	"nirvanatech.com/nirvana/external_data_management/common"
	"nirvanatech.com/nirvana/external_data_management/context_management"
	"nirvanatech.com/nirvana/external_data_management/data_fetching"
	"nirvanatech.com/nirvana/external_data_management/data_processing"
	"nirvanatech.com/nirvana/external_data_management/data_processing/vin"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/fmcsa_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/lni_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/policy_common/constants"
	"nirvanatech.com/nirvana/pricing/simulation/db"
	simulation_common_files "nirvanatech.com/nirvana/pricing/simulation/engine/common/files"
	"nirvanatech.com/nirvana/pricing/simulation/engine/fleet/types"
	simulation_enums "nirvanatech.com/nirvana/pricing/simulation/enums"
	"nirvanatech.com/nirvana/pricing/simulation/jobber"
	_ "nirvanatech.com/nirvana/pricing/simulation/jobber/imports"
	"nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator"
	"nirvanatech.com/nirvana/pricing/simulation/jobs/simulation_orchestrator/messages"
	rating "nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/vehicles"
	"nirvanatech.com/nirvana/rating/rtypes"
	"nirvanatech.com/nirvana/rating/utils"
	"nirvanatech.com/nirvana/telematics"
)

type simulationOrchestratorJobEnv struct {
	fx.In

	SimulationJobber   jobber.Client
	ApplicationWrapper application.DataWrapper
	FeatureFlagClient  feature_flag_lib.Client
	FMCSAWrapper       fmcsa.DataWrapper
	S3Client           s3_utils.Client
	ContextManager     context_management.ContextManager
	DBWrapper          db.Wrapper

	*fmcsa_fixture.FmcsaFixture
	*lni_fixture.VersionFixture
}

type SimulationOrchestratorJobSuite struct {
	suite.Suite

	fxApp *fxtest.App
	ctx   context.Context

	env               simulationOrchestratorJobEnv
	mockFetcherServer *data_fetching.MockFetcherServer

	submission application.SubmissionObject
}

func TestSimulationOrchestratorJobSuite(t *testing.T) {
	suite.Run(t, new(SimulationOrchestratorJobSuite))
}

func (s *SimulationOrchestratorJobSuite) SetupTest() {
	s.ctx = context.Background()
	ctrl := gomock.NewController(s.T())

	mockFetcherServer := data_fetching.NewMockFetcherServer(ctrl)
	newMockFetcherServer := func(actualServer data_fetching.FetcherServer) data_fetching.FetcherServer {
		mockFetcherServer.EXPECT().
			GetFMCSACensusInfoV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetFMCSACensusInfoV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetLatestObjectiveGradeV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetLatestObjectiveGradeV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetFMCSAViolationRecordsV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetVINDetailsV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetBIPDActiveOrPendingInsuranceV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetBIPDActiveOrPendingInsuranceV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetGrantedAuthorityHistoryV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetGrantedAuthorityHistoryV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetRatingTierRecordV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetRatingTierRecordV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetLatestObjectiveGradeV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetLatestObjectiveGradeV1).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetMVRAttractScoreV1(gomock.Any(), gomock.Any()).
			Return(
				&data_fetching.MVRAttractScoreV1{
					Score: 800,
				},
				nil,
			).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetVINDetailsV1(gomock.Any(), gomock.Any()).
			Return(
				&data_fetching.VINDetailsV1{
					Make:         "Chevrolet",
					Model:        "Silverado",
					ModelYear:    "2007",
					Manufacturer: "GM",
					Trim:         "LTZ",
					BodyClass:    "Truck",
					VehicleType:  data_fetching.VehicleTypeV1_VehicleTypeV1_Truck,
					WeightClass:  data_fetching.WeightClassV1_WeightClassV1_8,
				},
				nil,
			).
			AnyTimes()

		mockFetcherServer.
			EXPECT().
			GetNirvanaPoliciesV1(gomock.Any(), gomock.Any()).
			Return(
				&data_fetching.GetNirvanaPoliciesResponseV1{
					Policies: []*data_fetching.NirvanaPolicyV1{
						{
							PolicyNumber: "POL123456",
							State:        "Active",
						},
					},
				},
				nil,
			).
			AnyTimes()

		return mockFetcherServer
	}
	s.mockFetcherServer = mockFetcherServer

	mockProcessorServer := data_processing.NewMockProcessorServer(ctrl)
	newMockProcessorServer := func(actualServer data_processing.ProcessorServer) data_processing.ProcessorServer {
		mockProcessorServer.EXPECT().
			GetIsoFieldsFromNhtsaFieldsV1(gomock.Any(), gomock.Any()).
			Return(
				&data_processing.IsoFieldsFromNhtsaFieldsV1{
					VehicleType:   vin.IsoVehicleTypeV1_IsoVehicleTypeV1Truck,
					WeightGroup:   vin.IsoWeightGroupV1_IsoWeightGroupV1Medium,
					IsProblematic: false,
				},
				nil,
			).
			AnyTimes()

		mockProcessorServer.EXPECT().
			GetLatestValidRatingTierRecordV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetLatestValidRatingTierRecordV1).
			AnyTimes()

		mockProcessorServer.EXPECT().
			GetYearsInBusinessFromAuthorityHistoryV1(gomock.Any(), gomock.Any()).
			DoAndReturn(actualServer.GetYearsInBusinessFromAuthorityHistoryV1).
			AnyTimes()

		mockProcessorServer.EXPECT().
			GetFleetMovingViolationCountV1(gomock.Any(), gomock.Any()).
			Return(
				&data_processing.FleetMovingViolationCountV1{Count: 1},
				nil,
			).
			AnyTimes()

		mockProcessorServer.EXPECT().
			GetRetainedYearsV1(gomock.Any(), gomock.Any()).
			Return(
				&data_processing.RetainedYearsV1{
					Years: 5,
				},
				nil,
			).
			AnyTimes()

		mockProcessorServer.EXPECT().
			GetFleetModifiedMVRScoreV1(gomock.Any(), gomock.Any()).
			Return(
				&data_processing.FleetModifiedMVRScoreV1{Score: 20},
				nil,
			).
			AnyTimes()

		return mockProcessorServer
	}

	s.fxApp = testloader.RequireStart(
		s.T(),
		&s.env,
		testloader.Use(
			fx.Decorate(newMockFetcherServer),
			fx.Decorate(newMockProcessorServer),
		),
	)

	s.SeedData()
}

func (s *SimulationOrchestratorJobSuite) SeedData() {
	ctx := context.Background()
	appId := uuid.New()

	currentDate := time_utils.NewDate(2024, 12, 9).ToTime()
	app := application_builder.New(s.env.FeatureFlagClient).
		WithDefaultMockData().
		WithID(appId).
		WithTSP(telematics.TSPSamsara, uuid.New()).
		WithDOTNumber(506489).
		WithEffectiveDate(currentDate.AddDate(0, 0, 30)).
		WithLossInfo(
			&application.LossInfo{
				LossRunSummary: []application.LossRunSummaryPerCoverage{
					{
						CoverageType: enums.CoverageAutoLiability,
						Summary: []application.LossRunSummaryRecord{
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    10,
								PolicyPeriodEndDate:   currentDate.AddDate(-1, 0, 0),
								PolicyPeriodStartDate: currentDate,
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    10,
								PolicyPeriodEndDate:   currentDate,
								PolicyPeriodStartDate: currentDate.AddDate(1, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    10,
								PolicyPeriodEndDate:   currentDate.AddDate(1, 0, 0),
								PolicyPeriodStartDate: currentDate.AddDate(2, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    10,
								PolicyPeriodEndDate:   currentDate.AddDate(2, 0, 0),
								PolicyPeriodStartDate: currentDate.AddDate(3, 0, 0),
							},
							{
								LossIncurred:          0,
								NumberOfClaims:        0,
								NumberOfPowerUnits:    10,
								PolicyPeriodEndDate:   currentDate.AddDate(3, 0, 0),
								PolicyPeriodStartDate: currentDate.AddDate(4, 0, 0),
							},
						},
					},
				},
			},
		).
		Build()
	s.Require().NoError(s.env.ApplicationWrapper.InsertApp(ctx, *app))

	subId := uuid.New()
	dataContextId := uuid.New()
	defaultRatingTierDates := utils.NewDefaultRecordDatesV1()
	usState := us_states.OH
	vehicleZone, err := rating.FetchDefaultVehicleZones(rtypes.ProviderSentryMST, usState)
	s.Require().NoError(err)

	drivers := func() []application.DriverListRecord {
		var records []application.DriverListRecord
		for i := 0; i < 15; i++ {
			records = append(records, application.DriverListRecord{
				DriverLicenseNumber: fmt.Sprintf("123456789%d", i),
				UsState:             usState.String(),
				DateHired:           currentDate.AddDate(0, 0, -360),
				DateOfBirth:         null.TimeFrom(time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC)),
				FirstName:           null.StringFrom(fmt.Sprintf("Driver%d", i)),
				LastName:            null.StringFrom("Doe"),
				YearsOfExperience:   pointer_utils.ToPointer(float32(5 + i)),
			})
		}
		return records
	}()

	sub := submission_builder.New().
		FromApplication(*app).
		WithID(subId).
		WithBindable(true).
		WithMileageProjection(1_250_000, 15).
		WithPackageType(enums.IndicationOptionTagBasic).
		WithDataContextId(dataContextId).
		WithModelPinConfig(
			application.NewModelPinConfig(
				rtypes.ProviderSentry,
				usState,
				rtypes.Version081,
				false,
				defaultRatingTierDates.RecordDate.Time,
				defaultRatingTierDates.DumpDate.Time,
				constants.InsuranceCarrierFalseLake,
				false,
				false,
				false,
			)).
		WithCompanyInfo(
			&application.CompanyInfo{
				DOTNumber:          506489,
				Name:               "Test Company",
				NumberOfPowerUnits: 20,
				ProjectedMileage:   1_000_000,
				RadiusOfOperation: []*application.MileageRadiusRecord{
					{
						PercentageOfFleet: 100,
						RadiusBucket:      enums.MileageRadiusBucketFiveHundredPlus,
					},
				},
				USState: usState,
			}).
		WithCoverageInfo(
			&application.CoverageInfo{
				EffectiveDate: currentDate,
				Coverages: []application.CoverageDetails{
					{
						CoverageType: enums.CoverageAutoLiability,
						Limit:        pointer_utils.ToPointer(int32(1_000_000)),
						Deductible:   pointer_utils.ToPointer(int32(10_000)),
					},
				},
				CoveragesWithCombinedDeductibles: nil,
			}).
		WithEquipmentInfo(
			&application.EquipmentInfo{
				OperatingClassDistribution: []application.OperatingClassDistributionRecord{
					{
						Class:             enums.OperatingClassDryVan,
						PercentageOfFleet: 100,
					},
				},
				PrimaryOperatingClass: pointer_utils.ToPointer(enums.OperatingClassDryVan),
				PrimaryCommodity:      pointer_utils.ToPointer(old_enums.CommodityHauledLumberOrLogs),
				PrimaryCategory:       pointer_utils.ToPointer(enums.CommodityCategoryGeneralFreight),
				EquipmentList: application.EquipmentList{
					Info: func() []application.EquipmentListRecord {
						var records []application.EquipmentListRecord
						for i := 0; i < 20; i++ {
							records = append(records, application.EquipmentListRecord{
								VIN:         "VIN" + strconv.Itoa(i),
								StatedValue: pointer_utils.ToPointer(int32(100_000)),
							})
						}
						return records
					}(),
				},
				CommodityDistribution: &application.CommodityDistribution{
					Commodities: []application.WeightedCommodityRecord{
						{
							Category: enums.CommodityCategoryGeneralFreight,
							Commodity: application.Commodity{
								Type:  pointer_utils.ToPointer(enums.CommodityHauledLumberOrLogs),
								Label: "Lumber",
							},
							AvgDollarValueHauled: 50_000,
							MaxDollarValueHauled: 200_000,
							PercentageOfHauls:    100,
						},
					},
				},
			}).
		WithDriversInfo(
			&application.DriversInfo{
				Drivers: drivers,
			}).
		WithUnderwriterInput(
			&application.UnderwriterInput{
				LargeLosses: []application.LargeLoss{
					{
						Date:         currentDate.AddDate(0, 0, -30*9),
						CoverageType: enums.CoverageAutoLiability,
						LossIncurred: int32(150_000),
					},
				},
				AggregateCreditByCoverage: application.AggregateCreditByCoverage{
					AutoLiability:      pointer_utils.ToPointer(float32(0.00)),
					AutoPhysicalDamage: pointer_utils.ToPointer(float32(0.00)),
					MotorTruckCargo:    pointer_utils.ToPointer(float32(0.00)),
					GeneralLiability:   pointer_utils.ToPointer(float32(0.00)),
				},
				TransientSafetyCredit:   0.0,
				NegotiatedRates:         nil,
				FetchAttractScore:       pointer_utils.ToPointer(true),
				MinimumMileageGuarantee: nil,
				DepositAmount:           nil,
				RatingAddress: &application.TerminalLocation{
					AddressLineOne: "Baker Street",
					AddressLineTwo: nil,
					IsGated:        true,
					IsGuarded:      true,
					TypeOfTerminal: enums.TypeOfTerminalOffice,
					UsState:        usState,
					ZipCode:        45814,
					ZipCodeString:  "45814",
				},
				VehicleZoneDistribution: &[]application.VehicleZoneRecord{
					{
						StartZone:            vehicleZone.StartZone,
						EndZone:              vehicleZone.EndZone,
						PercentageOfVehicles: 100,
					},
				},
			},
		).
		Build()
	s.Require().NoError(s.env.ApplicationWrapper.InsertSubmission(ctx, *sub))
	s.submission = *sub

	s.seedDriversMVR(appId, dataContextId, drivers, currentDate)
}

func (s *SimulationOrchestratorJobSuite) seedDriversMVR(applicationID uuid.UUID, dataContextID uuid.UUID, drivers []application.DriverListRecord, effectiveDate time.Time) {
	for _, driver := range drivers {
		mvrRequest := &data_fetching.MVRReportRequestV1{
			FirstName:     driver.FirstName.String,
			LastName:      driver.LastName.String,
			Dob:           timestamppb.New(driver.DateOfBirth.Time),
			DlNumber:      driver.DriverLicenseNumber,
			UsState:       driver.UsState,
			ApplicationID: applicationID.String(),
		}

		mvrResource := &common.Resource{
			Data: &data_fetching.MVRReportV1{
				DlNumber: driver.DriverLicenseNumber,
				DlState:  driver.UsState,
				Violations: []*data_fetching.MVRViolationV1{
					{
						AssignedViolationCode: "1",
						ViolationDate:         timestamppb.New(effectiveDate.AddDate(0, 0, -30)),
					},
				},
			},
		}

		err := s.env.ContextManager.Save(
			s.ctx,
			dataContextID,
			mvrRequest,
			mvrResource,
		)
		s.Require().NoError(err)
	}
}

func (s *SimulationOrchestratorJobSuite) TearDownTest() {
	s.fxApp.RequireStop()
}

func (s *SimulationOrchestratorJobSuite) Test_SimulationOrchestratorJob_WithoutModifications() {
	// Create and run the job
	simulationId := uuid.New()
	modelKey := messages.ModelKey{
		Provider: rtypes.ProviderSentry,
		USState:  us_states.OH.ToCode(),
		Version:  rtypes.Version081,
	}
	simulationPairs := []messages.SimulationPair{
		{
			SubmissionID: uuid.MustParse(s.submission.ID),
			ModelKey:     modelKey,
		},
	}
	jobRunId, err := s.env.SimulationJobber.AddJobRun(
		s.ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			&messages.SimulationOrchestratorMessage{
				SimulationID:    simulationId,
				SimulationPairs: simulationPairs,
				CreatedBy:       uuid.New(),
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	s.Require().NoError(err)
	s.Require().NoError(s.env.SimulationJobber.WaitForJobRunCompletion(s.ctx, jobRunId))

	// Get and verify the simulation output
	output, err := s.getSimulationRunOutputForSubmission(s.ctx, simulationId, uuid.MustParse(s.submission.ID))
	s.Require().NoError(err)

	// Verify the output matches expected values
	s.Equal(s.submission.ID, output.SubmissionID.String())
	s.NotEmpty(output.ModelKey)
	s.NotEmpty(output.PackageType)
	s.NotEmpty(output.ArtifactFileKey)
	s.Equal(float64(20.0), output.FleetPowerUnitCount)
	s.Equal(float64(43_791.0), output.LiabPolicyPremium)
	s.Equal(float64(2_189.55), math_utils.RoundFloat(output.LiabPolicyPremiumPpu, 2))
	s.Equal(float64(0.0), output.PhysPolicyPremium)
	s.Equal(float64(0.0), output.MtcPolicyPremium)
	s.Equal(float64(0.0), output.GlPolicyPremium)
	s.Equal(float64(43_791.0), output.TotalPolicyPremium)
}

func (s *SimulationOrchestratorJobSuite) Test_SimulationOrchestratorJob_WithCoverageModification() {
	// Create modifications file content with Physical Damage coverage
	modifications := []simulation_common_files.FileModificationRow{
		{
			SubmissionID:      uuid.MustParse(s.submission.ID),
			ModificationType:  "Coverage",
			ModificationValue: "CoverageAutoPhysicalDamage,500000,1000",
		},
	}
	modificationsJSON, err := json.Marshal(modifications)
	s.Require().NoError(err)

	// Write modifications to S3
	modificationsFileKey := "test-modifications-file.json"
	_, err = s.env.S3Client.UploadWithContext(
		s.ctx,
		&s3manager.UploadInput{
			Bucket: aws.String(simulation_common_files.S3BucketName),
			Key:    aws.String(modificationsFileKey),
			Body:   bytes.NewReader(modificationsJSON),
		},
	)
	s.Require().NoError(err)

	modelKey := messages.ModelKey{
		Provider: rtypes.ProviderSentry,
		USState:  us_states.OH.ToCode(),
		Version:  rtypes.Version081,
	}
	simulationPairs := []messages.SimulationPair{
		{
			SubmissionID: uuid.MustParse(s.submission.ID),
			ModelKey:     modelKey,
		},
	}

	// Create and run the job
	simulationId := uuid.New()

	jobRunId, err := s.env.SimulationJobber.AddJobRun(
		s.ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			&messages.SimulationOrchestratorMessage{
				SimulationID:              simulationId,
				SimulationPairs:           simulationPairs,
				ModificationInputsFileKey: modificationsFileKey,
				CreatedBy:                 uuid.New(),
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	s.Require().NoError(err)
	s.Require().NoError(s.env.SimulationJobber.WaitForJobRunCompletion(s.ctx, jobRunId))

	// Get and verify the simulation output
	output, err := s.getSimulationRunOutputForSubmission(s.ctx, simulationId, uuid.MustParse(s.submission.ID))
	s.Require().NoError(err)

	// Verify the output contains Auto Physical Damage premiums
	s.Require().Greater(output.PhysPolicyPremium, float64(0.0))
	s.Require().Greater(output.PhysPolicyPremiumPtiv, float64(0.0))
}

func (s *SimulationOrchestratorJobSuite) Test_SimulationOrchestratorJob_WithSafetyCredit() {
	// Create modifications file content with safety credit
	modifications := []simulation_common_files.FileModificationRow{
		{
			SubmissionID:      uuid.MustParse(s.submission.ID),
			ModificationType:  "SafetyCredit",
			ModificationValue: "10.0",
		},
	}
	modificationsJSON, err := json.Marshal(modifications)
	s.Require().NoError(err)

	// Write modifications to S3
	modificationsFileKey := "test-modifications-file.json"
	_, err = s.env.S3Client.UploadWithContext(
		s.ctx,
		&s3manager.UploadInput{
			Bucket: aws.String(simulation_common_files.S3BucketName),
			Key:    aws.String(modificationsFileKey),
			Body:   bytes.NewReader(modificationsJSON),
		},
	)
	s.Require().NoError(err)

	modelKey := messages.ModelKey{
		Provider: rtypes.ProviderSentry,
		USState:  us_states.OH.ToCode(),
		Version:  rtypes.Version081,
	}
	simulationPairs := []messages.SimulationPair{
		{
			SubmissionID: uuid.MustParse(s.submission.ID),
			ModelKey:     modelKey,
		},
	}

	// Create and run the job
	simulationId := uuid.New()
	jobRunId, err := s.env.SimulationJobber.AddJobRun(
		s.ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			&messages.SimulationOrchestratorMessage{
				SimulationID:              simulationId,
				SimulationPairs:           simulationPairs,
				ModificationInputsFileKey: modificationsFileKey,
				CreatedBy:                 uuid.New(),
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	s.Require().NoError(err)
	s.Require().NoError(s.env.SimulationJobber.WaitForJobRunCompletion(s.ctx, jobRunId))

	// Get and verify the simulation output
	output, err := s.getSimulationRunOutputForSubmission(s.ctx, simulationId, uuid.MustParse(s.submission.ID))
	s.Require().NoError(err)

	// Run a base simulation without safety credit for comparison
	baseSimulationId := uuid.New()
	baseJobRunId, err := s.env.SimulationJobber.AddJobRun(
		s.ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			&messages.SimulationOrchestratorMessage{
				SimulationID:    baseSimulationId,
				SimulationPairs: simulationPairs,
				CreatedBy:       uuid.New(),
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	s.Require().NoError(err)
	s.Require().NoError(s.env.SimulationJobber.WaitForJobRunCompletion(s.ctx, baseJobRunId))

	// Get the base simulation output
	baseOutput, err := s.getSimulationRunOutputForSubmission(s.ctx, baseSimulationId, uuid.MustParse(s.submission.ID))
	s.Require().NoError(err)

	// Verify that the premium with safety credit is 10% lower than the base premium
	expectedPremium := baseOutput.TotalPolicyPremium * float64(0.9)      // 10% discount
	s.Require().InDelta(expectedPremium, output.TotalPolicyPremium, 0.1) // Allow small floating point differences
}

func (s *SimulationOrchestratorJobSuite) Test_SimulationOrchestratorJob_WithNewDriverAndMVRFetchingFlag() {
	// Add a new driver to the submission that doesn't have MVR data in the store
	newDriver := application.DriverListRecord{
		DriverLicenseNumber: "NEWDRIVER123",
		UsState:             us_states.OH.String(),
		DateHired:           time_utils.NewDate(2024, 12, 9).ToTime().AddDate(0, 0, -30),
		DateOfBirth:         null.TimeFrom(time.Date(1985, 5, 15, 0, 0, 0, 0, time.UTC)),
		FirstName:           null.StringFrom("NewDriver"),
		LastName:            null.StringFrom("Test"),
		YearsOfExperience:   pointer_utils.ToPointer(float32(8)),
	}

	// Update the submission to include the new driver
	updatedDrivers := append(s.submission.DriversInfo.Drivers, newDriver)
	s.Require().NoError(s.env.ApplicationWrapper.UpdateSub(s.ctx, s.submission.ID, func(sub application.SubmissionObject) (application.SubmissionObject, error) {
		sub.DriversInfo.Drivers = updatedDrivers
		return sub, nil
	}))

	modelKey := messages.ModelKey{
		Provider: rtypes.ProviderSentry,
		USState:  us_states.OH.ToCode(),
		Version:  rtypes.Version081,
	}
	simulationPairs := []messages.SimulationPair{
		{
			SubmissionID: uuid.MustParse(s.submission.ID),
			ModelKey:     modelKey,
		},
	}

	s.mockFetcherServer.
		EXPECT().
		GetMVRReportV1(gomock.Any(), gomock.Any()).
		Return(
			&data_fetching.MVRReportV1{
				DlNumber: "********",
				DlState:  "OH",
			},
			nil,
		).
		Times(1)

	// 1. With EnableMVRFetching = false, should produce output with Error populated
	simulationIdWithoutMVR := uuid.New()
	jobRunIdWithoutMVR, err := s.env.SimulationJobber.AddJobRun(
		s.ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			&messages.SimulationOrchestratorMessage{
				SimulationID:      simulationIdWithoutMVR,
				SimulationPairs:   simulationPairs,
				EnableMVRFetching: false,
				CreatedBy:         uuid.New(),
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	s.Require().NoError(err)
	s.Require().NoError(s.env.SimulationJobber.WaitForJobRunCompletion(s.ctx, jobRunIdWithoutMVR))

	outputWithError, err := s.getSimulationRunOutputForSubmission(s.ctx, simulationIdWithoutMVR, uuid.MustParse(s.submission.ID))
	s.Require().NoError(err)
	s.Require().Contains(outputWithError.Error, "unable to  fetch mvr reports")

	// 2. With EnableMVRFetching = true, should succeed
	simulationIdWithMVR := uuid.New()
	jobRunIdWithMVR, err := s.env.SimulationJobber.AddJobRun(
		s.ctx,
		jtypes.NewAddJobRunParams(
			simulation_orchestrator.SimulationOrchestratorJob,
			&messages.SimulationOrchestratorMessage{
				SimulationID:      simulationIdWithMVR,
				SimulationPairs:   simulationPairs,
				EnableMVRFetching: true,
				CreatedBy:         uuid.New(),
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	s.Require().NoError(err)
	s.Require().NoError(s.env.SimulationJobber.WaitForJobRunCompletion(s.ctx, jobRunIdWithMVR))

	// Get and verify the simulation output for the successful run
	output, err := s.getSimulationRunOutputForSubmission(s.ctx, simulationIdWithMVR, uuid.MustParse(s.submission.ID))
	s.Require().NoError(err)
	s.Equal(s.submission.ID, output.SubmissionID.String())
	s.NotEmpty(output.ModelKey)
	s.NotEmpty(output.PackageType)
	s.NotEmpty(output.ArtifactFileKey)
	s.Empty(output.Error)
	s.NotZero(output.TotalPolicyPremium)
}

// getSimulationRunOutputs retrieves and parses all outputs of a simulation run
func (s *SimulationOrchestratorJobSuite) getSimulationRunOutputs(
	ctx context.Context,
	simulationID uuid.UUID,
) ([]*types.PricingOutput, error) {
	// Get the simulation run
	run, err := s.env.DBWrapper.GetSimulationRun(
		ctx,
		pricing.SimulationRunWhere.ID.EQ(simulationID.String()),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get simulation run")
	}
	if run == nil {
		return nil, errors.New("simulation run not found")
	}
	if run.Status != simulation_enums.RunStatusSucceeded {
		return nil, errors.Newf("simulation run status is not succeeded: %s", run.Status)
	}
	if run.OutputFileKey == nil {
		return nil, errors.New("simulation run has no output file key")
	}

	// Download the output file from S3
	if run.OutputFileKey == nil {
		return nil, errors.New("simulation run has no output file key")
	}
	var obj aws.WriteAtBuffer
	_, err = s.env.S3Client.DownloadWithContext(
		ctx,
		&obj,
		&s3.GetObjectInput{
			Bucket: aws.String(simulation_common_files.S3BucketName),
			Key:    aws.String(*run.OutputFileKey),
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to download output file")
	}

	// Parse the JSON content
	content := make([]*types.PricingOutput, 0)
	if err := json.Unmarshal(obj.Bytes(), &content); err != nil {
		return nil, errors.Wrap(err, "failed to parse output file")
	}
	if len(content) == 0 {
		return nil, errors.New("output file is empty")
	}

	return content, nil
}

// getSimulationRunOutputForSubmission retrieves and parses the output of a simulation run for a specific submission ID
func (s *SimulationOrchestratorJobSuite) getSimulationRunOutputForSubmission(
	ctx context.Context,
	simulationID uuid.UUID,
	submissionID uuid.UUID,
) (*types.PricingOutput, error) {
	outputs, err := s.getSimulationRunOutputs(ctx, simulationID)
	if err != nil {
		return nil, err
	}

	// Find the output for the specific submission
	for _, output := range outputs {
		if output.SubmissionID == submissionID {
			return output, nil
		}
	}

	return nil, errors.Newf("no output found for submission %s", submissionID)
}
