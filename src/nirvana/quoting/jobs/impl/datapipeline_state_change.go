package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/cactus/go-statsd-client/v5/statsd"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/pre_telematics_quote"
	nonfleet_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"

	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	application2 "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	enums2 "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	quoting_jobs "nirvanatech.com/nirvana/nonfleet/quoting-jobs"
	"nirvanatech.com/nirvana/quoting/jobs"
	"nirvanatech.com/nirvana/quoting/utils"
	"nirvanatech.com/nirvana/servers/telematicsv2"
	telematics_jobs "nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	job_trigger_helper "nirvanatech.com/nirvana/underwriting/jobs"
)

// TODO: deprecate using tags for broad use-cases and deprecate application.telematics_info in favour of existing pipeline status funcs
var firstPipelineRunTags = []string{telematics_jobs.JobTagSourceConsent, telematics_jobs.JobTagSourceQA}

var NoSuccessfulPipelineRunFoundError = errors.New("no successful pipeline run found")

const (
	datapipelineStateChangeJobRunCounterMetric = "data_pipeline_state_change_job.run.count"
	firstPipelineSuccessTimestampFetchCounter  = "first_pipeline_success_timestamp_fetch.count"
)

func NewDataPipelineStateChangedJob(deps *Deps) (*jtypes.Job[*telematics_jobs.CallbackMessage], error) {
	return jtypes.NewJob(jobs.DataPipelineStateChanged,
		[]jtypes.TaskCreator[*telematics_jobs.CallbackMessage]{
			func() jtypes.Task[*telematics_jobs.CallbackMessage] {
				return &runOnDataPipelineStateChange{deps: deps}
			},
		},
		telematics_jobs.UnmarshalCallbackMessageFn,
	)
}

func getMetricTagValue(condition bool, trueValue, falseValue string) string {
	if condition {
		return trueValue
	}
	return falseValue
}

func (r *runOnDataPipelineStateChange) emitFirstPipelineSuccessAtMetric(
	ctx context.Context,
	isSuccess,
	isTestAgency,
	isNil bool,
	isRaceCondition bool,
) {
	statusTagValue := getMetricTagValue(isSuccess, metricTagValueSucceeded, metricTagValueFailed)
	isTestMetricTagValue := getMetricTagValue(isTestAgency, metricTagValueTrue, metricTagValueFalse)
	isNilTestMetricTagValue := getMetricTagValue(isNil, metricTagValueTrue, metricTagValueFalse)
	isRaceConditionMetricTagValue := getMetricTagValue(isRaceCondition, metricTagValueTrue, metricTagValueFalse)
	if metricErr := r.deps.MetricsClient.Inc(
		firstPipelineSuccessTimestampFetchCounter,
		1,
		1,
		statsd.Tag{metricTagKeyStatus, statusTagValue},
		statsd.Tag{metricTagKeyIsTest, isTestMetricTagValue},
		statsd.Tag{metricTagKeyIsNil, isNilTestMetricTagValue},
		statsd.Tag{metricTagKeyIsRaceCondition, isRaceConditionMetricTagValue},
	); metricErr != nil {
		log.Error(
			ctx,
			fmt.Sprintf("failed to emit metric %s", datapipelineStateChangeJobRunCounterMetric),
			log.Err(metricErr),
		)
	}
}

type runOnDataPipelineStateChange struct {
	deps *Deps

	job_utils.NoopUndoTask[*telematics_jobs.CallbackMessage]
	job_utils.DefaultRetryable[*telematics_jobs.CallbackMessage]
}

func (r *runOnDataPipelineStateChange) ID() string {
	return "runOnDataPipelineStateChange"
}

func (r *runOnDataPipelineStateChange) Run(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	log.Info(ctx, "DataPipelineStateChanged job started", log.Stringer("reason", message.Reason),
		log.Stringer("handleId", message.HandleId), log.Any("tags", message.Tags))
	if err := r.run(ctx, message); err != nil {
		log.Error(ctx, "DataPipelineStateChanged job run failed",
			log.Any("callbackMessage", message),
			log.Err(err))
		// emit failure metric
		if metricErr := r.deps.MetricsClient.Inc(datapipelineStateChangeJobRunCounterMetric, 1, 1, statsd.Tag{metricTagKeyStatus, metricTagValueFailed}); metricErr != nil {
			log.Error(ctx, fmt.Sprintf("failed to emit metric %s", datapipelineStateChangeJobRunCounterMetric), log.Err(metricErr))
		}

		return err
	}

	// emit success metric
	if metricErr := r.deps.MetricsClient.Inc(datapipelineStateChangeJobRunCounterMetric, 1, 1, statsd.Tag{metricTagKeyStatus, metricTagValueSucceeded}); metricErr != nil {
		log.Error(ctx, fmt.Sprintf("failed to emit metric %s", datapipelineStateChangeJobRunCounterMetric), log.Err(metricErr))
	}
	return nil
}

func isFirstPipelineRun(pipelineTags []string) bool {
	return slice_utils.IntersectionExists(pipelineTags, firstPipelineRunTags)
}

// GetFirstSuccessfulRunFinishedTimestamp is to be called only after verifying that the pipeline callback has returned with a success status
// This is because this func throws an error if a successful run is not found
func GetFirstSuccessfulRunFinishedTimestamp(
	ctx context.Context,
	telematicsPipelineManager telematicsv2.TelematicsPipelineManager,
	handleId uuid.UUID,
	existingTelematicsInfo *application.TelematicsInfo,
) (*time.Time, error) {
	// if already stored for this handleId, return finishedAt
	if existingTelematicsInfo != nil &&
		existingTelematicsInfo.TelematicsConnHandleId == handleId &&
		existingTelematicsInfo.FirstPipelineSuccessAt != nil {
		return existingTelematicsInfo.FirstPipelineSuccessAt, nil
	}
	// get first successful pipeline run.
	// NOTE: This method returns the first run if no successful runs are present
	pipelineSummary, err := utils.FirstSuccessfulPipelineStatus(
		ctx,
		telematicsPipelineManager,
		pointer_utils.String(handleId.String()),
	)
	if errors.Is(err, status.Error(codes.NotFound, "no pipeline found for given handle id and constraints")) {
		log.Info(
			ctx,
			"No pipeline found error encountered while fetching first pipeline status",
			log.String("handleId", handleId.String()),
			log.String("error", err.Error()),
		)
		return nil, NoSuccessfulPipelineRunFoundError
	}
	if err != nil {
		return nil, errors.Wrap(err, "failed to find the first successful run")
	}
	// checking that this is not the case where we received a non successful run
	if pipelineSummary.Status != telematicsv2.ExecutionStatus_Succeeded {
		return nil, NoSuccessfulPipelineRunFoundError
	}
	finishedAt := pipelineSummary.FinishedAt.AsTime()
	return &finishedAt, nil
}

func (r *runOnDataPipelineStateChange) decorateTelematicsInfoWithFirstPipelineSuccess(
	ctx context.Context,
	telematicsInfo *application.TelematicsInfo,
	handleId uuid.UUID,
	isTestAgency bool,
) {
	firstSuccessfulRunFinishedAt, err := GetFirstSuccessfulRunFinishedTimestamp(
		ctx,
		r.deps.TelematicsDataPlatformClient,
		handleId,
		telematicsInfo,
	)
	/*
		This callback is triggered by the data pipeline job as it's last step while the persistence
		of the run status happens post that. This means there can be cases where we try to fetch first
		data pipeline timestamp before it is actually persisted. That case is narrowed down and handled
		by throwing and catching noSuccessfulPipelineRunFoundError.
	*/
	isRaceCondition := errors.Is(err, NoSuccessfulPipelineRunFoundError)
	switch {
	// if race condition, set success timestamp to now and log success = true with race condition flag
	case isRaceCondition:
		firstSuccessfulRunFinishedAt = pointer_utils.ToPointer(time.Now())
		log.Info(
			ctx,
			"Race condition encountered where callback received with success message "+
				"but no successful pipeline found",
			log.Stringer("handleId", handleId),
		)
		r.emitFirstPipelineSuccessAtMetric(ctx, true, isTestAgency, false, isRaceCondition)
	// if any other error apart from race condition, log failure
	case err != nil:
		log.Error(
			ctx,
			"error occurred while fetching first successful run",
			log.String("error", err.Error()),
			log.Stringer("handleId", handleId),
		)
		r.emitFirstPipelineSuccessAtMetric(ctx, false, isTestAgency, false, isRaceCondition)
	// if non null timestamp (and no race condition), log and throw happy metric
	case firstSuccessfulRunFinishedAt != nil:
		log.Info(
			ctx,
			"first successful run finished",
			log.Stringer("handleId", handleId),
			log.Stringer("timestamp", *firstSuccessfulRunFinishedAt),
		)
		r.emitFirstPipelineSuccessAtMetric(ctx, true, isTestAgency, false, isRaceCondition)
	// else log false and throw metric with isnil flag (just for sanity)
	default:
		log.Info(ctx, "finishedAt is null", log.Stringer("handleId", handleId))
		r.emitFirstPipelineSuccessAtMetric(ctx, false, isTestAgency, true, isRaceCondition)
	}
	telematicsInfo.FirstPipelineSuccessAt = firstSuccessfulRunFinishedAt
	telematicsInfo.FirstPipelineSuccessWithRaceCondition = pointer_utils.Bool(isRaceCondition)
}

func (r *runOnDataPipelineStateChange) persistTelematicsInfoOnApp(
	ctx jtypes.Context,
	handleId uuid.UUID,
	telematicsStatusEnum enums.TelematicsDataStatus,
	telematicsPipelineStartedAt *time.Time,
	tags []string,
) error {
	apps, err := r.deps.AppWrapper.GetAllApplicationsByHandleId(ctx, handleId.String())
	if err != nil {
		return errors.Wrap(err, "could not get applications")
	}

	// Update all applications with the tsp handle id
	for _, app := range apps {
		agency, err := r.deps.AgencyWrapper.FetchAgency(ctx, app.AgencyID)
		if err != nil {
			return errors.Wrap(err, "could not fetch agency")
		}
		isTestAgency := agency.IsTestAgency
		// Also verify that an app-review for this app exists in pending state
		appReview, err := r.deps.AppReviewWrapper.GetLatestPendingReview(ctx, app.ID)
		if err != nil && !errors.Is(err, uw.ErrAppReviewNotFound) {
			return errors.Wrapf(err, "could not get application review for app id %s", app.ID)
		}

		if isFirstPipelineRun(tags) {
			// Persist Telematics Info in the application db
			err = r.deps.AppWrapper.UpdateApp(ctx, app.ID,
				func(applicationObj application.Application) (application.Application, error) {
					telematicsInfo := applicationObj.TelematicsInfo
					if telematicsInfo == nil {
						telematicsInfo = new(application.TelematicsInfo)
					}
					if telematicsStatusEnum == enums.TelematicsDataStatusDataAvailable {
						r.decorateTelematicsInfoWithFirstPipelineSuccess(
							ctx,
							telematicsInfo,
							handleId,
							isTestAgency,
						)
					}
					if telematicsPipelineStartedAt != nil {
						telematicsInfo.TelematicsPipelineStartedAt = telematicsPipelineStartedAt
					}
					telematicsInfo.TelematicsDataStatus = telematicsStatusEnum
					telematicsInfo.TelematicsConnHandleId = handleId
					applicationObj.TelematicsInfo = telematicsInfo
					return applicationObj, nil
				})
			if err != nil {
				return errors.Wrapf(err,
					"could not update applications with %s status for id %s", telematicsStatusEnum, app.ID)
			}

			if appReview != nil {
				// Trigger Application Ready for Review event
				if telematicsStatusEnum == enums.TelematicsDataStatusDataAvailable {
					logApplicationEvent(ctx, r.deps, Started, ReadyForReview, app, nil, nil)
				}
			} else {
				log.Info(ctx, "No pending app review found for app", log.String("app_id", app.ID))
			}
		}

		if appReview != nil && telematicsStatusEnum == enums.TelematicsDataStatusDataAvailable {
			// Trigger generate appetite factors job no matter which pipeline kind has been executed
			triggerGenerateAppetiteFactorsJob(ctx, r.deps.Jobber, appReview.Id)

			if _, err := job_trigger_helper.TriggerGenerateMstReferralRulesJob(ctx, r.deps.Jobber, appReview.Id,
				job_trigger_helper.TriggerSourceUpdateSubmissionForUW); err != nil {
				log.Error(ctx, "failed to trigger GenerateMstReferralRules job", log.Err(err))
				job_trigger_helper.EmitFailedMetricForJob(ctx,
					r.deps.MetricsClient, job_trigger_helper.GenerateMstReferralRules)
			}
		}

		if telematicsStatusEnum == enums.TelematicsDataStatusDataAvailable {
			log.Info(ctx, "Triggering CheckClearance job in data_pipeline_state_change", log.String("app_id", app.ID))
			err = triggerCheckClearanceJob(ctx, r.deps.Jobber, app.ID)
			if err != nil {
				return errors.Wrapf(err, "could not trigger CheckClearance job for app id %s", app.ID)
			}
		}
	}
	return nil
}

func (r *runOnDataPipelineStateChange) triggerUwActionForTelematicsDataStatusChange(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	if !isFirstPipelineRun(message.Tags) {
		return nil
	}

	handleId := message.HandleId.String()
	// multiple applications can have same handle ID, like in case of renewals
	apps, err := r.deps.AppWrapper.GetAllApplicationsByHandleId(ctx, handleId)
	if err != nil {
		return errors.Wrap(err, "could not get applications")
	}

	for _, app := range apps {

		telematicsStatus, err := utils.GenerateTelematicsDataStatus(
			ctx,
			r.deps.TspConnectionManager,
			r.deps.TelematicsDataPlatformClient,
			r.deps.AppWrapper,
			app.TSPConnHandleId,
			app.TSPEnum,
			app.ID,
		)
		if err != nil {
			return errors.Wrapf(err, "failed to get telematics data status for app %s", app.ID)
		}

		log.Info(ctx, "Triggering TelematicsStatusChanged action for app", log.String("app_id", app.ID), log.Any("status", telematicsStatus))
		// trigger action for each app
		err = r.deps.AppReviewStateMachine.TelematicsStatusChanged(ctx, app.ID, *telematicsStatus)
		if err != nil {
			return errors.Wrapf(err, "could not trigger TelematicsStatusChanged action for app with id %s", app.ID)
		}
	}
	return nil
}

// TODO convert to a generic func for both fleet and non fleet
func (r *runOnDataPipelineStateChange) triggerNFAutoSelectSafetyScoreJob(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	// multiple applications can have same handle ID, like in case of renewals
	// We want to update only the latest application here
	apps, err := r.deps.AdmittedAppWrapper.GetAllApplicationsByTSPConnHandleId(ctx, message.HandleId)
	if err != nil {
		return errors.Wrap(err, "could not get applications")
	}
	if len(apps) == 0 {
		// handleId is not associated with any NF application. This handle id probably belongs to a fleet app.
		log.Info(ctx, "No NF applications found for handle id", log.String("handleId", message.HandleId.String()))
		return nil
	}
	var latestApp *application2.Application[*admitted.AdmittedApp]
	for _, app := range apps {
		if latestApp == nil || app.CreatedAt.After(latestApp.CreatedAt) {
			latestApp = &app
		}
	}
	// sanity check to ensure we have a latest app
	if latestApp == nil {
		return errors.New("no latest application found for handle id")
	}
	// fetch user creator
	user, err := r.deps.AuthWrapper.FetchAuthzUser(ctx, latestApp.CreatedBy)
	if err != nil {
		return errors.Wrapf(err, "could not fetch user for app %s", latestApp.ID)
	}
	// fetch feature flag
	scoreAutoSelectionEnabled, err := r.deps.FeatureFlagClient.BoolVariation(
		feature_flag_lib.BuildLookupAttributes(*user),
		feature_flag_lib.FeatureNFSafetyScoreAutoSelection,
		false,
	)
	if err != nil {
		// just log. the variable will still have baseVal = false
		log.Error(ctx, "failed to get feature flag value", log.Err(err))
	}
	// trigger job if feature flag is enabled
	if scoreAutoSelectionEnabled {
		jobRunId, err := quoting_jobs.TriggerAutoSelectSafetyScoreJob(ctx, r.deps.Jobber, latestApp.ID, enums2.ProgramTypeNonFleetAdmitted)
		if err != nil {
			return errors.Wrapf(err, "failed to trigger AutoSelectSafetyScore job for app %s", latestApp.ID)
		}
		log.Info(ctx, "Triggered AutoSelectSafetyScore job", log.Stringer("JobRunId", jobRunId))
	}
	return nil
}

func (r *runOnDataPipelineStateChange) run(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	switch message.Reason {
	case telematics_jobs.DataPipelineStarted:
		err := r.persistTelematicsInfoOnApp(
			ctx, message.HandleId, enums.TelematicsDataStatusProcessingData, &message.StartedAt, message.Tags)
		if err != nil {
			return errors.Wrapf(err, "could not persist telematics info on app for handle id %s", message.HandleId)
		}
	case telematics_jobs.DataPipelineFailed:
		err := r.persistTelematicsInfoOnApp(
			ctx, message.HandleId, enums.TelematicsDataStatusConnectionFailed, &message.StartedAt, message.Tags)
		if err != nil {
			return errors.Wrapf(err, "could not persist telematics info on app for handle id %s", message.HandleId)
		}
		err = r.triggerUwActionForTelematicsDataStatusChange(ctx, message)
		if err != nil {
			return errors.Wrapf(err, "could not trigger uw action for telematics data status change for handle id %s", message.HandleId)
		}
		err = r.updateNFApplicationPreQuoteTelematicsStatus(ctx, message)
		if err != nil {
			return errors.Wrapf(err, "could not update NF application pre quote telematics status for handle id %s", message.HandleId)
		}
	case telematics_jobs.DataPipelineSuccessful:
		err := r.persistTelematicsInfoOnApp(
			ctx, message.HandleId, enums.TelematicsDataStatusDataAvailable, &message.StartedAt, message.Tags)
		if err != nil {
			return errors.Wrapf(err, "could not persist telematics info on app for handle id %s", message.HandleId)
		}
		err = r.triggerUwActionForTelematicsDataStatusChange(ctx, message)
		if err != nil {
			return errors.Wrapf(err, "could not trigger uw action for telematics data status change for handle id %s", message.HandleId)
		}
		err = r.triggerNFAutoSelectSafetyScoreJob(ctx, message)
		if err != nil {
			return errors.Wrapf(err, "could not trigger AutoSelectSafetyScore job for handle id %s", message.HandleId)
		}
		err = r.triggerExpressLaneAutoUnderwritingJob(ctx, message)
		if err != nil {
			return errors.Wrapf(err, "could not trigger ExpressLane job for handleId %s", message.HandleId)
		}
		err = r.updateNFApplicationPreQuoteTelematicsStatus(ctx, message)
		if err != nil {
			return errors.Wrapf(err, "could not update NF application pre quote telematics status for handle id %s", message.HandleId)
		}
	}
	return nil
}

func (r *runOnDataPipelineStateChange) Retry(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	return r.Run(ctx, message)
}

func triggerGenerateAppetiteFactorsJob(ctx context.Context, quotingJobber quoting_jobber.Client, reviewId string) {
	log.Info(ctx, "Adding GenerateAppetiteFactors job run after data pipeline state has changed")
	if _, err := job_trigger_helper.TriggerGenerateAppetiteFactorsJob(
		ctx, quotingJobber, reviewId, job_trigger_helper.TriggerSourceDataPipelineStateUpdate,
	); err != nil {
		// just log the error event
		// TODO generate a PD here!
		log.Error(ctx,
			"failed to add GenerateAppetiteFactors job after data pipeline state has changed", log.Err(err))
	}
}

func triggerCheckClearanceJob(
	ctx context.Context, quotingJobber quoting_jobber.Client, appID string,
) error {
	addJobRunParams := jobber.NewAddJobRunParams(
		jobs.CheckClearance,
		&jobs.CheckClearanceArgs{ApplicationID: appID},
		jtypes.NewMetadata(jtypes.Immediate),
	)
	jobRunId, err := quotingJobber.AddJobRun(ctx, addJobRunParams)
	if err != nil {
		log.Error(ctx, "failed to add CheckClearance job", log.Err(err),
			log.Any("addJobRunParams", addJobRunParams),
		)
		return errors.Wrap(err, "failed to add CheckClearance job")
	}
	log.Info(ctx, "Added CheckClearance job", log.Stringer("JobRunId", jobRunId))
	return nil
}

func (r *runOnDataPipelineStateChange) updateNFApplicationPreQuoteTelematicsStatus(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	latestApp, err := r.findLatestAppFromHandleId(ctx, message)
	if err != nil {
		log.Error(ctx, "failed to find latest app from handle id", log.Err(err))
		return nil
	}
	// Skip if the app is in policy created already
	if latestApp.State == nonfleet_enums.AppStatePolicyCreated || latestApp.State == nonfleet_enums.AppStateClosed {
		return nil
	}
	// This means the app is NOT a part of the pre-telematics quote experiment, thus skip it.
	if latestApp.PreTelematicsQuoteState == nil {
		return nil
	}

	var updatedStatus nonfleet_enums.PreTelematicsQuoteState

	// SUCCESS - Set to DataPipelineSuccess if withing time, else set to TimeElapsedAndDataPipelineSuccess
	// FAILED - Only update if pending, since we don't want to change the state if
	// it was already a success as we care for the 1st pipeline run for experiment
	//nolint:exhaustive
	switch *latestApp.PreTelematicsQuoteState {
	case nonfleet_enums.PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelinePending:
		switch message.Reason {
		case telematics_jobs.DataPipelineSuccessful:
			updatedStatus = nonfleet_enums.PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelineSuccess
		case telematics_jobs.DataPipelineFailed:
			updatedStatus = nonfleet_enums.PreTelematicsQuoteStateNotReadyBecauseDataPipelineIssues
		case telematics_jobs.DataPipelineStarted:
			return nil
		default:
			return errors.Newf("unexpected reason %s", message.Reason)
		}
	case nonfleet_enums.PreTelematicsQuoteStateNotReadyBecauseDataPipelinePending,
		nonfleet_enums.PreTelematicsQuoteStateNotReadyBecauseDataPipelineIssues:
		switch message.Reason {
		case telematics_jobs.DataPipelineSuccessful:
			updatedStatus = nonfleet_enums.PreTelematicsQuoteStateReadyBecauseDataPipelineSuccess
		case telematics_jobs.DataPipelineFailed:
			updatedStatus = nonfleet_enums.PreTelematicsQuoteStateNotReadyBecauseDataPipelineIssues
		case telematics_jobs.DataPipelineStarted:
			return nil
		default:
			return errors.Newf("unexpected reason %s", message.Reason)
		}
	case nonfleet_enums.PreTelematicsQuoteStateReadyBecauseDataPipelineSuccess,
		nonfleet_enums.PreTelematicsQuoteStateReadyBecauseTimeElapsedAndDataPipelineSuccess:
		return nil // No change needed, already in success state
	default:
		return errors.Newf("unexpected pre-quote telematics status %s", latestApp.PreTelematicsQuoteState.String())
	}

	if updatedStatus == *latestApp.PreTelematicsQuoteState {
		log.Info(ctx, "No need to update pre-quote telematics status for latest app", log.Stringer("app_id", latestApp.ID))
		return nil // No need to update if the status is already the same
	}

	// update the PreTelematicsQuoteState of the application in db
	err = r.deps.AdmittedAppWrapper.UpdateApp(ctx, latestApp.ID,
		func(appObj application2.Application[*admitted.AdmittedApp]) (application2.Application[*admitted.AdmittedApp], error) {
			appObj.PreTelematicsQuoteState = pointer_utils.ToPointer(updatedStatus)
			return appObj, nil
		})
	if err != nil {
		return errors.Wrap(err, "failed to update app pre-quote telematics status")
	}

	log.Info(ctx, "Updated pre-quote telematics status for latest app", log.Stringer("app_id", latestApp.ID),
		log.Stringer("updated_status", updatedStatus))

	// Record experiment event change.
	payload := &pre_telematics_quote.PreTelematicsExperimentStateChangeEvent{
		FromState: pointer_utils.ToPointer(latestApp.PreTelematicsQuoteState.String()),
		ToState:   pointer_utils.ToPointer(updatedStatus.String()),
	}

	err = r.deps.NFApplicationExperimentManager.RecordExperimentStateChangeEvent(ctx, pre_telematics_quote.PreQuoteTelematicsV1ExperimentId, latestApp.ID, payload)
	if err != nil {
		// Just log and move, since this is non-breaking, and if it fails, it won't harm a lot.
		log.Error(ctx, "failed to record experiment state change event for pre-quote telematics",
			log.Stringer("app_id", latestApp.ID),
			log.Stringer("experiment_id", pre_telematics_quote.PreQuoteTelematicsV1ExperimentId),
			log.Stringer("old_status", latestApp.PreTelematicsQuoteState),
			log.Stringer("new_status", updatedStatus),
			log.Err(err),
		)
	}

	return nil
}

func (r *runOnDataPipelineStateChange) triggerExpressLaneAutoUnderwritingJob(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) error {
	// STEP 1 - Fetch the latest app from the handle ID.
	latestApp, err := r.findLatestAppFromHandleId(ctx, message)
	if err != nil {
		log.Error(ctx, "failed to find latest app from handle id", log.Err(err))
		return nil
	}

	err = r.deps.ExpressLaneManager.TriggerJobsOnTelematicsSuccess(ctx, *latestApp)
	if err != nil {
		log.Error(ctx, "failed to trigger express lane jobs on telematics success", log.Err(err))
	}

	return nil
}

func (r *runOnDataPipelineStateChange) findLatestAppFromHandleId(ctx jtypes.Context, message *telematics_jobs.CallbackMessage) (*application2.Application[*admitted.AdmittedApp], error) {
	apps, err := r.deps.AdmittedAppWrapper.GetAllApplicationsByTSPConnHandleId(ctx, message.HandleId)
	if err != nil {
		return nil, errors.Wrap(err, "could not get applications for handle id "+message.HandleId.String())
	}
	if len(apps) == 0 {
		return nil, errors.New("no applications found for handle id " + message.HandleId.String())
	}
	var latestApp *application2.Application[*admitted.AdmittedApp]
	for _, app := range apps {
		if latestApp == nil || app.CreatedAt.After(latestApp.CreatedAt) {
			latestApp = &app
		}
	}
	// sanity check to ensure we have the latest app
	if latestApp == nil {
		return nil, errors.New("no latest application found for handle id " + message.HandleId.String())
	}
	return latestApp, nil
}
