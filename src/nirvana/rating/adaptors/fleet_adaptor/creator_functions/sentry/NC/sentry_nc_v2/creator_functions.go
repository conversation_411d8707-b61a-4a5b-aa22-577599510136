package sentry_nc_v2

import (
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/al_raters"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/coll_raters"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/comp_raters"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/company"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/coverage"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/drivers"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/gl_rater"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/large_coll_comp_losses"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/large_liability_losses"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/loss_history"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/mtc_rater"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/outputs_quote"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/quote_data"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/underwriting"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/vehicles"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions/yearly_loss_summaries"
)

func GetCreatorFunctions() []common.EntitiesCreatorFn[creator_functions.Input] {
	return []common.EntitiesCreatorFn[creator_functions.Input]{
		common.NewEntitiesCreatorFnFromSingletonFns(quote_data.New),
		common.NewEntitiesCreatorFnFromSingletonFns(
			underwriting.New,
			underwriting.Modifier1,
		),
		common.NewEntitiesCreatorFnFromSingletonFns(
			coverage.New,
			coverage.NewModifier1(coverage.Modifier1V1),
			coverage.NewModifier37(coverage.Modifier37V1),
			coverage.NewModifier2(coverage.Modifier2V1),
			coverage.Modifier4,
			coverage.Modifier5,
			coverage.Modifier6,
			coverage.Modifier10,
			coverage.Modifier12,
			coverage.Modifier17,
			coverage.Modifier18,
			coverage.Modifier19,
			coverage.Modifier27,
			coverage.Modifier28,
			coverage.Modifier40,
		),
		common.NewEntitiesCreatorFnFromSingletonFns(company.New),
		common.NewEntitiesCreatorFnFromSliceFns(
			vehicles.New,
			vehicles.Modifier2,
			vehicles.Modifier4,
			vehicles.Modifier3,
		),
		common.NewEntitiesCreatorFnFromSliceFns(drivers.New),
		common.NewEntitiesCreatorFnFromSingletonFns(loss_history.New),
		common.NewEntitiesCreatorFnFromSliceFns(yearly_loss_summaries.New),
		common.NewEntitiesCreatorFnFromSliceFns(large_liability_losses.New),
		common.NewEntitiesCreatorFnFromSliceFns(large_coll_comp_losses.New),
		common.NewEntitiesCreatorFnFromSliceFns(al_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(coll_raters.New),
		common.NewEntitiesCreatorFnFromSliceFns(comp_raters.New),
		common.NewEntitiesCreatorFnFromSingletonFns(gl_rater.New),
		common.NewEntitiesCreatorFnFromSingletonFns(mtc_rater.New),
		common.NewEntitiesCreatorFnFromSingletonFns(outputs_quote.New),
	}
}
