package entities

import (
	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/adaptors/common"
)

type Coverage struct {
	Id string

	QuoteData string `rml:"reference,QuoteData"`

	EffectiveDateYear int64 `rml:"integer,number.integer"`

	LiabilityCSL        string `rml:"enum,LiabCslEnum"`
	LiabilityDeductible string `rml:"enum,LiabDeductibleEnum"`
	CollDeductible      string `rml:"enum,CollDeductibleEnum"`
	CompDeductible      string `rml:"enum,CompDeductibleEnum"`
	GlDeductible        string `rml:"enum,GlDeductibleEnum"`
	MtcDeductible       string `rml:"enum,MtcDeductibleEnum"`

	GlOccuranceLimit        string `rml:"enum,GlOccuranceLimitEnum"`
	GlAggLimit              string `rml:"enum,GlAggLimitEnum"`
	MedPayLimit             string `rml:"enum,MedPayLimitEnum"`
	UmLimit                 string `rml:"enum,UmLimitEnum"`
	UimLimit                string `rml:"enum,UimLimitEnum"`
	UmUimLimit              string `rml:"enum,UmUimLimitEnum"`
	MtcLimit                int64  `rml:"integer,number.integer"`
	TrailerInterchangeLimit int64  `rml:"integer,number.integer"`
	TowingLimit             int64  `rml:"integer,number.integer"`
	PipLimit                string `rml:"enum,PipLimitEnum"`
	UmpdLimit               string `rml:"enum,UmpdLimitEnum"`

	HasGlMisdeliveryLiquids           int64 `rml:"integer,number.integer"`
	HasGlContractual                  int64 `rml:"integer,number.integer"`
	HasGlStopGap                      int64 `rml:"integer,number.integer"`
	HasGlCoverage                     bool  `rml:"bool,boolean"`
	HasPDCoverage                     bool  `rml:"bool,boolean"`
	HasMTCCoverage                    bool  `rml:"bool,boolean"`
	HasReeferBreakdownWithHumanError  bool  `rml:"bool,boolean"`
	HasReeferBreakdown                bool  `rml:"bool,boolean"`
	HasPIPCoverage                    bool  `rml:"bool,boolean"`
	HasGuestPIPCoverage               bool  `rml:"bool,boolean"`
	HasPPICoverage                    bool  `rml:"bool,boolean"`
	HasPIPExcessAttendantCareCoverage bool  `rml:"bool,boolean"`
	HasUMPDCoverage                   bool  `rml:"bool,boolean"`

	HasUMCoverage     bool `rml:"bool,boolean"`
	HasUIMCoverage    bool `rml:"bool,boolean"`
	HasUMUIMCoverage  bool `rml:"bool,boolean"`
	HasMedPayCoverage bool `rml:"bool,boolean"`

	CombineDeductiblePhysMTC bool `rml:"bool,boolean"`

	HasNegotiatedRate     bool    `rml:"bool,boolean"`
	NegotiatedModRateLiab float64 `rml:"float,number.decimal"`
	NegotiatedModRatePhys float64 `rml:"float,number.decimal"`

	OwnerRejectedTortLim bool    `rml:"bool,boolean"`
	PctDriversAccepted   float64 `rml:"float,number.decimal"`
	PercentSubhaul       float64 `rml:"float,number.decimal"`

	HasMTCNamedShipper     bool    `rml:"bool,boolean"`
	MtcNamedShipperLimit   int64   `rml:"integer,number.integer"`
	MtcNamedShipperHaulPct float64 `rml:"float,number.decimal"`

	HasMtcDebrisRemoval   bool  `rml:"bool,boolean"`
	MtcDebrisRemovalLimit int64 `rml:"integer,number.integer"`

	HasMtcEarnedFreight   bool  `rml:"bool,boolean"`
	MtcEarnedFreightLimit int64 `rml:"integer,number.integer"`

	HasMtcPollutantClean   bool  `rml:"bool,boolean"`
	MtcPollutantCleanLimit int64 `rml:"integer,number.integer"`

	HasMtcLossMitigation   bool  `rml:"bool,boolean"`
	MtcLossMitigationLimit int64 `rml:"integer,number.integer"`

	HasMtcMiscEquipment   bool  `rml:"bool,boolean"`
	MtcMiscEquipmentLimit int64 `rml:"integer,number.integer"`

	HasMtcTerminal bool `rml:"bool,boolean"`

	HasMtcTrailerInterchange        bool   `rml:"bool,boolean"`
	MtcTrailerInterchangeDeductible string `rml:"enum,MtcTrailerInterchangeDeductibleEnum"`
	MtcTrailerInterchangeLimit      string `rml:"enum,MtcTrailerInterchangeLimitEnum"`

	HasBIPDBlanketRegularAI bool `rml:"bool,boolean"`
	HasBIPDBlanketPNCAI     bool `rml:"bool,boolean"`
	HasBIPDBlanketWOS       bool `rml:"bool,boolean"`

	HasGLBlanketRegularAI bool `rml:"bool,boolean"`
	HasGLBlanketPNCAI     bool `rml:"bool,boolean"`
	HasGLBlanketWOS       bool `rml:"bool,boolean"`

	HasCargoBlanketRegularAI bool `rml:"bool,boolean"`
	HasCargoBlanketWOS       bool `rml:"bool,boolean"`

	// These three flags are used in RateML to determine surcharge applicability/calculation.
	//
	// Currently, these flags are independently set to true, meaning, they can all be true at the same time.
	// This is because we currently make one call to RateML to price all policies together, not one call per policy.
	//
	// After we migrate to the new API we will make one call to RateML per policy, and in that world only one flag
	// will be set to true at a time.
	//
	// These flags are being set based on the coverages present in the ModelInput object, but in the future we will
	// set them based on the PolicyName field within the Input object.
	IsMotorCarrierPolicy bool `rml:"bool,boolean"`
	IsGLPolicy           bool `rml:"bool,boolean"`
	IsMTCPolicy          bool `rml:"bool,boolean"`

	// These flags are used in RateML to determine which package it is currently being priced for.
	//
	// This can be used for things like determining how much to charge for the group of sub-coverages
	// that we call "APD Package".
	//
	// Note: for now, at least one of these flags will be set to true, but eventually we want to remove
	// their usage in RateML. RateML should be totally agnostic to the package logic, and instead it
	// should price based on the sub-coverages that are present, and their limits and deductibles.
	HasBasicPackage    bool `rml:"bool,boolean"`
	HasStandardPackage bool `rml:"bool,boolean"`
	HasCompletePackage bool `rml:"bool,boolean"`
}

var _ common.Entity = (*Coverage)(nil)

func (c *Coverage) RatemlId() string {
	return c.Id
}

func (c *Coverage) RatemlTypeName() common.EntityType {
	return common.EntityTypeCoverage
}

func (c *Coverage) HandleConnectedEntity(e common.Entity) error {
	switch e.RatemlTypeName() { // nolint:exhaustive
	case common.EntityTypeQuoteData:
		if c.QuoteData != "" {
			return errors.Newf("quotedata entity with id %s already connected", c.QuoteData)
		}
		c.QuoteData = e.RatemlId()
	default:
		return errors.Newf(
			"no linkage possible for %v(id %s) with %v",
			c.RatemlTypeName(),
			c.RatemlId(),
			e.RatemlTypeName(),
		)
	}
	return nil
}

func (c *Coverage) ConnectedEntityTypes() []common.EntityType {
	return []common.EntityType{
		common.EntityTypeQuoteData,
	}
}
