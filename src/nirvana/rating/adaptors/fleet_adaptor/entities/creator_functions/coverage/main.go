package coverage

import (
	"context"
	"reflect"
	"strconv"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	app_enums "nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
	"nirvanatech.com/nirvana/quoting/app_state_machine/app_logic"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/fleet_adaptor/entities/creator_functions"
)

const (
	coverageID = "coverage"

	defaultLiabilityCombinedSingleLimit = "1000000"
	defaultLiabilityDeductible          = "0"
	defaultMedPayLimit                  = "500"
	defaultUmLimit                      = "75000"
	defaultUimLimit                     = "75000"

	defaultCollDeductible = "1000"
	defaultCompDeductible = "1000"

	defaultGlOccurrenceLimit = "1000000"
	defaultGlAggregateLimit  = "2000000"
	defaultGlDeductible      = "0"

	defaultMtcLimit      = int64(100000)
	defaultMtcDeductible = "2500"
)

func New(
	_ context.Context,
	_ *common.ProgramContext,
	input *creator_functions.Input,
) (*entities.Coverage, error) {
	if input == nil {
		return nil, errors.New("input is nil")
	}

	mi := input.ModelInput
	if mi == nil {
		return nil, errors.New("model input is nil")
	}

	extraPricingInfo := mi.ExtraPricingInfo
	if extraPricingInfo == nil {
		return nil, errors.New("extra pricing info is nil")
	}

	// Sting fields that are enums in RateML can't be set to Go default's (i.e. empty string)
	// as they aren't allowed in RateML (the empty string isn't a valid value in the enum).
	// Thus, we need to give default non-empty values.
	coverage := &entities.Coverage{
		Id: coverageID,

		LiabilityCSL:        defaultLiabilityCombinedSingleLimit,
		LiabilityDeductible: defaultLiabilityDeductible,
		MedPayLimit:         defaultMedPayLimit,
		UmLimit:             defaultUmLimit,
		UimLimit:            defaultUimLimit,

		CollDeductible: defaultCollDeductible,
		CompDeductible: defaultCompDeductible,

		GlOccuranceLimit: defaultGlOccurrenceLimit,
		GlAggLimit:       defaultGlAggregateLimit,
		GlDeductible:     defaultGlDeductible,

		MtcLimit:      defaultMtcLimit,
		MtcDeductible: defaultMtcDeductible,
	}

	coverage.EffectiveDateYear = int64(mi.CoverageInfo.EffectiveDate.Year())

	if c := mi.CoverageInfo.GetCoverage(app_enums.CoverageAutoLiability); c != nil {
		coverage.IsMotorCarrierPolicy = true

		if c.Deductible == nil {
			return nil, errors.New("missing AL deductible")
		}

		coverage.LiabilityDeductible = strconv.Itoa(int(*c.Deductible))
	}

	if c := mi.CoverageInfo.GetCoverage(app_enums.CoverageAutoPhysicalDamage); c != nil {
		coverage.IsMotorCarrierPolicy = true

		coverage.HasPDCoverage = true

		if c.Deductible == nil {
			return nil, errors.New("missing APD deductible")
		}

		coverage.CollDeductible = strconv.Itoa(int(*c.Deductible))
		coverage.CompDeductible = strconv.Itoa(int(*c.Deductible))
	}

	if c := mi.CoverageInfo.GetCoverage(app_enums.CoverageGeneralLiability); c != nil {
		coverage.IsGLPolicy = true

		coverage.HasGlCoverage = true

		if c.Deductible == nil {
			return nil, errors.New("missing GL deductible")
		}
		coverage.GlDeductible = strconv.Itoa(int(*c.Deductible))

		if isStopGapCoverageApplicable(mi) {
			coverage.HasGlStopGap = 1
		}

		for _, vehicleType := range mi.EquipmentInfo.OperatingClassDistribution {
			if vehicleType.Class == app_enums.OperatingClassTanker {
				if vehicleType.PercentageOfFleet > 0 {
					coverage.HasGlMisdeliveryLiquids = 1
				}
				break
			}
		}
	}

	if c := mi.CoverageInfo.GetCoverage(app_enums.CoverageMotorTruckCargo); c != nil {
		coverage.IsMTCPolicy = true

		coverage.HasMTCCoverage = true

		if c.Deductible == nil {
			return nil, errors.New("MTC coverage missing deductible")
		}
		coverage.MtcDeductible = strconv.Itoa(int(*c.Deductible))

		if c.Limit == nil {
			return nil, errors.New("MTC coverage missing limit")
		}
		coverage.MtcLimit = int64(*c.Limit)
	}

	if checkIfCoveragesHaveCombinedDeductibles(mi.CoverageInfo, app_logic.MTCAndAPD) {
		coverage.CombineDeductiblePhysMTC = true
	}

	switch packageType := extraPricingInfo.PackageType; packageType {
	case app_enums.IndicationOptionTagBasic:
		coverage.HasBasicPackage = true
	case app_enums.IndicationOptionTagStandard:
		coverage.HasStandardPackage = true
	case app_enums.IndicationOptionTagComplete:
		coverage.HasCompletePackage = true
	default:
		return nil, errors.Errorf("unknown package type: %s", packageType)
	}

	return coverage, nil
}

func isStopGapCoverageApplicable(mi *application.ModelInput) bool {
	return (mi.CompanyInfo.USState == us_states.OH || mi.CompanyInfo.USState == us_states.WA) &&
		mi.CoverageInfo.ContainsCoverage(app_enums.CoverageStopGap)
}

// checkIfCoveragesHaveCombinedDeductibles checks whether a given
// coverageCombination is enabled by the agent in their application
// coverages
func checkIfCoveragesHaveCombinedDeductibles(
	coverageInfo *application.CoverageInfo,
	coverageCombinationToCheck map[app_enums.Coverage]bool,
) bool {
	if coverageInfo == nil {
		return false
	}

	covsWithCombinedDeductibles := coverageInfo.CoveragesWithCombinedDeductibles
	if covsWithCombinedDeductibles == nil {
		return false
	}

	combinedCoveragesList := covsWithCombinedDeductibles.CombinedCoveragesList
	for _, combinedCov := range combinedCoveragesList {
		if reflect.DeepEqual(map[app_enums.Coverage]bool(combinedCov), coverageCombinationToCheck) {
			return true
		}
	}

	return false
}
