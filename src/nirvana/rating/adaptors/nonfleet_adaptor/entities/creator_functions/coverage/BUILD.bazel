load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "coverage",
    srcs = [
        "common.go",
        "main.go",
        "modifier_1.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/coverage",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/time_utils",
        "//nirvana/common-go/us_states",
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/ptypes/programs/common",
        "@com_github_cockroachdb_errors//:errors",
    ],
)

go_test(
    name = "coverage_test",
    srcs = ["main_test.go"],
    embed = [":coverage"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/common-go/time_utils",
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions",
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
