package coverage

import (
	"context"
	"fmt"
	"strconv"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

const (
	defaultLiabLimit  = "1000000"
	defaultMtcLimit   = "100000"
	defaultGlOccLimit = "1000000"
	defaultGlAggLimit = "2000000"

	defaultLiabDeductible   = "0"
	defaultMtcDeductible    = "1000"
	defaultApdDeductible    = "1000"
	defaultUMPDDeductible   = "250"
	defaultTrlIntDeductible = "1000"
	defaultNotDeductible    = "1000"
)

func New(
	_ context.Context,
	_ *common.ProgramContext,
	input *creator_functions.Input,
) (*entities.Coverage, error) {
	policyOriginalStartDate, err := input.GetPolicyOriginalStartDate()
	if err != nil {
		return nil, err
	}

	policyOriginalStartDateAsTime := policyOriginalStartDate.AsTime()
	dateIntFormat, err := time_utils.FormatTimeToYYYYMMDD(policyOriginalStartDateAsTime)
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to convert effective date to YYYYMMDD format")
	}

	companyUSState, err := input.GetCompanyUSState()
	if err != nil {
		return nil, err
	}

	// Note: RateML models will never use the default values we set here
	// to price. The reason for this is that if a sub-coverage is present
	// (and therefore needs to be prices) we validate in the setCoverages
	// method that the custom limit and/or deductible are also present,
	// and use those instead of the defaults. In other words, the flag
	// hasCovX can't be true and use default values for limit/deductible.
	//
	// Nonetheless, if the hasCovX flag is false, RateML still validates
	// that the limit/deductible fields have valid enum values. If we pass
	// nil or the empty string, RateML complaints.
	c := &entities.Coverage{
		Id: "coverage",

		EffectiveDateYear:   int64(policyOriginalStartDateAsTime.Year()),
		PolicyEffectiveDate: int64(dateIntFormat),

		LiabCsl:        defaultLiabLimit,
		LiabDeductible: defaultLiabDeductible,

		TlsLimit: defaultLimits[companyUSState][towingSubCoverageGroupID],

		UmLimit: defaultLimits[companyUSState][umSubCoverageGroupID],

		UimLimit: defaultLimits[companyUSState][uimSubCoverageGroupID],

		UmUimLimit: defaultLimits[companyUSState][umuimSubCoverageGroupID],

		UmPdLimit:      defaultLimits[companyUSState][umpdSubCoverageGroupID],
		UmPdDeductible: defaultUMPDDeductible,

		MedPayLimit: defaultLimits[companyUSState][medPaySubCoverageGroupID],

		PipLimit: defaultLimits[companyUSState][pipSubCoverageGroupID],

		GlAggLimit:       defaultGlAggLimit,
		GlOccuranceLimit: defaultGlOccLimit,

		MtcLimit:      defaultMtcLimit,
		MtcDeductible: defaultMtcDeductible,

		RentalLimit: defaultLimits[companyUSState][rentalReimbursementSubCoverageGroupID],

		ApdDeductible: defaultApdDeductible,

		TrlintLimit:      defaultLimits[companyUSState][tiSubCoverageGroupID],
		TrlintDeductible: defaultTrlIntDeductible,

		NotLimit:      defaultLimits[companyUSState][notSubCoverageGroupID],
		NotDeductible: defaultNotDeductible,
	}

	apdSubCovGroup := &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Comprehensive,
			ptypes.SubCoverageType_SubCoverageType_Collision,
		},
	}
	mtcSubCovGroup := &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Cargo,
		},
	}
	apdMTCCombinedDeductibleSpec := &ptypes.CombinedDeductibleSpec{
		SubCoverageGroups: []*ptypes.SubCoverageGroup{
			apdSubCovGroup,
			mtcSubCovGroup,
		},
	}

	isAPDMTCDeductibleCombined, err := input.IsCombinedDeductiblePresent(apdMTCCombinedDeductibleSpec)
	if err != nil {
		return nil, err
	}

	if isAPDMTCDeductibleCombined {
		c.CombineDeductiblePhysMTC = true
	}

	setterFunctions := []func(*entities.Coverage, *creator_functions.Input) error{
		setLiabCoverage,
		setPDCoverage,
		setCargoCoverage,
		setGLCoverage,
		setUMCoverage,
		setUIMCoverage,
		setUMUIMCoverage,
		setUMPDCoverage,
		setPPICoverage,
		setMedPayCoverage,
		setPIPCoverage,
		setPIPAttendantCareCoverage,
		setTICoverage,
		setNOTCoverage,
		setRentalReimbursementCoverage,
		setTowingCoverage,
		setReeferWithoutHumanErrorCoverage,
		setReeferWithHumanErrorCoverage,
	}

	for _, fn := range setterFunctions {
		err := fn(c, input)
		if err != nil {
			return nil, err
		}
	}

	targetSubCoverageGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(input.PolicyName)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get sub coverage group for policy %s", input.PolicyName)
	}

	hasBlanketRegularAI, err := input.HasBlanketRegularAI(targetSubCoverageGroup)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to check blanket regular AI for policy %s", input.PolicyName)
	}
	c.HasBlanketAdditionalInsured = hasBlanketRegularAI

	hasBlanketPNCAI, err := input.HasBlanketPNCAI(targetSubCoverageGroup)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to check blanket PNC AI for policy %s", input.PolicyName)
	}
	c.HasBlanketPNCAdditionalInsured = hasBlanketPNCAI

	hasBlanketWOS, err := input.HasBlanketWOS(targetSubCoverageGroup)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to check blanket WOS for policy %s", input.PolicyName)
	}
	c.HasBlanketWaiverOfSubrogation = hasBlanketWOS

	return c, nil
}

func setLiabCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(liabSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasLiabCoverage = true

		ls, err := input.GetLimitSpec(liabSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain AL limit")
		}
		c.LiabCsl = strconv.Itoa(int(ls.Amount))

		ds, err := input.GetDeductibleSpec(liabSubCoverageGroup)
		if err != nil {
			return errors.Wrap(err, "failed to obtain AL deductible")
		}
		c.LiabDeductible = strconv.Itoa(int(ds.Amount))
	}

	return nil
}

func setPDCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(pdSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasAPDCoverage = true

		ds, err := input.GetDeductibleSpec(pdSubCoverageGroup)
		if err != nil {
			return errors.Wrap(err, "failed to obtain APD deductible")
		}
		c.ApdDeductible = strconv.Itoa(int(ds.Amount))
	}

	return nil
}

func setCargoCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(cargoSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasMTCCoverage = true

		ls, err := input.GetLimitSpec(cargoSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain Cargo limit")
		}
		c.MtcLimit = strconv.Itoa(int(ls.Amount))

		ds, err := input.GetDeductibleSpec(cargoSubCoverageGroup)
		if err != nil {
			return errors.Wrap(err, "failed to obtain Cargo deductible")
		}
		c.MtcDeductible = strconv.Itoa(int(ds.Amount))
	}

	return nil
}

func setGLCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(glSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasGLCoverage = true
	}

	return nil
}

func setUMCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(umSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasUMCoverage = true

		ls, err := input.GetLimitSpec(umSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain UM limit")
		}
		c.UmLimit = strconv.Itoa(int(ls.Amount))
	}

	return nil
}

func setUIMCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(uimSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasUIMCoverage = true

		ls, err := input.GetLimitSpec(uimSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain UIM limit")
		}
		c.UimLimit = strconv.Itoa(int(ls.Amount))
	}

	return nil
}

func setUMUIMCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(umuimSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasUMUIMCoverage = true

		ls, err := input.GetLimitSpec(umuimSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain UMUIM limit")
		}
		c.UmUimLimit = strconv.Itoa(int(ls.Amount))
	}

	return nil
}

func setUMPDCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(umpdSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasUMPDCoverage = true

		ls, err := input.GetLimitSpec(umpdSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain UMPD limit")
		}
		c.UmPdLimit = strconv.Itoa(int(ls.Amount))

		ds, err := input.GetDeductibleSpec(umpdSubCoverageGroup)
		if err != nil {
			return errors.Wrap(err, "failed to obtain UMPD deductible")
		}
		c.UmPdDeductible = strconv.Itoa(int(ds.Amount))
	}

	return nil
}

func setPPICoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(ppiSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasPpiCoverage = true
	}

	return nil
}

func setMedPayCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(medPaySubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasMedPayCoverage = true

		ls, err := input.GetLimitSpec(medPaySubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain MedPay limit")
		}
		c.MedPayLimit = strconv.Itoa(int(ls.Amount))
	}

	return nil
}

func setPIPCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(pipSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasPIPCoverage = true

		ls, err := input.GetLimitSpec(pipSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain PIP limit")
		}
		c.PipLimit = strconv.Itoa(int(ls.Amount))
	}

	return nil
}

func setPIPAttendantCareCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(pipAttendantCareSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasPIPExcessAttendantCareCoverage = true
	}

	return nil
}

func setTICoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(tiSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasTrailerInterchange = true

		ls, err := input.GetLimitSpec(tiSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain TI limit")
		}
		c.TrlintLimit = strconv.Itoa(int(ls.Amount))

		ds, err := input.GetDeductibleSpec(tiSubCoverageGroup)
		if err != nil {
			return errors.Wrap(err, "failed to obtain TI deductible")
		}
		c.TrlintDeductible = strconv.Itoa(int(ds.Amount))
	}

	return nil
}

func setNOTCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(notSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasNonOwnedTrailer = true

		ls, err := input.GetLimitSpec(notSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain NOT limit")
		}
		c.NotLimit = strconv.Itoa(int(ls.Amount))

		ds, err := input.GetDeductibleSpec(notSubCoverageGroup)
		if err != nil {
			return errors.Wrap(err, "failed to obtain NOT deductible")
		}
		c.NotDeductible = strconv.Itoa(int(ds.Amount))
	}

	return nil
}

func setRentalReimbursementCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(rentalReimbursementSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasRentalCoverage = true

		ls, err := input.GetLimitSpec(rentalReimbursementSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain RentalReimbursement limit")
		}
		c.RentalLimit = parseRentalReimbursementLimit(int(ls.Amount))
	}

	return nil
}

// parseRentalReimbursementLimit parses the rental reimbursement limit into the
// format expected by RateML. (i.e. "XperdayYmax" where X is the daily limit and
// Y is the maximum limit, assuming a 30-day month).
func parseRentalReimbursementLimit(limit int) string {
	dailyLimit := limit / 30
	return fmt.Sprintf("%dperday%dmax", dailyLimit, limit)
}

func setTowingCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(towingSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasTLSCoverage = true

		ls, err := input.GetLimitSpec(towingSubCoverageGroup, ptypes.LimitCadenceType_LimitCadenceType_Occurrence)
		if err != nil {
			return errors.Wrap(err, "failed to obtain Towing limit")
		}
		c.TlsLimit = strconv.Itoa(int(ls.Amount))
	}

	return nil
}

func setReeferWithoutHumanErrorCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(reeferWithoutHumanErrorSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasReeferBreakdown = true
	}

	return nil
}

func setReeferWithHumanErrorCoverage(c *entities.Coverage, input *creator_functions.Input) error {
	groupIsPresent, err := input.IsSubCoverageGroupPresent(reeferWithHumanErrorSubCoverageGroup)
	if err != nil {
		return err
	}

	if groupIsPresent {
		c.HasReeferBreakdownWithHumanError = true
	}

	return nil
}
