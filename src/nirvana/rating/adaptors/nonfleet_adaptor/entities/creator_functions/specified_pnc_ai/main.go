package specified_pnc_ai

import (
	"context"

	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

func New(
	_ context.Context,
	_ *common.ProgramContext,
	input *creator_functions.Input,
) ([]*entities.SpecifiedPNCAI, error) {
	targetSubCoverageGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(input.PolicyName)
	if err != nil {
		return nil, err
	}

	pncAIs, err := input.GetSpecifiedPNCAIs(targetSubCoverageGroup)
	if err != nil {
		return nil, err
	}

	records := make([]*entities.SpecifiedPNCAI, 0)
	for _, ai := range pncAIs {
		records = append(records, &entities.SpecifiedPNCAI{
			Id: ai.Id,
		})
	}

	return records, nil
}
