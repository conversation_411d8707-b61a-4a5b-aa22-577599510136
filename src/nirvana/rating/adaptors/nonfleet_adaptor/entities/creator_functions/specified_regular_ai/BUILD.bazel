load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "specified_regular_ai",
    srcs = ["main.go"],
    importpath = "nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions/specified_regular_ai",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/rating/adaptors/common",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities",
        "//nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions",
        "//nirvana/rating/pricing/api/ptypes/programs/common",
    ],
)
