package specified_regular_ai

import (
	"context"

	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

func New(
	_ context.Context,
	_ *common.ProgramContext,
	input *creator_functions.Input,
) ([]*entities.SpecifiedRegularAI, error) {
	targetSubCoverageGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(input.PolicyName)
	if err != nil {
		return nil, err
	}

	regularAIs, err := input.GetSpecifiedRegularAIs(targetSubCoverageGroup)
	if err != nil {
		return nil, err
	}

	records := make([]*entities.SpecifiedRegularAI, 0)
	for _, ai := range regularAIs {
		records = append(records, &entities.SpecifiedRegularAI{
			Id: ai.Id,
		})
	}

	return records, nil
}
