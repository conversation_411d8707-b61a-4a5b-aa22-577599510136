package specified_wos

import (
	"context"

	"nirvanatech.com/nirvana/rating/adaptors/common"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities"
	"nirvanatech.com/nirvana/rating/adaptors/nonfleet_adaptor/entities/creator_functions"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

func New(
	_ context.Context,
	_ *common.ProgramContext,
	input *creator_functions.Input,
) ([]*entities.SpecifiedWOS, error) {
	targetSubCoverageGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(input.PolicyName)
	if err != nil {
		return nil, err
	}

	specifiedWOSs, err := input.GetSpecifiedWOSs(targetSubCoverageGroup)
	if err != nil {
		return nil, err
	}

	records := make([]*entities.SpecifiedWOS, 0)
	for _, ai := range specifiedWOSs {
		records = append(records, &entities.SpecifiedWOS{
			Id: ai.Id,
		})
	}

	return records, nil
}
