entity Coverage {
    policyEffectiveDate number.integer // in YYYYMMDD format

    hasLiabBlanketRegularAdditionalInsured boolean
    hasLiabBlanketPNCAdditionalInsured boolean
    hasLiabBlanketWaiverOfSubrogation boolean

    hasWorkersCompPolicy boolean

    hasLiabCoverage boolean
    hasMedPayCoverage boolean
    hasUIMBICoverage boolean
    hasUMUIMCoverage boolean
    hasHiredAutoLiabCoverage boolean
    hasHiredAutoPDCoverage boolean
    hasNonOwnedVehicleCoverage boolean
    hasMedicalExpenseBenefitsCoverage boolean
    hasWorkLossBenefitsCoverage boolean
    hasFuneralExpenseBenefitsCoverage boolean
    hasAccidentalDeathBenefitsCoverage boolean
    hasExtraordinaryMedicalBenefitsCoverage boolean
    hasPIPCoverage boolean

    liabLimit LiabLimitEnum
    medPayLimit MedPayLimitEnum
    umLimit UMLimitEnum
    umbiLimit UMBILimitEnum
    uimbiLimit UIMBILimitEnum
    umuimLimit UMUIMLimitEnum
    umuimDeductible UMUIMDeductibleEnum
    medicalExpenseBenefitsLimit MedicalExpenseBenefitsLimitEnum
    workLossBenefitsLimit WorkLossBenefitsLimitEnum
    funeralExpenseBenefitsLimit FuneralExpenseBenefitsLimitEnum
    accidentalDeathBenefitsLimit AccidentalDeathBenefitsLimitEnum
    extraordinaryMedicalBenefitsLimit ExtraordinaryMedicalBenefitsLimitEnum
    pipLimit PIPLimitEnum

    areUMLimitsStacked boolean
    areUMUIMLimitsAddedOn boolean

    hiredAutoPDDeductible HiredAutoPDDeductibleEnum

    liabScheduleMod number.decimal
    liabLossFreeMod number.decimal
    liabExperienceRatingMod number.decimal

    pdScheduleMod number.decimal
    pdLossFreeMod number.decimal
    pdExperienceRatingMod number.decimal

    driverClassMod number.decimal
}

output Coverage liabBlanketRegularAdditionalInsuredPremium number.decimal {
    lookup([BlanketAdditionalInsuredPremium], [BlanketAdditionalInsuredPremium.premium],
        hasLiabBlanketRegularAdditionalInsured
    )
}

output Coverage liabBlanketPNCAdditionalInsuredPremium number.decimal {
    lookup([BlanketPNCPremium], [BlanketPNCPremium.premium],
        hasLiabBlanketPNCAdditionalInsured
    )
}

output Coverage liabBlanketWaiverOfSubrogationPremium number.decimal {
    lookup([BlanketWaiverOfSubrogationPremium], [BlanketWaiverOfSubrogationPremium.premium],
        hasLiabBlanketWaiverOfSubrogation
    )
}
