entity Surcharge {
    company Company
    coverage Coverage

    hiredAutoLiabRater HiredAutoLiabRater
    hiredAutoPDRater HiredAutoPDRater

    nonOwnedVehicleRater NonOwnedVehicleRater

    umRaters list[UMRater]
    umuimRaters list[UMUIMRater]
    umbiRaters list[UMBIRater]
    uimbiRaters list[UIMBIRater]
    umpdRaters list[UMPDRater]
    medPayRaters list[MedPayRater]
    fpbRaters list[FPBRater]
    pipRaters list[PIPRater]
    towingRaters list[TowingRater]
    rentalRaters list[RentalRater]
}

entity Surcharge includes BaseLiabRater
entity Surcharge includes BasePDRater

property Surcharge umpdTotalPremium number.decimal {
    sum(umpdRaters, [UMPDRater.vehicleUMPDPremium])
}

property Surcharge umbiTotalPremium number.decimal {
    sum(umbiRaters, [UMBIRater.vehicleUMBIPremium])
}

property Surcharge umTotalPremium number.decimal {
    sum(umRaters, [UMRater.vehicleUMPremium])
}

property Surcharge umuimTotalPremium number.decimal {
    sum(umuimRaters, [UMUIMRater.vehicleUMUIMPremium])
}

property Surcharge uimbiTotalPremium number.decimal {
    sum(uimbiRaters, [UIMBIRater.vehicleUIMBIPremium])
}

property Surcharge medPayTotalPremium number.decimal {
    sum(medPayRaters, [MedPayRater.vehicleMedPayPremium])
}

property Surcharge medicalExpenseBenefitsPremium number.decimal {
    sum(fpbRaters, [FPBRater.vehicleMedicalExpenseBenefitsPremium])
}

property Surcharge workLossBenefitsPremium number.decimal {
    sum(fpbRaters, [FPBRater.vehicleWorkLossBenefitsPremium])
}

property Surcharge funeralExpenseBenefitsPremium number.decimal {
    sum(fpbRaters, [FPBRater.vehicleFuneralExpenseBenefitsPremium])
}

property Surcharge accidentalDeathBenefitsPremium number.decimal {
    sum(fpbRaters, [FPBRater.vehicleAccidentalDeathBenefitsPremium])
}

property Surcharge extraordinaryMedicalBenefitsPremium number.decimal {
    sum(fpbRaters, [FPBRater.vehicleExtraordinaryMedicalBenefitsPremium])
}

property Surcharge pipTotalPremium number.decimal {
    sum(pipRaters, [PIPRater.vehiclePIPPremium])
}

property Surcharge towingTotalPremium number.decimal {
    sum(towingRaters, [TowingRater.vehicleTowingPremium])
}

property Surcharge rentalTotalPremium number.decimal {
    sum(rentalRaters, [RentalRater.vehicleRentalPremium])
}

property Surcharge refundablePrem number.decimal {
    (
      liabTotalPremium
      + umTotalPremium
      + umuimTotalPremium
      + umpdTotalPremium
      + umbiTotalPremium
      + uimbiTotalPremium
      + medPayTotalPremium
      + medicalExpenseBenefitsPremium
      + workLossBenefitsPremium
      + funeralExpenseBenefitsPremium
      + accidentalDeathBenefitsPremium
      + extraordinaryMedicalBenefitsPremium
      + pipTotalPremium
      + compCollTotalPremium
      + compOnlyTotalPremium
      + towingTotalPremium
      + rentalTotalPremium
      + hiredAutoLiabRater->hiredAutoLiabPremium
      + hiredAutoPDRater->hiredAutoPDPremium
      + nonOwnedVehicleRater->nonOwnedVehiclePremium
    )
}

property Surcharge nonRefundablePrem number.decimal {
    (
      coverage->liabBlanketRegularAdditionalInsuredPremium
      + coverage->liabBlanketWaiverOfSubrogationPremium
      + coverage->liabBlanketPNCAdditionalInsuredPremium
    )
}

property Surcharge surplusLinesTax number.decimal {
    lookup([SurplusSurcharges], [SurplusSurcharges.surplusLinesTax],
        company->state,
        coverage->policyEffectiveDate
    )
}

property Surcharge surplusStampingFee number.decimal {
    lookup([SurplusSurcharges], [SurplusSurcharges.surplusStampingFee],
        company->state,
        coverage->policyEffectiveDate
    )
}

property Surcharge surplusFilingServiceFee number.decimal {
    lookup([SurplusSurcharges], [SurplusSurcharges.surplusFilingServiceFee],
        company->state,
        coverage->policyEffectiveDate
    )
}

property Surcharge surplusFireTax number.decimal {
    lookup([SurplusSurcharges], [SurplusSurcharges.surplusFireTax],
        company->state,
        coverage->policyEffectiveDate
    )
}

output Surcharge surplusLinesTaxFromRefundablePremium number.decimal {
    round(surplusLinesTax * refundablePrem, 2)
}

output Surcharge surplusLinesTaxFromNonRefundablePremium number.decimal {
    round(surplusLinesTax * nonRefundablePrem, 2)
}

output Surcharge surplusStampingFeeFromRefundablePremium number.decimal {
    round(surplusStampingFee * refundablePrem, 2)
}

output Surcharge surplusStampingFeeFromNonRefundablePremium number.decimal {
    round(surplusStampingFee * nonRefundablePrem, 2)
}

output Surcharge surplusFilingServiceFeeFromRefundablePremium number.decimal {
    round(surplusFilingServiceFee * refundablePrem, 2)
}

output Surcharge surplusFilingServiceFeeFromNonRefundablePremium number.decimal {
    round(surplusFilingServiceFee * nonRefundablePrem, 2)
}

output Surcharge surplusFireTaxFromRefundablePremium number.decimal {
    round(surplusFireTax * refundablePrem, 2)
}

output Surcharge surplusFireTaxFromNonRefundablePremium number.decimal {
    round(surplusFireTax * nonRefundablePrem, 2)
}
