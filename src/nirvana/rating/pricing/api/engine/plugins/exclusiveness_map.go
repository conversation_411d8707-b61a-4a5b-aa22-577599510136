package plugins

import "nirvanatech.com/nirvana/rating/pricing/api/ptypes"

type ExclusivenessMap map[ptypes.PluginID][]ptypes.PluginID

// exclusivenessMap holds one entry for each plugin that is
// supported by the system. It maps a plugin ID to a list of
// plugin IDs with which it is mutually exclusive.
//
//nolint:exhaustive
var exclusivenessMap = ExclusivenessMap{
	ptypes.PluginID_PluginID_MetricsReporting_V1:                                    {},
	ptypes.PluginID_PluginID_RateMLArtifactUpload_V1:                                {},
	ptypes.PluginID_PluginID_LossHistoryExperiment_V1:                               {},
	ptypes.PluginID_PluginID_FullyEarnedChargesDedupRule_V1:                         {},
	ptypes.PluginID_PluginID_MaxFullyEarnedChargesRule_V1:                           {},
	ptypes.PluginID_PluginID_MCCASurchargeDedupRule_V1:                              {},
	ptypes.PluginID_PluginID_DriverEndorsementRule_V1:                               {},
	ptypes.PluginID_PluginID_HiredAutoPDRequiresHiredAutoLiabValidation_V1:          {},
	ptypes.PluginID_PluginID_CollRequiresCompValidation_V1:                          {},
	ptypes.PluginID_PluginID_SamePDCombinationForAllVehiclesValidation_V1:           {},
	ptypes.PluginID_PluginID_UMPDRestrictedToPPTVehiclesValidation_V1:               {},
	ptypes.PluginID_PluginID_CollAndUMPDMutualExclusivityValidation_V1:              {},
	ptypes.PluginID_PluginID_TowingRestrictedToCertainVehiclesValidation_V1:         {},
	ptypes.PluginID_PluginID_NoSpecifiedXModifierValidation_V1:                      {},
	ptypes.PluginID_PluginID_HiredAutoPDRequiresCompValidation_V1:                   {},
	ptypes.PluginID_PluginID_UMBIIsRequiredIfLiabIsPresentValidation_V1:             {},
	ptypes.PluginID_PluginID_TowingRequiresPDValidation_V1:                          {},
	ptypes.PluginID_PluginID_RentalRequiresPDValidation_V1:                          {},
	ptypes.PluginID_PluginID_MedPayRequiresLiabValidation_V1:                        {},
	ptypes.PluginID_PluginID_MedicalExpenseBenefitsRequiresLiabValidation_V1:        {},
	ptypes.PluginID_PluginID_UMBIRequiresLiabValidation_V1:                          {},
	ptypes.PluginID_PluginID_UMPDRequiresLiabValidation_V1:                          {},
	ptypes.PluginID_PluginID_UIMBIRequiresLiabValidation_V1:                         {},
	ptypes.PluginID_PluginID_HiredAutoLiabRequiresLiabValidation_V1:                 {},
	ptypes.PluginID_PluginID_HiredAutoPDRequiresLiabValidation_V1:                   {},
	ptypes.PluginID_PluginID_NonOwnedVehicleRequiresLiabValidation_V1:               {},
	ptypes.PluginID_PluginID_BIAndPDRequireJointSelectionValidation_V1:              {},
	ptypes.PluginID_PluginID_OnlyBAPolicyIsSupportedValidation_V1:                   {},
	ptypes.PluginID_PluginID_UMPDRequiresUMBIValidation_V1:                          {},
	ptypes.PluginID_PluginID_SamePerOccurrenceLimitForUMBIAndUIMBIValidation_V1:     {},
	ptypes.PluginID_PluginID_ChargesProration_V1:                                    {},
	ptypes.PluginID_PluginID_SamePerOccurrenceLimitForHiredAutoAndLiabValidation_V1: {},
	ptypes.PluginID_PluginID_PIPRequiresLiabValidation_V1:                           {},
	ptypes.PluginID_PluginID_UMUIMRequiresLiabValidation_V1:                         {},
	ptypes.PluginID_PluginID_MedPayAndPIPMutualExclusivityValidation_V1:             {},
	ptypes.PluginID_PluginID_PolicyModifiersSubCoveragesValidation_V1:               {},
}
