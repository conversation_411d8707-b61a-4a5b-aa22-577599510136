load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "policy_modifiers_sub_coverages_validation_v1",
    srcs = ["validation.go"],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/common/policy_modifiers_sub_coverages_validation_v1",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/slice_utils",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/ptypes/programs/common",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "policy_modifiers_sub_coverages_validation_v1_test",
    srcs = ["validation_test.go"],
    embed = [":policy_modifiers_sub_coverages_validation_v1"],
    deps = [
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/ptypes/programs/common",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
