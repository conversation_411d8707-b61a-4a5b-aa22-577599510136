package policy_modifiers_sub_coverages_validation_v1

import (
	"context"

	"github.com/cockroachdb/errors"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

type Deps struct {
	fx.In
}

type Validation struct {
	deps Deps

	nextPluginApplyFn common.PluginApplyFn
}

var _ common.PluginI = (*Validation)(nil)

type policyModifier interface {
	GetSubCoverageGroup() *ptypes.SubCoverageGroup
}

func (v *Validation) Apply(
	ctx context.Context,
	input *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	if v.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	policySpec, err := input.GetPolicySpec()
	if err != nil {
		return nil, err
	}

	expectedSubCoverageGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(policySpec.PolicyName)
	if err != nil {
		return nil, errors.Wrapf(err, "policy name %s is not supported for policy modifiers", policySpec.PolicyName)
	}

	for _, policyChunkSpec := range policySpec.ChunkSpecs {
		regularAIBlankets := slice_utils.Map(
			policyChunkSpec.GetRegularAdditionalInsuredBlankets(),
			func(bai *ptypes.BlanketRegularAdditionalInsured) policyModifier {
				if bai == nil {
					return nil
				}
				return bai
			},
		)
		if err := v.validatePolicyModifiers(regularAIBlankets, expectedSubCoverageGroup); err != nil {
			return nil, errors.Wrapf(
				err,
				"policy chunk spec (policy=%s, chunkID=%s): error validating regular additional insured blankets",
				policySpec.PolicyName,
				policyChunkSpec.ChunkId,
			)
		}

		pncAIBlankets := slice_utils.Map(
			policyChunkSpec.GetPrimaryAndNonContributoryAdditionalInsuredBlankets(),
			func(bai *ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured) policyModifier {
				if bai == nil {
					return nil
				}
				return bai
			},
		)
		if err := v.validatePolicyModifiers(pncAIBlankets, expectedSubCoverageGroup); err != nil {
			return nil, errors.Wrapf(
				err,
				"policy chunk spec (policy=%s, chunkID=%s): error validating primary and non-contributory additional insured blankets",
				policySpec.PolicyName,
				policyChunkSpec.ChunkId,
			)
		}

		wosBlankets := slice_utils.Map(
			policyChunkSpec.GetWaiverOfSubrogationBlankets(),
			func(wos *ptypes.BlanketWaiverOfSubrogation) policyModifier {
				if wos == nil {
					return nil
				}
				return wos
			},
		)
		if err := v.validatePolicyModifiers(wosBlankets, expectedSubCoverageGroup); err != nil {
			return nil, errors.Wrapf(
				err,
				"policy chunk spec (policy=%s, chunkID=%s): error validating waiver of subrogation blankets",
				policySpec.PolicyName,
				policyChunkSpec.ChunkId,
			)
		}

		specifiedRegularAIs := slice_utils.Map(
			policyChunkSpec.GetSpecifiedRegularAdditionalInsureds(),
			func(sai *ptypes.SpecifiedRegularAdditionalInsured) policyModifier {
				if sai == nil {
					return nil
				}
				return sai
			},
		)
		if err := v.validatePolicyModifiers(specifiedRegularAIs, expectedSubCoverageGroup); err != nil {
			return nil, errors.Wrapf(
				err,
				"policy chunk spec (policy=%s, chunkID=%s): error validating specified regular additional insureds",
				policySpec.PolicyName,
				policyChunkSpec.ChunkId,
			)
		}

		specifiedPNCAIs := slice_utils.Map(
			policyChunkSpec.GetSpecifiedPrimaryAndNonContributoryAdditionalInsureds(),
			func(sai *ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured) policyModifier {
				if sai == nil {
					return nil
				}
				return sai
			},
		)
		if err := v.validatePolicyModifiers(specifiedPNCAIs, expectedSubCoverageGroup); err != nil {
			return nil, errors.Wrapf(
				err,
				"policy chunk spec (policy=%s, chunkID=%s): error validating specified primary and non-contributory additional insureds",
				policySpec.PolicyName,
				policyChunkSpec.ChunkId,
			)
		}

		specifiedWOSs := slice_utils.Map(
			policyChunkSpec.GetSpecifiedThirdPartiesWithWOS(),
			func(wos *ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation) policyModifier {
				if wos == nil {
					return nil
				}
				return wos
			},
		)
		if err := v.validatePolicyModifiers(specifiedWOSs, expectedSubCoverageGroup); err != nil {
			return nil, errors.Wrapf(
				err,
				"policy chunk spec (policy=%s, chunkID=%s): error validating specified waiver of subrogation",
				policySpec.PolicyName,
				policyChunkSpec.ChunkId,
			)
		}
	}

	return v.nextPluginApplyFn(ctx, input)
}

func (v *Validation) validatePolicyModifiers(
	modifiers []policyModifier,
	expectedSubCoverageGroup *ptypes.SubCoverageGroup,
) error {
	for _, modifier := range modifiers {
		if modifier == nil {
			continue
		}

		actualSubCoverageGroup := modifier.GetSubCoverageGroup()
		if !actualSubCoverageGroup.Equal(expectedSubCoverageGroup) {
			return errors.Newf(
				"invalid sub coverage group (expected=%s, actual=%s)",
				expectedSubCoverageGroup,
				actualSubCoverageGroup,
			)
		}
	}

	return nil
}

func (v *Validation) SetNextPluginApplyFn(nextPluginApplyFn common.PluginApplyFn) error {
	v.nextPluginApplyFn = nextPluginApplyFn
	return nil
}

func NewValidation(deps Deps) *Validation {
	return &Validation{deps: deps}
}
