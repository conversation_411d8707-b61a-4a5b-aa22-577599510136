package policy_modifiers_sub_coverages_validation_v1

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

var (
	supportedPolicyNames = []ptypes.PolicyName{
		ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
		ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
		ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
		ptypes.PolicyName_PolicyName_BUSINESS_AUTO,
	}

	invalidSubCoverageGroup = &ptypes.SubCoverageGroup{
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Unspecified,
		},
	}
)

func Test_Apply_WithNilNextPluginApplyFn(t *testing.T) {
	validation := NewValidation(Deps{})

	output, err := validation.Apply(context.Background(), nil)
	require.Error(t, err)
	require.Nil(t, output)
	assert.Contains(t, err.Error(), "nextPluginApplyFn can't be nil")
}

func TestValidation_Apply_WithSuccess(t *testing.T) {
	type testCase struct {
		name          string
		chunkSpecFunc func(*ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec
	}

	testCases := []testCase{
		{
			name: "Empty modifiers",
			chunkSpecFunc: func(_ *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
				}
			},
		},
		{
			name: "All modifiers with correct sub-coverages",
			chunkSpecFunc: func(subCovGroup *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
						{SubCoverageGroup: subCovGroup},
					},
					PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
						{SubCoverageGroup: subCovGroup},
					},
					WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
						{SubCoverageGroup: subCovGroup},
					},
					SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
						{Id: "ai1", SubCoverageGroup: subCovGroup},
						{Id: "ai2", SubCoverageGroup: subCovGroup},
					},
					SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
						{Id: "pnc1", SubCoverageGroup: subCovGroup},
						{Id: "pnc2", SubCoverageGroup: subCovGroup},
					},
					SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
						{Id: "wos1", SubCoverageGroup: subCovGroup},
						{Id: "wos2", SubCoverageGroup: subCovGroup},
					},
				}
			},
		},
	}

	for _, policyName := range supportedPolicyNames {
		policyNumber := "TEST-POLICY-001"
		subCovGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(policyName)
		require.NoError(t, err)

		t.Run(policyName.String(), func(t *testing.T) {
			for _, tc := range testCases {
				tc := tc
				testName := tc.name

				t.Run(testName, func(t *testing.T) {
					validation := NewValidation(Deps{})
					expectedOutput := common.PluginChainOutput{
						"chunk1": &common.ChunkOutput{},
					}
					err := validation.SetNextPluginApplyFn(func(ctx context.Context, input *common.PluginChainInput) (common.PluginChainOutput, error) {
						return expectedOutput, nil
					})
					require.NoError(t, err)

					chunkSpec := tc.chunkSpecFunc(subCovGroup)
					request := &ptypes.Request{
						PolicySpecs: []*ptypes.PolicySpec{
							{
								PolicyNumber: policyNumber,
								PolicyName:   policyName,
								ChunkSpecs:   []*ptypes.PolicySpec_ChunkSpec{chunkSpec},
							},
						},
					}

					input := &common.PluginChainInput{
						PolicyNumber: policyNumber,
						Request:      request,
					}

					output, err := validation.Apply(context.Background(), input)
					require.NoError(t, err)
					require.NotNil(t, output)
					assert.Equal(t, expectedOutput, output)
				})
			}
		})
	}
}

func TestValidation_Apply_WithValidationErrors(t *testing.T) {
	type modifierTestCase struct {
		name          string
		chunkSpecFunc func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec
		expectedError string
	}

	modifierTestCases := []modifierTestCase{
		{
			name: "Blanket Regular AI with wrong sub-coverage",
			chunkSpecFunc: func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{
						{SubCoverageGroup: wrongSubCov},
					},
				}
			},
			expectedError: "error validating regular additional insured blankets",
		},
		{
			name: "Blanket PNC AI with wrong sub-coverage",
			chunkSpecFunc: func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{
						{SubCoverageGroup: wrongSubCov},
					},
				}
			},
			expectedError: "error validating primary and non-contributory additional insured blankets",
		},
		{
			name: "Blanket WOS with wrong sub-coverage",
			chunkSpecFunc: func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{
						{SubCoverageGroup: wrongSubCov},
					},
				}
			},
			expectedError: "error validating waiver of subrogation blankets",
		},
		{
			name: "Specified Regular AI with wrong sub-coverage",
			chunkSpecFunc: func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					SpecifiedRegularAdditionalInsureds: []*ptypes.SpecifiedRegularAdditionalInsured{
						{Id: "ai1", SubCoverageGroup: wrongSubCov},
					},
				}
			},
			expectedError: "error validating specified regular additional insureds",
		},
		{
			name: "Specified PNC AI with wrong sub-coverage",
			chunkSpecFunc: func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					SpecifiedPrimaryAndNonContributoryAdditionalInsureds: []*ptypes.SpecifiedPrimaryAndNonContributoryAdditionalInsured{
						{Id: "pnc1", SubCoverageGroup: wrongSubCov},
					},
				}
			},
			expectedError: "error validating specified primary and non-contributory additional insureds",
		},
		{
			name: "Specified WOS with wrong sub-coverage",
			chunkSpecFunc: func(wrongSubCov *ptypes.SubCoverageGroup) *ptypes.PolicySpec_ChunkSpec {
				return &ptypes.PolicySpec_ChunkSpec{
					ChunkId: "chunk1",
					SpecifiedThirdPartiesWithWOS: []*ptypes.SpecifiedThirdPartyWithWaiverOfSubrogation{
						{Id: "wos1", SubCoverageGroup: wrongSubCov},
					},
				}
			},
			expectedError: "error validating specified waiver of subrogation",
		},
	}

	for _, policyName := range supportedPolicyNames {
		policyNumber := "TEST-POLICY-001"

		t.Run(policyName.String(), func(t *testing.T) {
			for _, tc := range modifierTestCases {
				tc := tc

				t.Run(tc.name, func(t *testing.T) {
					validation := NewValidation(Deps{})
					err := validation.SetNextPluginApplyFn(func(ctx context.Context, input *common.PluginChainInput) (common.PluginChainOutput, error) {
						require.Fail(t, "next plugin should not be called")
						return nil, nil
					})
					require.NoError(t, err)

					chunkSpec := tc.chunkSpecFunc(invalidSubCoverageGroup)
					request := &ptypes.Request{
						PolicySpecs: []*ptypes.PolicySpec{
							{
								PolicyNumber: policyNumber,
								PolicyName:   policyName,
								ChunkSpecs:   []*ptypes.PolicySpec_ChunkSpec{chunkSpec},
							},
						},
					}

					input := &common.PluginChainInput{
						PolicyNumber: policyNumber,
						Request:      request,
					}

					output, err := validation.Apply(context.Background(), input)
					require.Error(t, err)
					require.Nil(t, output)
					require.Regexp(t, tc.expectedError, err.Error())
				})
			}
		})
	}
}

func TestValidation_Apply_WithUnsupportedPolicyType(t *testing.T) {
	validation := NewValidation(Deps{})
	err := validation.SetNextPluginApplyFn(func(ctx context.Context, input *common.PluginChainInput) (common.PluginChainOutput, error) {
		require.Fail(t, "next plugin should not be called")
		return nil, nil
	})
	require.NoError(t, err)

	request := &ptypes.Request{
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "TEST-POLICY-001",
				PolicyName:   ptypes.PolicyName_PolicyName_Unspecified,
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					{ChunkId: "chunk1"},
				},
			},
		},
	}

	input := &common.PluginChainInput{
		PolicyNumber: "TEST-POLICY-001",
		Request:      request,
	}

	output, err := validation.Apply(context.Background(), input)
	require.Error(t, err)
	require.Nil(t, output)
	require.Regexp(t, "policy name PolicyName_Unspecified is not supported for policy modifiers", err.Error())
}
