load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "fully_earned_charges_dedup_rule_v1",
    srcs = [
        "apply.go",
        "rule.go",
    ],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/fully_earned_charges_dedup_rule_v1",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/slice_utils",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/ptypes/programs/common",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "fully_earned_charges_dedup_rule_v1_test",
    srcs = ["rule_test.go"],
    embed = [":fully_earned_charges_dedup_rule_v1"],
    deps = [
        "//nirvana/common-go/proto",
        "//nirvana/infra/fx/testloader",
        "//nirvana/rating/pricing/api/engine/plugins/common",
        "//nirvana/rating/pricing/api/ptypes",
        "//nirvana/rating/pricing/api/ptypes/programs/common",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_fx//fxtest",
    ],
)
