package max_fully_earned_charges_rule_v1

import (
	"context"
	"slices"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/common"
	nf_common "nirvanatech.com/nirvana/rating/pricing/api/engine/plugins/impls/nonfleet/common"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	programs_common "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common"
)

const maxFullyEarnedChargeCount = 5

// The apply method assumes that the data was already
// validated.
//
// For instance, it assumes that AIs and TP with WOS
// are unique within a policy chunk.
//
// Note: this rule assumes that AIs are valid
// (no unspecified value and valid non-nil sub-coverage group).
// We don't validate this here, because it is assumed
// that the engine already validated it.
func (r *Rule) apply(
	ctx context.Context,
	p *common.PluginChainInput,
) (common.PluginChainOutput, error) {
	if r.nextPluginApplyFn == nil {
		return nil, errors.New("nextPluginApplyFn can't be nil")
	}

	req, err := p.GetPriceRequest()
	if err != nil {
		return nil, errors.Wrap(err, "failed to get price request from plugin chain input")
	}

	bundleChunks := req.BundleSpec.ChunkSpecs

	policySpecs := req.PolicySpecs

	mcPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
	)
	if err != nil {
		return nil, err
	}

	glPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
	)
	if err != nil {
		return nil, err
	}

	mtcPolicyChunksMap, err := nf_common.BuildPolicyChunksMap(
		policySpecs,
		ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
	)
	if err != nil {
		return nil, err
	}

	seenRegularAI := make(map[string]struct{})
	seenPNCAI := make(map[string]struct{})
	seenWOS := make(map[string]struct{})

	for i, bundleChunk := range bundleChunks {
		if bundleChunk == nil {
			return nil, errors.Newf("nil bundle chunk found at index %d", i)
		}

		chunkID := bundleChunk.ChunkId
		policyChunks := []*ptypes.PolicySpec_ChunkSpec{
			mcPolicyChunksMap[chunkID],
			glPolicyChunksMap[chunkID],
			mtcPolicyChunksMap[chunkID],
		}

		for _, pChunk := range policyChunks {
			if pChunk == nil {
				continue
			}

			var policyName ptypes.PolicyName
			if mcPolicyChunksMap[chunkID] == pChunk {
				policyName = ptypes.PolicyName_PolicyName_MOTOR_CARRIER
			} else if glPolicyChunksMap[chunkID] == pChunk {
				policyName = ptypes.PolicyName_PolicyName_GENERAL_LIABILITY
			} else if mtcPolicyChunksMap[chunkID] == pChunk {
				policyName = ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO
			}

			subCoverageGroup, err := programs_common.GetSubCoverageGroupForPolicyModifiers(policyName)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to get sub-coverage group for policy %s", policyName)
			}

			r.handleSpecifiedModifier(
				pChunk,
				subCoverageGroup,
				seenRegularAI,
				r.getSpecifiedRegularAIIDs,
				nf_common.RemoveSpecifiedRegularAIID,
			)
			r.handleSpecifiedModifier(
				pChunk,
				subCoverageGroup,
				seenPNCAI,
				r.getSpecifiedPNCAIIDs,
				nf_common.RemoveSpecifiedPNCAIID,
			)
			r.handleSpecifiedModifier(
				pChunk,
				subCoverageGroup,
				seenWOS,
				r.getSpecifiedWOSIDs,
				nf_common.RemoveSpecifiedWOSID,
			)
		}
	}

	return r.nextPluginApplyFn(ctx, p)
}

func (r *Rule) handleSpecifiedModifier(
	chunk *ptypes.PolicySpec_ChunkSpec,
	subCoverageGroup *ptypes.SubCoverageGroup,
	seenIDs map[string]struct{},
	getIDsFn func(*ptypes.PolicySpec_ChunkSpec, *ptypes.SubCoverageGroup) []string,
	removeIDFn func(*ptypes.PolicySpec_ChunkSpec, string),
) {
	for _, id := range getIDsFn(chunk, subCoverageGroup) {
		if len(seenIDs) < maxFullyEarnedChargeCount {
			seenIDs[id] = struct{}{}
			continue
		}
		removeIDFn(chunk, id)
	}
}

func (r *Rule) getSpecifiedRegularAIIDs(chunk *ptypes.PolicySpec_ChunkSpec, subCoverageGroup *ptypes.SubCoverageGroup) []string {
	return slices.Sorted(slices.Values(nf_common.GetSpecifiedRegularAIIDs(chunk, subCoverageGroup)))
}

func (r *Rule) getSpecifiedPNCAIIDs(chunk *ptypes.PolicySpec_ChunkSpec, subCoverageGroup *ptypes.SubCoverageGroup) []string {
	return slices.Sorted(slices.Values(nf_common.GetSpecifiedPNCAIIDs(chunk, subCoverageGroup)))
}

func (r *Rule) getSpecifiedWOSIDs(chunk *ptypes.PolicySpec_ChunkSpec, subCoverageGroup *ptypes.SubCoverageGroup) []string {
	return slices.Sorted(slices.Values(nf_common.GetSpecifiedWOSIDs(chunk, subCoverageGroup)))
}
