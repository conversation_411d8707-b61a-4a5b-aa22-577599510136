load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "common",
    srcs = ["policy_modifiers.go"],
    importpath = "nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/common",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_cockroachdb_errors//:errors",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "common_test",
    srcs = ["policy_modifiers_test.go"],
    embed = [":common"],
    deps = [
        "//nirvana/rating/pricing/api/ptypes",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_protobuf//proto",
    ],
)
