package common

import (
	"github.com/cockroachdb/errors"
	"google.golang.org/protobuf/proto"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

var policyModifierSubCoverageGroups = map[ptypes.PolicyName]*ptypes.SubCoverageGroup{
	ptypes.PolicyName_PolicyName_MOTOR_CARRIER: {
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
			ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
		},
	},
	ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO: {
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_Cargo,
		},
	},
	ptypes.PolicyName_PolicyName_GENERAL_LIABILITY: {
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
		},
	},
	ptypes.PolicyName_PolicyName_BUSINESS_AUTO: {
		SubCoverages: []ptypes.SubCoverageType{
			ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
			ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
		},
	},
}

func GetSubCoverageGroupForPolicyModifiers(policyName ptypes.PolicyName) (*ptypes.SubCoverageGroup, error) {
	subCoverageGroup, ok := policyModifierSubCoverageGroups[policyName]
	if !ok {
		return nil, errors.Newf("policy name %s not supported for policy modifiers", policyName)
	}
	return proto.Clone(subCoverageGroup).(*ptypes.SubCoverageGroup), nil
}
