package common

import (
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
)

func Test_GetSubCoverageGroupForPolicyModifiers(t *testing.T) {
	tests := []struct {
		name         string
		policyName   ptypes.PolicyName
		expected     *ptypes.SubCoverageGroup
		expectError  bool
		errorMessage string
	}{
		{
			name:       "motor carrier policy",
			policyName: ptypes.PolicyName_PolicyName_MOTOR_CARRIER,
			expected: &ptypes.SubCoverageGroup{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
					ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				},
			},
			expectError: false,
		},
		{
			name:       "motor truck cargo policy",
			policyName: ptypes.PolicyName_PolicyName_MOTOR_TRUCK_CARGO,
			expected: &ptypes.SubCoverageGroup{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_Cargo,
				},
			},
			expectError: false,
		},
		{
			name:       "general liability policy",
			policyName: ptypes.PolicyName_PolicyName_GENERAL_LIABILITY,
			expected: &ptypes.SubCoverageGroup{
				SubCoverages: []ptypes.SubCoverageType{
					ptypes.SubCoverageType_SubCoverageType_GeneralLiability,
				},
			},
			expectError: false,
		},
		{
			name:         "unsupported policy name",
			policyName:   ptypes.PolicyName_PolicyName_Unspecified,
			expected:     nil,
			expectError:  true,
			errorMessage: "policy name PolicyName_Unspecified not supported for policy modifiers",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetSubCoverageGroupForPolicyModifiers(tt.policyName)

			if tt.expectError {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.errorMessage)
				require.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.True(t, proto.Equal(tt.expected, result))
			}
		})
	}
}
