package e2e

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"
	"google.golang.org/protobuf/types/known/timestamppb"

	business_auto_proto "nirvanatech.com/nirvana/business-auto/model/proto"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	common_proto "nirvanatech.com/nirvana/common-go/proto"
	"nirvanatech.com/nirvana/common-go/us_states"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes"
	"nirvanatech.com/nirvana/rating/pricing/api/ptypes/programs/business_auto"
	"nirvanatech.com/nirvana/rating/rtypes"

	_ "nirvanatech.com/nirvana/rating/pricing/api"
	_ "nirvanatech.com/nirvana/rating/pricing/api/engine"
	_ "nirvanatech.com/nirvana/rating/pricing/api/metrics"
)

type ExpectedChargeAmounts struct {
	LiabAmount   float64
	MedPayAmount float64
	UIMBIAmount  float64
	UMBIAmount   float64
	UMPDAmount   float64
	UMAmount     float64
	UMUIMAmount  float64

	CompCollAmount float64
	CompAmount     float64
	RentalAmount   float64
	TowingAmount   float64

	HiredAutoLiabAmount   float64
	HiredAutoPDAmount     float64
	NonOwnedVehicleAmount float64

	PersonalInjuryProtectionAmount     float64
	MedicalExpenseBenefitsAmount       float64
	WorkLossBenefitsAmount             float64
	FuneralExpenseBenefitsAmount       float64
	AccidentalDeathBenefitsAmount      float64
	ExtraordinaryMedicalBenefitsAmount float64

	BlanketAIAmount                  float64
	BlanketPNCAIAmount               float64
	BlanketWaiverOfSubrogationAmount float64

	SurplusLineTaxAmount          float64
	StampingFeeAmount             float64
	FeeChargeSurplusLineTaxAmount float64
	FeeChargeStampingFeeAmount    float64
}

type VehicleConfig struct {
	SubCoverages    []ptypes.SubCoverageType
	LimitSpecs      []*ptypes.LimitSpec
	DeductibleSpecs []*ptypes.DeductibleSpec
	VehicleType     business_auto_proto.VehicleType
	GarageZipCode   string
}

type PolicyChunkSpecConfig struct {
	SubCoverages          []ptypes.SubCoverageType
	LimitSpecs            []*ptypes.LimitSpec
	DeductibleSpecs       []*ptypes.DeductibleSpec
	VehicleConfigs        []*VehicleConfig
	ExpectedChargesCount  int
	ExpectedChargeAmounts ExpectedChargeAmounts
}

var stateConfigs = map[us_states.USState][]*PolicyChunkSpecConfig{
	us_states.AZ: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  1000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "85003",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 1000,
						},
					},
				},
			},
			ExpectedChargesCount: 11,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:            962.0,
				MedPayAmount:          42.0,
				UIMBIAmount:           21.0,
				CompCollAmount:        1009.0,
				RentalAmount:          32.0,
				TowingAmount:          9.0,
				HiredAutoLiabAmount:   144.0,
				HiredAutoPDAmount:     136.0,
				NonOwnedVehicleAmount: 38.0,
				SurplusLineTaxAmount:  71.79,
				StampingFeeAmount:     4.78,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  2000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  125000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  300000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "85003",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "85003",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 14,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                       3839.0,
				MedPayAmount:                     141.0,
				UIMBIAmount:                      104.0,
				UMBIAmount:                       74.0,
				CompAmount:                       3134.0,
				HiredAutoLiabAmount:              288.0,
				NonOwnedVehicleAmount:            37.0,
				BlanketAIAmount:                  1250.0,
				BlanketPNCAIAmount:               500.0,
				BlanketWaiverOfSubrogationAmount: 625.0,
				SurplusLineTaxAmount:             228.53,
				StampingFeeAmount:                15.24,
				FeeChargeSurplusLineTaxAmount:    71.25,
				FeeChargeStampingFeeAmount:       4.75,
			},
		},
	},
	us_states.GA: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  1000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "30301",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 1000,
						},
					},
				},
			},
			ExpectedChargesCount: 9,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:            2675.0,
				MedPayAmount:          93.0,
				CompCollAmount:        1362.0,
				RentalAmount:          32.0,
				TowingAmount:          9.0,
				HiredAutoLiabAmount:   401.0,
				HiredAutoPDAmount:     183.0,
				NonOwnedVehicleAmount: 38.0,
				SurplusLineTaxAmount:  191.76,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  2000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
						},
					},
					Amount:  300000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					AddedOn: true,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "30301",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_PRIVATE_PASSENGER,
					GarageZipCode: "30301",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 11,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                       10931.0,
				MedPayAmount:                     316.0,
				UMUIMAmount:                      1091,
				CompCollAmount:                   8035.0,
				HiredAutoLiabAmount:              820.0,
				NonOwnedVehicleAmount:            37.0,
				BlanketAIAmount:                  1250.0,
				BlanketPNCAIAmount:               500.0,
				BlanketWaiverOfSubrogationAmount: 625.0,
				SurplusLineTaxAmount:             849.19,
				FeeChargeSurplusLineTaxAmount:    95.0,
			},
		},
	},
	us_states.IL: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  1000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_PRIVATE_PASSENGER,
					GarageZipCode: "62024",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								},
							},
							Amount:  7500,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								},
							},
							Amount: 250,
						},
					},
				},
			},
			ExpectedChargesCount: 11,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:            1181.0,
				MedPayAmount:          54.0,
				UIMBIAmount:           25.0,
				UMBIAmount:            25.0,
				UMPDAmount:            14.0,
				CompAmount:            455.0,
				HiredAutoLiabAmount:   177.0,
				HiredAutoPDAmount:     62.0,
				NonOwnedVehicleAmount: 38.0,
				SurplusLineTaxAmount:  71.09,
				StampingFeeAmount:     0.81,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  2000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  500000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  300000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "62024",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRAILER,
					GarageZipCode: "62024",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 16,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                       1640.0,
				MedPayAmount:                     163.0,
				UIMBIAmount:                      62.0,
				UMBIAmount:                       80.0,
				CompCollAmount:                   3586.0,
				RentalAmount:                     32.0,
				TowingAmount:                     9.0,
				HiredAutoLiabAmount:              246.0,
				NonOwnedVehicleAmount:            37.0,
				BlanketAIAmount:                  1250.0,
				BlanketPNCAIAmount:               500.0,
				BlanketWaiverOfSubrogationAmount: 625.0,
				SurplusLineTaxAmount:             204.87,
				StampingFeeAmount:                2.34,
				FeeChargeSurplusLineTaxAmount:    83.13,
				FeeChargeStampingFeeAmount:       0.95,
			},
		},
	},
	us_states.IN: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  1000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "46545",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 1000,
						},
					},
				},
			},
			ExpectedChargesCount: 10,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:            754.0,
				MedPayAmount:          41.0,
				UIMBIAmount:           16.0,
				CompCollAmount:        993.0,
				RentalAmount:          32.0,
				TowingAmount:          9.0,
				HiredAutoLiabAmount:   113.0,
				HiredAutoPDAmount:     134.0,
				NonOwnedVehicleAmount: 38.0,
				SurplusLineTaxAmount:  53.22,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  2000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  125000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
						},
					},
					Amount:  500000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  300000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "46545",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "46545",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 13,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                       2929.0,
				MedPayAmount:                     136.0,
				UIMBIAmount:                      79.0,
				UMBIAmount:                       29.0,
				UMAmount:                         79.0,
				CompAmount:                       3083.0,
				HiredAutoLiabAmount:              220.0,
				NonOwnedVehicleAmount:            37.0,
				BlanketAIAmount:                  1250.0,
				BlanketPNCAIAmount:               500.0,
				BlanketWaiverOfSubrogationAmount: 625.0,
				SurplusLineTaxAmount:             164.8,
				FeeChargeSurplusLineTaxAmount:    59.38,
			},
		},
	},
	us_states.OH: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  1000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "43322",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								},
							},
							Amount:  7500,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
								},
							},
							Amount: 250,
						},
					},
				},
			},
			ExpectedChargesCount: 12,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:            946.0,
				MedPayAmount:          32.0,
				UIMBIAmount:           20.0,
				UMBIAmount:            20.0,
				UMPDAmount:            12.0,
				CompCollAmount:        869.0,
				RentalAmount:          32.0,
				TowingAmount:          9.0,
				HiredAutoLiabAmount:   142.0,
				HiredAutoPDAmount:     117.0,
				NonOwnedVehicleAmount: 38.0,
				SurplusLineTaxAmount:  111.84,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  2000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  500000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  500000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "43322",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRAILER,
					GarageZipCode: "43322",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 12,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                       1449.0,
				MedPayAmount:                     104.0,
				UIMBIAmount:                      71.0,
				UMBIAmount:                       71.0,
				CompAmount:                       1894.0,
				HiredAutoLiabAmount:              217.0,
				NonOwnedVehicleAmount:            37.0,
				BlanketAIAmount:                  1250.0,
				BlanketPNCAIAmount:               500.0,
				BlanketWaiverOfSubrogationAmount: 625.0,
				SurplusLineTaxAmount:             192.13,
				FeeChargeSurplusLineTaxAmount:    118.75,
			},
		},
	},
	us_states.PA: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
				ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
				ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					Stacked: false,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
						},
					},
					Amount:  5000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
						},
					},
					Amount:  5000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
						},
					},
					Amount:  1000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Monthly,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
						},
					},
					Amount:  1500,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "15001",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 1000,
						},
					},
				},
			},
			ExpectedChargesCount: 13,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                   1108.0,
				UIMBIAmount:                  27.0,
				UMBIAmount:                   27.0,
				CompCollAmount:               1169.0,
				RentalAmount:                 32.0,
				TowingAmount:                 9.0,
				HiredAutoLiabAmount:          166.0,
				HiredAutoPDAmount:            157.0,
				NonOwnedVehicleAmount:        38.0,
				MedicalExpenseBenefitsAmount: 80.0,
				WorkLossBenefitsAmount:       17.0,
				FuneralExpenseBenefitsAmount: 2.0,
				SurplusLineTaxAmount:         84.92,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
				ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits,
				ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
						},
					},
					Amount:  125000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					Stacked: true,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
						},
					},
					Amount:  300000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
					Stacked: true,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
						},
					},
					Amount:  10000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits,
						},
					},
					Amount:  25000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Aggregate,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits,
						},
					},
					Amount:  100000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "15001",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "15001",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 14,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                         3907.0,
				MedPayAmount:                       0.0,
				UIMBIAmount:                        185.0,
				UMBIAmount:                         117.0,
				CompAmount:                         3622.0,
				HiredAutoLiabAmount:                293.0,
				NonOwnedVehicleAmount:              37.0,
				MedicalExpenseBenefitsAmount:       335.0,
				AccidentalDeathBenefitsAmount:      18.0,
				ExtraordinaryMedicalBenefitsAmount: 758.0,
				BlanketAIAmount:                    1250.0,
				BlanketPNCAIAmount:                 500.0,
				BlanketWaiverOfSubrogationAmount:   625.0,
				SurplusLineTaxAmount:               278.18,
				FeeChargeSurplusLineTaxAmount:      71.25,
			},
		},
	},
	us_states.TX: {
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
				ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
				ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
				ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  150000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
							ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
							ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
							ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses,
						},
					},
					Amount:  2500,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage,
						},
					},
					Amount: 1000,
				},
			},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "75001",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
						ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
						ptypes.SubCoverageType_SubCoverageType_Towing,
					},
					LimitSpecs: []*ptypes.LimitSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_RentalReimbursement,
								},
							},
							Amount:  3000,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Towing,
								},
							},
							Amount:  300,
							Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
						},
					},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 1000,
						},
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Collision,
								},
							},
							Amount: 1000,
						},
					},
				},
			},
			ExpectedChargesCount: 10,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                     1866.0,
				UMAmount:                       0.0,
				CompCollAmount:                 955.0,
				RentalAmount:                   32.0,
				TowingAmount:                   9.0,
				HiredAutoLiabAmount:            280.0,
				HiredAutoPDAmount:              129.0,
				NonOwnedVehicleAmount:          38.0,
				PersonalInjuryProtectionAmount: 51.0,
				SurplusLineTaxAmount:           162.95,
				StampingFeeAmount:              1.35,
			},
		},
		{
			SubCoverages: []ptypes.SubCoverageType{
				ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability,
				ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle,
				ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury,
				ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage,
				ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage,
			},
			LimitSpecs: []*ptypes.LimitSpec{
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
							ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
						},
					},
					Amount:  1000000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: &ptypes.SubCoverageGroup{
						SubCoverages: []ptypes.SubCoverageType{
							ptypes.SubCoverageType_SubCoverageType_MedicalPayments,
						},
					},
					Amount:  2000,
					Cadence: ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
				{
					SubCoverageGroup: business_auto.UMUIMSubCoverageGroup,
					Amount:           100000,
					Cadence:          ptypes.LimitCadenceType_LimitCadenceType_Occurrence,
				},
			},
			DeductibleSpecs: []*ptypes.DeductibleSpec{},
			VehicleConfigs: []*VehicleConfig{
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "75001",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
				{
					VehicleType:   business_auto_proto.VehicleType_VEHICLE_TYPE_TRUCK,
					GarageZipCode: "75001",
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
					},
					LimitSpecs: []*ptypes.LimitSpec{},
					DeductibleSpecs: []*ptypes.DeductibleSpec{
						{
							SubCoverageGroup: &ptypes.SubCoverageGroup{
								SubCoverages: []ptypes.SubCoverageType{
									ptypes.SubCoverageType_SubCoverageType_Comprehensive,
								},
							},
							Amount: 2500,
						},
					},
				},
			},
			ExpectedChargesCount: 13,
			ExpectedChargeAmounts: ExpectedChargeAmounts{
				LiabAmount:                       7633.0,
				MedPayAmount:                     226.0,
				UMUIMAmount:                      539.0,
				CompAmount:                       2961.0,
				HiredAutoLiabAmount:              573.0,
				NonOwnedVehicleAmount:            37.0,
				BlanketAIAmount:                  1250.0,
				BlanketPNCAIAmount:               500.0,
				BlanketWaiverOfSubrogationAmount: 625.0,
				SurplusLineTaxAmount:             580.5,
				StampingFeeAmount:                4.78,
				FeeChargeSurplusLineTaxAmount:    115.19,
				FeeChargeStampingFeeAmount:       0.95,
			},
		},
	},
}

type businessAutoE2ETestEnv struct {
	fx.In

	Server ptypes.PricingServer
}

type businessAutoE2ETestSuite struct {
	suite.Suite
	ctx context.Context

	env businessAutoE2ETestEnv

	fxapp *fxtest.App
}

// TODO: Make test parameterizable in terms of number of chunks and number of vehicles (and potentially other dimensions)
func TestBusinessAutoE2E(t *testing.T) {
	suite.Run(t, new(businessAutoE2ETestSuite))
}

func (s *businessAutoE2ETestSuite) SetupSuite() {
	s.ctx = context.Background()
	s.fxapp = testloader.RequireStart(s.T(), &s.env)
}

func (s *businessAutoE2ETestSuite) TearDownSuite() {
	s.fxapp.RequireStop()
}

func (s *businessAutoE2ETestSuite) TestBusinessAutoPricing_SimpleE2E() {
	for state := range stateConfigs {
		s.Run(state.ToCode(), func() {
			request := s.createPricingRequest(state)

			response, err := s.env.Server.GetPrice(s.ctx, request)

			s.Require().NoError(err)
			s.Require().NotNil(response)
			s.Require().NotEmpty(response.PolicyOutputs)
			s.Require().Equal(1, len(response.PolicyOutputs))

			policyOutput := response.PolicyOutputs[0]
			s.Require().Equal("TSNBA-123456", policyOutput.PolicyNumber)
			s.Require().NotEmpty(policyOutput.ChunkOutputs)
			s.Require().Equal(2, len(policyOutput.ChunkOutputs))

			s.validateChunkOutputs(
				"chunk-1",
				stateConfigs[state][0].ExpectedChargesCount,
				stateConfigs[state][0].ExpectedChargeAmounts,
				policyOutput.ChunkOutputs[0],
			)
			s.validateChunkOutputs(
				"chunk-2",
				stateConfigs[state][1].ExpectedChargesCount,
				stateConfigs[state][1].ExpectedChargeAmounts,
				policyOutput.ChunkOutputs[1],
			)
		})
	}
}

func (s *businessAutoE2ETestSuite) createPricingRequest(state us_states.USState) *ptypes.Request {
	now := timestamppb.Now()
	policyStart := now
	policyEnd := timestamppb.New(now.AsTime().AddDate(1, 0, 0))
	chunk1Start := now
	chunk1End := timestamppb.New(now.AsTime().AddDate(0, 6, 0))
	chunk2Start := chunk1End
	chunk2End := policyEnd

	return &ptypes.Request{
		ProgramType: ptypes.ProgramType_ProgramType_BusinessAuto,
		BundleSpec: &ptypes.BundleSpec{
			ChunkSpecs: []*ptypes.BundleSpec_ChunkSpec{
				{
					ChunkId: "chunk-1",
					Dates: &common_proto.Interval{
						Start: chunk1Start,
						End:   chunk1End,
					},
					Data: &ptypes.BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData{
						BusinessAutoBundleChunkSpecData: &ptypes.BusinessAuto_BundleChunkSpecData{},
					},
				},
				{
					ChunkId: "chunk-2",
					Dates: &common_proto.Interval{
						Start: chunk2Start,
						End:   chunk2End,
					},
					Data: &ptypes.BundleSpec_ChunkSpec_BusinessAutoBundleChunkSpecData{
						BusinessAutoBundleChunkSpecData: &ptypes.BusinessAuto_BundleChunkSpecData{},
					},
				},
			},
		},
		PolicySpecs: []*ptypes.PolicySpec{
			{
				PolicyNumber: "TSNBA-123456",
				PolicyName:   ptypes.PolicyName_PolicyName_BUSINESS_AUTO,
				PolicyDates: &common_proto.Interval{
					Start: policyStart,
					End:   policyEnd,
				},
				ModelSpec: &ptypes.ModelSpec{
					Provider: rtypes.ProviderNico.String(),
					State:    state.ToCode(),
					Version: &ptypes.ModelVersion{
						Major: 0,
						Minor: 0,
						Patch: 0,
					},
				},
				ChunkSpecs: []*ptypes.PolicySpec_ChunkSpec{
					s.createPolicyChunkSpec1(state),
					s.createPolicyChunkSpec2(state),
				},
			},
		},
	}
}

func (s *businessAutoE2ETestSuite) createPolicyChunkSpec1(state us_states.USState) *ptypes.PolicySpec_ChunkSpec {
	return &ptypes.PolicySpec_ChunkSpec{
		ChunkId:         "chunk-1",
		SubCoverages:    stateConfigs[state][0].SubCoverages,
		LimitSpecs:      stateConfigs[state][0].LimitSpecs,
		DeductibleSpecs: stateConfigs[state][0].DeductibleSpecs,
		ArtifactConfig: &ptypes.ArtifactConfig{
			FileKey: "business-auto/test-artifacts/chunk-1.json",
		},
		Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
			BusinessAutoPolicyChunkSpecData: s.createBusinessAutoPolicyChunkSpec1Data(state),
		},
	}
}

func (s *businessAutoE2ETestSuite) createPolicyChunkSpec2(state us_states.USState) *ptypes.PolicySpec_ChunkSpec {
	return &ptypes.PolicySpec_ChunkSpec{
		ChunkId:         "chunk-2",
		SubCoverages:    stateConfigs[state][1].SubCoverages,
		LimitSpecs:      stateConfigs[state][1].LimitSpecs,
		DeductibleSpecs: stateConfigs[state][1].DeductibleSpecs,
		ArtifactConfig: &ptypes.ArtifactConfig{
			FileKey: "business-auto/test-artifacts/chunk-2.json",
		},
		Data: &ptypes.PolicySpec_ChunkSpec_BusinessAutoPolicyChunkSpecData{
			BusinessAutoPolicyChunkSpecData: s.createBusinessAutoPolicyChunkSpec2Data(state),
		},
		RegularAdditionalInsuredBlankets: []*ptypes.BlanketRegularAdditionalInsured{{
			SubCoverageGroup: business_auto.LiabSubCoverageGroup,
		}},
		PrimaryAndNonContributoryAdditionalInsuredBlankets: []*ptypes.BlanketPrimaryAndNonContributoryAdditionalInsured{{
			SubCoverageGroup: business_auto.LiabSubCoverageGroup,
		}},
		WaiverOfSubrogationBlankets: []*ptypes.BlanketWaiverOfSubrogation{{
			SubCoverageGroup: business_auto.LiabSubCoverageGroup,
		}},
	}
}

func (s *businessAutoE2ETestSuite) createBusinessAutoPolicyChunkSpec1Data(state us_states.USState) *ptypes.BusinessAuto_PolicyChunkSpecData {
	return &ptypes.BusinessAuto_PolicyChunkSpecData{
		Company: &ptypes.BusinessAuto_Company{
			Id:               "TEST-COMPANY-001",
			State:            state.ToCode(),
			AnnualCostOfHire: pointer_utils.ToPointer(int64(50000)),
			NumberOfEmployeesOperatingPersonalVehiclesForWork: 0,
			HasMultiStateFilings:                              false,
			HasFMCSAFilings:                                   true,
			HasDOTFilings:                                     true,
			IsIndividualNamedInsured:                          false,
			MaxValueOfHiredAutos:                              100000,
		},
		Vehicles: []*ptypes.BusinessAuto_Vehicle{
			{
				Vin:               "1HGBH41JXMN109186",
				Type:              stateConfigs[state][0].VehicleConfigs[0].VehicleType,
				Size:              business_auto_proto.VehicleSize_VEHICLE_SIZE_MEDIUM_TRUCK,
				IndustryType:      business_auto_proto.IndustryType_INDUSTRY_TYPE_CONTRACTOR_TRUCKS,
				StateUsage:        business_auto_proto.StateUsage_STATE_USAGE_INTRASTATE,
				SpecialtyType:     business_auto_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET,
				BusinessUse:       business_auto_proto.BusinessUse_BUSINESS_USE_COMMERCIAL,
				RadiusOfOperation: business_auto_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_O_TO_100_MILES,
				GarageZipCode:     stateConfigs[state][0].VehicleConfigs[0].GarageZipCode,
				StatedValue:       75000,
				YearMade:          2020,
				IsGlassLined:      false,
				IsRefrigerated:    false,
				IsDoubleTrailer:   false,
				SubCoverages:      stateConfigs[state][0].VehicleConfigs[0].SubCoverages,
				LimitSpecs:        stateConfigs[state][0].VehicleConfigs[0].LimitSpecs,
				DeductibleSpecs:   stateConfigs[state][0].VehicleConfigs[0].DeductibleSpecs,
			},
		},
		DriverClassModification: 1.0,
		ScheduleModifications: []*ptypes.ScheduleModification{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					},
				},
				Percentage: 0.95,
			},
		},
	}
}

func (s *businessAutoE2ETestSuite) createBusinessAutoPolicyChunkSpec2Data(state us_states.USState) *ptypes.BusinessAuto_PolicyChunkSpecData {
	return &ptypes.BusinessAuto_PolicyChunkSpecData{
		Company: &ptypes.BusinessAuto_Company{
			Id:               "TEST-COMPANY-001",
			State:            state.ToCode(),
			AnnualCostOfHire: pointer_utils.ToPointer(int64(75000)),
			NumberOfEmployeesOperatingPersonalVehiclesForWork: 1,
			HasMultiStateFilings:                              false,
			HasFMCSAFilings:                                   true,
			HasDOTFilings:                                     true,
			IsIndividualNamedInsured:                          false,
			MaxValueOfHiredAutos:                              150000,
		},
		Vehicles: []*ptypes.BusinessAuto_Vehicle{
			{
				Vin:               "1HGBH41JXMN109186",
				Type:              stateConfigs[state][1].VehicleConfigs[0].VehicleType,
				Size:              business_auto_proto.VehicleSize_VEHICLE_SIZE_MEDIUM_TRUCK,
				IndustryType:      business_auto_proto.IndustryType_INDUSTRY_TYPE_CONTRACTOR_TRUCKS,
				StateUsage:        business_auto_proto.StateUsage_STATE_USAGE_INTRASTATE,
				SpecialtyType:     business_auto_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_0_TO_49_FEET,
				BusinessUse:       business_auto_proto.BusinessUse_BUSINESS_USE_COMMERCIAL,
				RadiusOfOperation: business_auto_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_O_TO_100_MILES,
				GarageZipCode:     stateConfigs[state][1].VehicleConfigs[0].GarageZipCode,
				StatedValue:       75000,
				YearMade:          2020,
				IsGlassLined:      false,
				IsRefrigerated:    false,
				IsDoubleTrailer:   false,
				SubCoverages:      stateConfigs[state][1].VehicleConfigs[0].SubCoverages,
				LimitSpecs:        stateConfigs[state][1].VehicleConfigs[0].LimitSpecs,
				DeductibleSpecs:   stateConfigs[state][1].VehicleConfigs[0].DeductibleSpecs,
			},
			{
				Vin:               "2HGBH41JXMN109187",
				Type:              stateConfigs[state][1].VehicleConfigs[1].VehicleType,
				Size:              business_auto_proto.VehicleSize_VEHICLE_SIZE_FULL_TRAILER,
				IndustryType:      business_auto_proto.IndustryType_INDUSTRY_TYPE_WHOLESALERS_MANUFACTURERS,
				StateUsage:        business_auto_proto.StateUsage_STATE_USAGE_INTERSTATE,
				SpecialtyType:     business_auto_proto.VehicleSpecialtyType_VEHICLE_SPECIALTY_BOOM_TRUCKS_101_TO_120_FEET,
				BusinessUse:       business_auto_proto.BusinessUse_BUSINESS_USE_COMMERCIAL,
				RadiusOfOperation: business_auto_proto.RadiusOfOperation_MAX_RADIUS_OF_OPERATION_301_PLUS_MILES,
				GarageZipCode:     stateConfigs[state][1].VehicleConfigs[1].GarageZipCode,
				StatedValue:       120000,
				YearMade:          2021,
				IsGlassLined:      true,
				IsRefrigerated:    true,
				IsDoubleTrailer:   true,
				SubCoverages:      stateConfigs[state][1].VehicleConfigs[1].SubCoverages,
				LimitSpecs:        stateConfigs[state][1].VehicleConfigs[1].LimitSpecs,
				DeductibleSpecs:   stateConfigs[state][1].VehicleConfigs[1].DeductibleSpecs,
			},
		},
		DriverClassModification: 1.1,
		ScheduleModifications: []*ptypes.ScheduleModification{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					},
				},
				Percentage: 0.90,
			},
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_Comprehensive,
						ptypes.SubCoverageType_SubCoverageType_Collision,
					},
				},
				Percentage: 0.95,
			},
		},
		ExperienceRatingModifications: []*ptypes.ExperienceRatingModification{
			{
				SubCoverageGroup: &ptypes.SubCoverageGroup{
					SubCoverages: []ptypes.SubCoverageType{
						ptypes.SubCoverageType_SubCoverageType_BodilyInjury,
						ptypes.SubCoverageType_SubCoverageType_PropertyDamage,
					},
				},
				Percentage: 0.85,
			},
		},
	}
}

func (s *businessAutoE2ETestSuite) validateChunkOutputs(
	expectedID string,
	expectedChargesCount int,
	expectedChargeAmounts ExpectedChargeAmounts,
	chunkOutput *ptypes.ChunkOutput,
) {
	s.Require().Equal(expectedID, chunkOutput.ChunkID)
	s.Require().Equal(expectedChargesCount, len(chunkOutput.Charges))

	liabAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_BodilyInjury, ptypes.SubCoverageType_SubCoverageType_PropertyDamage)
	medPayAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_MedicalPayments)
	uimbiAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury)
	umbiAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury)
	umpdAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage)
	umAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage)
	umuimAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristBodilyInjury, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristBodilyInjury, ptypes.SubCoverageType_SubCoverageType_UninsuredMotoristPropertyDamage, ptypes.SubCoverageType_SubCoverageType_UnderinsuredMotoristPropertyDamage)

	compCollAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_Comprehensive, ptypes.SubCoverageType_SubCoverageType_Collision)
	compAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_Comprehensive)
	rentalAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_RentalReimbursement)
	towingAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_Towing)

	hiredAutoLiabAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_HiredAutoLiability)
	hiredAutoPDAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_HiredAutoPhysicalDamage)
	nonOwnedVehicleAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_NonOwnedVehicle)

	medicalExpenseBenefitsAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits)
	workLossBenefitsAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits)
	funeralExpenseBenefitsAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits)
	accidentalDeathBenefitsAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_AccidentalDeathBenefits)
	extraordinaryMedicalBenefitsAmount := s.getAmountForSubCoverageCharge(chunkOutput.Charges, ptypes.SubCoverageType_SubCoverageType_ExtraordinaryMedicalBenefits)
	personalInjuryProtectionAmount := s.getAmountForSubCoverageCharge(
		chunkOutput.Charges,
		ptypes.SubCoverageType_SubCoverageType_MedicalExpenseBenefits,
		ptypes.SubCoverageType_SubCoverageType_FuneralExpenseBenefits,
		ptypes.SubCoverageType_SubCoverageType_WorkLossBenefits,
		ptypes.SubCoverageType_SubCoverageType_EssentialServiceExpenses,
	)

	blanketAIAmount := s.getBlanketRegularAdditionalInsuredAmount(chunkOutput.Charges)
	blanketPNCAIAmount := s.getBlanketPNCAdditionalInsuredAmount(chunkOutput.Charges)
	waiverOfSubrogationAmount := s.getBlanketWaiverOfSubrogationAmount(chunkOutput.Charges)

	surplusLineTaxAmount := s.getAmountForSurcharge(chunkOutput.Charges, ptypes.Surcharge_Type_SURPLUS_TAX)
	stampingFeeAmount := s.getAmountForSurcharge(chunkOutput.Charges, ptypes.Surcharge_Type_STAMPING_FEE)
	feeChargeSurplusLineTaxAmount := s.getAmountForSurcharge(chunkOutput.Charges, ptypes.Surcharge_Type_FEE_CHARGE_SURPLUS_TAX)
	feeChargeStampingFeeAmount := s.getAmountForSurcharge(chunkOutput.Charges, ptypes.Surcharge_Type_FEE_CHARGE_STAMPING_FEE)

	s.Require().Equal(expectedChargeAmounts.LiabAmount, liabAmount)
	s.Require().Equal(expectedChargeAmounts.MedPayAmount, medPayAmount)
	s.Require().Equal(expectedChargeAmounts.UIMBIAmount, uimbiAmount)
	s.Require().Equal(expectedChargeAmounts.UMBIAmount, umbiAmount)
	s.Require().Equal(expectedChargeAmounts.UMPDAmount, umpdAmount)
	s.Require().Equal(expectedChargeAmounts.UMAmount, umAmount)
	s.Require().Equal(expectedChargeAmounts.UMUIMAmount, umuimAmount)

	s.Require().Equal(expectedChargeAmounts.CompCollAmount, compCollAmount)
	s.Require().Equal(expectedChargeAmounts.CompAmount, compAmount)
	s.Require().Equal(expectedChargeAmounts.RentalAmount, rentalAmount)
	s.Require().Equal(expectedChargeAmounts.TowingAmount, towingAmount)

	s.Require().Equal(expectedChargeAmounts.HiredAutoLiabAmount, hiredAutoLiabAmount)
	s.Require().Equal(expectedChargeAmounts.HiredAutoPDAmount, hiredAutoPDAmount)
	s.Require().Equal(expectedChargeAmounts.NonOwnedVehicleAmount, nonOwnedVehicleAmount)

	s.Require().Equal(expectedChargeAmounts.MedicalExpenseBenefitsAmount, medicalExpenseBenefitsAmount)
	s.Require().Equal(expectedChargeAmounts.WorkLossBenefitsAmount, workLossBenefitsAmount)
	s.Require().Equal(expectedChargeAmounts.FuneralExpenseBenefitsAmount, funeralExpenseBenefitsAmount)
	s.Require().Equal(expectedChargeAmounts.AccidentalDeathBenefitsAmount, accidentalDeathBenefitsAmount)
	s.Require().Equal(expectedChargeAmounts.ExtraordinaryMedicalBenefitsAmount, extraordinaryMedicalBenefitsAmount)

	s.Require().Equal(expectedChargeAmounts.PersonalInjuryProtectionAmount, personalInjuryProtectionAmount)

	s.Require().Equal(expectedChargeAmounts.BlanketAIAmount, blanketAIAmount)
	s.Require().Equal(expectedChargeAmounts.BlanketPNCAIAmount, blanketPNCAIAmount)
	s.Require().Equal(expectedChargeAmounts.BlanketWaiverOfSubrogationAmount, waiverOfSubrogationAmount)

	s.Require().Equal(expectedChargeAmounts.SurplusLineTaxAmount, surplusLineTaxAmount)
	s.Require().Equal(expectedChargeAmounts.StampingFeeAmount, stampingFeeAmount)
	s.Require().Equal(expectedChargeAmounts.FeeChargeSurplusLineTaxAmount, feeChargeSurplusLineTaxAmount)
	s.Require().Equal(expectedChargeAmounts.FeeChargeStampingFeeAmount, feeChargeStampingFeeAmount)
}

func (s *businessAutoE2ETestSuite) getAmountForSubCoverageCharge(charges []*ptypes.Charge, subCoverages ...ptypes.SubCoverageType) float64 {
	for _, charge := range charges {
		if s.isSubCoverageCharge(charge, subCoverages...) {
			return s.getAmountForCharge(charge)
		}
	}
	return 0.0
}

func (s *businessAutoE2ETestSuite) getAmountForSurcharge(charges []*ptypes.Charge, surchargeType ptypes.Surcharge_Type) float64 {
	for _, charge := range charges {
		if charge.IsSurchargeType(surchargeType) {
			return s.getAmountForCharge(charge)
		}
	}
	return 0.0
}

func (s *businessAutoE2ETestSuite) isSubCoverageCharge(charge *ptypes.Charge, subCoverages ...ptypes.SubCoverageType) bool {
	if !charge.IsBaseCharge() || !charge.AppliesToSubCoverageGroup() {
		return false
	}

	chargeSubCovs := charge.GetChargedSubCoverageGroup().GetGroup().GetSubCoverages()
	if len(chargeSubCovs) != len(subCoverages) {
		return false
	}

	chargeSubCovMap := make(map[ptypes.SubCoverageType]bool)
	for _, sc := range chargeSubCovs {
		chargeSubCovMap[sc] = true
	}

	expectedSubCovMap := make(map[ptypes.SubCoverageType]bool)
	for _, sc := range subCoverages {
		expectedSubCovMap[sc] = true
	}

	for sc := range expectedSubCovMap {
		if !chargeSubCovMap[sc] {
			return false
		}
	}

	for sc := range chargeSubCovMap {
		if !expectedSubCovMap[sc] {
			return false
		}
	}

	return true
}

func (s *businessAutoE2ETestSuite) getBlanketRegularAdditionalInsuredAmount(charges []*ptypes.Charge) float64 {
	for _, charge := range charges {
		if charge.IsBaseCharge() && charge.AppliesToBlanketRegularAdditionalInsured() {
			return s.getAmountForCharge(charge)
		}
	}
	return 0.0
}

func (s *businessAutoE2ETestSuite) getBlanketPNCAdditionalInsuredAmount(charges []*ptypes.Charge) float64 {
	for _, charge := range charges {
		if charge.IsBaseCharge() && charge.AppliesToBlanketPrimaryAndNonContributoryAdditionalInsured() {
			return s.getAmountForCharge(charge)
		}
	}
	return 0.0
}

func (s *businessAutoE2ETestSuite) getBlanketWaiverOfSubrogationAmount(charges []*ptypes.Charge) float64 {
	for _, charge := range charges {
		if charge.IsBaseCharge() && charge.AppliesToBlanketWaiverOfSubrogation() {
			return s.getAmountForCharge(charge)
		}
	}
	return 0.0
}

func (s *businessAutoE2ETestSuite) getAmountForCharge(charge *ptypes.Charge) float64 {
	amount, err := charge.Calculate(nil)
	if err != nil {
		return 0.0
	}

	result, _ := amount.Float64()
	return result
}
