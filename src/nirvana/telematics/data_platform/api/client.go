package api

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/trace"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/tracing"
	telematics_db_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/api/dispatcher"
	"nirvanatech.com/nirvana/telematics/data_platform/api/experimental"
	"nirvanatech.com/nirvana/telematics/data_platform/api/normalized"
)

// Client is the main entrypoint for all endpoints exposed with the telematics data
// manager API. The endpoints are grouped into three high level categories:
// Experimental: raw, unprocessed data specific to the data provider
// Normalized: processed and normalized data across data providers
// Meta: information about a specific integration or DOT-VIN and DOT-DL mapping
//
// The Client exposes Normalized and Experimental endpoints within
// their namespace(s), while the "Meta" endpoints are exposed at the root level.
// Example Usage:
//
//	gpsNormalizedForVIN, err := client.
//	   	Normalized.
//	   	Stats.
//	   	GPSLog.
//	   	For(queryParams)
type Client struct {
	dispatcher      *dispatcher.ProviderDispatcher
	entitiesWrapper *telematics_db_wrapper.EntitiesWrapper

	Normalized   *normalized.Client
	Experimental *experimental.Client
}

// ConnectionProperties returns the telematics.ConnectionProperties of the
// telematics connection for the requested handleId.
func (c *Client) ConnectionProperties(
	ctx context.Context,
	connHandleId uuid.UUID,
) (*telematics.ConnectionProperties, error) {
	ctx, span := tracing.Start(ctx, "client.ConnectionProperties",
		trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	return c.dispatcher.ConnectionProperties(ctx, connHandleId)
}

// Vehicles returns the slice of Vehicles mapped for the connHandleId.
// Note: It is possible to have multiple Vehicles with the same VIN. To de-duplicate
// VINs, a helper function (`GetUniqueVINs`) is provided in the `data_platform` package.
func (c *Client) Vehicles(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.Vehicle, error) {
	ctx, span := tracing.Start(ctx, "client.Vehicles",
		trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	vehicles, err := c.entitiesWrapper.FetchVehicles(ctx, connHandleId)
	return slice_utils.ToSliceOfPointers(vehicles), err
}

// VehicleGroups returns the slice of data_platform_Tag(s) mapped for the connHandleId
func (c *Client) VehicleGroups(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.VehicleGroup, error) {
	ctx, span := tracing.Start(ctx, "client.VehicleGroups",
		trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	groups, err := c.entitiesWrapper.FetchVehicleGroups(ctx, connHandleId)
	return slice_utils.ToSliceOfPointers(groups), err
}

// Drivers returns the slice of Drivers mapped for the requested dotNo
func (c *Client) Drivers(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]data_platform.Driver, error) {
	ctx, span := tracing.Start(ctx, "client.Drivers",
		trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	return c.entitiesWrapper.FetchDrivers(ctx, connHandleId)
}

// SyncEntities sync the entities (Org/Vehicle/Driver) for the given
// connection handle id (from source provider) to the data platform.
func (c *Client) SyncEntities(
	ctx context.Context,
	connHandleId uuid.UUID,
) error {
	ctx, span := tracing.Start(ctx, "client.SyncEntities",
		trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	entities, err := c.dispatcher.SyncEntities(ctx, connHandleId)
	if err != nil {
		return errors.Wrap(err, "failed to sync entities from the respective provider")
	}
	// we shall now write-back the synced entity lists to the normalised tables
	// so that they can be read by subsequent calls to Vehicles/Drivers/Groups.
	return errors.Join(
		c.entitiesWrapper.UpsertVehicles(ctx, slice_utils.FromSliceOfPointers(entities.Vehicles)...),
		c.entitiesWrapper.UpsertVehicleGroups(ctx, slice_utils.FromSliceOfPointers(entities.VehicleGroups)...),
		c.entitiesWrapper.UpsertDrivers(ctx, entities.Drivers...),
	)
}

// SyncData is a non-blocking function to sync the data for the given
// connection handle id (from source provider).
// The interval is the time interval for which the data is to be synced, and is
// inclusive of the start and end time.
// The method should keep returning ErrNotAvailableYet until data is available
// for the given interval.
// NOTE: This method is a no-op for providers where this isn't required.
func (c *Client) SyncData(
	ctx context.Context,
	connHandleId uuid.UUID,
	interval time_utils.Interval,
) error {
	ctx, span := tracing.Start(ctx, "client.SyncData",
		trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	return c.dispatcher.SyncData(ctx, connHandleId, interval)
}

// Ping may be used to perform a light-weight health check for the given connection to
// provide confidence on whether the subsequent SyncData / SyncEntities would succeed.
// It does not persist or mutate any persisted data for the connection.
func (c *Client) Ping(
	ctx context.Context,
	handleId uuid.UUID,
) error {
	return c.dispatcher.Ping(ctx, handleId)
}
