load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "dispatcher",
    srcs = [
        "dispatcher.go",
        "endpoint_impl.go",
        "fx.go",
    ],
    importpath = "nirvanatech.com/nirvana/telematics/data_platform/api/dispatcher",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/map_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/providers/keeptruckin",
        "//nirvana/telematics/data_platform/providers/samsara",
        "//nirvana/telematics/data_platform/providers/smartdrive",
        "//nirvana/telematics/data_platform/providers/terminal",
        "//nirvana/telematics/data_platform/stream",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@org_uber_go_fx//:fx",
    ],
)
