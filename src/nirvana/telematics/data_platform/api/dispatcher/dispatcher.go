package dispatcher

import (
	"context"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/telematics"
	tsp_connections "nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/data_platform"
)

type ProviderDispatcherBuilder struct {
	nativeProviders    map[telematics.TSP]data_platform.Provider
	aggregateProviders map[telematics.DataProvider]data_platform.Provider
}

func New() ProviderDispatcherBuilder {
	return ProviderDispatcherBuilder{
		nativeProviders:    make(map[telematics.TSP]data_platform.Provider),
		aggregateProviders: make(map[telematics.DataProvider]data_platform.Provider),
	}
}

func (p ProviderDispatcherBuilder) WithNative(tsp telematics.TSP, provider data_platform.Provider) ProviderDispatcherBuilder {
	p.nativeProviders[tsp] = provider
	return p
}

func (p ProviderDispatcherBuilder) WithAggregator(source telematics.DataProvider, provider data_platform.Provider) ProviderDispatcherBuilder {
	p.aggregateProviders[source] = provider
	return p
}

func (p ProviderDispatcherBuilder) Build(tspConnManager *tsp_connections.TSPConnManager) *ProviderDispatcher {
	pCopy := make(map[telematics.TSP]data_platform.Provider)
	for tsp, provider := range p.nativeProviders {
		pCopy[tsp] = provider
	}
	apCopy := make(map[telematics.DataProvider]data_platform.Provider)
	for source, provider := range p.aggregateProviders {
		apCopy[source] = provider
	}
	return &ProviderDispatcher{tspConnManager: tspConnManager, nativeProviders: pCopy, aggregatorProviders: apCopy}
}

// ProviderDispatcher is a special implementation of Provider interface.
// Before the data platform client can satisfy a request it must first be routed to the
// appropriate Provider. ProviderDispatcher encapsulates logic to handle this dispatch.
type ProviderDispatcher struct {
	tspConnManager      *tsp_connections.TSPConnManager
	nativeProviders     map[telematics.TSP]data_platform.Provider
	aggregatorProviders map[telematics.DataProvider]data_platform.Provider
}

// ProviderDispatcher implements Provider interface, so it is a drop-in substitute for any
// API which works with Provider implementations.
var _ data_platform.Provider = (*ProviderDispatcher)(nil)

func (p *ProviderDispatcher) String() string {
	return "ProviderDispatcher"
}

func (p *ProviderDispatcher) NativeProviders() map[telematics.TSP]data_platform.Provider {
	return map_utils.ShallowCopy(p.nativeProviders)
}

func (p *ProviderDispatcher) AggregateProviders() map[telematics.DataProvider]data_platform.Provider {
	return map_utils.ShallowCopy(p.aggregatorProviders)
}

func (p *ProviderDispatcher) ConnectionProperties(
	ctx context.Context,
	connHandleId uuid.UUID,
) (*telematics.ConnectionProperties, error) {
	return p.tspConnManager.GetConnectionProperties(ctx, connHandleId)
}

func (p *ProviderDispatcher) SyncEntities(
	ctx context.Context,
	connHandleId uuid.UUID,
) (*data_platform.Entities, error) {
	provider, err := p.findProvider(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	if err := p.isRequestUnderLegalLimits(ctx, connHandleId, time.Now()); err != nil {
		return nil, err
	}
	return provider.SyncEntities(ctx, connHandleId)
}

func (p *ProviderDispatcher) SyncData(
	ctx context.Context,
	connHandleId uuid.UUID,
	interval time_utils.Interval,
) error {
	provider, err := p.ValidateDataRequest(ctx, connHandleId, interval)
	if err != nil {
		return err
	}
	return provider.SyncData(ctx, connHandleId, interval)
}

func (p *ProviderDispatcher) Ping(ctx context.Context, handleId uuid.UUID) error {
	provider, err := p.findProvider(ctx, handleId)
	if err != nil {
		return err
	}
	return provider.Ping(ctx, handleId)
}

// ValidateDataRequest attempts to find the Provider implementation matching the
// connection characteristics for the given connHandleId. It also performs
// a few other validations to ensure that the request is valid.
// At the moment though, we only perform one validation: we check that the
// requested interval (if non-nil) is within the legal limits for the given connection.
func (p *ProviderDispatcher) ValidateDataRequest(
	ctx context.Context,
	connHandleId uuid.UUID,
	interval time_utils.Interval,
) (data_platform.Provider, error) {
	provider, err := p.findProvider(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	if err := p.isRequestUnderLegalLimits(
		ctx, connHandleId, interval.End,
	); err != nil {
		return nil, err
	}
	return provider, err
}

// findProvider attempts to find the Provider implementation matching the
// connection characteristics for the given connHandleId.
func (p *ProviderDispatcher) findProvider(
	ctx context.Context,
	connHandleId uuid.UUID,
) (data_platform.Provider, error) {
	connProperties, err := p.ConnectionProperties(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get connection properties")
	}
	switch connProperties.DataProvider {
	case telematics.DataProviderNative:
		tsp := connProperties.TSP
		provider, ok := p.nativeProviders[tsp]
		if ok {
			return provider, nil
		}
		return nil, errors.Wrapf(
			data_platform.ErrConnectionNotFound,
			"unsupported tsp %s", tsp.String(),
		)
	case telematics.DataProviderTerminal, telematics.DataProviderSmartDrive:
		provider, ok := p.aggregatorProviders[connProperties.DataProvider]
		if ok {
			return provider, nil
		}
		return nil, errors.Wrapf(
			data_platform.ErrConnectionNotFound,
			"unsupported provider %s",
			connProperties.DataProvider,
		)
	default:
		return nil, errors.Wrapf(
			data_platform.ErrNotImplemented,
			"unsupported provider %s", connProperties.DataProvider.String(),
		)
	}
}

func (p *ProviderDispatcher) isRequestUnderLegalLimits(
	ctx context.Context,
	handleId uuid.UUID,
	requestedTime time.Time,
) error {
	connProps, err := p.tspConnManager.GetConnectionProperties(ctx, handleId)
	if err != nil {
		return errors.Wrap(err, "could not get connection info for the handle")
	}
	if connProps.DataPullLegalLimit.Valid {
		if requestedTime.After(connProps.DataPullLegalLimit.Time) {
			return errors.Wrapf(data_platform.ErrNoLongerPrivileged,
				"can not satisfy request because we do not have privilege to pull "+
					"data beyond %s for this handle, possibly the application has been declined/withdrawn.",
				connProps.DataPullLegalLimit.Time.Format("2006/01/02"),
			)
		}
	}
	return nil
}
