load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "experimental",
    srcs = [
        "api.go",
        "fx.go",
        "mileage.go",
        "mileage_ds.go",
        "mileage_ifta.go",
        "samsara.go",
    ],
    importpath = "nirvanatech.com/nirvana/telematics/data_platform/api/experimental",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/common-go/time_utils",
        "//nirvana/db-api/db_wrappers/telematics",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/api/dispatcher",
        "//nirvana/telematics/data_platform/api/experimental/mileage",
        "//nirvana/telematics/data_platform/providers/keeptruckin",
        "//nirvana/telematics/data_platform/providers/samsara",
        "//nirvana/telematics/data_platform/providers/terminal",
        "//nirvana/telematics/data_platform/stream",
        "//nirvana/telematics/integrations/samsara_lib",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
    ],
)

go_test(
    name = "experimental_test",
    srcs = [
        "mileage_ifta_test.go",
        "mileage_test.go",
    ],
    embed = [":experimental"],
    deps = [
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/time_utils",
        "//nirvana/infra/fx/testloader",
        "//nirvana/telematics/data_platform",
        "//nirvana/telematics/data_platform/providers/keeptruckin",
        "//nirvana/telematics/data_platform/providers/samsara",
        "//nirvana/telematics/data_platform/providers/terminal",
        "//nirvana/telematics/integrations/keeptruckin_lib",
        "//nirvana/telematics/integrations/samsara_lib",
        "//nirvana/telematics/integrations/terminal/data_api",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_google_uuid//:uuid",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_uber_go_fx//:fx",
    ],
)
