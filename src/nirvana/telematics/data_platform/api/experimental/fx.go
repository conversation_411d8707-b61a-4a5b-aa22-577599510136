package experimental

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
	"nirvanatech.com/nirvana/telematics/data_platform/api/dispatcher"
	"nirvanatech.com/nirvana/telematics/data_platform/api/experimental/mileage"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/samsara"
)

var _ = fxregistry.Register(fx.Provide(
	func(
		providerDispatcher *dispatcher.ProviderDispatcher,
		samsaraProvider *samsara.Provider,
		snowflakeWrapper *mileage.SnowflakeDSDailyMileageWrapper,
		wrapper *telematics.EntitiesWrapper,
	) *Client {
		return &Client{
			Samsara: buildSamsaraApi(samsaraProvider),
			Mileage: buildMileageApi(providerDispatcher, snowflakeWrapper, wrapper),
		}
	},
))
