package experimental

import (
	"time"

	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/api/dispatcher"
	"nirvanatech.com/nirvana/telematics/data_platform/api/experimental/mileage"
)

// MonthlyMileage represents the mileage information for a VIN in a given month.
type MonthlyMileage struct {
	VIN       data_platform.VIN
	Month     time_utils.YearAndMonth
	Miles     float64
	FetchedAt time.Time
}

// DailyMileage represents the mileage information for a VIN on a given day.
type DailyMileage struct {
	PipelineId       string // The pipeline that generated this mileage information.
	VIN              data_platform.VIN
	Day              time_utils.Date
	GPSMiles         null.Float64
	OdometerMiles    null.Float64
	ModelOutputMiles null.Float64
	GeneratedAt      time.Time
}

type mileageApi struct {
	// FromIFTA is the endpoint to get mileage information sourced from IFTA
	// reports. The mileage information is returned as a stream of MonthlyMileage(s).
	// Note that even though the API supports providing an identifier in the
	// query parameters, at this time we only accept requests for the connection
	// handle id; and will reject requests that provide a VIN. In other words,
	// DO NOT SET the IdentifierType & IdentifierValue query parameters.
	FromIFTA data_platform.Endpoint[*MonthlyMileage]

	// FromDS is the endpoint to get mileage information sourced from Data
	// Science pipelines. The mileage information is returned as a stream of
	// DailyMileage(s).
	// Note that even though the API supports providing an identifier in the
	// query parameters, at this time we only accept requests for the connection
	// handle id; and will reject requests that provide a VIN. In other words,
	// DO NOT SET the IdentifierType & IdentifierValue query parameters.
	FromDS data_platform.Endpoint[*DailyMileage]
}

func buildMileageApi(
	providerDispatcher *dispatcher.ProviderDispatcher,
	snowflakeWrapper *mileage.SnowflakeDSDailyMileageWrapper,
	wrapper *telematics.EntitiesWrapper,
) *mileageApi {
	return &mileageApi{
		FromIFTA: data_platform.NewEndpoint(
			data_platform.GenerateEndpointId[*MonthlyMileage]("MileageFromIFTA"),
			iftaMileageEndpointForFn(providerDispatcher, wrapper),
			data_platform.UnimplementedCacheDataAvailabilityFn,
			data_platform.NoDeps,
		),
		FromDS: data_platform.NewEndpoint(
			data_platform.GenerateEndpointId[*DailyMileage]("MileageFromDS"),
			dataScienceMileageEndpointForFn(providerDispatcher, snowflakeWrapper),
			data_platform.UnimplementedCacheDataAvailabilityFn,
			data_platform.NoDeps,
		),
	}
}
