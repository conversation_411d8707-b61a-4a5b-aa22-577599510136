package experimental

import (
	"context"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/time_utils"
	telematics_db_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/api/dispatcher"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/keeptruckin"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/samsara"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/terminal"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
)

// iftaMileageEndpointForFn returns the EndpointForFn for the endpoint to get
// mileage information sourced from IFTA reports (for supported providers).
func iftaMileageEndpointForFn(
	providerDispatcher *dispatcher.ProviderDispatcher,
	wrapper *telematics_db_wrapper.EntitiesWrapper,
) data_platform.EndpointForFn[*MonthlyMileage] {
	return func(
		ctx context.Context,
		connHandleId uuid.UUID,
		params data_platform.QueryParams,
		deps ...data_platform.AnonymizedEndpoint,
	) (data_platform.ResultStream[*MonthlyMileage], error) {
		if !params.IdentifierType.IsAIdentifierType() {
			return nil, errors.Wrap(
				data_platform.ErrInvalidParams,
				"identifier type is not supported for this endpoint",
			)
		}
		providerI, err := providerDispatcher.ValidateDataRequest(
			ctx, connHandleId, params.Interval,
		)
		if err != nil {
			return nil, errors.Wrap(err, "failed to validate data request")
		}

		switch provider := providerI.(type) {
		case *samsara.Provider:
			vehicleIdToVIN, err := getVehicleMapToVIN(ctx, wrapper, connHandleId)
			if err != nil {
				return nil, err
			}

			rawReports, err := provider.IFTAReport.For(ctx, connHandleId, params)
			if err != nil {
				return nil, err
			}
			return stream.Transform(
				rawReports,
				samsaraIFTAMileageNormalizer(time_utils.TimeToYearAndMonth(params.Interval.Start), vehicleIdToVIN),
			), nil
		case *keeptruckin.Provider:
			rawReports, err := provider.IFTAReport.For(ctx, connHandleId, params)
			if err != nil {
				return nil, err
			}
			return stream.Transform(
				rawReports,
				keepTruckinIFTAMileageNormalizer(time_utils.TimeToYearAndMonth(params.Interval.Start)),
			), nil
		case *terminal.Provider:
			vehicleIdToVIN, err := getVehicleMapToVIN(ctx, wrapper, connHandleId)
			if err != nil {
				return nil, err
			}
			rawReports, err := provider.IFTAVehicleReport.For(ctx, connHandleId, params)
			if err != nil {
				return nil, err
			}
			return stream.Transform(
				rawReports,
				terminalIFTAMileageNormalizer(time_utils.TimeToYearAndMonth(params.Interval.Start), vehicleIdToVIN),
			), nil
		default:
			return nil, errors.Newf("unknown provider type: %T", provider)
		}
	}
}

func samsaraIFTAMileageNormalizer(
	month time_utils.YearAndMonth,
	vehicleIdToVIN map[string]data_platform.VIN,
) func(*samsara.WrappedIFTAVehicleReport) (*MonthlyMileage, error) {
	return func(item *samsara.WrappedIFTAVehicleReport) (*MonthlyMileage, error) {
		totalMeters := 0.0
		for _, j := range item.IFTAVehicleReport.Jurisdictions {
			totalMeters += j.TotalMeters
		}

		return &MonthlyMileage{
			VIN:       vehicleIdToVIN[item.IFTAVehicleReport.Vehicle.Id],
			Month:     month,
			Miles:     data_platform.MetersToMiles(totalMeters),
			FetchedAt: item.FetchedAt,
		}, nil
	}
}

func keepTruckinIFTAMileageNormalizer(
	month time_utils.YearAndMonth,
) func(timestamp *keeptruckin.WrappedIFTATripSummary) (*MonthlyMileage, error) {
	return func(item *keeptruckin.WrappedIFTATripSummary) (*MonthlyMileage, error) {
		return &MonthlyMileage{
			VIN:   data_platform.VIN(item.IFTATripSummary.IftaTrip.Vehicle.Vin),
			Month: month,
			// we're asking for metric via "X-Metric-Units" header in integrations/keeptruckin_lib/api_integration_impl
			Miles:     item.IFTATripSummary.IftaTrip.Distance * data_platform.KphToMph,
			FetchedAt: item.FetchedAt,
		}, nil
	}
}

func terminalIFTAMileageNormalizer(
	month time_utils.YearAndMonth,
	vehicleIdToVIN map[string]data_platform.VIN,
) func(timestamp *terminal.WrappedIFTASummary) (*MonthlyMileage, error) {
	return func(item *terminal.WrappedIFTASummary) (*MonthlyMileage, error) {
		return &MonthlyMileage{
			VIN:       vehicleIdToVIN[*item.Vehicle],
			Month:     month,
			Miles:     float64(item.Distance * data_platform.KphToMph),
			FetchedAt: item.FetchedAt,
		}, nil
	}
}

func getVehicleMapToVIN(
	ctx context.Context,
	w *telematics_db_wrapper.EntitiesWrapper,
	connHandleId uuid.UUID,
) (map[string]data_platform.VIN, error) {
	vehicles, err := w.FetchVehicles(ctx, connHandleId)
	if err != nil {
		return nil, err
	}

	vehicleIdToVIN := make(map[string]data_platform.VIN)
	for _, veh := range vehicles {
		vehicleIdToVIN[veh.TspId] = veh.VIN
	}

	return vehicleIdToVIN, nil
}
