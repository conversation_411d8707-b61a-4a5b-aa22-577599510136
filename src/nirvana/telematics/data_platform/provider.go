package data_platform

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/time_utils"
)

// Provider is the interface to be implemented for a (Data-)Provider.
// All implementations of this interface are expected to wrap these errors for the cases
// mentioned alongside them.
//   - ErrConnectionNotFound: if the Provider didn't find connection details for the given identifier.
//   - ErrRequestInProgress: if connection details were found, but data is not yet available.
//   - ErrRequestedDataUnavailable: if data isn't available at the provider for the given interval.
//   - ErrGeneric: for all other types of errors
type Provider interface {
	fmt.Stringer

	// SyncEntities sync the entities (Org/Vehicle/Driver) for the given
	// connection handle id (from source provider) to the data platform.
	SyncEntities(ctx context.Context, connHandleId uuid.UUID) (*Entities, error)

	// SyncData is a non-blocking function to sync the data for the given
	// connection handle id (from source provider).
	// The interval is the time interval for which the data is to be synced, and is
	// inclusive of the start and end time.
	// The method should keep returning ErrNotAvailableYet until data is available
	// for the given interval.
	// NOTE: This method is a no-op for providers where this isn't required.
	SyncData(ctx context.Context, connHandleId uuid.UUID, interval time_utils.Interval) error

	// Ping may be used to perform a light-weight health check for the given connection to
	// provide confidence on whether the subsequent SyncData / SyncEntities would succeed.
	// It does not persist or mutate any persisted data for the connection.
	Ping(ctx context.Context, handleId uuid.UUID) error
}

// Entities refers to a common collection for Vehicles, Drivers and VehicleGroups
type Entities struct {
	Vehicles      []*Vehicle
	Drivers       []Driver
	VehicleGroups []*VehicleGroup
}
