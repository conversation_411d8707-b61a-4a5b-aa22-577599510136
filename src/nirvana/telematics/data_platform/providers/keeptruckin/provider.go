package keeptruckin

import (
	"cmp"
	"context"
	"slices"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/keeptruckin"
	"nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/utils"
	dp_utils "nirvanatech.com/nirvana/telematics/data_platform/utils"
	"nirvanatech.com/nirvana/telematics/integrations/keeptruckin_lib"
)

// Provider implements core.Provider interface for Native, KeepTruckin integration
type Provider struct {
	clk       clock.Clock
	dbWrapper keeptruckin.DataWrapper

	apiWrapper       *apiWrapper
	metadataProvider *metadataProvider
	statsProvider    *statsProvider
	iftaProvider     *iftaProvider

	Endpoints
}

var _ data_platform.Provider = (*Provider)(nil)

func NewProvider(
	clk clock.Clock,
	store *utils.Store,
	integration keeptruckin_lib.APIIntegration,
	dbWrapper keeptruckin.DataWrapper,
	tspConnManager *connections.TSPConnManager,
) *Provider {
	aw := newApiWrapper(integration, tspConnManager)
	return (&Provider{
		clk:        clk,
		dbWrapper:  dbWrapper,
		apiWrapper: aw,
		metadataProvider: &metadataProvider{
			dbWrapper:  dbWrapper,
			apiWrapper: aw,
		},
		statsProvider: &statsProvider{
			clk:        clk,
			apiWrapper: aw,
			storeWrapper: &storeWrapper{
				store: store,
			},
			vlRequestSequentializer:     dp_utils.NewReqSerializer(),
			eventsRequestSequentializer: dp_utils.NewReqSerializer(),
		},
		iftaProvider: &iftaProvider{
			apiWrapper: aw,
			clk:        clk,
		},
	}).buildEndpoints()
}

func (p *Provider) String() string {
	return "NativeKeepTruckin"
}

func (p *Provider) Vehicles(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.Vehicle, error) {
	ctx, span := tracing.Start(ctx, "gomotive.Vehicles")
	defer span.End()

	id, err := p.metadataProvider.identifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	resp, err := p.metadataProvider.vehicles(ctx, id)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *Provider) VehicleGroups(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.VehicleGroup, error) {
	ctx, span := tracing.Start(ctx, "gomotive.VehicleGroups")
	defer span.End()

	id, err := p.metadataProvider.identifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	resp, err := p.metadataProvider.vehicleGroups(ctx, id)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *Provider) Drivers(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]data_platform.Driver, error) {
	ctx, span := tracing.Start(ctx, "gomotive.Drivers")
	defer span.End()

	id, err := p.metadataProvider.identifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	resp, err := p.metadataProvider.drivers(ctx, id)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *Provider) SyncEntities(ctx context.Context, connHandleId uuid.UUID) (
	*data_platform.Entities, error,
) {
	ctx, span := tracing.Start(ctx, "gomotive.SyncEntities")
	defer span.End()

	rid, err := p.metadataProvider.identifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, errors.Mark(err, data_platform.ErrConnectionNotFound)
	}
	err = p.metadataProvider.refreshEntities(ctx, rid)
	if err != nil {
		return nil, errors.Wrap(err, "failed to refresh entities")
	}
	vehicles, err := p.Vehicles(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicles")
	}
	drivers, err := p.Drivers(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch drivers")
	}
	groups, err := p.VehicleGroups(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicle groups")
	}
	return &data_platform.Entities{
		Vehicles:      vehicles,
		Drivers:       drivers,
		VehicleGroups: groups,
	}, nil
}

func (p *Provider) SyncData(
	_ context.Context, _ uuid.UUID, _ time_utils.Interval,
) error {
	// no-op
	return nil
}

func (p *Provider) Ping(ctx context.Context, handleId uuid.UUID) error {
	// For GoMotive, no organization level metadata endpoint is available to ping,
	// so we use list vehicles endpoint as the proxy for ping.
	vehicles, err := p.apiWrapper.vehicles(ctx, handleId)
	if err != nil {
		return errors.Wrap(err, "ping failed because failed to get vehicles list for connection")
	}
	if len(vehicles) == 0 {
		return errors.New("ping failed because no vehicles found for connection")
	}
	return nil
}

func (p *Provider) cachedDataAvailability(
	ctx context.Context,
	connHandleId uuid.UUID,
	params data_platform.QueryParams,
	resource managedResourceType,
) (data_platform.ResourceAvailabilityLog, error) {
	ctx, span := tracing.Start(ctx, "gomotive.CachedDataAvailability")
	defer span.End()

	interval := params.Interval
	if interval.End.After(p.clk.Now()) {
		return nil, errors.Newf(
			"invalid interval=%v, End cannot be after current time",
			interval,
		)
	}
	if params.IdentifierType != data_platform.IdentifierTypeProviderVehicleId {
		return nil, errors.Newf("identifier type must be a vehicleId identifier")
	}
	requestIdentifiers, err := identify(
		ctx,
		p.dbWrapper,
		connHandleId,
		params.IdentifierType,
		params.IdentifierValue,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to identify %s=%s", params.IdentifierType, params.IdentifierValue)
	}
	// We must always iterate in a deterministic order,
	// as if there are multiple identifiers, their ETag values must be combined
	// in a deterministic order always.
	slices.SortFunc(requestIdentifiers, func(a, b *requestIdentifier) int {
		// both vehicleComapny guaranteed non-nil because we enforce above that identifier type is a vehicle-level.
		return cmp.Compare(a.vehicleCompany.vehicleId, b.vehicleCompany.vehicleId)
	})
	m := utils.NewWeekResourceAvailabilityMap(
		interval, data_platform.Unavailable,
	)
	for idx := range requestIdentifiers {
		rid := requestIdentifiers[idx]
		avb, err := p.statsProvider.vehicleResourceLogAvailability(
			ctx,
			rid.handleId,
			rid.vehicleCompany.vehicleId,
			resource,
			interval,
		)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to check availability for vehicleId=%d interval=%v",
				rid.vehicleCompany.vehicleId,
				interval,
			)
		}
		m = m.CombineWithInfoMap(avb)
	}
	return m.Finalize(), nil
}
