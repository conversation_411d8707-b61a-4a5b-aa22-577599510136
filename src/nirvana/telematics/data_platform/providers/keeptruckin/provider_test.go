package keeptruckin_test

import (
	"context"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/keeptruckin_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/keeptruckin"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
	"nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	integration_test_utils "nirvanatech.com/nirvana/telematics/integrations/keeptruckin_lib/test_utils"
)

var year2021 = time_utils.Interval{
	Start: time.Date(2021, time.January, 1, 0, 0, 0, 0, time.UTC),
	End:   time.Date(2021, time.December, 31, 23, 59, 59, 0, time.UTC),
}

// time interval for the first week of February 2021
var year2021FebWeek1 = time_utils.Interval{
	Start: time.Date(2021, time.February, 1, 0, 0, 0, 0, time.UTC),
	End:   time.Date(2021, time.February, 7, 23, 59, 59, 0, time.UTC),
}

// time interval for the second week of February 2021
var year2021FebWeek2 = time_utils.Interval{
	Start: time.Date(2021, time.February, 8, 0, 0, 0, 0, time.UTC),
	End:   time.Date(2021, time.February, 14, 23, 59, 59, 0, time.UTC),
}

type env struct {
	fx.In

	*keeptruckin_fixture.KeepTruckin
	Provider *keeptruckin.Provider
	Clk      clock.Clock
}

func TestRefreshMetadata(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// Verify that API is called 3 times to get all 150 vehicles @ 50/page
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(150),
	).Times(3)
	// Verify that API is called 1 time to get all 45 users @ 50/page
	env.MockServer.EXPECT().GetV1Users(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1UsersGolden(45),
	).Times(1)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	vehicles, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, vehicles, 150)

	drivers, err := env.Provider.Drivers(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, drivers, 45)

	// Repeat provider calls should get values from cache instead of API
	vehicles2, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)
	require.Equal(t, vehicles, vehicles2)

	drivers2, err := env.Provider.Drivers(ctx, handleId)
	require.NoError(t, err)
	require.Equal(t, drivers2, drivers)
	// verify that most fields are set
	for _, d := range drivers2 {
		require.NotZero(t, d.License)
		require.NotZero(t, d.LicenseState)
		require.NotZero(t, d.FullName)
		require.NotZero(t, d.AliasName)
		require.NotZero(t, d.CreatedAt)
		require.NotZero(t, d.TspCreatedAt)
		require.NotZero(t, d.UpdatedAt)
		require.NotZero(t, d.TspUpdatedAt)
		require.True(t, d.TspActivationStatus.Bool)
	}

	// Verify that refreshing metadata forces a re-pull
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(250),
	).Times(5)
	env.MockServer.EXPECT().GetV1Users(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1UsersGolden(150),
	).Times(3)

	_, err = env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Upsert should work properly. Number of vehicles should be 250 and not 150+250
	vehicles, err = env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, vehicles, 250)

	// Upsert should work properly. Number of drivers should be 150 and not 45+150
	drivers, err = env.Provider.Drivers(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, drivers, 150) // Admins should not be included
}

func TestVehicleLocations(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocationsTestCase(t)
	})
}

func TestVehicleLocations_Concurrent(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocationsConcurrentTestCase(t)
	})
}

func TestVehicleLocations_504AutoRetry(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocations504AutoRetryTestCase(t)
	})
}

func TestEvents(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runEventsTestCase(t)
	})
}

func TestDrivingPeriod(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	env.MockServer.EXPECT().GetV1DrivingPeriods(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetDrivingPeriodGolden(
			[]int{1, 2, 3}, // vehicleIds
			[]int{1, 2, 3}, // driverIds
			year2021,
			time.Hour*24*365,
		),
	).Times(365) // paginated api, we get one page of output for each day

	resStream, err := env.Provider.DrivingPeriods.For(
		ctx, handleId,
		data_platform.QueryParams{
			Interval:       year2021,
			IdentifierType: data_platform.IdentifierTypeProviderConnection,
		},
	)
	require.NoError(t, err)
	drivingPeriods, err := stream.GetAll(resStream)

	require.NoError(t, err)
	require.Len(t, drivingPeriods, 50*365*3) // 50 driving periods each day for each vehicle

	// setting max request interval as small to test 504 auto retry case
	env.MockServer.EXPECT().GetV1DrivingPeriods(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetDrivingPeriodGolden(
			[]int{1, 2, 3}, // vehicleIds
			[]int{1, 2, 3}, // driverIds
			year2021,
			time.Hour*24*15,
		),
	).Times(12 + 365) // one failed call per month

	resStream, err = env.Provider.DrivingPeriods.For(
		ctx, handleId,
		data_platform.QueryParams{
			Interval:       year2021,
			IdentifierType: data_platform.IdentifierTypeProviderConnection,
		},
	)
	require.NoError(t, err)
	drivingPeriods, err = stream.GetAll(resStream)

	require.NoError(t, err)
	require.Len(t, drivingPeriods, 50*365*3) // 50 driving periods each day per vehicle
}

// TestVehicleLocations_EdgeCases asserts the provider correctly responds to request of less than one week.
func TestVehicleLocations_EdgeCases(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocationsEdgeCasesTestCase(t)
	})
}

func TestIFTASummary(t *testing.T) {
	t.Run("with one vehicle", func(t *testing.T) {
		runIFTATestCaseOneVehicle(t)
	})
	t.Run("with more than one vehicle", func(t *testing.T) {
		runIFTATestCaseMultipleVehicles(t)
	})
}

// Test_CachedDataAvailabilityForVehicleId asserts provider correctly responds with data availability
// queries once data request has been fulfilled.
func Test_CachedDataAvailabilityForVehicleId(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// configure mock to return 1 vehicle only
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// configure mock to return vehicle locations & events for year 2021
	env.MockServer.EXPECT().GetV1DriverPerformanceEvents(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetEventsGolden([]int{1}, year2021),
	).AnyTimes()
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, 2*7*24*time.Hour),
	).AnyTimes()

	year2019Start := time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)
	year2020Start := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	year2021Start := time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)
	year2022Start := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)
	queryParams := data_platform.QueryParams{
		IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
		IdentifierValue: "1",
		Interval: time_utils.Interval{
			Start: year2020Start,
			End:   year2022Start.Add(-time.Second),
		},
	}

	// request data from 1 Jan 2020 - 31 Dec 2021 to warm up caches
	{
		// gps log
		rs, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// harsh acceleration log
		rs, err := env.Provider.HarshAccelerationEventLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// harsh braking log
		rs, err := env.Provider.HarshBrakingEventLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}

	// verify availability for "warmed up" stats
	for _, endpoint := range []data_platform.CachedDataAvailabilityFn{
		env.Provider.GPSLog.CachedDataAvailability,
		env.Provider.HarshAccelerationEventLog.CachedDataAvailability,
		env.Provider.HarshBrakingEventLog.CachedDataAvailability,
	} {
		// request availability log from 2019 - 2021 (Dec)
		avlLog, err := endpoint(
			ctx, handleId, data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: "1",
				Interval: time_utils.Interval{
					Start: year2019Start,
					End:   year2022Start.Add(-time.Second),
				},
			},
		)
		require.NoError(t, err)

		expectedWeeksWithNoCache := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2019Start,
					End:   year2020Start,
				},
			).GetISOWeekListRightBounded(year2020Start),
		)
		expectedWeeksWithZeroData := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2020Start,
					End:   year2021Start,
				},
			).GetISOWeekListRightBounded(year2021Start),
		)
		expectedWeeksWithActualData := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2021Start,
					End:   year2022Start,
				},
			).GetISOWeekListRightBounded(year2022Start),
		)

		require.Len(t, avlLog, expectedWeeksWithZeroData+expectedWeeksWithNoCache+expectedWeeksWithActualData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.DataAvailable), expectedWeeksWithActualData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.NoReadings), expectedWeeksWithZeroData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.Unavailable), expectedWeeksWithNoCache)

		// Check that ETag is populated
		for _, avl := range avlLog {
			if avl.State != data_platform.Unavailable {
				require.NotEmpty(t, avl.ETag)
			} else {
				require.Empty(t, avl.ETag)
			}
		}
	}
}

func updateQueryParamsWithVehicleIdentifier(
	qp data_platform.QueryParams, vehicleId int,
) data_platform.QueryParams {
	qp.IdentifierType = data_platform.IdentifierTypeProviderVehicleId
	qp.IdentifierValue = strconv.Itoa(vehicleId)
	return qp
}

func runVehicleLocationsTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// configure mock to return 1 vehicle only
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// configure mock to return vehicle locations for year 2021
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations(
			[]int{1},
			// Allow mock to return data +/- 1 month of year2021 to also verify time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
			2*7*24*time.Hour,
		),
	).Times(53) // .Times(..) may need to be edited if we ever change vehicleLocationsAPIBatchingFactor

	resStream, err := env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	gpsLogs, err := stream.GetAll(resStream)
	require.NoError(t, err)
	// Mock is programmed to return 50 GPS points per day. Year 2021 has 365 days.
	require.Len(t, gpsLogs, 50*365)

	// a repeat call should return the same data WITHOUT making any further API calls.
	// This verifies that caching layer is working properly.
	resStream, err = env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	gpsLogs2, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Equal(t, gpsLogs, gpsLogs2)

	// Verify that a repeat call with ignore provider cache set actually ignores provider cache
	env.Clk.(*clock.Mock).Add(time.Hour)

	// configure mock with expectations
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, 2*7*24*time.Hour),
	).Times(53) // .Times(..) may need to be edited if we ever change vehicleLocationsAPIBatchingFactor

	resStream, err = env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval:            year2021,
				IgnoreProviderCache: null.TimeFrom(env.Clk.Now()),
			}, 1,
		),
	)
	require.NoError(t, err)
	gpsLogs3, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs3, 50*365)
}

func runVehicleLocationsConcurrentTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// configure mock to return 1 vehicle only
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// configure mock to return vehicle locations for year 2021
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, 2*7*24*time.Hour),
	).Times(53) // .Times(..) may need to be edited if we ever change vehicleLocationsAPIBatchingFactor

	// Concurrently fetch vehicle locations from 10 goroutines.
	// The expectation is that requests will be serialized in a manner such that no duplicate API request is made.
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			resStream, err := env.Provider.GPSLog.For(
				ctx, handleId,
				updateQueryParamsWithVehicleIdentifier(
					data_platform.QueryParams{
						Interval: year2021,
					}, 1,
				),
			)
			require.NoError(t, err)
			gpsLogs, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Len(t, gpsLogs, 50*365)
			wg.Done()
		}()
	}
	wg.Wait()
}

func runVehicleLocations504AutoRetryTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// configure mock to return 1 vehicle only
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// configure mock to return vehicle locations for year 2021, we set maxRequestDuration at 1 day to trigger 504s.
	// the expectation is that provider will automatically retry with shorter intervals recursively.
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, time_utils.Day-time.Second),
	).Times(53 * (1 + 2 + 4 + 6)) // .Times(..) may need to be edited if we ever change vehicleLocationsAPIBatchingFactor

	resStream, err := env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	gpsLogs, err := stream.GetAll(resStream)
	require.NoError(t, err)
	// Mock is programmed to return 50 GPS points per day. Year 2021 has 365 days.
	require.Len(t, gpsLogs, 50*365, "found length %d", len(gpsLogs))
}

func runEventsTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// configure mock to return 1 vehicle only
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// configure mock to return events for year 2021
	env.MockServer.EXPECT().GetV1DriverPerformanceEvents(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetEventsGolden(
			[]int{1},
			// Allow mock to return data +/- 1 month of year2021 to also verify time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
		),
	).Times(53) // 53 ISO-weeks

	resStream, err := env.Provider.HarshBrakingEventLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	events, err := stream.GetAll(resStream)
	require.NoError(t, err)
	// Mock is programmed to return 1 event per day. Year 2021 has 365 days.
	require.Len(t, events, 365)

	// a repeat call should return the same data WITHOUT making any further API calls.
	// This verifies that caching layer is working properly.
	resStream, err = env.Provider.HarshBrakingEventLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	events2, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Equal(t, events, events2) // see note above for 5 days of data loss

	// harsh acceleration events will also be read from the same cache (though they will be 0)
	resStream, err = env.Provider.HarshAccelerationEventLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	acclEvents, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Empty(t, acclEvents)

	// Verify that a repeat call with ignore provider cache set actually ignores provider cache
	env.Clk.(*clock.Mock).Add(time.Hour)

	// configure mock with expectations
	env.MockServer.EXPECT().GetV1DriverPerformanceEvents(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetEventsGolden([]int{1}, year2021),
	).Times(53)

	resStream, err = env.Provider.HarshBrakingEventLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval:            year2021,
				IgnoreProviderCache: null.TimeFrom(env.Clk.Now()),
			}, 1,
		),
	)
	require.NoError(t, err)
	events3, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, events3, 365)
}

// runVehicleLocationsEdgeCasesTestCase asserts the provider correctly responds to request of less than one week.
func runVehicleLocationsEdgeCasesTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	// configure mock to return 1 vehicle only
	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// configure mock to return vehicle locations for year 2021
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, 2*7*24*time.Hour),
	).Times(1) // .Times(..) may need to be edited if we ever change vehicleLocationsAPIBatchingFactor

	// Make a request such that both start and end of interval is within same ISO week.
	queryParams := updateQueryParamsWithVehicleIdentifier(data_platform.QueryParams{
		// 1, 2 Jan (this 2 penultimate days of 2020-W53)
		Interval: time_utils.Interval{
			Start: time.Date(2021, time.January, 1, 0, 0, 0, 0, time.UTC),
			End:   time.Date(2021, time.January, 2, 23, 59, 59, 1e9-1, time.UTC),
		},
	}, 1)
	resStream, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs, 50*2) // 50 GPS points each day

	// Retry the request, verify that cache hit occurs.
	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs2, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Equal(t, gpsLogs, gpsLogs2)

	// Unwind the clock so that it reads 3rd Jan, 2021 (12 noon)
	env.Clk.(*clock.Mock).Set(time.Date(2021, time.January, 3, 12, 0, 0, 0, time.UTC))
	// Retry the request, there should be no data because clk.Now() is within ISO week.
	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs3, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Empty(t, gpsLogs3)

	// Retry the request with no-cache set.
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, 2*7*24*time.Hour),
	).Times(1)

	queryParams.OverrideCachingBehaviourOnInterval = null.BoolFrom(true)
	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs4, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs4, 2*50)

	// What if queryParams.Interval.Start was in a different ISO week?
	// We expect 2 provider API calls, one for last ISO week, and another for this one.
	env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV3VehicleLocations([]int{1}, year2021, 2*7*24*time.Hour),
	).Times(2)
	// 9th Jan lies in 2021-W01
	queryParams.Interval.Start = time.Date(2021, time.January, 9, 0, 0, 0, 0, time.UTC)
	// 14th Jan lies in 2021-W02
	queryParams.Interval.End = time.Date(2021, time.January, 14, 0, 0, 0, 0, time.UTC)
	// Reset clock to be 15th Jan (still in 2021-W02)
	env.Clk.(*clock.Mock).Set(time.Date(2021, time.January, 15, 0, 0, 0, 0, time.UTC))

	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs5, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs5, 5*50)
}

func runIFTATestCaseMultipleVehicles(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	testCases := []struct {
		name           string
		mockedResponse []integration_test_utils.IFTAData
		callTimes      int
		params         data_platform.QueryParams
		wantErr        bool
	}{
		{
			name: "by handle id, 3 vehicles, full 1 month period",
			mockedResponse: []integration_test_utils.IFTAData{
				{
					Distance: 23,
					VIN:      "VINKT1",
				},
				{
					Distance: 23,
					VIN:      "VINKT2",
				},
				{
					Distance: 23,
					VIN:      "VINKT3",
				},
			},
			callTimes: 1,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType: data_platform.IdentifierTypeUndefined,
			},
			wantErr: false,
		},
		{
			name: "by handle id, 3 vehicles, partial 1 month period",
			mockedResponse: []integration_test_utils.IFTAData{
				{
					Distance: 23,
					VIN:      "VINKT1",
				},
			},
			callTimes: 0,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 18).ToTime(),
				},
				IdentifierType: data_platform.IdentifierTypeUndefined,
			},
			wantErr: true,
		},
		{
			name:           "by handle id, 3 vehicles, two pages, full 1 month period",
			mockedResponse: getMockedIFTAResponse(51),
			callTimes:      2,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType: data_platform.IdentifierTypeUndefined,
			},
			wantErr: false,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			env.MockServer.EXPECT().GetV1IftaSummary(gomock.Any(), gomock.Any()).DoAndReturn(
				integration_test_utils.GetV1IFTASummaryGolden(tt.mockedResponse),
			).Times(tt.callTimes)

			resStream, err := env.Provider.IFTAReport.For(ctx, handleId, tt.params)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			iftaSummaries, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.NotZero(t, len(iftaSummaries))
		})
	}
}

func getMockedIFTAResponse(n int) []integration_test_utils.IFTAData {
	mockedResponse := make([]integration_test_utils.IFTAData, 0, n)
	for i := 0; i < n; i++ {
		mockedResponse = append(mockedResponse, integration_test_utils.IFTAData{
			Distance: float64(i * 23),
			VIN:      data_platform.VIN("VINKT" + strconv.Itoa(i)),
		})
	}
	return mockedResponse
}

func runIFTATestCaseOneVehicle(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	testCases := []struct {
		name           string
		mockedResponse []integration_test_utils.IFTAData
		callTimes      int
		params         data_platform.QueryParams
		wantErr        bool
	}{
		{
			name: "one trip summary, full 1 month period",
			mockedResponse: []integration_test_utils.IFTAData{
				{
					Distance: 25,
					VIN:      "VINKT1",
				},
			},
			callTimes: 1,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeVIN,
				IdentifierValue: "",
			},
			wantErr: false,
		},
		{
			name: "one trip summary, partial 1 month period",
			mockedResponse: []integration_test_utils.IFTAData{
				{
					Distance: 25,
					VIN:      "VINKT1",
				},
			},
			callTimes: 0,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 15).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeVIN,
				IdentifierValue: "",
			},
			wantErr: true,
		},
		{
			name: "one trip summary, full 3 months period",
			mockedResponse: []integration_test_utils.IFTAData{
				{
					Distance: 25,
					VIN:      "VINKT1",
				},
			},
			callTimes: 1,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 3, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeVIN,
				IdentifierValue: "",
			},
			wantErr: false,
		},
		{
			name:           "100 trip summaries (full 2 pages), full 1 month period",
			mockedResponse: getMockedIFTAResponse(100),
			callTimes:      2,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeVIN,
				IdentifierValue: "",
			},
			wantErr: false,
		},
		{
			name:           "100 trip summaries (2 pages), full 1 month period",
			mockedResponse: getMockedIFTAResponse(99),
			callTimes:      2,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeVIN,
				IdentifierValue: "",
			},
			wantErr: false,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			env.MockServer.EXPECT().GetV1IftaSummary(gomock.Any(), gomock.Any()).DoAndReturn(
				integration_test_utils.GetV1IFTASummaryGolden(tt.mockedResponse),
			).Times(tt.callTimes)

			resStream, err := env.Provider.IFTAReport.For(ctx, handleId, tt.params)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			iftaSummaries, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Len(t, iftaSummaries, len(tt.mockedResponse))
		})
	}
}

// TestReadOnlyQueryParam is the main test function that verifies the behavior of different endpoints (GPS, Safety)
// when querying data with and with and without the ReadOnly parameter. This is done by testing two time intervals for each normalized stat,
// where in one interval we pull the normalized stats in cache first and then call the endpoint with read only flag true, while in second
// interval call is made with read only true but data is not in the cache.
func TestReadOnlyQueryParam(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	env.MockServer.EXPECT().GetV1Vehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		integration_test_utils.GetV1Vehicles(1),
	).Times(1)
	_, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)

	// Run subtests for each endpoint/
	t.Run("GPSlog", func(t *testing.T) {
		// mock calls for gps log.
		env.MockServer.EXPECT().GetV3VehicleLocationsId(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			integration_test_utils.GetV3VehicleLocations([]int{1}, year2021FebWeek1, 1*7*24*time.Hour)).
			Times(1) // should be invoked only once, in case of data pull for week 1, , as 1 call returns 7 days of data.
		testReadOnlyForNormalizedEndpoint(
			ctx,
			t,
			year2021FebWeek1,
			year2021FebWeek2,
			handleId,
			env.Provider.GPSLog,
			50, // number of points in a day
			7,  // 1(number of iso weeks) * 7
		)
	})

	t.Run("SafetyEventLog", func(t *testing.T) {
		// mock calls for safety event.
		env.MockServer.EXPECT().GetV1DriverPerformanceEvents(gomock.Any(), gomock.Any()).DoAndReturn(
			integration_test_utils.GetEventsGolden([]int{1}, year2021FebWeek1)).
			Times(1) // should be invoked only once, in case of data pull for week 1, as 1 call returns 7 days of data.
		testReadOnlyForNormalizedEndpoint(
			ctx,
			t,
			year2021FebWeek1,
			year2021FebWeek2,
			handleId,
			env.Provider.SafetyEventLog,
			1, // number of points in a day
			7, // 1(number of iso weeks) * 7
		)
	})
}

// testReadOnlyForNormalizedEndpoint is a helper function that tests the ReadOnly parameter behavior for a given endpoint.
// It first pulls the data for interval1 and calls interval1 with ReadOnly=true while for interval2 it directly calls with ReadOnly true
// where it expects data_platform.ErrNotAvailableForReadOnly error.
func testReadOnlyForNormalizedEndpoint[K any](
	ctx context.Context,
	t *testing.T,
	interval1 time_utils.Interval,
	interval2 time_utils.Interval,
	handleId uuid.UUID,
	endpoint data_platform.Endpoint[K],
	pointsPerDay int,
	numDays int,
) {
	params := updateQueryParamsWithVehicleIdentifier(data_platform.QueryParams{Interval: interval1}, 1)
	// Test for ReadOnly = false
	resStream, err := endpoint.For(ctx, handleId, params)
	require.NoError(t, err)
	normalizedStats, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, normalizedStats, pointsPerDay*numDays) // Validate that the number of returned points is correct.

	// Test for ReadOnly = true, same interval
	params.ReadOnly = null.BoolFrom(true)
	resStream, err = endpoint.For(ctx, handleId, params)
	require.NoError(t, err)
	normalizedStats, err = stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, normalizedStats, pointsPerDay*numDays) // number of points should remain same as above.

	// Test ReadOnly = true with the second interval, expecting an error because the data is not available in cache.
	params = updateQueryParamsWithVehicleIdentifier(data_platform.QueryParams{Interval: interval2}, 1)
	params.ReadOnly = null.BoolFrom(true)
	resStream, err = endpoint.For(ctx, handleId, params)
	require.NoError(t, err)
	normalizedStats, err = stream.GetAll(resStream)
	require.ErrorIs(t, err, data_platform.ErrNotAvailableForReadOnly)
}
