package samsara

import (
	"cmp"
	"context"
	"slices"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/tracing"
	"nirvanatech.com/nirvana/db-api/db_wrappers/samsara"
	"nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/utils"
	dp_utils "nirvanatech.com/nirvana/telematics/data_platform/utils"
	"nirvanatech.com/nirvana/telematics/integrations/samsara_lib"
)

// Provider implements core.Provider interface for Native, Samsara integration
type Provider struct {
	clk       clock.Clock
	dbWrapper samsara.DataWrapper

	store                            *utils.Store
	apiWrapper                       *apiWrapper
	MetadataProvider                 *MetadataProvider
	statsProvider                    *statsProvider
	eventsProvider                   *eventsProvider
	tripsProvider                    *tripsProvider
	vehicleDriverAssignmentsProvider *vehicleDriverAssignmentsProvider
	iftaProvider                     *iftaProvider

	Endpoints
}

func NewProvider(
	clk clock.Clock,
	store *utils.Store,
	integration samsara_lib.APIIntegration,
	dbWrapper samsara.DataWrapper,
	tspConnManager *connections.TSPConnManager,
) *Provider {
	aw := apiWrapper{
		apiIntegration: integration,
		tspConnManager: tspConnManager,
	}
	reqSerializer := dp_utils.NewReqSerializerOf[requestSzKey]()
	return (&Provider{
		clk:        clk,
		dbWrapper:  dbWrapper,
		store:      store,
		apiWrapper: &aw,
		MetadataProvider: &MetadataProvider{
			clk:        clk,
			dbWrapper:  dbWrapper,
			apiWrapper: &aw,
		},
		statsProvider: &statsProvider{
			apiWrapper:              &aw,
			store:                   store,
			vrRequestSequentialiser: reqSerializer,
		},
		eventsProvider: &eventsProvider{
			apiWrapper:              &aw,
			store:                   store,
			vrRequestSequentialiser: reqSerializer,
		},
		tripsProvider: &tripsProvider{
			apiWrapper:              &aw,
			store:                   store,
			vrRequestSequentialiser: reqSerializer,
		},
		vehicleDriverAssignmentsProvider: &vehicleDriverAssignmentsProvider{
			apiWrapper:              &aw,
			store:                   store,
			vrRequestSequentialiser: reqSerializer,
		},
		iftaProvider: &iftaProvider{
			apiWrapper: &aw,
			clk:        clk,
		},
	}).buildEndpoints()
}

var _ data_platform.Provider = &Provider{}

func (p *Provider) String() string {
	return "NativeSamsara"
}

func (p *Provider) Vehicles(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.Vehicle, error) {
	ctx, span := tracing.Start(ctx, "samsara.Vehicles")
	defer span.End()

	id, err := p.MetadataProvider.IdentifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to identify Samsara handle %s", connHandleId)
	}
	resp, err := p.MetadataProvider.vehicles(ctx, id)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch Samsara vehicles for handle %s", id.handleId)
	}
	return resp, nil
}

func (p *Provider) Drivers(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]data_platform.Driver, error) {
	ctx, span := tracing.Start(ctx, "samsara.Drivers")
	defer span.End()

	id, err := p.MetadataProvider.IdentifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	resp, err := p.MetadataProvider.drivers(ctx, id)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *Provider) VehicleGroups(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.VehicleGroup, error) {
	ctx, span := tracing.Start(ctx, "samsara.VehicleGroups")
	defer span.End()

	id, err := p.MetadataProvider.IdentifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	resp, err := p.MetadataProvider.vehicleGroups(ctx, id)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *Provider) SyncEntities(ctx context.Context, connHandleId uuid.UUID) (
	*data_platform.Entities, error,
) {
	ctx, span := tracing.Start(ctx, "samsara.SyncEntities")
	defer span.End()

	rid, err := p.MetadataProvider.IdentifyByHandle(ctx, connHandleId)
	if err != nil {
		return nil, errors.Mark(err, data_platform.ErrConnectionNotFound)
	}
	if err = p.MetadataProvider.refreshVINsAndTags(ctx, rid); err != nil {
		return nil, errors.Wrap(err, "failed to refresh VINs")
	}
	if err = p.MetadataProvider.refreshDLs(ctx, rid); err != nil {
		return nil, errors.Wrap(err, "failed to refresh Drivers")
	}
	vehicles, err := p.Vehicles(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicles")
	}
	drivers, err := p.Drivers(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch drivers")
	}
	groups, err := p.VehicleGroups(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicle groups")
	}
	return &data_platform.Entities{
		Vehicles:      vehicles,
		Drivers:       drivers,
		VehicleGroups: groups,
	}, nil
}

func (p *Provider) SyncData(
	_ context.Context, _ uuid.UUID, _ time_utils.Interval,
) error {
	// no-op
	return nil
}

func (p *Provider) Ping(ctx context.Context, handleId uuid.UUID) error {
	// For samsara we define ping as an api call to /me endpoint.
	_, err := p.apiWrapper.fleet(ctx, handleId)
	return errors.Wrap(err, "ping failed because failed to hit /me endpoint for samsara")
}

func (p *Provider) cachedDataAvailability(
	ctx context.Context,
	connHandleId uuid.UUID,
	params data_platform.QueryParams,
	resource resourceType,
) (data_platform.ResourceAvailabilityLog, error) {
	ctx, span := tracing.Start(ctx, "samsara.cachedDataAvailability")
	defer span.End()

	interval := params.Interval
	if params.Interval.End.After(p.clk.Now()) {
		return nil, errors.Newf(
			"invalid interval=%v, End cannot be after current time",
			interval,
		)
	}
	if params.IdentifierType != data_platform.IdentifierTypeProviderVehicleId {
		return nil, errors.Newf("identifier type must be a vehicleId identifier")
	}
	requestIdentifiers, err := identify(
		ctx,
		p.dbWrapper,
		connHandleId,
		params.IdentifierType,
		params.IdentifierValue,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to identify %s=%s", params.IdentifierType, params.IdentifierValue)
	}
	m := utils.NewWeekResourceAvailabilityMap(
		interval, data_platform.Unavailable,
	)
	// We must always iterate in a deterministic order,
	// as if there are multiple identifiers, their ETag values must be combined
	// in a deterministic order always.
	slices.SortFunc(requestIdentifiers, func(a, b *RequestIdentifier) int {
		// both vehicleId guaranteed non-nil because we enforce above that identifier type is a vehicle-level.
		return cmp.Compare(*a.vehicleId, *b.vehicleId)
	})
	for idx := range requestIdentifiers {
		rid := requestIdentifiers[idx]
		avb, err := vehicleResourceLogAvailability(
			ctx,
			p.store,
			rid.handleId,
			*rid.vehicleId,
			resource,
			interval,
		)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to check availability for vehicleId=%d interval=%v",
				*rid.vehicleId,
				interval,
			)
		}
		m = m.CombineWithInfoMap(avb)
	}
	return m.Finalize(), nil
}
