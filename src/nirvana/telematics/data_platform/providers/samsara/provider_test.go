package samsara_test

import (
	"context"
	"sort"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/telematics/integrations/samsara_lib"
	"nirvanatech.com/nirvana/telematics/terrors"

	"github.com/cockroachdb/errors"

	"nirvanatech.com/nirvana/telematics/integrations/common"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/samsara_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/samsara"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
	"nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	samsara_test_utils "nirvanatech.com/nirvana/telematics/integrations/samsara_lib/test_utils"
)

type env struct {
	fx.In

	*samsara_fixture.Samsara
	Provider *samsara.Provider
	Clk      clock.Clock
}

var year2021 = time_utils.Interval{
	Start: time.Date(2021, time.January, 1, 0, 0, 0, 0, time.UTC),
	End:   time.Date(2021, time.December, 31, 23, 59, 59, 0, time.UTC),
}

var year2021Feb = time_utils.Interval{
	Start: time.Date(2021, time.February, 1, 0, 0, 0, 0, time.UTC),
	End:   time.Date(2021, time.February, 28, 23, 59, 59, 0, time.UTC),
}

func registerMockCarrierMetadata(mock *samsara_test_utils.MockServerInterface) {
	// Register a mock carrier
	mock.EXPECT().GetMe(gomock.Any()).DoAndReturn(
		samsara_test_utils.GetMeGolden(123456),
	).Times(1)
	// We will verify that API is called 5 times for getting 50 vehicles @ 10 / req
	mock.EXPECT().GetFleetVehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetVehiclesGolden(50),
	).Times(5)
	// We will verify that API is called 5 times for getting 50 drivers @ 10 / req
	mock.EXPECT().GetFleetDrivers(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetDriversGolden(50),
	).Times(5)
}

// Test_RefreshMetadata verifies that all metadata is being correctly pulled
func Test_RefreshMetadata(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Verify that org info is correctly persisted
	orgInfo, err := env.DataWrapper.OrganizationByHandleId(ctx, handleId)
	require.NoError(t, err)
	require.Equal(t, "123456", orgInfo.DotNumber)
	require.Equal(t, "mock", orgInfo.Name)

	// Get a list of vehicles from provider now (this should not make new API calls)
	vehicles, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, vehicles, 50)

	// Get a list of drivers from provider now (this should not make new API calls)
	drivers, err := env.Provider.Drivers(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, drivers, 50)
	// verify that some useful fields are set
	for _, d := range drivers {
		require.NotZero(t, d.License)
		require.NotZero(t, d.LicenseState)
		require.NotZero(t, d.FullName)
		require.NotZero(t, d.AliasName)
		require.NotZero(t, d.CreatedAt)
		require.NotZero(t, d.TspCreatedAt)
		require.NotZero(t, d.UpdatedAt)
		require.NotZero(t, d.TspUpdatedAt)
		require.NotEmpty(t, d.VehicleGroupIds)
		require.True(t, d.TspActivationStatus.Bool)
	}

	// Now suppose 7 new vehicles & drivers get registered to provider
	// We will verify that API is called 6 times for getting 57 vehicles @ 10 / req
	env.MockServer.EXPECT().GetFleetVehicles(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetVehiclesGolden(57),
	).Times(6)
	// We will verify that API is called 6 times for getting 57 vehicles @ 10 / req
	env.MockServer.EXPECT().GetFleetDrivers(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetDriversGolden(57),
	).Times(6)

	// Forcefully refreshing metadata should result in newer api calls
	_, err = env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Verify that vehicles have become 57 in our database
	vehicles, err = env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, vehicles, 57)

	// Verify that drivers have become 57 in our database
	drivers, err = env.Provider.Drivers(ctx, handleId)
	require.NoError(t, err)
	require.Len(t, drivers, 57)
}

// Test_VehicleStats tests that we can correctly pull vehicle stats from samsara
func Test_VehicleStats(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleStatsTestCase(t)
	})
}

// Test_VehicleStatsConcurrent verifies that the samsara vehicle stats endpoint is safe to call concurrently,
// in case of multiple concurrent requests, requests should be serialised, and no redundant api calls should be observed.
func Test_VehicleStatsConcurrent(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Mock will return stats for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year2021, 2*7*24*time.Hour, nil),
	).Times(4 * 7) // Mock endpoint is paginated at per-day level so one API call per day will be made

	// Concurrently fetch vehicle locations from 10 goroutines.
	// The expectation is that requests will be serialized in a manner such that no duplicate API request is made.
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			resStream, err := env.Provider.GPSLog.For(
				ctx, handleId,
				data_platform.QueryParams{
					IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
					IdentifierValue: "1",
					Interval:        year2021Feb,
				},
			)
			require.NoError(t, err)
			gpsLogs, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Len(t, gpsLogs, 293*28)
			wg.Done()
		}()
	}
	wg.Wait()
}

// Test_SafetySettings verifies that we are able to get safety settings for a fleet, and persist it properly.
func Test_SafetySettings(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	// Setup mock to return safety settings for an org
	env.MockServer.EXPECT().GetMe(gomock.Any()).DoAndReturn(
		samsara_test_utils.GetMeGolden(123456),
	).Times(1)
	env.MockServer.EXPECT().GetFleetSettingsSafety(gomock.Any()).DoAndReturn(
		samsara_test_utils.GetSafetySettingsGolden(),
	).Times(1)

	// Get identifier (also upserts to org row)
	identifier, err := env.Provider.MetadataProvider.IdentifyByHandle(ctx, handleId)
	require.NoError(t, err)

	settings, err := env.Provider.MetadataProvider.SafetySettings(ctx, identifier)
	require.NoError(t, err)
	require.Len(t, settings, 1)

	// Now repeat query, this time the response should come from cache
	settings2, err := env.Provider.MetadataProvider.SafetySettings(ctx, identifier)
	require.NoError(t, err)
	require.Equal(t, settings, settings2)
}

// Test_SafetyEvents verifies that we can correctly pull harsh braking and accl events from Samsara
func Test_SafetyEvents(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runSafetyEventsTestCase(t)
	})
}

// TestVehicleLocations_EdgeCases asserts the provider correctly responds to request of less than one week.
func TestVehicleLocations_EdgeCases(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocationsEdgeCasesTestCase(t)
	})
}

func TestVehicleLocations_504AutoRetry(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocations504AutoRetryTestCase(t)
	})
}

// Test_CachedDataAvailabilityForVehicleId asserts provider correctly responds with data availability
// queries once data request has been fulfilled.
func Test_CachedDataAvailabilityForVehicleId(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Program mock to return stats and events for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year2021, 2*7*24*time.Hour, nil),
	).AnyTimes()
	env.MockServer.EXPECT().GetFleetSafetyEvents(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetSafetyEventsGolden([]string{"1"}, year2021),
	).AnyTimes()

	year2019Start := time.Date(2019, 1, 1, 0, 0, 0, 0, time.UTC)
	year2020Start := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	year2021Start := time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC)
	year2022Start := time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC)
	queryParams := data_platform.QueryParams{
		IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
		IdentifierValue: "1",
		Interval: time_utils.Interval{
			Start: year2020Start,
			End:   year2022Start.Add(-time.Second),
		},
	}

	// request data from 1 Jan 2020 - 31 Dec 2021 to warm up caches
	{
		// gps log
		rs, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// harsh acceleration log
		rs, err := env.Provider.HarshAccelerationLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// harsh braking log
		rs, err := env.Provider.HarshBrakingLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}

	// verify availability for "warmed up" stats
	for _, endpoint := range []data_platform.CachedDataAvailabilityFn{
		env.Provider.GPSLog.CachedDataAvailability,
		env.Provider.HarshAccelerationLog.CachedDataAvailability,
		env.Provider.HarshBrakingLog.CachedDataAvailability,
	} {
		// request availability log from 2019 - 2021 (Dec)
		avlLog, err := endpoint(
			ctx, handleId, data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: "1",
				Interval: time_utils.Interval{
					Start: year2019Start,
					End:   year2022Start.Add(-time.Second),
				},
			},
		)
		require.NoError(t, err)

		expectedWeeksWithNoCache := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2019Start,
					End:   year2020Start,
				},
			).GetISOWeekListRightBounded(year2020Start),
		)
		expectedWeeksWithZeroData := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2020Start,
					End:   year2021Start,
				},
			).GetISOWeekListRightBounded(year2021Start),
		)
		expectedWeeksWithActualData := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2021Start,
					End:   year2022Start,
				},
			).GetISOWeekListRightBounded(year2022Start),
		)

		require.Len(t, avlLog, expectedWeeksWithZeroData+expectedWeeksWithNoCache+expectedWeeksWithActualData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.DataAvailable), expectedWeeksWithActualData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.NoReadings), expectedWeeksWithZeroData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.Unavailable), expectedWeeksWithNoCache)

		// Check that ETag is populated
		for _, avl := range avlLog {
			if avl.State != data_platform.Unavailable {
				require.NotEmpty(t, avl.ETag)
			} else {
				require.Empty(t, avl.ETag)
			}
		}
	}
}

func TestIFTAReports(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	numVehiclesInMock := 50
	var mockedResponse []samsara_test_utils.IFTAData
	for i := 1; i <= numVehiclesInMock; i++ {
		mockedResponse = append(mockedResponse, samsara_test_utils.IFTAData{
			Id:       strconv.Itoa(i),
			Distance: 23,
		})
	}

	testCases := []struct {
		name              string
		reportsPerVehicle int
		callTimes         int
		params            data_platform.QueryParams
		wantErr           bool
	}{
		{
			name:              "50 vehicles reports, full 1 month period",
			reportsPerVehicle: 1,
			callTimes:         1,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeUndefined,
				IdentifierValue: "",
			},
			wantErr: false,
		},
		{
			name:              "error raise for partial month period",
			reportsPerVehicle: 1,
			callTimes:         0,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 15).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeUndefined,
				IdentifierValue: "",
			},
			wantErr: true,
		},
		{
			name:              "50 vehicles reports, full 3 months period",
			reportsPerVehicle: 3,
			callTimes:         3,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 3, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeUndefined,
				IdentifierValue: "",
			},
			wantErr: false,
		},
		{
			name:              "reports for one connection handle, full 1 month period",
			reportsPerVehicle: 1,
			callTimes:         1,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType: data_platform.IdentifierTypeUndefined,
			},
			wantErr: false,
		},
		{
			name:              "error raised for VIN",
			reportsPerVehicle: 0,
			callTimes:         0,
			params: data_platform.QueryParams{
				Interval: time_utils.Interval{
					Start: time_utils.NewDate(2021, 1, 1).ToTime(),
					End:   time_utils.NewDate(2021, 1, 31).ToTime(),
				},
				IdentifierType:  data_platform.IdentifierTypeVIN,
				IdentifierValue: "nonexistent",
			},
			wantErr: true,
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			env.MockServer.EXPECT().GetIftaVehicleReports(gomock.Any(), gomock.Any()).DoAndReturn(
				samsara_test_utils.GetV1IFTAReportGolden(mockedResponse),
			).Times(tt.callTimes)

			resStream, err := env.Provider.IFTAReport.For(ctx, handleId, tt.params)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			iftaSummaries, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Len(t, iftaSummaries, tt.reportsPerVehicle*numVehiclesInMock)
		})
	}
}

func TestVehicleDriverAssignments(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// after pagination we expect mock to respond to a total of 1 API per day in the request interval
	env.MockServer.EXPECT().
		GetFleetDriverVehicleAssignments(gomock.Any(), gomock.Any()).
		DoAndReturn(samsara_test_utils.GetVehicleDriverAssignmentsGolden(
			[]string{"1", "2", "3"}, []string{"1", "2", "3"}, year2021,
		)).Times(365)

	vdaStream, err := env.Provider.VehicleDriverAssignmentLog.For(ctx, handleId, data_platform.QueryParams{
		IdentifierType: data_platform.IdentifierTypeProviderConnection,
		Interval:       year2021,
	})
	require.NoError(t, err)
	vda, err := stream.GetAll(vdaStream)
	require.NoError(t, err)

	// mock is programmed to return a VDA entry per vehicle per day
	require.Len(t, vda, 3*365)
	countByVid := slice_utils.GroupByAndReduce(
		vda,
		func(item *samsara_lib.VehicleDriverAssignmentModel) string {
			return item.Vehicle.Id
		},
		slice_utils.UseCountReducer[*samsara_lib.VehicleDriverAssignmentModel],
	)
	require.Len(t, countByVid, 3)
	for _, count := range countByVid {
		require.Equal(t, 365, count)
	}
}

// TestIntegrationScheduledDowntime tests that the underlying integration
// appropriately handles scheduled downtimes by rejecting all API requests and
// returning a RetryableError with during the downtime. Along the way, we also
// verify that downtimes only affect "live" data, and that cached data is still
// accessible.
//
// NOTE: We only encounter the error when trying to read from the stream, since
// the provider endpoint always returns a lazy result stream if the requested
// params are valid.
func TestIntegrationScheduledDowntime(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()

	// We will make requests to the GPSLog endpoint for two weeks in 2021. This
	// means that the provider will make 14 API calls to the Samsara API (mock
	// endpoint is paginated at per-day level, so one API call per day will be made).
	// To simulate the case where the provider serves data from cache if available,
	// we pick Week Five as the one for which we pre-cache data before
	// moving the clock to the scheduled downtime.
	var (
		handleId         = test_utils.GoldenHandleId(telematics.TSPSamsara)
		year2021WeekFive = time_utils.ISOWeek{Year: 2021, WeekNumber: 5}
		year2021WeekSix  = time_utils.ISOWeek{Year: 2021, WeekNumber: 6}
		fullInterval     = time_utils.Interval{
			Start: year2021WeekFive.StartTime(time.UTC),
			End:   year2021WeekSix.EndTime(time.UTC),
		}
		scheduledDownTime = common.ScheduledDowntime{
			Interval: time_utils.Interval{
				Start: time.Date(2022, 4, 26, 3, 0, 0, 0, time.UTC),
				End:   time.Date(2022, 4, 26, 4, 0, 0, 0, time.UTC),
			},
			IssueLink: errors.IssueLink{
				IssueURL: "https://www.example.com/incidents/j3gd1bg3nrvk",
			},
		}
	)

	// Configure the scheduled downtime
	env.Samsara.ConfigureScheduledDowntimes(scheduledDownTime)

	// Register a mock carrier, sync entities, and set up the mock server to
	// expect 14 API calls for the two weeks of data.
	registerMockCarrierMetadata(env.MockServer)
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(
		gomock.Any(), gomock.Any(),
	).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, fullInterval, 2*7*24*time.Hour, nil),
	).Times(14)

	// Make a request for the pre-cached week, a minute before the scheduled
	// downtime starts. This request should succeed.
	env.Clk.(*clock.Mock).Set(scheduledDownTime.Interval.Start.Add(-time.Minute))
	t.Log("Making request for the pre-cached week at ", env.Clk.Now())
	{
		rs, err := env.Provider.GPSLog.For(
			ctx, handleId,
			data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: "1",
				Interval:        year2021WeekFive.GetInterval(time.UTC),
			},
		)
		require.NoError(t, err)
		items, err := stream.GetAll(rs)
		require.NoError(t, err)
		require.Len(t, items, 293*7)
	}

	// Move the clock to a minute into the scheduled downtime and make a request
	// for the complete duration. The returned stream should error out exactly
	// midway (since the first week is pre-cached).
	env.Clk.(*clock.Mock).Set(scheduledDownTime.Interval.Start.Add(time.Minute))
	t.Log("Making request for the full interval at ", env.Clk.Now())
	{
		rs, err := env.Provider.GPSLog.For(
			ctx, handleId,
			data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: "1",
				Interval:        fullInterval,
			},
		)
		require.NoError(t, err)
		for i := 0; i < 293*7; i++ {
			// we should be able to read the first week of data
			_, err := rs.One()
			require.NoError(t, err)
		}
		// but fail on the next read
		_, err = rs.One()
		require.Error(t, err)
		retErr := &terrors.RetryableError{}
		require.ErrorAs(t, err, &retErr)
		require.Equal(t, 59*time.Minute /* a minute into the downtime */, *retErr.RetryAfter)
	}

	// Move the clock to a minute after the scheduled downtime ends.
	// All requests should now succeed.
	env.Clk.(*clock.Mock).Set(scheduledDownTime.Interval.End.Add(time.Minute))
	t.Log("Making request for the full interval at ", env.Clk.Now())
	{
		rs, err := env.Provider.GPSLog.For(
			ctx, handleId,
			data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: "1",
				Interval:        fullInterval,
			},
		)
		require.NoError(t, err)
		items, err := stream.GetAll(rs)
		require.NoError(t, err)
		require.Len(t, items, 293*14 /* Two weeks */)
	}
}

func updateQueryParamsWithVehicleIdentifier(
	qp data_platform.QueryParams, vehicleId int,
) data_platform.QueryParams {
	qp.IdentifierType = data_platform.IdentifierTypeProviderVehicleId
	qp.IdentifierValue = strconv.Itoa(vehicleId)
	return qp
}

func runVehicleStatsTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Mock will return stats for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden(
			[]string{"1"},
			// We add +- 1 month to both sides of year2021 so that we can also verify if time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
			2*7*24*time.Hour,
			nil,
		),
	).Times(53 * 7) // Mock endpoint is paginated at per-day level so one API call per day will be made

	resStream, err := env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)

	gpsLogs, err := stream.GetAll(resStream)
	require.NoError(t, err)
	// Mock is programmed to return 293 GPS points per day
	require.Len(t, gpsLogs, 293*365)

	// Refetch GPS logs. Verify that no extra API call is made (caching works)
	resStream, err = env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)

	gpsLogs2, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Equal(t, gpsLogs, gpsLogs2)

	// Verify that a repeat call with ignore provider cache set actually ignores provider cache
	env.Clk.(*clock.Mock).Add(time.Hour)

	// configure mock with expectations
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden(
			[]string{"1"},
			// We add +- 1 month to both sides of year2021 so that we can also verify if time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
			2*7*24*time.Hour,
			nil,
		),
	).Times(53 * 7) // Mock endpoint is paginated at per-day level so one API call per day will be made

	resStream, err = env.Provider.GPSLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval:            year2021,
				IgnoreProviderCache: null.TimeFrom(env.Clk.Now()),
			}, 1,
		),
	)
	require.NoError(t, err)
	gpsLogs3, err := stream.GetAll(resStream)
	require.NoError(t, err)
	// Mock is programmed to return 293 GPS points per day
	require.Len(t, gpsLogs3, 293*365)

	// We should also get Odometer logs for free
	odometerStream, err := env.Provider.OdometerLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	odo, err := stream.GetAll(odometerStream)
	require.NoError(t, err)
	// Odometer logs are one per GPS entry

	// fetching engineStaes will require new API calls
	// Mock will return stats for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden(
			[]string{"1"},
			// We add +- 1 month to both sides of year2021 so that we can also verify if time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
			2*7*24*time.Hour,
			nil,
		),
	).Times(53 * 7) // Mock endpoint is paginated at per-day level so one API call per day will be made
	require.Len(t, odo, len(gpsLogs2))

	engineStream, err := env.Provider.EngineStateLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	engStates, err := stream.GetAll(engineStream)
	require.NoError(t, err)
	// Mock is programmed to return 12 states per day
	require.Len(t, engStates, 12*365)

	// fetching accelerometer will require new API calls
	// Mock will return stats for one vehicle for year 2021
	accelerometerUnavailableDate := year2021.Start.AddDate(0, 0, 12)
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden(
			[]string{"1"},
			// We add +- 1 month to both sides of year2021 so that we can also verify if time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
			2*7*24*time.Hour,
			&accelerometerUnavailableDate,
		),
	).Times(52*7 + 1) // Mock endpoint is paginated at per-day level so one API call per day will be made,
	// for one week only one call will be made which will result internal server error

	accelerometerStream, err := env.Provider.AccelerometerLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	accelerometerLog, err := stream.GetAll(accelerometerStream)
	require.NoError(t, err)

	// Mock is programmed to return 19 states per day
	require.Len(t, accelerometerLog, 19*358) // 1 week data is not present because of expected internal server error
}

// runSafetyEventsTestCase verifies that we can correctly pull harsh braking and accl events from Samsara
func runSafetyEventsTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Mock will return events for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetSafetyEvents(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetSafetyEventsGolden(
			[]string{"1"},
			// We add +- 1 month to both sides of year2021 so that we can also verify if time filtering logic is correct
			time_utils.Interval{
				Start: year2021.Start.AddDate(0, -1, 0),
				End:   year2021.End.AddDate(0, 1, 0),
			},
		),
	).Times(53 * 7) // Mock endpoint is paginated at per-day level so one API call per day should be made

	// First get harsh braking events
	resStream, err := env.Provider.HarshBrakingLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)

	brakingEvents, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, brakingEvents, 365) // There should be one event per day in response

	// Next get harsh accl events (no new api calls)
	harshAccelStream, err := env.Provider.HarshAccelerationLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
			}, 1,
		),
	)
	require.NoError(t, err)
	acclEvents, err := stream.GetAll(harshAccelStream)
	require.NoError(t, err)
	require.Len(t, acclEvents, 365) // There should be one event per day in response

	// We now once again get the responses to verify that caching behaviour is correct
	// No new api calls should be made
	resStream, err = env.Provider.HarshBrakingLog.For(
		ctx, handleId,
		updateQueryParamsWithVehicleIdentifier(
			data_platform.QueryParams{
				Interval: year2021,
				// NoCache disabled
			}, 1,
		),
	)
	require.NoError(t, err)

	brakingEvents2, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Equal(t, brakingEvents, brakingEvents2) // There should be one event per day in response
}

// TestVehicleLocations_EdgeCases asserts the provider correctly responds to request of less than one week.
func runVehicleLocationsEdgeCasesTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	year202021 := time_utils.Interval{
		Start: time.Date(2020, time.January, 1, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2022, time.January, 1, 0, 0, 0, 0, time.UTC),
	}

	// configure mock to return vehicle locations for year 2021
	// Mock will return stats for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year202021, 2*7*24*time.Hour, nil),
	).Times(1 * 7) // Mock endpoint is paginated at per-day level so one API call per day will be made

	// Make a request such that both start and end of interval is within same ISO week.
	queryParams := updateQueryParamsWithVehicleIdentifier(
		data_platform.QueryParams{
			// 1, 2 Jan (this 2 penultimate days of 2020-W53)
			Interval: time_utils.Interval{
				Start: time.Date(2021, time.January, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2021, time.January, 2, 23, 59, 59, 1e9-1, time.UTC),
			},
		}, 1,
	)
	resStream, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs, 293*2) // 293 GPS points each day

	// Retry the request, verify that cache hit occurs.
	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs2, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Equal(t, gpsLogs, gpsLogs2)

	// Unwind the clock so that it reads 3rd Jan, 2021 (12 noon)
	env.Clk.(*clock.Mock).Set(time.Date(2021, time.January, 3, 12, 0, 0, 0, time.UTC))
	// Retry the request, there should be no data because clk.Now() is within ISO week.
	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs3, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Empty(t, gpsLogs3)

	// Retry the request with no-cache set.
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year202021, 2*7*24*time.Hour, nil),
	).Times(2) // Mock endpoint is paginated at per-day level so one API call per day will be made

	queryParams.OverrideCachingBehaviourOnInterval = null.BoolFrom(true)
	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs4, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs4, 2*293)

	// What if queryParams.Interval.Start was in a different ISO week?
	// We expect 2 provider API calls, one for last ISO week, and another for this one.
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year202021, 2*7*24*time.Hour, nil),
	).Times(1*7 + 4) // Mock endpoint is paginated at per-day level so one API call per day will be made
	// 9th Jan lies in 2021-W01
	queryParams.Interval.Start = time.Date(2021, time.January, 9, 0, 0, 0, 0, time.UTC)
	// 14th Jan lies in 2021-W02
	queryParams.Interval.End = time.Date(2021, time.January, 14, 0, 0, 0, 0, time.UTC)
	// Reset clock to be 15th Jan (still in 2021-W02)
	env.Clk.(*clock.Mock).Set(time.Date(2021, time.January, 15, 0, 0, 0, 0, time.UTC))

	resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs5, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs5, 5*293)
}

func runVehicleLocations504AutoRetryTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	year202021 := time_utils.Interval{
		Start: time.Date(2020, time.January, 1, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2022, time.January, 1, 0, 0, 0, 0, time.UTC),
	}

	// configure mock to return vehicle locations for year 2021
	// Mock will return stats for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year202021, 24*time.Hour, nil),
	).Times(1 + 2 + 4 + 6) // recursive call in case interval is more than a day

	// Make a request such that both start and end of interval is within same ISO week.
	queryParams := updateQueryParamsWithVehicleIdentifier(
		data_platform.QueryParams{
			// 1, 2 Jan (this 2 penultimate days of 2020-W53)
			Interval: time_utils.Interval{
				Start: time.Date(2021, time.January, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2021, time.January, 2, 23, 59, 59, 1e9-1, time.UTC),
			},
		}, 1,
	)
	resStream, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	gpsLogs, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, gpsLogs, 293*2) // 293 GPS points each day

	// checking if all points are in ascending order
	if !sort.SliceIsSorted(gpsLogs, func(i, j int) bool {
		t1, err1 := gpsLogs[i].Timestamp()
		t2, err2 := gpsLogs[j].Timestamp()
		require.NoError(t, err1)
		require.NoError(t, err2)
		return t1.Before(t2)
	}) {
		t.Error("GPSLog is not in ascending order")
	}

	accelerometerUnavailableDate := time.Date(2021, time.January, 1, 0, 122, 0, 0, time.UTC)
	// configure mock to return vehicle locations for year 2021
	// Mock will return stats for one vehicle for year 2021
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden([]string{"1"}, year202021, 24*time.Hour, &accelerometerUnavailableDate),
	).Times(1 + 2 + 4 + 6) // recursive call in case interval is more than a day

	// Make a request such that both start and end of interval is within same ISO week.
	queryParams = updateQueryParamsWithVehicleIdentifier(
		data_platform.QueryParams{
			// 1, 2 Jan (this 2 penultimate days of 2020-W53)
			Interval: time_utils.Interval{
				Start: time.Date(2021, time.January, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2021, time.January, 2, 23, 59, 59, 1e9-1, time.UTC),
			},
		}, 1,
	)
	accelerometerStream, err := env.Provider.AccelerometerLog.For(ctx, handleId, queryParams)
	require.NoError(t, err)
	accelerometerLog, err := stream.GetAll(accelerometerStream)
	require.NoError(t, err)
	require.Len(t, accelerometerLog, 19) // 19 Accelerometer points each day

	// checking if all points are in ascending order
	if !sort.SliceIsSorted(accelerometerLog, func(i, j int) bool {
		t1, err1 := accelerometerLog[i].Timestamp()
		t2, err2 := accelerometerLog[j].Timestamp()
		require.NoError(t, err1)
		require.NoError(t, err2)
		return t1.Before(t2)
	}) {
		t.Error("accelerometerLog is not in ascending order")
	}
}

// TestReadOnlyQueryParam is the main test function that verifies the behavior of different endpoints (GPS, Engine State, Odometer, Safety)
// when querying data with and with and without the ReadOnly parameter. This is done by testing two time intervals for each normalized stat,
// where in one interval we pull the normalized stats in cache first and then call the endpoint with read only flag true, while in second
// interval call is made with read only true but data is not in the cache.
func TestReadOnlyQueryParam(t *testing.T) {
	// Initialize environment and ensure proper setup and teardown.
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPSamsara)

	registerMockCarrierMetadata(env.MockServer)
	// Define the time interval for the first week of February 2021.
	year2021FebWeek1 := time_utils.Interval{
		Start: time.Date(2021, time.February, 1, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2021, time.February, 7, 23, 59, 59, 0, time.UTC),
	}
	// Define the time interval for the second week of February 2021.
	year2021FebWeek2 := time_utils.Interval{
		Start: time.Date(2021, time.February, 8, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2021, time.February, 14, 23, 59, 59, 0, time.UTC),
	}

	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Mock the GetFleetVehiclesStatsHistory API for GPS, Odometer, and EngineState logs.
	env.MockServer.EXPECT().GetFleetVehiclesStatsHistory(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetStatsGolden(
			[]string{"1"},
			year2021FebWeek1,
			1*7*24*time.Hour,
			nil,
		),
	).Times(7 * 2) // this should be called for only feb first week, to pull data in cache. So for 1 day one call,
	// therefore for 7 days 7 calls. Repeated twice for (GPS, Odo) & (Engine States)

	// Mock the GetFleetSafetyEvents API for the SafetyEvent log
	env.MockServer.EXPECT().GetFleetSafetyEvents(gomock.Any(), gomock.Any()).DoAndReturn(
		samsara_test_utils.GetSafetyEventsGolden(
			[]string{"1"},
			year2021FebWeek1,
		),
	).Times(7) // this should be called for only feb first week, to pull data in cache. So for 1 day one call, therefore for 7 days 7 calls.

	// Run subtests for each endpoint/
	t.Run("GPSlog", func(t *testing.T) {
		testReadOnlyForNormalizedEndpoint(
			ctx,
			t,
			year2021FebWeek1,
			year2021FebWeek2,
			handleId,
			env.Provider.GPSLog,
			293, // number of points in a day
			7,   // 1(number of iso weeks) * 7
		)
	})
	t.Run("EngineStateLog", func(t *testing.T) {
		testReadOnlyForNormalizedEndpoint(
			ctx,
			t,
			year2021FebWeek1,
			year2021FebWeek2,
			handleId,
			env.Provider.EngineStateLog,
			12, // number of points in a day
			7,  // 1(number of iso weeks) * 7
		)
	})
	t.Run("OdometerLog", func(t *testing.T) {
		testReadOnlyForNormalizedEndpoint(
			ctx,
			t,
			year2021FebWeek1,
			year2021FebWeek2,
			handleId,
			env.Provider.OdometerLog,
			293, // number of points in a day
			7,   // 1(number of iso weeks) * 7
		)
	})
	t.Run("SafetyEventLog", func(t *testing.T) {
		testReadOnlyForNormalizedEndpoint(
			ctx,
			t,
			year2021FebWeek1,
			year2021FebWeek2,
			handleId,
			env.Provider.SafetyEventLog,
			2, // number of points in a day
			7, // 1(number of iso weeks) * 7
		)
	})
}

// testReadOnlyForNormalizedEndpoint is a helper function that tests the ReadOnly parameter behavior for a given endpoint.
// It first pulls the data for interval1 and calls interval1 with ReadOnly=true while for interval2 it directly calls with ReadOnly true
// where it expects data_platform.ErrNotAvailableForReadOnly error.
func testReadOnlyForNormalizedEndpoint[K any](
	ctx context.Context,
	t *testing.T,
	interval1 time_utils.Interval,
	interval2 time_utils.Interval,
	handleId uuid.UUID,
	endpoint data_platform.Endpoint[K],
	pointsPerDay int,
	numDays int,
) {
	params := updateQueryParamsWithVehicleIdentifier(data_platform.QueryParams{Interval: interval1}, 1)
	// Test for ReadOnly = false
	resStream, err := endpoint.For(ctx, handleId, params)
	require.NoError(t, err)
	normalizedStats, err := stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, normalizedStats, pointsPerDay*numDays) // Validate that the number of returned points is correct.

	// Test for ReadOnly = true, same interval
	params.ReadOnly = null.BoolFrom(true)
	resStream, err = endpoint.For(ctx, handleId, params)
	require.NoError(t, err)
	normalizedStats, err = stream.GetAll(resStream)
	require.NoError(t, err)
	require.Len(t, normalizedStats, pointsPerDay*numDays) // number of points should remain same as above.

	// Test ReadOnly = true with the second interval, expecting an error because the data is not available in cache.
	params = updateQueryParamsWithVehicleIdentifier(data_platform.QueryParams{Interval: interval2}, 1)
	params.ReadOnly = null.BoolFrom(true)
	resStream, err = endpoint.For(ctx, handleId, params)
	require.NoError(t, err)
	normalizedStats, err = stream.GetAll(resStream)
	require.ErrorIs(t, err, data_platform.ErrNotAvailableForReadOnly)
}
