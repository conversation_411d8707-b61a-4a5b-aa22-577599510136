package smartdrive

import (
	"context"

	smartdriveConn "nirvanatech.com/nirvana/telematics/connections/smartdrive"

	"github.com/benb<PERSON><PERSON>son/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"

	"nirvanatech.com/nirvana/common-go/time_utils"
	smartdrive_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/smartdrive"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/integrations/smartdrive"
)

type Provider struct {
	clk clock.Clock

	mp *metadataProvider
	sp *statsProvider

	Endpoints
}

var _ data_platform.Provider = (*Provider)(nil)

func NewProvider(
	clk clock.Clock,
	integration smartdrive.Integration,
	dataWrapper *smartdrive_wrapper.DataWrapper,
	smartdrive *smartdriveConn.SmartDriveConnManager,
) *Provider {
	return (&Provider{
		clk: clk,
		mp: &metadataProvider{
			dw:          dataWrapper,
			integration: integration,
			smartdrive:  smartdrive,
		},
		sp: &statsProvider{
			integration: integration,
		},
	}).buildEndpoints()
}

func (p *Provider) String() string {
	return telematics.DataProviderSmartDrive.String()
}

func (p *Provider) Vehicles(ctx context.Context, connHandleId uuid.UUID) ([]*data_platform.Vehicle, error) {
	return p.mp.vehicles(ctx, connHandleId)
}

func (p *Provider) VehicleGroups(ctx context.Context, connHandleId uuid.UUID) ([]*data_platform.VehicleGroup, error) {
	return nil, data_platform.ErrNotImplemented
}

func (p *Provider) Drivers(ctx context.Context, connHandleId uuid.UUID) ([]data_platform.Driver, error) {
	return nil, data_platform.ErrNotImplemented
}

func (p *Provider) SyncEntities(ctx context.Context, connHandleId uuid.UUID) (
	*data_platform.Entities, error,
) {
	err := p.mp.refreshVehicles(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to refresh vehicles")
	}
	vehicles, err := p.Vehicles(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicles")
	}
	return &data_platform.Entities{
		Vehicles: vehicles,
	}, nil
}

func (p *Provider) SyncData(ctx context.Context, connHandleId uuid.UUID, interval time_utils.Interval) error {
	return nil
}

func (p *Provider) Ping(ctx context.Context, handleId uuid.UUID) error {
	vehicles, err := p.mp.fetchVehiclesFromApi(ctx, handleId)
	if err != nil {
		return errors.Wrap(err, "ping failed because unable to fetch vehicle list")
	}
	if len(vehicles) == 0 {
		return errors.Newf("ping failed because no vehicles found")
	}
	return nil
}
