package smartdrive_test

import (
	"context"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/smartdrive_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/smartdrive"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
	"nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	smartdrive_api "nirvanatech.com/nirvana/telematics/integrations/smartdrive"
	smartdrive_test_utils "nirvanatech.com/nirvana/telematics/integrations/smartdrive/test_utils"
)

func TestProvider_Vehicles(t *testing.T) {
	var env struct {
		fx.In

		P   *smartdrive.Provider
		Clk clock.Clock
		*smartdrive_fixture.Fixture
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()

	// First mock the integration to return, say, 10 vehicles
	env.Mock.EXPECT().
		VehiclesBySite(gomock.Any(), env.GoldenSiteId).
		DoAndReturn(smartdrive_test_utils.GoldenVehiclesBySite(10)).
		Times(1)

	// We expect our provider to populate our db with 10 vehicles from the integration on
	// the first call to Vehicles API
	vehicles, err := env.P.Vehicles(ctx, env.GoldenHandleId)
	require.NoError(t, err)
	require.Len(t, vehicles, 10)
	for _, v := range vehicles {
		require.Len(t, v.VIN, 17)
		require.NotEmpty(t, v.TspId)
		require.Equal(t, &data_platform.Vehicle{
			HandleId:  test_utils.GoldenHandleId(telematics.TSPSmartDrive),
			VIN:       data_platform.VIN(smartdrive.FakeVINForTest(v.TspId)),
			IsVINFake: true,
			TspId:     v.TspId,
			IsTracked: true,
			CreatedAt: env.Clk.Now(),
			UpdatedAt: env.Clk.Now(),
		}, v)
	}

	// After the first call, subsequent calls to vehicles API should not cause a refresh
	newVehicles, err := env.P.Vehicles(ctx, env.GoldenHandleId)
	require.NoError(t, err)
	require.ElementsMatch(t, vehicles, newVehicles)

	// Now, we re-program the mock to return 5 more vehicles
	env.Mock.EXPECT().
		VehiclesBySite(gomock.Any(), env.GoldenSiteId).
		DoAndReturn(smartdrive_test_utils.GoldenVehiclesBySite(15)).
		Times(1)

	// Force refresh vehicle list, and then verify that:
	// 1. New vehicle has 15 vehicles
	// 2. All pre-existing 10 vehicles are present in the new vehicle list unchanged
	_, err = env.P.SyncEntities(ctx, env.GoldenHandleId)
	require.NoError(t, err)
	newVehicles, err = env.P.Vehicles(ctx, env.GoldenHandleId)
	require.NoError(t, err)
	require.Len(t, newVehicles, 15)
	for _, v := range vehicles {
		require.Contains(t, newVehicles, v)
	}
}

func TestDataEndpoints(t *testing.T) {
	t.Run("GPSLog", func(t *testing.T) {
		testDataEndpoint(t, dataEndpointTc[smartdrive_api.RTTDataPoint]{
			endpoint: func(p *smartdrive.Provider) data_platform.Endpoint[smartdrive_api.RTTDataPoint] {
				return p.GPSLog
			},
			dataPerDay: 45,
			validator: func(t *testing.T, params data_platform.QueryParams, gpsLog []smartdrive_api.RTTDataPoint) {
				nonZeroSpeedCount := 0
				for _, gps := range gpsLog {
					require.WithinRange(t, gps.Timestamp, params.Interval.Start, params.Interval.End)
					require.NotZero(t, gps.Latitude)
					require.NotZero(t, gps.Longitude)
					if gps.Speed.Int > 0 {
						nonZeroSpeedCount++
					}
				}
				require.NotZero(t, nonZeroSpeedCount)
			},
		})
	})
	t.Run("HarshAccelLog", func(t *testing.T) {
		testDataEndpoint(t, dataEndpointTc[smartdrive_api.SafetyEvent]{
			endpoint: func(p *smartdrive.Provider) data_platform.Endpoint[smartdrive_api.SafetyEvent] {
				return p.HarshAccelLog
			},
			dataPerDay: 5,
			validator: func(t *testing.T, params data_platform.QueryParams, accelLog []smartdrive_api.SafetyEvent) {
				nonZeroGForceCount := 0
				for _, accel := range accelLog {
					require.WithinRange(t, accel.Timestamp, params.Interval.Start, params.Interval.End)
					require.True(t, accel.DurationSeconds.Valid)
					require.NotZero(t, accel.Latitude)
					require.NotZero(t, accel.Longitude)
					if accel.GForce.Valid && accel.GForce.Float64 != 0 {
						nonZeroGForceCount++
					}
				}
				require.NotZero(t, nonZeroGForceCount)
				require.NotEqual(t, len(accelLog), nonZeroGForceCount)
			},
		})
	})
	t.Run("HarshBrakeLog", func(t *testing.T) {
		testDataEndpoint(t, dataEndpointTc[smartdrive_api.SafetyEvent]{
			endpoint: func(p *smartdrive.Provider) data_platform.Endpoint[smartdrive_api.SafetyEvent] {
				return p.HarshBrakingLog
			},
			dataPerDay: 4,
			validator: func(t *testing.T, params data_platform.QueryParams, accelLog []smartdrive_api.SafetyEvent) {
				nonZeroGForceCount := 0
				for _, accel := range accelLog {
					require.WithinRange(t, accel.Timestamp, params.Interval.Start, params.Interval.End)
					require.True(t, accel.DurationSeconds.Valid)
					require.NotZero(t, accel.Latitude)
					require.NotZero(t, accel.Longitude)
					if accel.GForce.Valid && accel.GForce.Float64 != 0 {
						nonZeroGForceCount++
					}
				}
				require.NotZero(t, nonZeroGForceCount)
				require.NotEqual(t, len(accelLog), nonZeroGForceCount)
			},
		})
	})
}

type dataEndpointTc[Raw any] struct {
	endpoint   func(*smartdrive.Provider) data_platform.Endpoint[Raw]
	dataPerDay int
	validator  func(*testing.T, data_platform.QueryParams, []Raw)
}

func testDataEndpoint[Raw any](t *testing.T, tc dataEndpointTc[Raw]) {
	var env struct {
		fx.In

		P   *smartdrive.Provider
		Clk clock.Clock
		*smartdrive_fixture.Fixture
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	env.RegisterGoldenDataset(smartdrive_test_utils.DefaultConfig)

	// Without fetching the vehicle list, endpoints would fail.
	params := data_platform.QueryParams{
		IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
		IdentifierValue: smartdrive_test_utils.GetMockVehicle(env.GoldenSiteId, 0).Id,
		Interval: time_utils.Interval{
			Start: time_utils.ISOWeek{Year: 2022, WeekNumber: 10}.StartTime(time.UTC),
			End:   time_utils.ISOWeek{Year: 2022, WeekNumber: 12}.EndTime(time.UTC).AddDate(0, 0, 2),
		},
	}
	endpoint := tc.endpoint(env.P)
	_, err := endpoint.For(ctx, env.GoldenHandleId, params)
	require.Error(t, err)
	_, err = env.P.SyncEntities(ctx, env.GoldenHandleId)
	require.NoError(t, err)

	// For 3 iso weeks & 2 extra days in total, we expect 3 * 7 * dataPerDay points without OverrideCachingBehaviour set
	rawRs, err := endpoint.For(ctx, env.GoldenHandleId, params)
	require.NoError(t, err)
	rawLog, err := stream.GetAll(rawRs)
	require.NoError(t, err)

	require.Len(t, rawLog, 3*7*tc.dataPerDay)
	tc.validator(t, params, rawLog)

	// With OverrideCachingBehaviour set we expect 2 extra days of data as well
	params.OverrideCachingBehaviourOnInterval = null.BoolFrom(true)
	rawRs, err = endpoint.For(ctx, env.GoldenHandleId, params)
	require.NoError(t, err)
	newLog, err := stream.GetAll(rawRs)
	require.NoError(t, err)
	require.Len(t, newLog, (3*7+2)*tc.dataPerDay)
	require.Equal(t, rawLog, newLog[:len(rawLog)])

	// No data case is handled well
	params.Interval.Start = params.Interval.Start.AddDate(-1, 0, 0)
	params.Interval.End = params.Interval.End.AddDate(-1, 0, 0)
	rawRs, err = endpoint.For(ctx, env.GoldenHandleId, params)
	require.NoError(t, err)
	newLog, err = stream.GetAll(rawRs)
	require.NoError(t, err)
	require.Len(t, newLog, 0)
}
