package terminal

import (
	"cmp"
	"context"
	"encoding/json"
	"slices"

	"nirvanatech.com/nirvana/common-go/tracing"
	terminalConn "nirvanatech.com/nirvana/telematics/connections/terminal"
	terminal_data_api "nirvanatech.com/nirvana/telematics/integrations/terminal/data_api"

	"github.com/benbjohnson/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	db_model "nirvanatech.com/nirvana/db-api/db_models/terminal"

	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/terminal"
	"nirvanatech.com/nirvana/telematics/connections"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/providers/utils"
	dp_utils "nirvanatech.com/nirvana/telematics/data_platform/utils"
	terminal_lib "nirvanatech.com/nirvana/telematics/integrations/terminal"
)

type Provider struct {
	clk                 clock.Clock
	tspConnManager      *connections.TSPConnManager
	terminalConnManager *terminalConn.TerminalConnManager

	syncHandler *syncHandler
	entities    *entitiesProvider
	stats       *statsProvider
	ifta        *iftaProvider

	Endpoints
}

func newProvider(
	clk clock.Clock,
	tspConnManager *connections.TSPConnManager,
	manager *terminalConn.TerminalConnManager,
	dw *terminal.DataWrapper,
	integration *terminal_lib.APIIntegration,
	store *utils.Store,
) *Provider {
	return (&Provider{
		clk:                 clk,
		tspConnManager:      tspConnManager,
		terminalConnManager: manager,
		syncHandler: &syncHandler{
			clk:            clk,
			tspConnManager: tspConnManager,
			dw:             dw,
			integration:    integration,
		},
		entities: &entitiesProvider{
			clk:           clk,
			dw:            dw,
			integration:   integration,
			reqSerializer: dp_utils.NewReqSerializerOf[uuid.UUID](),
		},
		stats: &statsProvider{
			sw: &storeWrapper{
				store: store,
			},
			integration:                 integration,
			entityRequestSequentialiser: dp_utils.NewReqSerializerOf[requestSzKey](),
		},
		ifta: &iftaProvider{
			integration: integration,
			clk:         clk,
		},
	}).buildEndpoints()
}

var _ data_platform.Provider = (*Provider)(nil)

func (p *Provider) String() string {
	return "Terminal"
}

func (p *Provider) Vehicles(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.Vehicle, error) {
	vehicles, err := p.entities.vehicles(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	var resp []*data_platform.Vehicle
	for _, v := range vehicles {
		vin, err := getRealOrFakeVin(v)
		if err != nil {
			return nil, err
		}
		var api terminal_data_api.Vehicle
		if err := json.Unmarshal(v.Raw, &api); err != nil {
			return nil, errors.Wrap(err, "failed to unmarshal terminal vehicle")
		}
		var groups []string
		if api.Groups != nil {
			groups = append(groups, *api.Groups...)
		}
		vehicle := data_platform.Vehicle{
			HandleId: connHandleId,
			// Since we use this field for identifying the vehicle during data
			// access calls, we return the TerminalUlid instead of TSPVehicleId here.
			TspId:        v.TerminalUlid,
			VIN:          vin,
			IsVINFake:    utils.IsFakeVin(fakeVINPrefix, vin),
			AliasName:    v.Name,
			TagIds:       groups,
			IsTracked:    !v.Discarded.Bool,
			CreatedAt:    v.CreatedAt,
			UpdatedAt:    v.UpdatedAt,
			TspCreatedAt: v.TSPCreatedAt,
			TspUpdatedAt: v.TSPUpdatedAt,
		}
		switch api.Status { //notlint:exhaustive
		case terminal_data_api.Active:
			vehicle.TspActivationStatus = null.BoolFrom(true)
		case terminal_data_api.Inactive:
			vehicle.TspActivationStatus = null.BoolFrom(false)
		default:
			// leave at null
		}
		resp = append(resp, &vehicle)
	}
	return resp, nil
}

func (p *Provider) Drivers(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]data_platform.Driver, error) {
	drivers, err := p.entities.drivers(ctx, connHandleId)
	if err != nil {
		return nil, err
	}
	var resp []data_platform.Driver

	for _, d := range drivers {
		var api terminal_data_api.Driver
		if err := json.Unmarshal(d.Raw, &api); err != nil {
			return nil, errors.Wrap(err, "failed to unmarshal driver object")
		}
		var groups []string
		if api.Groups != nil {
			groups = append(groups, *api.Groups...)
		}
		driver := data_platform.Driver{
			HandleId:        connHandleId,
			SourceId:        d.TerminalUlid,
			FullName:        api.GetFullName(),
			AliasName:       api.FirstName, // not provided by terminal, so we use first name as alias
			License:         (*data_platform.DL)(d.LicenseNumber.Ptr()),
			LicenseState:    d.LicenseState, // terminal returns 2 character code so it is safe to do
			VehicleGroupIds: groups,
			IsTracked:       true,
			CreatedAt:       d.CreatedAt,
			UpdatedAt:       d.UpdatedAt,
			TspCreatedAt:    d.TSPCreatedAt,
			TspUpdatedAt:    d.TSPUpdatedAt,
		}
		switch api.Status { //notlint:exhaustive
		case terminal_data_api.DriverStatusActive:
			driver.TspActivationStatus = null.BoolFrom(true)
		case terminal_data_api.DriverStatusInactive:
			driver.TspActivationStatus = null.BoolFrom(false)
		default:
			// leave at null
		}
		resp = append(resp, driver)
	}
	return resp, err
}

func (p *Provider) VehicleGroups(
	ctx context.Context,
	connHandleId uuid.UUID,
) ([]*data_platform.VehicleGroup, error) {
	groups, err := p.entities.groups(ctx, connHandleId)
	if err != nil {
		return nil, err
	}

	return groupsToDpVehicleGroups(groups)
}

func groupsToDpVehicleGroups(groups []*db_model.Group) ([]*data_platform.VehicleGroup, error) {
	var resp []*data_platform.VehicleGroup

	for _, g := range groups {
		tag := data_platform.VehicleGroup{
			HandleId:     uuid.MustParse(g.HandleID),
			SourceId:     g.TerminalUlid,
			Name:         g.Name,
			IsTracked:    true,
			CreatedAt:    g.CreatedAt,
			UpdatedAt:    g.UpdatedAt,
			TspCreatedAt: g.TSPCreatedAt,
			TspUpdatedAt: g.TSPUpdatedAt,
		}
		resp = append(resp, &tag)
	}
	return resp, nil
}

func (p *Provider) SyncEntities(ctx context.Context, connHandleId uuid.UUID) (
	*data_platform.Entities, error,
) {
	if err := p.entities.checkForTerminatedSync(ctx, connHandleId); err != nil {
		return nil, err
	}

	if orgErr := p.entities.createOrgIfDoesntExist(ctx, connHandleId); orgErr != nil {
		return nil, errors.Wrapf(orgErr, "failed to create org")
	}

	if err := p.entities.refreshVehicles(ctx, connHandleId); err != nil {
		return nil, err
	}

	if err := p.entities.refreshGroups(ctx, connHandleId); err != nil {
		return nil, errors.Wrapf(err, "failed to refresh groups")
	}

	vehicles, err := p.Vehicles(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicles")
	}
	drivers, err := p.Drivers(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch drivers")
	}
	groups, err := p.VehicleGroups(ctx, connHandleId)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch vehicle groups")
	}
	return &data_platform.Entities{
		Vehicles:      vehicles,
		Drivers:       drivers,
		VehicleGroups: groups,
	}, nil
}

func (p *Provider) SyncData(
	ctx context.Context,
	connHandleId uuid.UUID,
	interval time_utils.Interval,
) error {
	return p.syncHandler.syncData(ctx, connHandleId, interval)
}

func (p *Provider) Ping(ctx context.Context, handleId uuid.UUID) error {
	// For terminal, no organization-level metadata endpoint is available,
	// so we use vehicle list endpoint as proxy to ping.
	vehicles, err := fetchVehicles(ctx, p.entities.integration, handleId, null.Time{})
	if err != nil {
		return errors.Wrap(err, "ping failed because failed to get vehicles list")
	}
	if len(vehicles) == 0 {
		return errors.New("ping failed because no vehicles found for connection")
	}
	return nil
}

// isSynced is used to check if the requested interval is already synced.
func (p *Provider) isSynced(
	ctx context.Context,
	connHandleId uuid.UUID,
	requestedInterval time_utils.Interval,
	readOnly null.Bool,
) error {
	// in case of read only as data is not supposed to be read from terminal apis.
	// no need to return ErrNotAvailableYet as we will not sync data
	if readOnly.Valid && readOnly.Bool {
		return nil
	}
	org, err := p.syncHandler.dw.GetOrgInfo(ctx, connHandleId)
	if err != nil {
		return err
	}
	if org.IsSynced(requestedInterval) {
		return nil
	}
	return data_platform.ErrNotAvailableYet
}

func (p *Provider) cachedDataAvailability(
	ctx context.Context,
	connHandleId uuid.UUID,
	params data_platform.QueryParams,
	statKind statKind,
) (data_platform.ResourceAvailabilityLog, error) {
	ctx, span := tracing.Start(ctx, "terminal.cachedDataAvailability")
	defer span.End()

	interval := params.Interval
	if params.Interval.End.After(p.clk.Now()) {
		return nil, errors.Newf(
			"invalid interval=%v, End cannot be after current time",
			interval,
		)
	}
	if params.IdentifierType != data_platform.IdentifierTypeProviderVehicleId {
		return nil, errors.Newf("identifier type must a vehicle-level identifier")
	}
	entityIdentifiers, err := p.entities.identify(
		ctx,
		connHandleId,
		params.IdentifierType,
		params.IdentifierValue,
	)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to identify %s=%s", params.IdentifierType, params.IdentifierValue)
	}
	m := utils.NewWeekResourceAvailabilityMap(
		interval, data_platform.Unavailable,
	)
	// We must always iterate in a deterministic order,
	// as if there are multiple identifiers, their ETag values must be combined
	// in a deterministic order always.
	slices.SortFunc(entityIdentifiers, func(a, b entityIdentifier) int {
		return cmp.Compare(a.ulid, b.ulid)
	})
	weeks := time_utils.GetISOWeekInterval(interval).GetISOWeekList()
	for idx := range entityIdentifiers {
		avb, err := p.stats.sw.terminalResourceLogAvailability(ctx, statKind, entityIdentifiers[idx], weeks)
		if err != nil {
			return nil, errors.Wrapf(
				err,
				"failed to check availability for ulid=%s interval=%v",
				entityIdentifiers[idx].ulid,
				interval,
			)
		}
		m = m.CombineWithInfoMap(avb)
	}
	return m.Finalize(), nil
}
