package terminal

import (
	"context"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/mock/gomock"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	terminal_db "nirvanatech.com/nirvana/db-api/db_wrappers/terminal"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/terminal_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/telematics"
	terminal_manager "nirvanatech.com/nirvana/telematics/connections/terminal"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
	"nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	"nirvanatech.com/nirvana/telematics/integrations/terminal/data_api"
)

const (
	defaultEntityValue = "1"
)

var year2021Feb = time_utils.Interval{
	Start: time.Date(2021, time.February, 1, 0, 0, 0, 0, time.UTC),
	End:   time.Date(2021, time.February, 28, 23, 59, 59, 0, time.UTC),
}

type env struct {
	fx.In
	WithGoldenData *terminal_fixture.WithGoldenData
	Provider       *Provider
	Clk            *clock.Mock
	DB             *terminal_db.DataWrapper
}

// setupSyncEntitiesExpectations sets expectations for syncing entities.
func setupSyncEntitiesExpectations(env *env) {
	env.WithGoldenData.ExpectCreateConnectionInvocations(1)
	env.WithGoldenData.ExpectListVehiclesInvocations(2)
	env.WithGoldenData.ExpectListDriversInvocations(1)
	env.WithGoldenData.ExpectListGroupsInvocations(1)
	env.WithGoldenData.ExpectListSyncHistory(2, func(sync data_api.Sync) data_api.Sync {
		// transition the zero-th sync to success
		if strings.HasPrefix(sync.Id, "zero+") {
			sync.Status = data_api.Completed
			sync.CompletedAt = pointer_utils.Time(env.Clk.Now())
		}
		return sync
	})
}

// initializeConnection create connection with mock server for the source account.
func initializeConnection(ctx context.Context, t *testing.T, env *env, handleId uuid.UUID) {
	err := env.WithGoldenData.TerminalConnMgr.InitializeConnection(
		ctx,
		terminal_manager.InitializeConnectionParams{
			HandleId:    handleId,
			TSP:         telematics.TSPGeotab,
			CompanyName: "companyName",
			Credentials: terminal_fixture.CredsGeotab,
			ConsentKind: telematics.ConsentKindBasicAuth,
		},
	)
	require.NoError(t, err)
	connErr := env.WithGoldenData.TerminalConnMgr.InternalConnectSynchronously(ctx, handleId)

	require.NoError(t, connErr)
}

func syncEntitiesAndData(ctx context.Context, t *testing.T, env *env, handleId uuid.UUID) {
	// Set up sync entities expectations
	setupSyncEntitiesExpectations(env)
	// initialize connection
	initializeConnection(ctx, t, env, handleId)

	// sync entities
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)
	// Set up sync data expectations
	env.WithGoldenData.ExpectRequestSyncInvocations(1)

	env.WithGoldenData.ExpectGetSyncStatusInvocations(1, func(sync data_api.Sync) data_api.Sync {
		sync.Status = data_api.Completed
		isoDateTime := env.Clk.Now()
		sync.CompletedAt = &isoDateTime
		return sync
	})

	// sync data
	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
	env.Clk.Add(time.Minute)
	require.NoError(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
}

// Test_RefreshMetadata verifies that all metadata is being correctly pulled.
func Test_RefreshMetadata(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)

	// Set up sync entities expectations
	setupSyncEntitiesExpectations(&env)
	// initialize connection
	initializeConnection(ctx, t, &env, handleId)

	// sync entities
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// Verify Vehicle Count
	vehicles, err := env.Provider.Vehicles(ctx, handleId)
	require.NoError(t, err)
	require.Equal(t, len(vehicles), terminal_fixture.SourceAccountGeotab.Props().VehiclesCount)

	// Verify tags Count
	tags, err := env.Provider.VehicleGroups(ctx, handleId)
	require.NoError(t, err)
	require.Equal(t, len(tags), terminal_fixture.SourceAccountGeotab.Props().GroupsCount)

	// Verify Drivers count
	dls, err := env.Provider.Drivers(ctx, handleId)
	require.NoError(t, err)
	require.Equal(t, len(dls), terminal_fixture.SourceAccountGeotab.Props().DriversCount)
	// verify essential fields are set
	for _, d := range dls {
		require.NotZero(t, d.License)
		require.NotZero(t, d.LicenseState)
		require.NotZero(t, d.FullName)
		require.NotZero(t, d.AliasName)
		require.NotZero(t, d.CreatedAt)
		require.NotZero(t, d.UpdatedAt)
		require.True(t, d.TspActivationStatus.Bool)
	}
}

func Test_SyncDataWithConsent(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPVerizonConnectReveal)
	// Set up sync entities expectations
	setupSyncEntitiesExpectations(&env)
	env.WithGoldenData.ExpectListVehiclesInvocations(4)

	// initialize connection verizon connect reveal
	err := env.WithGoldenData.TerminalConnMgr.InitializeConnection(
		ctx,
		terminal_manager.InitializeConnectionParams{
			HandleId:    handleId,
			TSP:         telematics.TSPVerizonConnectReveal,
			CompanyName: "companyName",
			Credentials: terminal_fixture.CredsVerizonReveal,
			ConsentKind: telematics.ConsentKindBasicAuth,
		},
	)
	require.NoError(t, err)
	connErr := env.WithGoldenData.TerminalConnMgr.InternalConnectSynchronously(ctx, handleId)
	require.NoError(t, connErr)

	// sync entities
	_, err = env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// expectations for sync data.
	env.WithGoldenData.ExpectRequestSyncInvocations(1)
	env.WithGoldenData.ExpectGetSyncStatusInvocations(1, func(sync data_api.Sync) data_api.Sync {
		sync.Status = data_api.Completed
		return sync
	})
	// Sync Data, should get error as vehicle count is more than maxVehicleForSyncDataLimit.
	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb))

	// give consent
	require.NoError(t, env.DB.UpdateOrgExplicitConsentUpto(ctx, handleId, env.Clk.Now()))

	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
	env.Clk.Add(time.Minute)
	require.NoError(t, env.Provider.SyncData(ctx, handleId, year2021Feb))

	// Advance the mock clock by a month
	env.Clk.Add(time.Hour * 24 * 30)
	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb)) // should get error as month changed
}

func Test_SyncData(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)

	// Set up sync entities expectations
	setupSyncEntitiesExpectations(&env)
	// initialize connection
	initializeConnection(ctx, t, &env, handleId)

	// sync entities
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)

	// expectations for sync data.
	env.WithGoldenData.ExpectRequestSyncInvocations(1)
	env.WithGoldenData.ExpectGetSyncStatusInvocations(2, func(sync data_api.Sync) data_api.Sync {
		switch sync.Status {
		case data_api.Requested:
			// If the current status is 'Requested', transition it to 'In Progress'.
			sync.Status = data_api.InProgress
		case data_api.InProgress:
			// If the status is 'In Progress' and the job has been requested for more than a minute,
			// transition it to 'Completed'.
			if env.Clk.Since(sync.RequestedAt) > time.Minute {
				sync.Status = data_api.Completed
			}
		default: // No changes.
		}
		return sync
	})

	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb)) // Expect ErrNotAvailableYet
	// Advance the mock clock and test SyncData again
	env.Clk.Add(time.Minute)
	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
	env.Clk.Add(time.Minute)
	require.NoError(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
}

// Test_Safety tests that we can correctly pull safety events from terminal
func Test_Safety(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleSafetyConcurrentTestCase(t)
	})
}

// runVehicleSafetyConcurrentTestCase verifies that the terminal safety events endpoint is safe to call concurrently,
// in case of multiple concurrent requests, requests should be serialised, and no redundant api calls should be observed.
func runVehicleSafetyConcurrentTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)
	// sync entities and sync data, are preconditions to start data pull
	syncEntitiesAndData(ctx, t, &env, handleId)
	// Since the server returns data for one day per API call in a single page.
	// Total number of days calls is total number of iso weeks * 7 i.e, 7 * 4 = 28
	env.WithGoldenData.ExpectListSafetyData(28, year2021Feb)
	// Concurrently fetch safety events from 10 goroutines.
	// The expectation is that requests will be serialized in a manner such that no duplicate API request is made.
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			queryParams := data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: defaultEntityValue,
				Interval:        year2021Feb,
			}
			resStream, err := env.Provider.SafetyEventLog.For(
				ctx, handleId,
				queryParams,
			)
			require.NoError(t, err)
			safetyEvents, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Equal(t, len(safetyEvents), env.WithGoldenData.MockServer.SafetyEventsDataSize()*(4*7)) // 1day of data from mock server * 4(number of iso weeks) * 7
		}()
	}
	wg.Wait()
}

// Test_VehicleLocation tests that we can correctly pull vehicle location from terminal
func Test_VehicleLocation(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleLocationConcurrentTestCase(t)
	})
}

// runVehicleLocationConcurrentTestCase verifies that the terminal gps log endpoint is safe to call concurrently,
// in case of multiple concurrent requests, requests should be serialised, and no redundant api calls should be observed.
func runVehicleLocationConcurrentTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)

	// sync entities and sync data, are preconditions to start data pull
	syncEntitiesAndData(ctx, t, &env, handleId)
	// Since the server returns data for one day per API call in a single page.
	// Total number of days calls is total number of iso weeks * 7 i.e, 7 * 4 = 28
	env.WithGoldenData.ExpectListVehicleHistoricalData(28, year2021Feb)
	// Concurrently fetch vehicle locations from 10 goroutines.
	// The expectation is that requests will be serialized in a manner such that no duplicate API request is made.
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			queryParams := data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: defaultEntityValue,
				Interval:        year2021Feb,
			}
			resStream, err := env.Provider.GPSLog.For(
				ctx, handleId,
				queryParams,
			)
			require.NoError(t, err)
			gpsLogs, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Equal(t, len(gpsLogs), env.WithGoldenData.MockServer.VehicleLocationDataSize()*(4*7)) // 1day of data from mock server * 4(number of iso weeks) * 7
		}()
	}
	wg.Wait()
}

// Test_VehicleStats tests that we can correctly pull vehicle stats from terminal
func Test_VehicleStats(t *testing.T) {
	t.Run("with ProviderId as identifier", func(t *testing.T) {
		runVehicleStatsConcurrentTestCase(t)
	})
}

// runVehicleStatsConcurrentTestCase verifies that the terminal odometer endpoint is safe to call concurrently,
// in case of multiple concurrent requests, requests should be serialised, and no redundant api calls should be observed.
func runVehicleStatsConcurrentTestCase(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)

	// sync entities and sync data, are preconditions to start data pull
	syncEntitiesAndData(ctx, t, &env, handleId)
	// Since the server returns data for one day per API call in a single page.
	// Total number of days calls is total number of iso weeks * 7 i.e, 7 * 4 = 28
	env.WithGoldenData.ExpectListVehicleStatData(28, year2021Feb)
	// Concurrently fetch vehicle stats from 10 goroutines.
	// The expectation is that requests will be serialized in a manner such that no duplicate API request is made.
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			queryParams := data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: defaultEntityValue,
				Interval:        year2021Feb,
			}
			resStream, err := env.Provider.OdometerLog.For(
				ctx, handleId,
				queryParams,
			)
			require.NoError(t, err)
			vehicleStats, err := stream.GetAll(resStream)
			require.NoError(t, err)
			require.Equal(t, len(vehicleStats), env.WithGoldenData.MockServer.VehicleStatDataSize()*(4*7)) // 1day of data from mock server * 4(number of iso weeks) * 7
		}()
	}
	wg.Wait()
}

func TestReadOnlyQueryParam(t *testing.T) {
	// Initialize environment and defer cleanup
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()

	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)

	// Precondition: Sync entities and data before starting the data pull
	syncEntitiesAndData(ctx, t, &env, handleId)

	// Define the time interval for the first week of February 2021
	year2021FebWeek1 := time_utils.Interval{
		Start: time.Date(2021, time.February, 1, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2021, time.February, 7, 23, 59, 59, 0, time.UTC),
	}

	// Define the time interval for the second week of February 2021
	year2021FebWeek2 := time_utils.Interval{
		Start: time.Date(2021, time.February, 8, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2021, time.February, 14, 23, 59, 59, 0, time.UTC),
	}

	// ReadOnly set true, data present in cache for feb week1
	t.Run("Data present in cache", func(t *testing.T) {
		// first pull data in cache for week 1
		queryParams := data_platform.QueryParams{
			IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
			IdentifierValue: defaultEntityValue,
			Interval:        year2021FebWeek1,     // feb week 1
			ReadOnly:        null.BoolFrom(false), // Allow data to be cached
		}
		// Expect API calls to fetch data for 7 days (one week)
		env.WithGoldenData.ExpectListVehicleHistoricalData(7, year2021Feb)
		// Fetch GPS logs to cache data
		resStream, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		// Retrieve all GPS logs and verify the data size
		gpsLogs, err := stream.GetAll(resStream)
		require.NoError(t, err)
		require.Equal(t, len(gpsLogs), env.WithGoldenData.MockServer.VehicleLocationDataSize()*1*7) // (number of data points) * 1(iso weeks) * 7

		// data is pulled for week1, now call gps log
		// with readOnly true.
		queryParams.ReadOnly = null.BoolFrom(true)
		// Fetch GPS logs, no api call to terminal is expected
		resStream, err = env.Provider.GPSLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		// Retrieve all GPS logs and verify the data size
		gpsLogs, err = stream.GetAll(resStream)
		require.NoError(t, err)
		require.Equal(t, len(gpsLogs), env.WithGoldenData.MockServer.VehicleLocationDataSize()*1*7) // (number of data points) * 1(iso weeks) * 7
	})

	// ReadOnly set true, data not present in cache for feb week2
	// no api call is expected as read only is true
	t.Run("Data not present in cache", func(t *testing.T) {
		queryParams := data_platform.QueryParams{
			IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
			IdentifierValue: defaultEntityValue,
			Interval:        year2021FebWeek2,    // feb week 2
			ReadOnly:        null.BoolFrom(true), // Enable ReadOnly mode
		}
		// Fetch GPS logs
		resStream, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		// Expect an error since data is not in cache
		_, err = stream.GetAll(resStream)
		require.ErrorIs(t, err, data_platform.ErrNotAvailableForReadOnly)
	})
}

// Test_CachedDataAvailability asserts provider correctly responds with data availability
// queries once data request has been fulfilled.
func Test_CachedDataAvailability(t *testing.T) {
	// Initialize environment and defer cleanup
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)
	// Precondition: Sync entities and data before starting the data pull
	syncEntitiesAndData(ctx, t, &env, handleId)

	year2021MarchStart := time.Date(2021, time.March, 1, 0, 0, 0, 0, time.UTC)
	year2021AprilStart := time.Date(2021, time.April, 1, 0, 0, 0, 0, time.UTC)
	year2021Feb1stWeek := time_utils.Interval{
		Start: year2021Feb.Start,
		End:   time.Date(2021, time.February, 7, 23, 0, 0, 0, time.UTC),
	}
	year2021Feb2ndWeekStart := time.Date(2021, time.February, 8, 0, 0, 0, 0, time.UTC)

	env.WithGoldenData.SetupTerminalMockDataExpectation(time_utils.Interval{
		Start: year2021Feb2ndWeekStart,
		End:   year2021AprilStart,
	}) // allowed interval is from feb second week to 1st April

	// request data for 2021 Feb
	queryParams := data_platform.QueryParams{
		IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
		IdentifierValue: "1",
		Interval:        year2021Feb,
	}
	{
		// gps log
		rs, err := env.Provider.GPSLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// engine state log
		rs, err := env.Provider.EngineStateLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// odometer log
		rs, err := env.Provider.OdometerLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}
	{
		// safety event log
		rs, err := env.Provider.SafetyEventLog.For(ctx, handleId, queryParams)
		require.NoError(t, err)
		_, err = stream.GetAll(rs)
		require.NoError(t, err)
	}

	// verify availability for "warmed up" stats
	for _, endpoint := range []data_platform.CachedDataAvailabilityFn{
		env.Provider.GPSLog.CachedDataAvailability,
		env.Provider.EngineStateLog.CachedDataAvailability,
		env.Provider.OdometerLog.CachedDataAvailability,
		env.Provider.SafetyEventLog.CachedDataAvailability,
	} {
		// request availability log
		avlLog, err := endpoint(
			ctx, handleId, data_platform.QueryParams{
				IdentifierType:  data_platform.IdentifierTypeProviderVehicleId,
				IdentifierValue: "1",
				Interval: time_utils.Interval{
					Start: year2021Feb.Start,
					End:   year2021AprilStart.Add(-time.Second),
				},
			},
		)
		require.NoError(t, err)

		expectedWeeksWithNoCache := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2021MarchStart,
					End:   year2021AprilStart,
				},
			).GetISOWeekListRightBounded(year2021AprilStart),
		)
		expectedWeeksWithActualData := len(
			time_utils.GetISOWeekInterval(
				time_utils.Interval{
					Start: year2021Feb2ndWeekStart,
					End:   year2021MarchStart,
				},
			).GetISOWeekListRightBounded(year2021MarchStart),
		)

		expectedWeeksWithNoReading := len(
			time_utils.GetISOWeekInterval(
				year2021Feb1stWeek,
			).GetISOWeekListRightBounded(year2021MarchStart),
		)
		require.Len(t, avlLog, expectedWeeksWithNoCache+expectedWeeksWithActualData+expectedWeeksWithNoReading)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.DataAvailable), expectedWeeksWithActualData)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.Unavailable), expectedWeeksWithNoCache)
		require.Len(t, avlLog.WhereStateOneOf(data_platform.NoReadings), expectedWeeksWithNoReading)

		// Check that ETag is populated
		for _, avl := range avlLog {
			if avl.State != data_platform.Unavailable {
				require.NotEmpty(t, avl.ETag)
			} else {
				require.Empty(t, avl.ETag)
			}
		}
	}
}

// Execute SyncData - this should demonstrate the complete cancellation flow:
// 1. Encounter 409 conflict
// 2. Trigger conflict resolution
// 3. Find incompatible active sync
// 4. Cancel the incompatible sync
// 5. Create new sync successfully
// 7. check sync to find not available yet
// 6. move the clock and successful sync
func Test_SyncDataWith409ConflictResolution(t *testing.T) {
	var env env
	defer testloader.RequireStart(t, &env).RequireStop()
	ctx := context.Background()
	handleId := test_utils.GoldenHandleId(telematics.TSPGeotab)
	setupSyncEntitiesExpectations(&env)
	initializeConnection(ctx, t, &env, handleId)
	// sync entities
	_, err := env.Provider.SyncEntities(ctx, handleId)
	require.NoError(t, err)
	env.WithGoldenData.MockServer.DataApi.EXPECT().
		RequestSync(gomock.Any(), gomock.Any()).
		DoAndReturn(func(c echo.Context, params data_api.RequestSyncParams) error {
			// Return 409 conflict error
			return c.JSON(409, &data_api.ConflictError{
				Code:    "conflict",
				Message: "Sync already in progress",
				Detail:  null.StringFrom("Existing sync in progress"),
			})
		}).
		Times(1)
	env.WithGoldenData.ExpectListSyncHistory(1, func(sync data_api.Sync) data_api.Sync {
		incompatibleStartTime := time.Date(2021, time.March, 1, 0, 0, 0, 0, time.UTC)
		sync.StartFrom = &incompatibleStartTime
		sync.Status = data_api.InProgress
		sync.RequestedAt = env.Clk.Now()
		return sync
	})
	env.WithGoldenData.MockServer.DataApi.EXPECT().
		CancelSync(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(env.WithGoldenData.MockServer.CancelSyncHandler).
		Times(1)
	env.WithGoldenData.ExpectRequestSyncInvocations(1)
	env.WithGoldenData.ExpectGetSyncStatusInvocations(1, func(sync data_api.Sync) data_api.Sync {
		sync.Status = data_api.Completed
		completedTime := time.Date(2021, time.January, 15, 0, 0, 0, 0, time.UTC)
		sync.CompletedAt = &completedTime
		return sync
	})

	// first time it should return not available yet error
	require.Error(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
	// Advance clock when the new sync status is being checked
	env.Clk.Add(5 * time.Minute)
	require.NoError(t, env.Provider.SyncData(ctx, handleId, year2021Feb))
}
