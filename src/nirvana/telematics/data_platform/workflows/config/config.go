package config

import (
	"github.com/volatiletech/null/v8"

	"nirvanatech.com/nirvana/metaflow/grpc"
	"nirvanatech.com/nirvana/telematics/data_platform"
)

// Base configurations for TSPs
var (
	// baseConfig is the base configuration for TSPs.
	// This configuration includes the essential output stats and optional stats.
	baseConfig = &DefaultDataPipelineConfig{
		OutputStats: []data_platform.NormalizedVehicleStat{
			data_platform.NormalizedVehicleStatGPSLogWithGeography,
			data_platform.NormalizedVehicleStatOdometerLog,
			data_platform.NormalizedVehicleStatSafetyEvents,
		},
		OptionalStats: []data_platform.NormalizedVehicleStat{
			data_platform.NormalizedVehicleStatGPSLogWithMaps,
			data_platform.NormalizedVehicleStatGPSLogWithWeather,
		},
		DatascienceOutputs: []DatascienceOutput{},
		MaxConcurrency:     null.IntFrom(10),
	}

	// baseWithHarshEvent is the base configuration for TSPs supporting harsh acceleration and braking logs
	baseWithHarshEvent = baseConfig.WithAdditionalOutputStats(
		data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
		data_platform.NormalizedVehicleStatHarshBrakingEventLog,
	)

	// baseWithHarshEventAndEngineLogConfig is the base configuration for TSPs supporting both unified safety events
	// and harsh events (acceleration and braking). Also it will have engine logs.
	baseWithHarshEventAndEngineLogConfig = baseConfig.WithAdditionalOutputStats(
		data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
		data_platform.NormalizedVehicleStatHarshBrakingEventLog,
		data_platform.NormalizedVehicleStatEngineLog,
	)

	testDSOutputs = []DatascienceOutput{
		{
			Name:    grpc.OutputType_GRPCTestOutput,
			Version: 1,
		},
		{
			Name:    grpc.OutputType_GRPCTestOutput,
			Version: 2,
		},
		{
			Name:    grpc.OutputType_GRPCTestOutput,
			Version: 3,
		},
	}
	// baseTestConfig is the basicConfig for writing tests.
	baseTestConfig = DefaultDataPipelineConfig{
		OutputStats: []data_platform.NormalizedVehicleStat{
			data_platform.NormalizedVehicleStatGPSLog,
			data_platform.NormalizedVehicleStatGPSLogWithGeography,
			data_platform.NormalizedVehicleStatOdometerLog,
			data_platform.NormalizedVehicleStatSafetyEvents,
			data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
			data_platform.NormalizedVehicleStatHarshBrakingEventLog,
			data_platform.NormalizedVehicleStatEngineLog,
		},
		OptionalStats: []data_platform.NormalizedVehicleStat{
			data_platform.NormalizedVehicleStatGPSLogWithMaps,
			data_platform.NormalizedVehicleStatGPSLogWithWeather,
		},
		DatascienceOutputs: testDSOutputs,
		MaxConcurrency:     null.IntFrom(10),
		MaxReportingDays:   null.IntFrom(730),
	}
)

// Derived configurations
var (
	// premiumTSPConfig is the configuration for Premium TSPs.
	// Supports up to 730 days (2 years) of data.
	premiumTSPConfig = baseConfig.WithMaxReportingDays(730)

	// premiumTSPWithHarshEvents is the configuration for Premium TSPs.
	// Supports up to 730 days (2 years) of data, including both unified safety events and harsh events.
	// Also it will have engine logs.
	premiumTSPWithHarshEvents = baseWithHarshEvent.WithMaxReportingDays(730)

	// premiumTSPWithHarshEventsAndEngineLogConfig is the configuration for Premium TSPs.
	// Supports up to 730 days (2 years) of data, including both unified safety events and harsh events.
	// Also it will have engine logs.
	premiumTSPWithHarshEventsAndEngineLogConfig = baseWithHarshEventAndEngineLogConfig.WithMaxReportingDays(730)

	// nonPremiumTSP180Config is the configuration for Non-Premium TSPs.
	// Supports up to 180 days (6 months) of data.
	nonPremiumTSP180Config = baseConfig.WithMaxReportingDays(180)

	// nonPremiumTSP365Config is the configuration for Non-Premium TSPs.
	// Supports up to 365 days (1 year) of data.
	nonPremiumTSP365Config = baseConfig.WithMaxReportingDays(365)

	// samsaraConfig is the configuration for Samsara TSP.
	// Based on the premiumTSPWithHarshEventsAndEngineLogConfig with additional optional stats for driver assignment logs.
	samsaraConfig = premiumTSPWithHarshEvents.WithAdditionalOptionalStats(
		data_platform.NormalizedVehicleStatEngineLog, // not supported for all customers (licensing issues)
		data_platform.NormalizedVehicleStatAccelerometerLog,
	)

	// smartDriveConfig is the configuration for SmartDrive TSP.
	// Based on nonPremiumTSP365Config with specific output stats and up to 365 days (1 year) of data.
	smartDriveConfig = nonPremiumTSP365Config.WithOutputStats(
		data_platform.NormalizedVehicleStatGPSLogWithGeography,
		data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
		data_platform.NormalizedVehicleStatHarshBrakingEventLog,
	)

	// keeptruckInConfig is the configuration for KeepTruckin TSP.
	// Based on premiumTSPWithHarshEventsAndEngineLogConfig with increased maximum concurrency.
	keeptruckInConfig = premiumTSPWithHarshEventsAndEngineLogConfig.
				WithMaxConcurrency(20).
				WithMaxReportingDays(22 * 30) // Approx 22 months
)
