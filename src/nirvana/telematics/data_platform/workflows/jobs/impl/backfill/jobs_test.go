package backfill_test

import (
	"context"
	"sync/atomic"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/data_infra_jobber"
	db_api "nirvanatech.com/nirvana/db-api"
	telematics_db_models "nirvanatech.com/nirvana/db-api/db_models/telematics"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/oauth_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/job_utils"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/jobber/registry"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs/impl/backfill"
)

// TestJob_Smoke runs a smoke test for the job.
// In this smoke test we prepare fleet_telematics_consent & fleet tables with a dummy row,
// and then run the auto-backfill job for that fleet.
func TestJob_Smoke(t *testing.T) {
	var env struct {
		fx.In
		J    data_infra_jobber.Client
		R    *data_infra_jobber.Registry
		Deps backfill.Deps
		Db   db_api.NirvanaRW
		*oauth_fixture.OAuthFixture
	}
	defer testloader.RequireStart(t, &env).RequireStop()
	// loading oauth fixture will automatically insert mock rows into ftc, fleet and connections table.

	// Create successful pipeline records for each test handle so the filterConnectionsWithSuccessfulRuns filter works
	ctx := context.Background()

	// Create pipeline records for the golden handles from the oauth fixture
	samsaraHandleId := test_utils.GoldenHandleId(telematics.TSPSamsara)
	keepTruckinHandleId := test_utils.GoldenHandleId(telematics.TSPKeepTruckin)

	samsaraPipeline := &telematics_db_models.Pipeline{
		PipelineID: uuid.NewString(),
		JobID:      "test-job-samsara",
		RunID:      "1",
		HandleID:   samsaraHandleId.String(),
		StartedAt:  time.Now(),
		FinishedAt: null.TimeFrom(time.Now()), // Successful run
		Kind:       "test",
	}

	keepTruckinPipeline := &telematics_db_models.Pipeline{
		PipelineID: uuid.NewString(),
		JobID:      "test-job-keeptruckin",
		RunID:      "1",
		HandleID:   keepTruckinHandleId.String(),
		StartedAt:  time.Now(),
		FinishedAt: null.TimeFrom(time.Now()), // Successful run
		Kind:       "test",
	}

	require.NoError(t, samsaraPipeline.Insert(ctx, env.Db, boil.Infer()))
	require.NoError(t, keepTruckinPipeline.Insert(ctx, env.Db, boil.Infer()))

	// prepare jobber registry
	backfillJob, err := backfill.NewJob(env.Deps)
	require.NoError(t, err)
	require.NoError(t, registry.AddJob(env.R, backfillJob))

	timesRun := atomic.Int32{}
	dpJob, err := jtypes.NewJob(jobs.ConfigurableTelematicsWorkflow,
		slice_utils.From(
			job_utils.NewFunctionalTaskBuilder("task",
				func(ctx jtypes.Context, msg *jobs.ConfigurableTelematicsWorkflowMessage) error {
					timesRun.Add(1)
					return nil
				},
			).Build(),
		),
		jobs.UnmarshalConfigurableWorkflowMessageFn,
	)
	require.NoError(t, err)
	require.NoError(t, registry.AddJob(env.R, dpJob))

	// simply trigger the backfill job and wait for completion.
	// we do it twice.
	ctx = t.Context()
	for range 2 {
		jrId, err := env.J.AddJobRun(ctx, jtypes.NewAddJobRunParams(
			jobs.AutoBackfillJob,
			jobs.AutoBackfillJobMsg{},
			jtypes.NewMetadata(jtypes.Immediate),
		))
		require.NoError(t, err)
		require.NoError(t, env.J.WaitForJobRunCompletion(ctx, jrId))

		jr, err := env.J.GetJobRun(ctx, jrId)
		require.NoError(t, err)
		require.Equal(t, jtypes.JobRunStatusSucceeded, jr.Status)
	}
	require.Equal(t, int32(1), timesRun.Load())
}
