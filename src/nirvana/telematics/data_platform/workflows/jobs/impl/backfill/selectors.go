package backfill

import (
	"context"
	"fmt"
	"strconv"

	db_api "nirvanatech.com/nirvana/db-api"

	"nirvanatech.com/nirvana/common-go/slice_utils"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"nirvanatech.com/nirvana/common-go/map_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/common-go/uuid_utils"
	telematics_db_models "nirvanatech.com/nirvana/db-api/db_models/telematics"
	"nirvanatech.com/nirvana/db-api/db_wrappers/fleet"
	ftc "nirvanatech.com/nirvana/db-api/db_wrappers/fleet_telematics"
	"nirvanatech.com/nirvana/db-api/db_wrappers/telematics/subscriptions"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/connection_selector"
)

var SupportedDataProviderTSPs = []telematics.DataProviderTSP{
	{
		DataProvider: telematics.DataProviderNative,
		TSP:          telematics.TSPSamsara,
	},
	{
		DataProvider: telematics.DataProviderNative,
		TSP:          telematics.TSPSamsaraSafety,
	},
	// Keeptruckin was originally supported but was removed in 2025-08-18 (See DAT-208)
	// TODO: verify which terminal TSPs support >2yr historical data
}

// GetSubscriptionRequests returns a list of subscriptions.Request to upsert into the database.
// We set an expiry of 1d on all requests so that the subscription processor may automatically GC
// un-needed requests instead of us being responsible for deleting them.
func GetSubscriptionRequests(ctx context.Context, deps Deps) ([]*subscriptions.Request, error) {
	fleetIdToConnections, err := SelectConnections(ctx, deps.Ftc, deps.Cs)
	if err != nil {
		return nil, err
	}

	// Filter connections to only include those with at least one successful run. We are doing to make sure that
	// consent pull was successful before backfill otherwise it will result in data holes.
	// For more context see: DAT-142
	filteredConnections, err := filterConnectionsWithSuccessfulRuns(ctx, deps.Db, fleetIdToConnections)
	if err != nil {
		return nil, err
	}

	fleetIds := map_utils.Keys(filteredConnections)
	fleets, err := deps.FleetWrapper.FetchFleets(ctx, fleet.WhereFleetIdIn(fleetIds...))
	if err != nil {
		return nil, errors.Wrapf(err, "failed to fetch fleets")
	}
	fleetIdToFleets := make(map[uuid.UUID]*fleet.Fleet, len(fleets))
	for _, fleet := range fleets {
		fleetIdToFleets[fleet.ID] = fleet
	}
	reqs := make([]*subscriptions.Request, 0, len(filteredConnections))
	for fleetId, conn := range filteredConnections {
		dotNumberStr := fleetIdToFleets[fleetId].DotNumber
		dotNumber, err := strconv.ParseInt(dotNumberStr, 10, 64)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to parse dotNumber %s", dotNumberStr)
		}
		reqs = append(reqs, &subscriptions.Request{
			// Use a combination of fleetId and handleId as the stable request identifier
			Id:        uuid_utils.StableUUID(fmt.Sprintf("%s+%s", fleetId, conn.HandleID)),
			Type:      subscriptions.TypeBackfill,
			HandleId:  conn.HandleID,
			DOT:       dotNumber,
			FleetName: fleetIdToFleets[fleetId].Name,
			CreatedAt: deps.Clk.Now(),
			CreatedBy: "AutoBackfill",
			ExpireAt:  null.TimeFrom(deps.Clk.Now().Add(time_utils.Day)),
		})
	}
	return reqs, nil
}

// filterConnectionsWithSuccessfulRuns filters the connections to only include those
// that have at least one successful pipeline run (finished_at IS NOT NULL).
func filterConnectionsWithSuccessfulRuns(
	ctx context.Context,
	db db_api.NirvanaRW,
	fleetIdToConnections map[uuid.UUID]*connection_selector.DecoratedConnection,
) (map[uuid.UUID]*connection_selector.DecoratedConnection, error) {
	if len(fleetIdToConnections) == 0 {
		return fleetIdToConnections, nil
	}

	// Extract all HandleIds from the connections
	var handleIds []uuid.UUID
	for _, conn := range fleetIdToConnections {
		handleIds = append(handleIds, conn.HandleID)
	}

	// Query for HandleIds that have at least one successful pipeline run
	successfulHandleIds, err := getHandleIdsWithSuccessfulRuns(ctx, db, handleIds)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get HandleIds with successful pipeline runs")
	}
	successfulHandleIdSet := make(map[uuid.UUID]bool)
	for _, handleId := range successfulHandleIds {
		successfulHandleIdSet[handleId] = true
	}

	// Filter connections to only include those with successful runs
	filteredConnections := make(map[uuid.UUID]*connection_selector.DecoratedConnection)
	for fleetId, conn := range fleetIdToConnections {
		if successfulHandleIdSet[conn.HandleID] {
			filteredConnections[fleetId] = conn
		}
	}
	return filteredConnections, nil
}

// getHandleIdsWithSuccessfulRuns queries the database to find HandleIds
// that have at least one successful pipeline run (finished_at IS NOT NULL).
func getHandleIdsWithSuccessfulRuns(
	ctx context.Context,
	db db_api.NirvanaRW,
	handleIds []uuid.UUID,
) ([]uuid.UUID, error) {
	if len(handleIds) == 0 {
		return nil, nil
	}
	handleIdStrings := slice_utils.Map(handleIds, uuid.UUID.String)

	// Use optimized query to get only DISTINCT handle_ids (not full pipeline records)
	pipelines, err := telematics_db_models.Pipelines(
		telematics_db_models.PipelineWhere.HandleID.IN(handleIdStrings),
		telematics_db_models.PipelineWhere.FinishedAt.IsNotNull(),
		qm.Select("DISTINCT handle_id"),
	).All(ctx, db)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query for HandleIds with successful pipeline runs")
	}
	return slice_utils.MapErr(pipelines, func(pipeline *telematics_db_models.Pipeline) (uuid.UUID, error) {
		return uuid.Parse(pipeline.HandleID)
	})
}

// SelectConnections is responsible for preparing a list of telematics connections that need to
// be backfilled. We use connection_selector for encapsulating the selection logic. This method
// returns map from fleetId -> DecoratedConnection object.
// TODO: Right now we do not take into account multi-TSP account fleets. Revise this assumption
// in future when we have better handle on such fleets.
func SelectConnections(
	ctx context.Context,
	ftcWrapper ftc.Wrapper,
	cs *connection_selector.Selector,
) (map[uuid.UUID]*connection_selector.DecoratedConnection, error) {
	// For now we get all the consents, in future we may decide to apply some filters as well
	// for example excluding certain DOTs or class of consents which have legal restrictions on
	// backfill.
	consents, err := ftcWrapper.GetAllConsents(ctx)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get fleet_telematics_consent")
	}
	conns := cs.Connections(consents...)
	if err := cs.PrefetchConnInfo(ctx, conns); err != nil {
		return nil, errors.Wrapf(err, "failed to pre-fetch connInfos")
	}
	return filterAndSortConnections(ctx, conns)
}

func filterAndSortConnections(
	ctx context.Context,
	connections *connection_selector.SortableConnections,
) (map[uuid.UUID]*connection_selector.DecoratedConnection, error) {
	selectedList, err := connections.
		WhereDataProviderTSPIn(SupportedDataProviderTSPs...).
		WhereConnectionStatusIn(
			telematics.ConnectionStatusConnected,
			telematics.ConnectionStatusDisconnected,
			telematics.ConnectionStatusPermanentlyLost,
		).
		WithSorter(
			connection_selector.Sorter{
				// We prefer a Connected connection if found,
				connection_selector.ConnectionStatusComparator(connection_selector.Desc),
				// Among equals, prefer the "least" unhealthy connection
				connection_selector.DataProviderHealthStatusComparator(connection_selector.Desc),
				// Among equals, prefer the latest connection
				connection_selector.ConnectionDateComparator(connection_selector.Desc),
			},
		).
		All(ctx)
	if err != nil {
		return nil, errors.Wrap(
			err,
			"failed to filter & sort connections using connection_selector",
		)
	}

	selectedByFleetId := make(map[uuid.UUID]*connection_selector.DecoratedConnection)
	for _, conn := range selectedList {
		if _, ok := selectedByFleetId[conn.FleetID]; ok {
			continue
		}
		selectedByFleetId[conn.FleetID] = conn
	}
	return selectedByFleetId, nil
}
