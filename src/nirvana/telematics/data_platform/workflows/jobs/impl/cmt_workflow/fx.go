package cmt_workflow

import (
	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/s3_utils"
	"nirvanatech.com/nirvana/infra/fx/fxregistry"
)

func init() {
	fxregistry.Register(fx.Provide(
		fx.Annotate(func() (s3_utils.Client, error) {
			return s3_utils.NewClientWithRegion("us-west-2")
		}, fx.ResultTags(`name:"cmts3"`)),
	))
	fxregistry.RegisterForTest(fx.Decorate(
		fx.Annotate(func(client s3_utils.Client) (s3_utils.Client, error) {
			return client, nil
		}, fx.ResultTags(`name:"cmts3"`)),
	))
}
