package tests

import (
	"bytes"
	"context"
	"database/sql"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/common-go/time_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/telematics/workflows"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/heremaps_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/keeptruckin_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/openmeteo_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/samsara_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/smartdrive_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/terminal_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	"nirvanatech.com/nirvana/jobber/event"
	data_platform_events "nirvanatech.com/nirvana/jobber/event/registry/data_platform"
	"nirvanatech.com/nirvana/jobber/jtypes"
	"nirvanatech.com/nirvana/telematics"
	"nirvanatech.com/nirvana/telematics/connections/terminal"
	"nirvanatech.com/nirvana/telematics/data_platform"
	"nirvanatech.com/nirvana/telematics/data_platform/api/dispatcher"
	"nirvanatech.com/nirvana/telematics/data_platform/stream"
	dp_test_utils "nirvanatech.com/nirvana/telematics/data_platform/test_utils"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/jobs"
	"nirvanatech.com/nirvana/telematics/data_platform/workflows/test_utils"
	kt_test_utils "nirvanatech.com/nirvana/telematics/integrations/keeptruckin_lib/test_utils"
	samsara_test_utils "nirvanatech.com/nirvana/telematics/integrations/samsara_lib/test_utils"
	smartdrive_test_utils "nirvanatech.com/nirvana/telematics/integrations/smartdrive/test_utils"
)

// TestMillimanScoringWorkflowTestSuite tests the API handlers related to telematics
// pipeline executions.
func TestMillimanScoringWorkflowTestSuite(t *testing.T) {
	// Run the test suite with (now) legacy way of spawning by VINs
	suite.Run(t, new(millimanScoringWorkflowTestSuite))
}

// millimanScoringWorkflowTestSuite represents the test suite to validate
// RunMillimanScoringWorkflow JobRun behavior. At the moment, we only
// validate the success case.
type millimanScoringWorkflowTestSuite struct {
	suite.Suite

	suiteCtx           context.Context
	harness            *test_utils.Harness
	providerDispatcher *dispatcher.ProviderDispatcher
	testApp            *fxtest.App
}

func (n *millimanScoringWorkflowTestSuite) SetupTest() {
	n.suiteCtx = context.Background()
	var env struct {
		fx.In
		Harness            *test_utils.Harness
		ProviderDispatcher *dispatcher.ProviderDispatcher
		*keeptruckin_fixture.KeepTruckin
		*samsara_fixture.Samsara
		*heremaps_fixture.HereMaps
		SmartDriveFixture *smartdrive_fixture.Fixture
		*openmeteo_fixture.OpenMeteo
		*terminal_fixture.WithGoldenData
	}
	n.testApp = testloader.RequireStart(n.T(), &env)
	n.harness = env.Harness
	n.providerDispatcher = env.ProviderDispatcher

	// Register golden datasets for KT & Samsara
	n.Require().NoError(env.KeepTruckin.RegisterDataset(
		n.suiteCtx, dp_test_utils.GoldenHandleId(telematics.TSPKeepTruckin),
		kt_test_utils.GoldenDatasetOptions),
	)
	env.Samsara.RegisterGoldenDataset(samsara_test_utils.GoldenDatasetOptions)
	env.HereMaps.RegisterGoldenDataset()
	env.SmartDriveFixture.RegisterGoldenDataset(smartdrive_test_utils.DefaultConfig)
	env.OpenMeteo.RegisterGoldenDataset()

	// Terminal test setup
	env.WithGoldenData.SetupTerminalMockDataExpectation(time_utils.Interval{
		Start: time.Date(2022, time.January, 1, 0, 0, 0, 0, time.UTC),
		End:   time.Date(2022, time.January, 31, 0, 0, 0, 0, time.UTC),
	})
	n.Require().NoError(env.WithGoldenData.TerminalConnMgr.InitializeConnection(
		n.suiteCtx,
		terminal.InitializeConnectionParams{
			HandleId:    dp_test_utils.GoldenHandleId(telematics.TSPGeotab),
			TSP:         telematics.TSPGeotab,
			CompanyName: "companyName",
			Credentials: terminal_fixture.CredsGeotab,
			ConsentKind: telematics.ConsentKindBasicAuth,
		},
	))
	n.Require().NoError(env.WithGoldenData.TerminalConnMgr.InternalConnectSynchronously(n.suiteCtx, dp_test_utils.GoldenHandleId(telematics.TSPGeotab)))
}

func (n *millimanScoringWorkflowTestSuite) TearDownTest() {
	n.Require().NoError(n.harness.Close())
	n.testApp.RequireStop()
}

func (n *millimanScoringWorkflowTestSuite) TestSuccess() {
	ctx := n.suiteCtx

	type rowCountsSummary struct {
		tspId   string
		byKinds map[data_platform.NormalizedVehicleStat]int
	}

	type testCase struct {
		name             string
		tsp              telematics.TSP
		handleId         uuid.UUID
		interval         time_utils.Interval
		vins             map[data_platform.VIN]rowCountsSummary // map of vin to expected trip count
		kindsToNormalise []data_platform.NormalizedVehicleStat
		shouldBeCached   bool
	}

	testCases := []testCase{
		{
			name:     "1 Dec 2022 - 31 Dec 2022 for KT000000000000001",
			tsp:      telematics.TSPKeepTruckin,
			handleId: dp_test_utils.GoldenHandleId(telematics.TSPKeepTruckin),
			interval: time_utils.Interval{
				Start: time.Date(2020, time.December, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2020, time.December, 31, 0, 0, 0, 0, time.UTC),
			},
			kindsToNormalise: []data_platform.NormalizedVehicleStat{
				data_platform.NormalizedVehicleStatEngineLog,
				data_platform.NormalizedVehicleStatOdometerLog,
				data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
				data_platform.NormalizedVehicleStatHarshBrakingEventLog,
				data_platform.NormalizedVehicleStatGPSLogWithGeography,
				data_platform.NormalizedVehicleStatSafetyEvents,
				data_platform.NormalizedVehicleStatGPSLogWithWeather,
			},
			vins: map[data_platform.VIN]rowCountsSummary{
				data_platform.VIN("KT000000000000001"): {
					tspId: "1",
					byKinds: map[data_platform.NormalizedVehicleStat]int{
						data_platform.NormalizedVehicleStatGPSLogWithGeography:       50 * (4 * 7), // 50 GPS points per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatOdometerLog:               50 * (4 * 7),
						data_platform.NormalizedVehicleStatEngineLog:                 0,
						data_platform.NormalizedVehicleStatHarshBrakingEventLog:      1 * (4 * 7), // 1 Brake event per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatHarshAccelerationEventLog: 0,           // No Acceleration events
						data_platform.NormalizedVehicleStatSafetyEvents:              4 * 7,
						data_platform.NormalizedVehicleStatGPSLogWithWeather:         50 * (4 * 7), // Same as GPS points per day
					},
					// TODO: enable byMillimanTrips once KT mock provider supports milliman trips
				},
			},
			shouldBeCached: false,
		},
		{
			name:     "1 Dec 2022 - 31 Dec 2022 for SM000000000000001",
			tsp:      telematics.TSPSamsara,
			handleId: dp_test_utils.GoldenHandleId(telematics.TSPSamsara),
			interval: time_utils.Interval{
				Start: time.Date(2020, time.December, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2020, time.December, 31, 0, 0, 0, 0, time.UTC),
			},
			kindsToNormalise: []data_platform.NormalizedVehicleStat{
				data_platform.NormalizedVehicleStatEngineLog,
				data_platform.NormalizedVehicleStatOdometerLog,
				data_platform.NormalizedVehicleStatHarshAccelerationEventLog,
				data_platform.NormalizedVehicleStatHarshBrakingEventLog,
				data_platform.NormalizedVehicleStatGPSLogWithGeography,
				data_platform.NormalizedVehicleStatSafetyEvents,
				data_platform.NormalizedVehicleStatGPSLogWithMaps, // an optional attribute
				data_platform.NormalizedVehicleStatGPSLogWithWeather,
				data_platform.NormalizedVehicleStatAccelerometerLog,
			},
			vins: map[data_platform.VIN]rowCountsSummary{
				data_platform.VIN("SM000000000000001"): {
					tspId: "1",
					byKinds: map[data_platform.NormalizedVehicleStat]int{
						data_platform.NormalizedVehicleStatGPSLogWithGeography:       293 * (4 * 7), // 50 GPS points per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatOdometerLog:               293 * (4 * 7),
						data_platform.NormalizedVehicleStatEngineLog:                 12 * (4 * 7),  // 12 Engine events per day
						data_platform.NormalizedVehicleStatHarshBrakingEventLog:      1 * (4 * 7),   // 1 Brake event per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatHarshAccelerationEventLog: 1 * (4 * 7),   // 1 Accl event per day
						data_platform.NormalizedVehicleStatSafetyEvents:              2 * (4 * 7),   // combined safety events for 2 events per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatGPSLogWithMaps:            293 * (4 * 7), // Same as GPS points per day
						data_platform.NormalizedVehicleStatGPSLogWithWeather:         293 * (4 * 7), // Same as GPS points per day
						data_platform.NormalizedVehicleStatAccelerometerLog:          19 * (4 * 7),  // 19 accelerometer log per day
					},
				},
			},
			shouldBeCached: false,
		},
		{
			name:     "1 Jan 2022 - 31 Jan 2022 for Terminal",
			tsp:      telematics.TSPGeotab,
			handleId: dp_test_utils.GoldenHandleId(telematics.TSPGeotab),
			interval: time_utils.Interval{
				Start: time.Date(2022, time.January, 1, 0, 0, 0, 0, time.UTC),
				End:   time.Date(2022, time.January, 31, 0, 0, 0, 0, time.UTC),
			},
			kindsToNormalise: []data_platform.NormalizedVehicleStat{
				data_platform.NormalizedVehicleStatGPSLogWithGeography,
				data_platform.NormalizedVehicleStatOdometerLog,
				data_platform.NormalizedVehicleStatSafetyEvents,
			},
			vins: map[data_platform.VIN]rowCountsSummary{
				data_platform.VIN("TE000000000010000"): {
					tspId: "1",
					byKinds: map[data_platform.NormalizedVehicleStat]int{
						data_platform.NormalizedVehicleStatGPSLogWithGeography: 78 * (4 * 7), // 78 GPS points per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatOdometerLog:         2 * (4 * 7),  // 2 odometer points per day for 4 ISO weeks
						data_platform.NormalizedVehicleStatSafetyEvents:        64 * (4 * 7), // 64 safety events points per day for 4 ISO weeks
					},
				},
			},
		},
	}
	testClient := n.harness.Deps.EventsClient.(*event.TestClient)

	for idx := range testCases {
		tc := testCases[idx]
		n.Run(tc.name, func() {
			var vehicles []data_platform.VIN
			for vin := range tc.vins {
				vehicles = append(vehicles, vin)
			}
			message := jobs.RunMillimanScoringMessage{
				PipelineId:          null.StringFrom(uuid.New().String()),
				HandleID:            tc.handleId,
				TSP:                 tc.tsp.String(),
				AllowConfigurations: true,
				Config: &jobs.PipelineConfig{
					OnlyVINs:         vehicles,
					ForceInterval:    &tc.interval,
					KindsToNormalise: tc.kindsToNormalise,
				},
			}

			// run the "milliman scoring" workflow
			scoringWorkflow, err := n.harness.RunMillimanScoringWorkflow(ctx, &message)
			n.Require().NoError(err)
			// increment wall clock by 1 minute every 10 ms
			doneCh := make(chan bool)
			waitCh := make(chan bool)
			go func() {
				timer := time.NewTicker(10 * time.Millisecond)
				defer timer.Stop()
				for {
					select {
					case <-doneCh:
						close(waitCh)
						return
					case <-timer.C:
						n.harness.Deps.Clk.(*clock.Mock).Add(5 * time.Minute)
					}
				}
			}()
			n.Require().NoError(scoringWorkflow.WaitForCompletion(ctx))
			close(doneCh)
			<-waitCh

			n.Require().NoError(n.harness.CheckJobRunStatus(ctx, scoringWorkflow.JobRunId(), jtypes.JobRunStatusSucceeded))

			// verify that pipeline id and info is persisted properly
			pipeline, err := scoringWorkflow.GetPipelineRecord(ctx, message.HandleID)
			n.Require().NoError(err)
			n.Require().Equal(message.PipelineId.String, pipeline.PipelineID)
			n.Require().Equal(message.PipelineId.String, pipeline.R.PipelineInfo.PipelineID)
			n.Require().True(pipeline.FinishedAt.Valid)
			n.Require().Equal(tc.tsp.String(), pipeline.R.PipelineInfo.TSP)

			entities, err := n.providerDispatcher.SyncEntities(ctx, tc.handleId)
			n.Require().Truef(err == nil || errors.IsAny(err, sql.ErrNoRows, data_platform.ErrNotImplemented), "unexpected error: %+v", err)
			expectedVehicles := entities.Vehicles
			expectedDrivers := entities.Drivers
			expectedVehicleGroups := entities.VehicleGroups

			actualVehicles, err := n.harness.Deps.DpApiClient.Vehicles(ctx, tc.handleId)
			n.Require().Truef(err == nil || errors.IsAny(err, sql.ErrNoRows, data_platform.ErrNotImplemented), "unexpected error: %+v", err)
			actualDrivers, err := n.harness.Deps.DpApiClient.Drivers(ctx, tc.handleId)
			n.Require().Truef(err == nil || errors.IsAny(err, sql.ErrNoRows, data_platform.ErrNotImplemented), "unexpected error: %+v", err)
			actualVehicleGroups, err := n.harness.Deps.DpApiClient.VehicleGroups(ctx, tc.handleId)
			n.Require().Truef(err == nil || errors.IsAny(err, sql.ErrNoRows, data_platform.ErrNotImplemented), "unexpected error: %+v", err)

			// everything except CreatedAt and UpdatedAt must match
			for i := range expectedVehicles {
				expectedVehicles[i].CreatedAt = time.Time{}
				expectedVehicles[i].UpdatedAt = time.Time{}
				actualVehicles[i].CreatedAt = time.Time{}
				actualVehicles[i].UpdatedAt = time.Time{}
			}
			n.Require().ElementsMatch(expectedVehicles, actualVehicles)
			for i := range expectedDrivers {
				expectedDrivers[i].CreatedAt = time.Time{}
				expectedDrivers[i].UpdatedAt = time.Time{}
				actualDrivers[i].CreatedAt = time.Time{}
				actualDrivers[i].UpdatedAt = time.Time{}
				expectedDrivers[i].TspCreatedAt = null.Time{}
				actualDrivers[i].TspCreatedAt = null.Time{}
				expectedDrivers[i].TspUpdatedAt = null.Time{}
				actualDrivers[i].TspUpdatedAt = null.Time{}
			}
			n.Require().ElementsMatch(expectedDrivers, actualDrivers)
			for i := range expectedVehicleGroups {
				expectedVehicleGroups[i].CreatedAt = time.Time{}
				expectedVehicleGroups[i].UpdatedAt = time.Time{}
				actualVehicleGroups[i].CreatedAt = time.Time{}
				actualVehicleGroups[i].UpdatedAt = time.Time{}
			}
			n.Require().ElementsMatch(expectedVehicleGroups, actualVehicleGroups)

			handleWorkflow, err := scoringWorkflow.MillimanHandleWorkflow(ctx)
			n.Require().NoError(err)

			// assert that workflow completed successfully
			n.Require().NoError(handleWorkflow.ValidateStatus(ctx, jtypes.JobRunStatusSucceeded))

			// Get all spawned Vehicle workflows from artifactory and assert
			// that its length is equal to the number of vehicles in input params
			spawnedVehicleWorkflows, err := handleWorkflow.VehicleWorkflows(ctx)
			n.Require().NoError(err)
			workflowsByVin := slice_utils.Filter(spawnedVehicleWorkflows, func(wrapper *test_utils.NativeMillimanVehicleWorkflowWrapper) bool {
				return wrapper.InputMessage.VINVehicleIds != nil
			})
			workflowsByTspId := slice_utils.Filter(spawnedVehicleWorkflows, func(wrapper *test_utils.NativeMillimanVehicleWorkflowWrapper) bool {
				return wrapper.InputMessage.VehicleId.Valid
			})
			n.Require().Len(workflowsByVin, len(message.Config.OnlyVINs))
			n.Require().Len(workflowsByTspId, len(workflowsByVin))

			// validate each of the spawned vehicle workflows, comparing their
			// artifacts and compiled stats with expected values
			for _, vinWorkflow := range spawnedVehicleWorkflows {
				n.Require().Equal(jtypes.JobRunStatusSucceeded, vinWorkflow.Status)

				// Verify that stats are persisted properly
				// verify that for each vehicle its stats are properly persisted
				var vid string
				var forTspId bool
				var rowCountsSummary rowCountsSummary
				if vinWorkflow.InputMessage.VINVehicleIds != nil {
					vid = string(vinWorkflow.InputMessage.VINVehicleIds.VIN)
					rowCountsSummary = tc.vins[data_platform.VIN(vid)]
				} else {
					vid = vinWorkflow.InputMessage.VehicleId.String
					forTspId = true
					for _, summary := range tc.vins {
						if summary.tspId == vid {
							rowCountsSummary = summary
						}
					}
				}
				for stat, wantCount := range rowCountsSummary.byKinds {
					weeklyFiles := n.harness.ListWeeklyStatsFiles(
						ctx, message.HandleID, vid, stat.String(), 0, tc.interval.Start.Year(), forTspId,
					)
					currCount := 0
					for _, filePath := range weeklyFiles {
						var versionedObj aws.WriteAtBuffer
						_, err = n.harness.Deps.S3Client.DownloadWithContext(ctx, &versionedObj, &s3.GetObjectInput{
							Bucket: pointer_utils.String("nirvana-telematics-default"),
							Key:    pointer_utils.String(filePath),
						})
						n.Require().NoError(err)

						var items data_platform.ResultStream[data_platform.NormalizedStat]
						items, err := stream.FromCSV(bytes.NewReader(versionedObj.Bytes()), func() data_platform.NormalizedStat {
							switch stat {
							case data_platform.NormalizedVehicleStatGPSLog:
								return new(data_platform.NormalizedGPSLogItem)
							case data_platform.NormalizedVehicleStatOdometerLog:
								return new(data_platform.NormalizedOdometerLogItem)
							case data_platform.NormalizedVehicleStatEngineLog:
								return new(data_platform.NormalizedEngineStateLogItem)
							case data_platform.NormalizedVehicleStatHarshAccelerationEventLog:
								return new(data_platform.NormalizedHarshAccelerationEventItem)
							case data_platform.NormalizedVehicleStatHarshBrakingEventLog:
								return new(data_platform.NormalizedHarshBrakingEventItem)
							case data_platform.NormalizedVehicleStatGPSLogWithSpeedLimits:
								return new(data_platform.NormalizedGPSLogWithSpeedLimits)
							case data_platform.NormalizedVehicleStatGPSLogWithGeography:
								return new(data_platform.NormalizedGPSLogWithGeography)
							case data_platform.NormalizedVehicleStatSafetyEvents:
								return new(data_platform.NormalizedGPSLogWithGeography)
							case data_platform.NormalizedVehicleStatGPSLogWithMaps:
								return new(data_platform.NormalizedGPSLogWithMaps)
							case data_platform.NormalizedVehicleStatGPSLogWithWeather:
								return new(data_platform.NormalizedGPSLogWithWeather)
							case data_platform.NormalizedVehicleStatAccelerometerLog:
								return new(data_platform.NormalizedAccelerometerLogItem)

							default:
								n.FailNowf("unsupported stat kind %s", stat.String())
							}
							return nil
						})
						n.Require().NoError(err)
						itemsSlice, err := stream.GetAll(items)
						n.Require().NoError(err)
						currCount += len(itemsSlice)
					}
					n.Assert().Equalf(wantCount, currCount, "Kind=%s", stat)
				}

			}

			dsw, err := scoringWorkflow.DataScienceWorkflow(ctx)
			n.Require().NoError(err)

			// verify that metaflow task artifact is properly persisted
			dsArt, err := dsw.GetArtifact(ctx)
			n.Require().NoError(err)
			n.Require().False(dsArt.Error.Valid)
			n.Require().Len(dsArt.MetaflowRuns, 1)
			run := dsArt.MetaflowRuns["UWDataFlow"]
			n.Require().Equal("metaflow-mock", run.RunID)
			n.Require().True(run.FinishedAt.Valid)
			n.Require().Equal("FINISHED", run.State)

			// verify that the event was dispatched
			n.Require().Len(
				event.GetAllRecordedEvents(testClient)[event.TelematicsPipelineStatusChangeEvent],
				2*(idx+1),
			)
			events, err := event.GetLastNEventsOfKind[data_platform_events.DataPullingStatusChange](
				testClient,
				event.TelematicsPipelineStatusChangeEvent,
				2,
			)
			n.Require().NoError(err)
			startEvent, endEvent := events[0], events[1]
			comparePayloads(n.T(), startEvent.Payload, data_platform_events.Started, message, pipeline, false)
			comparePayloads(n.T(), endEvent.Payload, data_platform_events.Completed, message, pipeline, false)
		})
	}
}

func comparePayloads(t *testing.T,
	payload data_platform_events.DataPullingStatusChange,
	status data_platform_events.Status,
	msg jobs.RunMillimanScoringMessage,
	model *workflows.PipelineModel,
	isErr bool,
) {
	require.Equal(t, payload.Status, status)
	require.Equal(t, payload.HandleID, msg.HandleID)
	require.Equal(t, payload.PipelineID, msg.PipelineId.String)
	require.Equal(t, payload.PipelineKind, model.Kind)
	require.Equal(t, payload.CompanyName, msg.CompanyName)
	require.Equal(t, payload.Tsp, msg.TSP)
	require.Equal(t, payload.DotNumber, msg.DotNumber)
	require.Equal(t, payload.PolicyState, msg.PolicyState)
	require.Equal(t, payload.Tags, msg.Tags)
	require.Equal(t, payload.Error != "", isErr)
	require.Equal(t, payload.Priority, int(msg.Config.StatsNormalisationConfig.Priority))
}
