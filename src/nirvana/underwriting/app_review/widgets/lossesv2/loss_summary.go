package lossesv2

import (
	"context"
	"database/sql"

	"github.com/cockroachdb/errors"

	"go.uber.org/fx"

	"nirvanatech.com/nirvana/common-go/pointer_utils"
	"nirvanatech.com/nirvana/common-go/slice_utils"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	"nirvanatech.com/nirvana/db-api/db_wrappers/external/pibit"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/underwriting"
	app_review_widgets "nirvanatech.com/nirvana/underwriting/app_review/widgets"
)

type LossSummary struct {
	fx.In

	Deps                  underwriting.ReviewManagerDeps
	ParsedLossRunsWrapper pibit.DataWrapper
}

func (l *LossSummary) Get(ctx context.Context, request GetLossSummaryRequestV2) (*LossSummaryResponse, error) {
	// Step 1: Fetch the app review
	review, err := l.Deps.ApplicationReviewWrapper.GetReview(ctx, request.ApplicationReviewId)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get review %s", request.ApplicationReviewId)
	}

	// Step 2: Fetch the core data and the review object needed for validation.
	summary, err := l.GetCoreSummary(ctx, *review)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get core loss summary for app review %s", request.ApplicationReviewId)
	}

	// Step 3:validate the calculation inputs. This enforces the function's contract.
	if err := validateCalculationInputs(*review, request.CalculationInputs); err != nil {
		return nil, errors.Wrapf(err, "invalid calculation inputs for app review %s", request.ApplicationReviewId)
	}

	// Step 4: apply calculations.
	if err := addLossRatioCalculations(summary, request.CalculationInputs); err != nil {
		return nil, errors.Wrapf(err, "failed to apply loss ratio calculations for app review %s", request.ApplicationReviewId)
	}

	return summary, nil
}

func (l *LossSummary) GetCoreSummary(ctx context.Context, review uw.ApplicationReview) (*LossSummaryResponse, error) {
	// Only validates what it needs to proceed.
	if err := validateReviewPrerequisites(review); err != nil {
		return nil, errors.Wrapf(err, "core prerequisites failed for app review %s", review.Id)
	}

	hasPendingDocs, err := l.hasPendingDocuments(ctx, review)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to check if review has pending documents")
	}

	aggregation, err := l.Deps.ParsedLossRunWrapper.GetLatestAggregationForAppReview(ctx, review.Id)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, errors.Wrapf(err, "unable to get latest aggregation for app review %s", review.Id)
	}

	summary, err := buildCoreSummaries(review.Submission.LossInfo.LossRunSummary, review, aggregation, *hasPendingDocs)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to build core summaries for app review %s", review.Id)
	}

	summary.Meta = pointer_utils.ToPointer(getMetadata(review))

	return summary, nil
}

// buildCoreSummaries is a private helper that contains the core data merging logic.
func buildCoreSummaries(
	agentSummary []application.LossRunSummaryPerCoverage,
	review uw.ApplicationReview,
	aggregation *pibit.Aggregation,
	hasPendingDocuments bool,
) (*LossSummaryResponse, error) {
	overrides := review.Overrides.LossSummary
	filteredOverrides := filterOverrides(overrides)

	var aggregationSummary []pibit.AggregationCoveragePeriodSummary
	if aggregation != nil {
		summary, err := getAggregationSummary(*aggregation)
		if err != nil {
			return nil, errors.Wrapf(err, "unable to get aggregation summary")
		}
		aggregationSummary = summary
	}

	latestCoverageSummaries := make([]CoverageSummary, 0, len(agentSummary))
	for _, agentCoverage := range agentSummary {
		matchingOverridesSummary := findMatchingOverrideCoverage(agentCoverage.CoverageType, filteredOverrides)
		matchingAggregationSummary := findMatchingAggregationCoverage(agentCoverage.CoverageType, aggregationSummary)
		coverageSummary := processCoverageSummary(agentCoverage, matchingOverridesSummary, matchingAggregationSummary, review.Submission.CompanyInfo.ProjectedMileage)
		latestCoverageSummaries = append(latestCoverageSummaries, coverageSummary)
	}

	return &LossSummaryResponse{
		ParsingStatus:           getParsingStatus(hasPendingDocuments),
		LatestCoverageSummaries: latestCoverageSummaries,
	}, nil
}

func (l *LossSummary) Set(ctx context.Context, request UpdateLossSummaryRequestV2) error {
	if request.FormData != nil {
		// 1. Fetch all necessary data.
		review, err := l.Deps.ApplicationReviewWrapper.GetReview(ctx, request.ApplicationReviewId)
		if err != nil {
			return errors.Wrapf(err, "unable to get review %s", request.ApplicationReviewId)
		}

		aggregation, err := l.Deps.ParsedLossRunWrapper.GetLatestAggregationForAppReview(ctx, request.ApplicationReviewId)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return errors.Wrapf(err, "unable to get latest aggregation for app review %s", request.ApplicationReviewId)
		}

		// 2. Calculate the new snapshot, ensuring existing overrides are preserved.
		newOverrides, err := CalculateOverrideSnapshot(
			review.Submission.LossInfo.LossRunSummary,
			review.Overrides.LossSummary, // Pass existing overrides
			aggregation,
			*request.FormData,
		)
		if err != nil {
			return errors.Wrap(err, "failed to calculate new override snapshot")
		}

		// 3. Update the review with the new complete snapshot.
		err = l.Deps.ApplicationReviewWrapper.UpdateAppReview(
			ctx,
			request.ApplicationReviewId,
			func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
				review.Overrides.LossSummary = pointer_utils.ToPointer(newOverrides)
				return review, nil
			},
		)
		if err != nil {
			return errors.Wrapf(err, "failed to update overrides for app review %s", request.ApplicationReviewId)
		}
	}

	return updateMetadata(ctx, request.ApplicationReviewId, request.Meta, l.Deps.ApplicationReviewWrapper)
}

// RecomputeOverrides orchestrates the re-computation of loss run overrides when new aggregation data is available.
func RecomputeOverrides(review uw.ApplicationReview, aggregation pibit.Aggregation) ([]*application.LossRunSummaryPerCoverage, error) {
	agentSummaries := review.Submission.LossInfo.LossRunSummary
	currentOverrides := review.Overrides.LossSummary

	recomputedSnapshot, err := CalculateOverrideSnapshot(
		agentSummaries,
		currentOverrides,
		&aggregation,
		UpdateLossSummaryFormData{}, // No new form input. Just recomputation.
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to recompute override snapshot on new aggregation")
	}

	return recomputedSnapshot, nil
}

func (l *LossSummary) hasPendingDocuments(ctx context.Context, review uw.ApplicationReview) (*bool, error) {
	submissions, err := l.Deps.ApplicationWrapper.GetSubmissionsByAppId(ctx, review.ApplicationID)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get submissions for app %s", review.ApplicationID)
	}
	submissionIds := slice_utils.Map(submissions, func(submission application.SubmissionObject) string {
		return submission.ID
	})
	documents, err := l.ParsedLossRunsWrapper.GetDocumentsBySubmissionIds(ctx, submissionIds)
	if err != nil {
		return nil, errors.Wrapf(err, "unable to get documents for submissions %v", submissionIds)
	}
	isPending := slice_utils.Any(documents, func(document pibit.Document) bool {
		return document.Status == pibit.DocumentStatusSent
	})
	return &isPending, nil
}

func getMetadata(review uw.ApplicationReview) app_review_widgets.InternalWidgetMeta {
	res := app_review_widgets.InternalWidgetMeta{}
	res.Credit = &review.ReviewInfo.Losses.LossSummary.Credit
	res.Merit = &review.ReviewInfo.Losses.LossSummary.Merit
	if review.CoverageReview.AutoLiability != nil {
		res.AutoLiability = &app_review_widgets.InternalWidgetCoverageMeta{
			Credit: &review.CoverageReview.AutoLiability.ReviewInfo.Losses.LossSummary.Credit,
		}
	}
	if review.CoverageReview.AutoPhysicalDamage != nil {
		res.AutoPhysicalDamage = &app_review_widgets.InternalWidgetCoverageMeta{
			Credit: &review.CoverageReview.AutoPhysicalDamage.ReviewInfo.Losses.LossSummary.Credit,
		}
	}
	if review.CoverageReview.GeneralLiability != nil {
		res.GeneralLiability = &app_review_widgets.InternalWidgetCoverageMeta{
			Credit: &review.CoverageReview.GeneralLiability.ReviewInfo.Losses.LossSummary.Credit,
		}
	}
	if review.CoverageReview.MotorTruckCargo != nil {
		res.MotorTruckCargo = &app_review_widgets.InternalWidgetCoverageMeta{
			Credit: &review.CoverageReview.MotorTruckCargo.ReviewInfo.Losses.LossSummary.Credit,
		}
	}
	return res
}

func updateMetadata(ctx context.Context, reviewId string, meta *app_review_widgets.InternalWidgetMeta, appReviewWrapper uw.ApplicationReviewWrapper) error {
	if meta != nil {
		err := appReviewWrapper.UpdateAppReview(ctx,
			reviewId,
			func(review uw.ApplicationReview) (uw.ApplicationReview, error) {
				if meta.Merit != nil {
					review.ReviewInfo.Losses.LossSummary.Merit = *meta.Merit
				}
				if meta.Credit != nil {
					review.ReviewInfo.Losses.LossSummary.Credit = *meta.Credit
					review.CoverageReview.AutoLiability.ReviewInfo.Losses.LossSummary.Credit = *meta.Credit
				}
				if meta.AutoLiability != nil && meta.AutoLiability.Credit != nil && review.CoverageReview.AutoLiability != nil {
					review.CoverageReview.AutoLiability.ReviewInfo.Losses.LossSummary.Credit = *meta.AutoLiability.Credit
				}
				if meta.AutoPhysicalDamage != nil && meta.AutoPhysicalDamage.Credit != nil && review.CoverageReview.AutoPhysicalDamage != nil {
					review.CoverageReview.AutoPhysicalDamage.ReviewInfo.Losses.LossSummary.Credit = *meta.AutoPhysicalDamage.Credit
				}
				if meta.GeneralLiability != nil && meta.GeneralLiability.Credit != nil && review.CoverageReview.GeneralLiability != nil {
					review.CoverageReview.GeneralLiability.ReviewInfo.Losses.LossSummary.Credit = *meta.GeneralLiability.Credit
				}
				if meta.MotorTruckCargo != nil && meta.MotorTruckCargo.Credit != nil && review.CoverageReview.MotorTruckCargo != nil {
					review.CoverageReview.MotorTruckCargo.ReviewInfo.Losses.LossSummary.Credit = *meta.MotorTruckCargo.Credit
				}
				review.ReviewInfo = uw.UpdateAggregateMeritAndCredit(review.ReviewInfo)
				review.CoverageReview = uw.UpdateAggregateMeritAndCreditPerCoverage(review.CoverageReview,
					review.PricingExperimentsInfo)
				return review, nil
			},
		)
		if err != nil {
			return errors.Wrapf(err, "unable to update review info for review %s", reviewId)
		}
	}
	return nil
}
