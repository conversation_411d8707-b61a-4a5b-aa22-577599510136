package lossesv2

import (
	"time"

	app_review_widgets "nirvanatech.com/nirvana/underwriting/app_review/widgets"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application"

	"nirvanatech.com/nirvana/db-api/db_wrappers/application/enums"
)

type GetLossSummaryRequestV2 struct {
	ApplicationReviewId string
	CalculationInputs   []CalculationInputs
}

type CalculationInputs struct {
	CoverageType          enums.Coverage
	RequestedPremiumPerPU float64
	PreClaimDeductible    float64
}

type LossSummaryResponse struct {
	ParsingStatus           ParsingStatus
	LatestCoverageSummaries []CoverageSummary
	Meta                    *app_review_widgets.InternalWidgetMeta
}

// LossRatioAggregate represents the loss ratio for a single, specific time period.
type LossRatioAggregate struct {
	PeriodLabel  string
	ValuePercent float32
}

type CoverageSummary struct {
	Averages            LossRunAverages
	CoverageType        enums.Coverage
	LossRatioAggregates []LossRatioAggregate
	Summary             []LossSummaryRecord
}

// LossValue represents a metric that includes the original value, its source,
// and an optional manual override.
type LossValue struct {
	Value       float32
	ValueSource application.LossRunValueSource
	Override    *float32 // A pointer is used to represent an optional override.
}

// LossSummaryRecord is the internal model for a single loss summary period.
type LossSummaryRecord struct {
	GrossLoss          LossValue
	LossRatioPercent   *float32
	NumberOfClaims     LossValue
	NumberOfPowerUnits LossValue
	PeriodStartDate    time.Time
	PeriodEndDate      time.Time
	Tags               []LossSummaryTag
}

// LossFrequency represents the frequency of losses, either per million miles or per unit.
type LossFrequency struct {
	PerMillionMiles float32
	PerUnit         float32
}

// LossRunAverages is the internal model for displaying loss run average data.
type LossRunAverages struct {
	AverageBurnRate  float32
	AverageClaimSize float32
	LossFrequency    LossFrequency
}

type UpdateLossSummaryRequestV2 struct {
	ApplicationReviewId string
	FormData            *UpdateLossSummaryFormData
	Meta                *app_review_widgets.InternalWidgetMeta
}

type UpdateLossSummaryFormData struct {
	CoverageType enums.Coverage
	Summary      []UpdatedSummary
}

type UpdatedSummary struct {
	PeriodStartDate            time.Time
	PeriodEndDate              time.Time
	GrossLossOverride          *float32
	NumberOfClaimsOverride     *float32
	NumberOfPowerUnitsOverride *float32
}

//go:generate go run github.com/dmarkham/enumer -type=ParsingStatus -json
type ParsingStatus int

const (
	ParsingStatusProcessing ParsingStatus = iota
	ParsingStatusValidated
)

//go:generate go run github.com/dmarkham/enumer -type=LossSummaryTag -json
type LossSummaryTag int

const (
	LossSummaryTagFileMissing LossSummaryTag = iota
	LossSummaryTagFileOutOfDate
)
