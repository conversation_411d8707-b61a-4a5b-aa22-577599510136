// Package authority provides a system for managing underwriting authority requests
// in Nirvana's fleet underwriting platform. It handles the complete lifecycle of
// authority requests including creation, validation, review, approval/rejection, and execution.
//
// Authority requests are formal requests made during the underwriting process when a decision
// requires approval from someone with higher authority levels. The system manages different
// types of requests through a pluggable handler system.
//
// Requests follow a simplified state machine:
//   - pending: Request is awaiting review by an underwriting manager
//   - declined: Request was declined by manager with feedback (manager_note)
//   - approved: Request was approved and successfully executed
//
// State transitions:
//   - pending → approved: Manager approves and executes the request
//   - pending → declined: Manager declines with feedback note
//   - declined → approved: Manager approves after review, executing the decline action
//
// The package uses a handler pattern where each request type has a dedicated handler
// implementing the RequestHandler interface.
package authority

import (
	"context"
	"database/sql"
	"encoding/json"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	authorities_db_models "nirvanatech.com/nirvana/db-api/db_models/authorities"
	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	"nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"
	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
	"nirvanatech.com/nirvana/underwriting/authority/types"
)

// ManagerImpl implements the Manager interface for managing authority requests throughout their lifecycle
type ManagerImpl struct {
	dbWrapper        authorities_wrapper.AuthorityRequestWrapper
	appReviewWrapper uw.ApplicationReviewWrapper
	handlers         map[enums.RequestType]RequestHandler
}

// NewManager creates a new authorities request manager with the provided dependencies
func NewManager(d deps) *ManagerImpl {
	return &ManagerImpl{
		dbWrapper:        d.DBWrapper,
		appReviewWrapper: d.ApplicationReviewWrapper,
		handlers:         make(map[enums.RequestType]RequestHandler),
	}
}

// RegisterHandler registers a handler for a specific request type
func (m *ManagerImpl) RegisterHandler(reqType enums.RequestType, handler RequestHandler) {
	m.handlers[reqType] = handler
}

// validateRequester validates user authentication and returns their user ID
func (m *ManagerImpl) validateRequester(ctx context.Context) (uuid.UUID, error) {
	user := authz.UserFromContext(ctx)
	if user.ID == uuid.Nil {
		return uuid.Nil, errors.New("user not authenticated")
	}
	return user.ID, nil
}

// validateAndGetHandler validates request and returns the appropriate handler
func (m *ManagerImpl) validateAndGetHandler(ctx context.Context, applicationReviewID uuid.UUID, requestData RequestPayload) (RequestHandler, enums.RequestType, error) {
	// Get the request type from the payload
	requestType := requestData.GetRequestType()

	// Get the appropriate handler
	handler, exists := m.handlers[requestType]
	if !exists {
		return nil, requestType, errors.Newf("unsupported request type: %s", requestType)
	}

	// Check permissions
	canCreate, err := handler.CanCreate(ctx, applicationReviewID)
	if err != nil {
		return nil, requestType, errors.Wrap(err, "error checking create permissions")
	}
	if !canCreate {
		return nil, requestType, errors.New("permission denied for creating request")
	}

	// Validate the payload
	_, err = handler.ValidatePayload(requestData)
	if err != nil {
		return nil, requestType, errors.Wrap(err, "invalid request data")
	}

	return handler, requestType, nil
}

// checkExistingPendingRequests checks if there are any existing pending requests
// for the same application review and returns an error if found
func (m *ManagerImpl) checkExistingPendingRequests(ctx context.Context, applicationReviewID uuid.UUID, requestType enums.RequestType) error {
	// Check for existing pending requests of the same type
	existingRequests, err := m.dbWrapper.GetRequests(ctx,
		authorities_db_models.RequestWhere.ApplicationReviewID.EQ(applicationReviewID.String()),
		authorities_db_models.RequestWhere.RequestType.EQ(requestType.String()),
		authorities_db_models.RequestWhere.State.EQ(enums.RequestStatePending.String()),
	)
	if err != nil {
		return errors.Wrap(err, "failed to check for existing requests")
	}

	// If there are existing pending requests, return an error
	if len(existingRequests) > 0 {
		return errors.Newf("authority request already exists for application review %s with request ID %s",
			applicationReviewID.String(), existingRequests[0].ID)
	}

	return nil
}

// createAuthorityRequest creates and saves a new authority request to the database
func (m *ManagerImpl) createAuthorityRequest(ctx context.Context, applicationReviewID uuid.UUID, requestType enums.RequestType, requestData RequestPayload, requesterID uuid.UUID) (*uuid.UUID, error) {
	// Convert request data to JSON for storage
	// Since requestData now implements RequestPayload, we can directly marshal it
	requestDataJSON, err := json.Marshal(requestData)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal request data")
	}

	// Create the new request (v1: directly to pending state, skipping draft)
	now := time.Now()
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         requestType,
		State:               enums.RequestStatePending,
		RequestData:         null.JSONFrom(requestDataJSON),
		RequesterID:         requesterID,
		CreatedAt:           now,
		UpdatedAt:           now,
		SubmittedAt:         null.TimeFrom(now),
	}

	// Save to database
	savedRequest, err := m.dbWrapper.CreateRequest(ctx, request)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create request")
	}

	return &savedRequest.ID, nil
}

// CreateRequest creates a new authorities request and transitions it to pending state.
// Returns an error if a pending request already exists for the same application review
func (m *ManagerImpl) CreateRequest(
	ctx context.Context,
	applicationReviewID uuid.UUID,
	requestData RequestPayload,
) (*uuid.UUID, error) {
	// Validate the requester
	requesterID, err := m.validateRequester(ctx)
	if err != nil {
		return nil, err
	}

	// Validate request and get handler
	_, requestType, err := m.validateAndGetHandler(ctx, applicationReviewID, requestData)
	if err != nil {
		return nil, err
	}

	// Check if a pending request already exists
	if err := m.checkExistingPendingRequests(ctx, applicationReviewID, requestType); err != nil {
		return nil, err
	}

	// Create the new authority request
	return m.createAuthorityRequest(ctx, applicationReviewID, requestType, requestData, requesterID)
}

// extractAuthorityInfo retrieves authority information for the given application review and action
func (m *ManagerImpl) extractAuthorityInfo(ctx context.Context, applicationReviewID uuid.UUID, action authority_enums.ReviewAction) (*types.AuthorityInfo, error) {
	// Use the fleet extractor for now - this can be made configurable in the future
	// when non-fleet support is added
	extractor := NewFleetAuthorityInfoExtractor(m.appReviewWrapper)
	return extractor.Extract(ctx, applicationReviewID, action)
}

// extractReviewerLevel extracts the reviewer's authority level from their role groups
func (m *ManagerImpl) extractReviewerLevel(user authz.User) int32 {
	// Get the highest underwriter authority role
	highestRole := authz.HighestUnderwriterAuthorityLevel(user.RoleGroups())

	// Convert role to authority level (1-7, or 0 if invalid)
	authorityLevel := authz.FetchAuthorityIntFromRole(highestRole)

	return int32(authorityLevel)
}

// validateReviewer validates reviewer authentication and returns their user ID
func (m *ManagerImpl) validateReviewer(ctx context.Context) (uuid.UUID, error) {
	user := authz.UserFromContext(ctx)
	if user.ID == uuid.Nil {
		return uuid.Nil, errors.New("user not authenticated")
	}
	return user.ID, nil
}

// validateRequestState validates that a request is in a valid state for processing
func (m *ManagerImpl) validateRequestState(request *authorities_wrapper.AuthorityRequest) error {
	// Allow processing of pending and declined requests
	if request.State != enums.RequestStatePending && request.State != enums.RequestStateDeclined {
		return errors.Newf("request is not in valid state for processing, current state: %s", request.State)
	}
	return nil
}

// prepareReviewData creates structured review data with authority information
// extracted from the application review context
func (m *ManagerImpl) prepareReviewData(
	ctx context.Context,
	request *authorities_wrapper.AuthorityRequest,
	action authority_enums.ReviewAction,
	notes string,
	reviewerID uuid.UUID,
	authorityInfo *types.AuthorityInfo,
) (*types.ReviewData, error) {
	// Get reviewer level from context
	user := authz.UserFromContext(ctx)
	reviewerLevel := m.extractReviewerLevel(user)

	// Create review data with extracted authority information
	reviewData := types.NewReviewData(
		action,
		notes,
		reviewerID,
		reviewerLevel,
		authorityInfo.MinLevelRequired,
	)

	// Add risk type level requirements
	for riskType, minLevel := range authorityInfo.MinLevelPerRiskType {
		reviewData.AddRiskTypeLevelRequirement(riskType, minLevel)
	}

	// Add risk factors
	for _, riskFactor := range authorityInfo.RiskFactors {
		reviewData.AddRiskFactor(riskFactor)
	}

	return reviewData, nil
}

// processApproval handles the approval workflow including execution and state updates
func (m *ManagerImpl) processApproval(
	ctx context.Context,
	handler RequestHandler,
	request *authorities_wrapper.AuthorityRequest,
	reviewData *types.ReviewData,
	reviewerID uuid.UUID,
	action authority_enums.ReviewAction,
	notes string,
) error {
	// Validate state transition: can approve from pending or declined state
	if request.State != enums.RequestStatePending && request.State != enums.RequestStateDeclined {
		return errors.Newf("invalid state transition: cannot approve request in state %s", request.State)
	}

	// For approval, first try to execute the action
	if err := handler.Execute(ctx, request); err != nil {
		// Execution failed - rollback by keeping request in pending state
		return errors.Wrap(err, "failed to execute approved request - request remains pending")
	}

	// Execution succeeded - mark as executed
	reviewData.SetExecutedAt(time.Now())

	_, err := m.dbWrapper.UpdateRequest(ctx, request.ID,
		func(req *authorities_wrapper.AuthorityRequest) (*authorities_wrapper.AuthorityRequest, *authorities_wrapper.StateTransitionInfo, error) {
			// Update review fields
			req.LastReviewedBy = null.StringFrom(reviewerID.String())
			req.LastReviewedAt = null.TimeFrom(reviewData.ReviewedAt)
			reviewDataBytes, err := json.Marshal(reviewData)
			if err != nil {
				return nil, nil, errors.Wrap(err, "failed to marshal review data")
			}
			req.ReviewData = null.JSONFrom(reviewDataBytes)

			// Update to executed state
			// Approved state represents executed state in the simplified model
			req.State = enums.RequestStateApproved
			req.UpdatedAt = time.Now()
			req.ExecutedAt = null.TimeFrom(*reviewData.ExecutedAt)

			// Prepare state transition info with concrete metadata
			approvalMetadata := types.NewApprovalMetadata(reviewerID, action, notes, enums.RequestStateApproved)
			transitionInfo := &authorities_wrapper.StateTransitionInfo{
				Metadata:   approvalMetadata,
				ReviewerID: &reviewerID,
			}

			return req, transitionInfo, nil
		},
	)
	if err != nil {
		// This is a critical error - execution succeeded but state update failed
		// Log this for manual intervention
		return errors.Wrap(err, "CRITICAL: execution succeeded but failed to update request state - manual intervention required")
	}

	return nil
}

// processRejection handles the rejection workflow and state updates
// When a manager rejects, it sets the state to Declined with manager_note
func (m *ManagerImpl) processRejection(
	ctx context.Context,
	requestID uuid.UUID,
	reviewData *types.ReviewData,
	reviewerID uuid.UUID,
	action authority_enums.ReviewAction,
	notes string,
) error {
	// Add manager note to the review data
	reviewData.ManagerNote = notes

	_, err := m.dbWrapper.UpdateRequest(ctx, requestID,
		func(req *authorities_wrapper.AuthorityRequest) (*authorities_wrapper.AuthorityRequest, *authorities_wrapper.StateTransitionInfo, error) {
			// Validate state transition: can only decline from pending state
			if req.State != enums.RequestStatePending {
				return nil, nil, errors.Newf("invalid state transition: cannot decline request in state %s", req.State)
			}

			// Update review fields
			req.LastReviewedBy = null.StringFrom(reviewerID.String())
			req.LastReviewedAt = null.TimeFrom(reviewData.ReviewedAt)
			reviewDataBytes, err := json.Marshal(reviewData)
			if err != nil {
				return nil, nil, errors.Wrap(err, "failed to marshal review data")
			}
			req.ReviewData = null.JSONFrom(reviewDataBytes)

			// Update to declined state
			req.State = enums.RequestStateDeclined
			req.UpdatedAt = time.Now()

			// Prepare state transition info with concrete metadata
			rejectionMetadata := types.NewRejectionMetadata(reviewerID, action, notes, enums.RequestStateDeclined)
			transitionInfo := &authorities_wrapper.StateTransitionInfo{
				Metadata:   rejectionMetadata,
				ReviewerID: &reviewerID,
			}

			return req, transitionInfo, nil
		},
	)
	if err != nil {
		return errors.Wrap(err, "failed to reject request")
	}

	return nil
}

// ProcessRequest reviews and executes a request in a single atomic operation.
// For approved requests, if execution fails, the entire operation is rolled back
// and the request remains in pending state to allow retry
func (m *ManagerImpl) ProcessRequest(
	ctx context.Context,
	requestID uuid.UUID,
	action authority_enums.ReviewAction,
	notes string,
) error {
	// Validate reviewer authentication
	reviewerID, err := m.validateReviewer(ctx)
	if err != nil {
		return err
	}

	// Fetch the request
	request, err := m.dbWrapper.GetRequest(ctx, requestID)
	if err != nil {
		// If it's sql.ErrNoRows, return it unwrapped so the handler can detect it
		if errors.Is(err, sql.ErrNoRows) {
			return err
		}
		return errors.Wrap(err, "request not found")
	}

	// Validate request state
	if err := m.validateRequestState(request); err != nil {
		return err
	}

	// Get the handler for this request type
	handler, exists := m.handlers[request.RequestType]
	if !exists {
		return errors.Newf("no handler for request type: %s", request.RequestType)
	}

	// Check review permissions
	canReview, err := handler.CanReview(ctx)
	if err != nil {
		return errors.Wrap(err, "error checking review permissions")
	}
	if !canReview {
		return errors.New("user does not have underwriter manager access")
	}

	// Extract authority information for this action
	authorityInfo, err := m.extractAuthorityInfo(ctx, request.ApplicationReviewID, action)
	if err != nil {
		return errors.Wrap(err, "failed to extract authority information")
	}

	// Note: Authority level checks removed - only managers can approve/reject requests
	// The required authority levels are still extracted and stored in ReviewData
	// for future enhancement, but are not used for authorization

	// Prepare review data
	reviewData, err := m.prepareReviewData(ctx, request, action, notes, reviewerID, authorityInfo)
	if err != nil {
		return errors.Wrap(err, "failed to prepare review data")
	}

	// Process based on action type
	// Allow transitions from: Pending → Approved/Declined
	// Also allow from: Declined → Approved (manager can approve after providing feedback)
	switch request.State {
	case enums.RequestStatePending:
		// From pending, can approve or decline
		switch action {
		case authority_enums.ReviewActionApprove:
			return m.processApproval(ctx, handler, request, reviewData, reviewerID, action, notes)
		case authority_enums.ReviewActionReject:
			return m.processRejection(ctx, requestID, reviewData, reviewerID, action, notes)
		default:
			return errors.Newf("invalid review action: %s", action)
		}
	case enums.RequestStateDeclined:
		// From declined (with manager note), can only approve to execute the decline
		if action == authority_enums.ReviewActionApprove {
			return m.processApproval(ctx, handler, request, reviewData, reviewerID, action, notes)
		}
		return errors.New("can only approve a declined request to execute the decline action")
	case enums.RequestStateApproved:
		return errors.New("cannot process an already approved request")
	default:
		return errors.Newf("invalid request state: %s", request.State)
	}
}

// GetRequestsForApplication retrieves all authority requests for the specified
// application review, ordered by creation time (most recent first)
func (m *ManagerImpl) GetRequestsForApplication(
	ctx context.Context,
	applicationReviewID uuid.UUID,
) ([]*authorities_wrapper.AuthorityRequest, error) {
	requests, err := m.dbWrapper.GetRequests(ctx,
		authorities_db_models.RequestWhere.ApplicationReviewID.EQ(applicationReviewID.String()),
		qm.OrderBy(authorities_db_models.RequestColumns.CreatedAt+" DESC"),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get requests for application")
	}

	return requests, nil
}

// GetLatestRequestForApplication retrieves the most recent authority request
// for the specified application review
func (m *ManagerImpl) GetLatestRequestForApplication(
	ctx context.Context,
	applicationReviewID uuid.UUID,
) (*authorities_wrapper.AuthorityRequest, error) {
	// Get the latest request (limit 1, ordered by creation time desc)
	requests, err := m.dbWrapper.GetRequests(ctx,
		authorities_db_models.RequestWhere.ApplicationReviewID.EQ(applicationReviewID.String()),
		qm.OrderBy(authorities_db_models.RequestColumns.CreatedAt+" DESC"),
		qm.Limit(1),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get latest request for application")
	}

	if len(requests) == 0 {
		return nil, sql.ErrNoRows
	}

	return requests[0], nil
}

// GetRequest retrieves a specific authority request by its ID
func (m *ManagerImpl) GetRequest(ctx context.Context, requestID uuid.UUID) (*authorities_wrapper.AuthorityRequest, error) {
	request, err := m.dbWrapper.GetRequest(ctx, requestID)
	if err != nil {
		// If it's sql.ErrNoRows, return it unwrapped so the handler can detect it
		if errors.Is(err, sql.ErrNoRows) {
			return nil, err
		}
		return nil, errors.Wrap(err, "failed to get request")
	}

	return request, nil
}

// transitionRequestState safely transitions a request from one state to another
// with validation and metadata recording
func (m *ManagerImpl) transitionRequestState(
	ctx context.Context,
	requestID uuid.UUID,
	fromState enums.RequestState,
	toState enums.RequestState,
	metadata authorities_wrapper.StateTransitionMetadata,
) error {
	_, err := m.dbWrapper.UpdateRequest(ctx, requestID,
		func(req *authorities_wrapper.AuthorityRequest) (*authorities_wrapper.AuthorityRequest, *authorities_wrapper.StateTransitionInfo, error) {
			// Verify current state
			if req.State != fromState {
				return nil, nil, errors.Newf("invalid state transition: expected %s, got %s", fromState, req.State)
			}

			// Update state
			req.State = toState
			req.UpdatedAt = time.Now()

			// Prepare transition info
			transitionInfo := &authorities_wrapper.StateTransitionInfo{
				Metadata: metadata,
			}

			return req, transitionInfo, nil
		},
	)

	return err
}

// GetRequestsByState retrieves all authority requests in the specified state,
// ordered by creation time (most recent first)
func (m *ManagerImpl) GetRequestsByState(
	ctx context.Context,
	state enums.RequestState,
) ([]*authorities_wrapper.AuthorityRequest, error) {
	requests, err := m.dbWrapper.GetRequests(ctx,
		authorities_db_models.RequestWhere.State.EQ(state.String()),
		qm.OrderBy(authorities_db_models.RequestColumns.CreatedAt+" DESC"),
	)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get requests by state")
	}

	return requests, nil
}
