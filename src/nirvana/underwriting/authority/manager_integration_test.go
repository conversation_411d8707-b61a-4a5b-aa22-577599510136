package authority_test

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/cockroachdb/errors"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/volatiletech/null/v8"
	"go.uber.org/fx"
	"go.uber.org/fx/fxtest"

	"nirvanatech.com/nirvana/common-go/feature_flag_lib"
	"nirvanatech.com/nirvana/common-go/test_utils/builders"
	"nirvanatech.com/nirvana/db-api/db_wrappers/application"
	authorities_wrapper "nirvanatech.com/nirvana/db-api/db_wrappers/authorities"
	"nirvanatech.com/nirvana/db-api/db_wrappers/authorities/enums"
	uw "nirvanatech.com/nirvana/db-api/db_wrappers/uw"
	"nirvanatech.com/nirvana/infra/authz"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_fixture"
	"nirvanatech.com/nirvana/infra/fx/testfixtures/application_review_fixture"
	"nirvanatech.com/nirvana/infra/fx/testloader"
	app_state_enums "nirvanatech.com/nirvana/quoting/app_state_machine/enums"
	authority "nirvanatech.com/nirvana/underwriting/authority"
	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
	"nirvanatech.com/nirvana/underwriting/authority/types"
)

func TestAuthorityManagerSuite(t *testing.T) {
	suite.Run(t, new(AuthorityManagerTestSuite))
}

type AuthorityManagerTestSuite struct {
	suite.Suite
	ctx               context.Context
	fxApp             *fxtest.App
	manager           authority.Manager
	dbWrapper         authorities_wrapper.AuthorityRequestWrapper
	appReviewWrapper  uw.ApplicationReviewWrapper
	appWrapper        application.DataWrapper
	featureFlagClient feature_flag_lib.Client
	appFixture        *application_fixture.ApplicationsFixture
	appReviewFixture  *application_review_fixture.ApplicationReviewsFixture
}

func (s *AuthorityManagerTestSuite) SetupTest() {
	var env struct {
		fx.In
		Manager           authority.Manager
		DBWrapper         authorities_wrapper.AuthorityRequestWrapper
		AppReviewWrapper  uw.ApplicationReviewWrapper
		AppWrapper        application.DataWrapper
		FeatureFlagClient feature_flag_lib.Client
		AppFixture        *application_fixture.ApplicationsFixture
		AppReviewFixture  *application_review_fixture.ApplicationReviewsFixture
	}

	s.ctx = context.Background()
	s.fxApp = testloader.RequireStart(s.T(), &env)
	s.manager = env.Manager
	s.dbWrapper = env.DBWrapper
	s.appReviewWrapper = env.AppReviewWrapper
	s.appWrapper = env.AppWrapper
	s.featureFlagClient = env.FeatureFlagClient
	s.appFixture = env.AppFixture
	s.appReviewFixture = env.AppReviewFixture
}

func (s *AuthorityManagerTestSuite) TearDownTest() {
	s.fxApp.RequireStop()
}

// createManagerContext creates a context with a user that has manager privileges
// This helper ensures consistent setup for tests that require manager authorization
func (s *AuthorityManagerTestSuite) createManagerContext(userID uuid.UUID) context.Context {
	return authz.WithUser(s.ctx, authz.User{
		UserInfo: authz.UserInfo{
			ID:    userID,
			Email: "<EMAIL>",
		},
		Roles: []authz.Role{
			{Group: authz.UnderwriterManagerRole, Domain: "*"},
			{Group: authz.Level3UnderwriterRole, Domain: "*"},
			{Group: authz.SuperuserRole, Domain: "*"}, // Ensure authorization succeeds
		},
	})
}

// createUnderwriterContext creates a context with a user that has underwriter privileges
func (s *AuthorityManagerTestSuite) createUnderwriterContext(userID uuid.UUID) context.Context {
	return authz.WithUser(s.ctx, authz.User{
		UserInfo: authz.UserInfo{
			ID:    userID,
			Email: "<EMAIL>",
		},
		Roles: []authz.Role{
			{Group: authz.Level3UnderwriterRole, Domain: "*"},
		},
	})
}

// createTestApplicationReview creates a proper application review using fixtures
func (s *AuthorityManagerTestSuite) createTestApplicationReview() uuid.UUID {
	// Get the fixture data
	fromReview := s.appReviewFixture.ApplicationReview
	fromApp := s.appFixture.Application

	// Create a new application first
	newAppID := uuid.New()
	newShortID := builders.GenerateShortIDFromUUID(newAppID)
	fromApp.ID = newAppID.String()
	fromApp.ShortID = newShortID
	fromApp.DataContextID = uuid.New()
	err := s.appWrapper.InsertApp(s.ctx, *fromApp)
	s.Require().NoError(err)

	// Create a new application review
	newReviewID := uuid.New().String()
	fromReview.Id = newReviewID
	fromReview.ApplicationID = newAppID.String()
	fromReview.State = uw.ApplicationReviewStatePending // Set to pending state for testing
	err = s.appReviewWrapper.CreateReview(s.ctx, *fromReview)
	s.Require().NoError(err, "Failed to create test application review")

	reviewUUID, err := uuid.Parse(newReviewID)
	s.Require().NoError(err)

	return reviewUUID
}

// Helper function to create a test decline reason with the new structure
func createTestDeclineReason(reasonId, reasonText, subReasonId, subReasonText, externalNote string) types.DeclineReason {
	category := "Guideline"
	return types.DeclineReason{
		Reason:         reasonText,
		ReasonId:       reasonId,
		SubReason:      subReasonText,
		SubReasonId:    subReasonId,
		ExternalNote:   externalNote,
		ReasonCategory: &category,
	}
}

func (s *AuthorityManagerTestSuite) TestCreateRequest_ValidDeclineRequest() {
	// Setup test data - create a test application review
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()

	// Create decline request payload using concrete struct
	declineReasons := []types.DeclineReason{
		createTestDeclineReason(
			"LH001",
			"Loss History",
			"LH001-01",
			"High frequency of claims",
			"Business has been operating for less than 3 years with significant loss history in the past 24 months",
		),
		createTestDeclineReason(
			"SR001",
			"Safety Record",
			"SR001-01",
			"Multiple violations",
			"Poor safety record with multiple violations and failed inspections",
		),
	}
	targetPrice := 15000.0
	noQuoteExpl := "The risk profile exceeds our underwriting guidelines due to the combination of new venture status and poor loss history. No premium adjustment can adequately compensate for the risk."
	
	requestData := &types.DeclineRequestPayload{
		DeclineReasons:     &declineReasons,
		TargetQuotePrice:   &targetPrice,
		NoQuoteExplanation: &noQuoteExpl,
	}

	// Create request with user in context using the helper method
	ctxWithUser := s.createUnderwriterContext(requesterID)

	requestID, err := s.manager.CreateRequest(
		ctxWithUser,
		applicationReviewID,
		requestData,
	)

	// Verify request was created
	s.Assert().NoError(err)
	s.Assert().NotNil(requestID)

	// Fetch the created request to verify
	request, err := s.dbWrapper.GetRequest(s.ctx, *requestID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStatePending, request.State)
	s.Assert().Equal(applicationReviewID, request.ApplicationReviewID)
	s.Assert().Equal(requesterID, request.RequesterID)
	s.Assert().True(request.SubmittedAt.Valid)

	// Verify state transition was recorded
	transitions, err := s.dbWrapper.GetStateTransitions(s.ctx, *requestID)
	s.Require().NoError(err)
	s.Assert().Len(transitions, 1)
	s.Assert().Equal(enums.RequestStateInvalid, transitions[0].FromState) // Invalid represents no previous state for new requests
	s.Assert().Equal(enums.RequestStatePending, transitions[0].ToState)
}

func (s *AuthorityManagerTestSuite) TestCreateRequest_InvalidPayload() {
	// Setup test data - create a test application review
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()

	// Create invalid decline request payload (missing required fields)
	emptyReasons := []types.DeclineReason{} // Empty array should fail validation
	requestData := &types.DeclineRequestPayload{
		DeclineReasons: &emptyReasons,
		// Missing TargetQuotePrice and NoQuoteExplanation
	}

	// Attempt to create request with user in context using the helper method
	ctxWithUser := s.createUnderwriterContext(requesterID)

	requestID, err := s.manager.CreateRequest(
		ctxWithUser,
		applicationReviewID,
		requestData,
	)

	// Should fail due to invalid payload
	s.Assert().Error(err)
	s.Assert().Nil(requestID)
	s.Assert().Contains(err.Error(), "invalid request data")
}

func (s *AuthorityManagerTestSuite) TestCreateRequest_FailsWhenDuplicateExists() {
	// Create a test application review
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()

	// Create valid request data
	category := "Guideline"
	reasons := []types.DeclineReason{
		{
			Reason:         "Loss History",
			ReasonId:       "LH001",
			SubReason:      "High frequency of claims",
			SubReasonId:    "LH001-01",
			ExternalNote:   "Business has been operating for less than 3 years",
			ReasonCategory: &category,
		},
	}
	targetPrice := 15000.0
	noQuoteExpl := "Risk exceeds acceptable parameters"
	requestData := &types.DeclineRequestPayload{
		DeclineReasons:     &reasons,
		TargetQuotePrice:   &targetPrice,
		NoQuoteExplanation: &noQuoteExpl,
	}

	// Create context with underwriter user
	ctxWithUser := s.createUnderwriterContext(requesterID)

	// First request should succeed
	firstRequestID, err := s.manager.CreateRequest(ctxWithUser, applicationReviewID, requestData)
	s.Require().NoError(err)
	s.Require().NotNil(firstRequestID)

	// Second request with same application review should fail
	secondRequestID, err := s.manager.CreateRequest(ctxWithUser, applicationReviewID, requestData)
	s.Assert().Error(err)
	s.Assert().Nil(secondRequestID)
	s.Assert().Contains(err.Error(), "authority request already exists")
	s.Assert().Contains(err.Error(), applicationReviewID.String())
	s.Assert().Contains(err.Error(), firstRequestID.String())
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_Approve() {
	// First create a request - create a test application review
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending request directly via DB wrapper
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"LH001",
					"Loss History",
					"LH001-01",
					"High frequency of claims",
					"Business has been operating for less than 3 years with significant loss history",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Risk exceeds acceptable parameters due to new venture status and loss history"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Create a proper test user with manager role using the helper method
	ctxWithManager := s.createManagerContext(reviewerID)

	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Approved based on review of circumstances",
	)

	// The test should now succeed with proper role setup
	s.Require().NoError(err, "Process request should succeed with proper manager role setup")

	// Verify request state changed to executed (approved and executed in one step)
	updatedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateApproved, updatedRequest.State)
	s.Assert().Equal(reviewerID.String(), updatedRequest.LastReviewedBy.String)
	s.Assert().True(updatedRequest.LastReviewedAt.Valid)
	s.Assert().True(updatedRequest.ReviewData.Valid)
	s.Assert().True(updatedRequest.ExecutedAt.Valid)

	// Verify state transition
	transitions, err := s.dbWrapper.GetStateTransitions(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().GreaterOrEqual(len(transitions), 2)

	// Find the executed transition
	var foundExecutedTransition bool
	for _, t := range transitions {
		if t.FromState == enums.RequestStatePending && t.ToState == enums.RequestStateApproved {
			foundExecutedTransition = true
			break
		}
	}
	s.Assert().True(foundExecutedTransition, "Should have a transition from pending to approved")

	// Also verify the underlying app review and application are declined
	appReview, err := s.appReviewWrapper.GetReview(s.ctx, createdRequest.ApplicationReviewID.String())
	s.Require().NoError(err)
	s.Require().Equal(uw.ApplicationReviewStateDeclined, appReview.State)

	appObj, err := s.appWrapper.GetAppById(s.ctx, appReview.ApplicationID)
	s.Require().NoError(err)
	s.Require().Equal(app_state_enums.AppStateDeclined, appObj.State)
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_Reject() {
	// First create a request - create a test application review
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending request directly via DB wrapper
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"LH001",
					"Loss History",
					"LH001-01",
					"High frequency of claims",
					"Business has been operating for less than 3 years with significant loss history",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Risk exceeds acceptable parameters due to new venture status and loss history which makes the risk uninsurable"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Create a proper test user with manager role using the helper method
	ctxWithManager := s.createManagerContext(reviewerID)

	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionReject,
		"Need additional documentation before proceeding",
	)

	// The test should now succeed with proper role setup
	s.Require().NoError(err, "Process request should succeed with proper manager role setup")

	// Verify request state changed to declined
	updatedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateDeclined, updatedRequest.State)
	s.Assert().Equal(reviewerID.String(), updatedRequest.LastReviewedBy.String)
	s.Assert().True(updatedRequest.LastReviewedAt.Valid)
	s.Assert().True(updatedRequest.ReviewData.Valid)
	s.Assert().False(updatedRequest.ExecutedAt.Valid) // Should not be executed when rejected
}

func (s *AuthorityManagerTestSuite) TestGetRequestsForApplication() {
	// Setup test data - create a test application review
	applicationReviewID := s.createTestApplicationReview()

	// Create multiple requests for the same application
	for i := 0; i < 3; i++ {
		request := authorities_wrapper.AuthorityRequest{
			ID:                  uuid.New(),
			ApplicationReviewID: applicationReviewID,
			RequestType:         enums.RequestTypeDecline,
			State:               enums.RequestStatePending,
			RequestData: func() null.JSON {
				reasons := []types.DeclineReason{
					createTestDeclineReason(
						"LH001",
						"Loss History",
						"LH001-01",
						"High frequency",
						"Test request details",
					),
				}
				targetPrice := 15000.0
				noQuoteExpl := "Test explanation for the decline"
				payload := types.DeclineRequestPayload{
					DeclineReasons:     &reasons,
					TargetQuotePrice:   &targetPrice,
					NoQuoteExplanation: &noQuoteExpl,
				}
				jsonBytes, _ := json.Marshal(payload)
				return null.JSONFrom(jsonBytes)
			}(),
			RequesterID: uuid.New(),
			CreatedAt:   time.Now().Add(time.Duration(i) * time.Hour),
			UpdatedAt:   time.Now(),
			SubmittedAt: null.TimeFrom(time.Now()),
		}

		_, err := s.dbWrapper.CreateRequest(s.ctx, request)
		s.Require().NoError(err)
	}

	// Create a request for a different application
	otherRequest := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: uuid.New(), // Different application
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData:         null.JSONFrom([]byte(`{"test": "data"}`)),
		RequesterID:         uuid.New(),
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	_, err := s.dbWrapper.CreateRequest(s.ctx, otherRequest)
	s.Require().NoError(err)

	// Get requests for the specific application
	requests, err := s.manager.GetRequestsForApplication(s.ctx, applicationReviewID)

	s.Assert().NoError(err)
	s.Assert().Len(requests, 3) // Should only return requests for the specific application

	// Verify all returned requests belong to the correct application
	for _, req := range requests {
		s.Assert().Equal(applicationReviewID, req.ApplicationReviewID)
	}
}

func (s *AuthorityManagerTestSuite) TestGetLatestRequestForApplication() {
	// Setup test data - create a test application review
	applicationReviewID := s.createTestApplicationReview()
	
	// Create multiple requests for the same application with different creation times
	var latestRequestID uuid.UUID
	for i := 0; i < 3; i++ {
		requestID := uuid.New()
		if i == 2 {
			latestRequestID = requestID // The last one created should be the latest
		}
		
		request := authorities_wrapper.AuthorityRequest{
			ID:                  requestID,
			ApplicationReviewID: applicationReviewID,
			RequestType:         enums.RequestTypeDecline,
			State:               enums.RequestStatePending,
			RequestData: func() null.JSON {
				reasons := []types.DeclineReason{
					createTestDeclineReason(
						"LH001",
						"Loss History",
						"LH001-01",
						"High frequency",
						fmt.Sprintf("Test request %d", i),
					),
				}
				targetPrice := float64(15000 + i*1000)
				noQuoteExpl := fmt.Sprintf("Test explanation %d", i)
				payload := types.DeclineRequestPayload{
					DeclineReasons:     &reasons,
					TargetQuotePrice:   &targetPrice,
					NoQuoteExplanation: &noQuoteExpl,
				}
				jsonBytes, _ := json.Marshal(payload)
				return null.JSONFrom(jsonBytes)
			}(),
			RequesterID: uuid.New(),
			CreatedAt:   time.Now().Add(time.Duration(i) * time.Hour), // Different creation times
			UpdatedAt:   time.Now(),
			SubmittedAt: null.TimeFrom(time.Now()),
		}
		
		_, err := s.dbWrapper.CreateRequest(s.ctx, request)
		s.Require().NoError(err)
		
		// Small delay to ensure different timestamps
		time.Sleep(10 * time.Millisecond)
	}
	
	// Test getting latest request
	latestRequest, err := s.manager.GetLatestRequestForApplication(s.ctx, applicationReviewID)
	s.Assert().NoError(err)
	s.Assert().NotNil(latestRequest)
	s.Assert().Equal(latestRequestID, latestRequest.ID, "Should return the most recently created request")
	
	// Test with non-existent application review
	nonExistentID := uuid.New()
	latestRequest, err = s.manager.GetLatestRequestForApplication(s.ctx, nonExistentID)
	s.Assert().Error(err)
	s.Assert().True(errors.Is(err, sql.ErrNoRows), "Should return sql.ErrNoRows for non-existent application")
	s.Assert().Nil(latestRequest)
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_CombinedApproveAndExecute() {
	// Test that ProcessRequest atomically approves and executes in one call
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending request (not approved yet)
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending, // Start as pending
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"LH001",
					"Loss History",
					"LH001-01",
					"High frequency of claims",
					"Business has been operating for less than 3 years with significant loss history",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Risk exceeds guidelines due to new venture status and poor loss history"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Create a proper test user with manager role
	ctxWithManager := s.createManagerContext(reviewerID)

	// Process the request with approval (should both approve and execute atomically)
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Approved and executing decline action",
	)
	s.Require().NoError(err, "ProcessRequest should succeed with combined approve and execute")

	// Verify request went straight to executed state
	updatedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateApproved, updatedRequest.State)
	s.Assert().True(updatedRequest.ExecutedAt.Valid)
	s.Assert().True(updatedRequest.LastReviewedAt.Valid)
	s.Assert().Equal(reviewerID.String(), updatedRequest.LastReviewedBy.String)
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_RollbackOnExecutionFailure() {
	// Test that if execution fails, the entire operation is rolled back
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending request with invalid data that will cause execution to fail
	// For example, using an application review ID that doesn't exist in the state machine
	invalidApplicationReviewID := uuid.New() // This ID won't exist in the app review table

	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: invalidApplicationReviewID, // Invalid ID will cause execution to fail
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"LH001",
					"Loss History",
					"LH001-01",
					"High frequency of claims",
					"Business has been operating for less than 3 years with significant loss history",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Risk exceeds guidelines due to new venture status and poor loss history"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Create a proper test user with manager role
	ctxWithManager := s.createManagerContext(reviewerID)

	// Try to process the request with approval - should fail during execution
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Attempting to approve and execute",
	)

	// The operation should fail
	s.Require().Error(err, "ProcessRequest should fail when execution fails")
	s.Assert().Contains(err.Error(), "failed to execute approved request - request remains pending")

	// Verify request is still in pending state (rollback occurred)
	unchangedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStatePending, unchangedRequest.State, "Request should remain in pending state after failed execution")
	s.Assert().False(unchangedRequest.ExecutedAt.Valid, "ExecutedAt should not be set")
	s.Assert().False(unchangedRequest.LastReviewedAt.Valid, "LastReviewedAt should not be set")
	s.Assert().False(unchangedRequest.LastReviewedBy.Valid, "LastReviewedBy should not be set")
	s.Assert().False(unchangedRequest.ReviewData.Valid, "ReviewData should not be set")
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_ReviewDataStructure() {
	// Test that ReviewData includes authority-related fields when processing requests
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending decline request
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"TD001",
					"Test Decline",
					"TD001-01",
					"Test verification",
					"Test decline for ReviewData structure verification",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Testing ReviewData structure with authority information"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Process the request with approval
	ctxWithManager := s.createManagerContext(reviewerID)
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Approved for testing ReviewData structure",
	)
	s.Require().NoError(err)

	// Verify ReviewData contains expected authority fields
	updatedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().True(updatedRequest.ReviewData.Valid)

	var reviewData types.ReviewData
	err = json.Unmarshal(updatedRequest.ReviewData.JSON, &reviewData)
	s.Require().NoError(err)

	// Verify authority-related fields are present
	s.Assert().GreaterOrEqual(reviewData.MinLevelRequired, 0.0)
	// Note: with json:",omitempty" tags, empty maps/slices become nil after unmarshaling
	// This is standard Go behavior and acceptable for our use case
	s.Assert().Greater(reviewData.ReviewerLevel, int32(0))

	// Verify standard review fields
	s.Assert().Equal(authority_enums.ReviewActionApprove, reviewData.Decision)
	s.Assert().Equal(reviewerID, reviewData.ReviewedBy)
	s.Assert().NotNil(reviewData.ExecutedAt)
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_RejectWithAuthorityInfo() {
	// Test that rejection also captures authority information in ReviewData
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending request
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"TR001",
					"Test Reject",
					"TR001-01",
					"Authority verification",
					"Test rejection for authority info verification",
				),
			}
			targetPrice := 25000.0
			noQuoteExpl := "Testing rejection with authority information"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Process with rejection
	ctxWithManager := s.createManagerContext(reviewerID)
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionReject,
		"Rejected - additional documentation needed",
	)
	s.Require().NoError(err)

	// Verify ReviewData for rejected request
	updatedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateDeclined, updatedRequest.State)
	s.Assert().False(updatedRequest.ExecutedAt.Valid)

	var reviewData types.ReviewData
	err = json.Unmarshal(updatedRequest.ReviewData.JSON, &reviewData)
	s.Require().NoError(err)

	// Verify authority fields are captured for rejections
	s.Assert().GreaterOrEqual(reviewData.MinLevelRequired, 0.0)
	// Note: with json:",omitempty" tags, empty maps/slices become nil after unmarshaling
	// This is standard Go behavior and acceptable for our use case
	s.Assert().Greater(reviewData.ReviewerLevel, int32(0))

	// Verify rejection-specific fields
	s.Assert().Equal(authority_enums.ReviewActionReject, reviewData.Decision)
	s.Assert().Nil(reviewData.ExecutedAt)
	s.Assert().Equal(reviewerID, reviewData.ReviewedBy)
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_ReviewerLevelExtraction() {
	// Test that reviewer levels are correctly extracted and stored in ReviewData
	// Only managers can review/approve authority requests
	
	testCases := []struct {
		name           string
		roles          []authz.Role
		expectedLevel  int32
		description    string
	}{
		{
			name: "ManagerWithLevel1",
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.Level1UnderwriterRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
			},
			expectedLevel: 1,
			description:   "Manager with Level1UnderwriterRole should have reviewer level 1",
		},
		{
			name: "ManagerWithLevel7", 
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.Level7UnderwriterRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
			},
			expectedLevel: 7,
			description:   "Manager with Level7UnderwriterRole should have reviewer level 7",
		},
		{
			name: "ManagerWithMultipleUnderwriterRoles",
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.Level3UnderwriterRole, Domain: "*"},
				{Group: authz.Level5UnderwriterRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
			},
			expectedLevel: 5,
			description:   "Manager with multiple underwriter roles should use the highest level (5)",
		},
		{
			name: "ManagerWithNoUnderwriterLevel",
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
				{Group: authz.PowerUserRole, Domain: "*"},
			},
			expectedLevel: 0,
			description:   "Manager with no underwriter level role should have reviewer level 0",
		},
		{
			name: "ManagerWithSeniorUnderwriterOnly",
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.SeniorUnderwriterRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
			},
			expectedLevel: 0,
			description:   "Manager with SeniorUnderwriterRole but no Level roles should have reviewer level 0",
		},
		{
			name: "ManagerWithLevel2AndLevel6",
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.Level2UnderwriterRole, Domain: "*"},
				{Group: authz.Level6UnderwriterRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
			},
			expectedLevel: 6,
			description:   "Manager with Level2 and Level6 roles should use the highest level (6)",
		},
		{
			name: "ManagerRoleWithLevel4",
			roles: []authz.Role{
				{Group: authz.UnderwriterManagerRole, Domain: "*"},
				{Group: authz.Level4UnderwriterRole, Domain: "*"},
				{Group: authz.SuperuserRole, Domain: "*"},
			},
			expectedLevel: 4,
			description:   "User with manager role and Level4 should use Level4 authority",
		},
	}

	for _, tc := range testCases {
		s.Run(tc.name, func() {
			// Setup test application and request
			applicationReviewID := s.createTestApplicationReview()
			requesterID := uuid.New()
			reviewerID := uuid.New()

			// Create a pending request
			request := authorities_wrapper.AuthorityRequest{
				ID:                  uuid.New(),
				ApplicationReviewID: applicationReviewID,
				RequestType:         enums.RequestTypeDecline,
				State:               enums.RequestStatePending,
				RequestData: func() null.JSON {
					reasons := []types.DeclineReason{
						createTestDeclineReason(
							"TRL001",
							"Test Reviewer Level",
							"TRL001-01",
							"Level extraction",
							"Testing reviewer level extraction for " + tc.name,
						),
					}
					targetPrice := 15000.0
					noQuoteExpl := "Test case for reviewer level extraction"
					payload := types.DeclineRequestPayload{
						DeclineReasons:     &reasons,
						TargetQuotePrice:   &targetPrice,
						NoQuoteExplanation: &noQuoteExpl,
					}
					jsonBytes, _ := json.Marshal(payload)
					return null.JSONFrom(jsonBytes)
				}(),
				RequesterID: requesterID,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
				SubmittedAt: null.TimeFrom(time.Now()),
			}

			createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
			s.Require().NoError(err)

			// Create context with specific roles for this test case
			ctxWithReviewer := authz.WithUser(s.ctx, authz.User{
				UserInfo: authz.UserInfo{
					ID:    reviewerID,
					Email: "<EMAIL>",
				},
				Roles: tc.roles,
			})

			// Test approval flow
			err = s.manager.ProcessRequest(
				ctxWithReviewer,
				createdRequest.ID,
				authority_enums.ReviewActionApprove,
				"Approved for testing reviewer level extraction",
			)
			s.Require().NoError(err, "ProcessRequest should succeed for test case: %s", tc.description)

			// Verify request was approved and executed
			updatedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
			s.Require().NoError(err)
			s.Assert().Equal(enums.RequestStateApproved, updatedRequest.State)
			s.Assert().True(updatedRequest.ReviewData.Valid)

			// Parse and verify ReviewData
			var reviewData types.ReviewData
			err = json.Unmarshal(updatedRequest.ReviewData.JSON, &reviewData)
			s.Require().NoError(err)

			// Verify reviewer level is correctly extracted and stored
			s.Assert().Equal(tc.expectedLevel, reviewData.ReviewerLevel, 
				"Expected reviewer level %d for test case: %s", tc.expectedLevel, tc.description)

			// Verify other ReviewData fields are properly set
			s.Assert().Equal(authority_enums.ReviewActionApprove, reviewData.Decision)
			s.Assert().Equal(reviewerID, reviewData.ReviewedBy)
			s.Assert().NotNil(reviewData.ExecutedAt)
			s.Assert().GreaterOrEqual(reviewData.MinLevelRequired, 0.0)

			// Test rejection flow with the same setup
			// Create another request to test rejection
			rejectRequest := authorities_wrapper.AuthorityRequest{
				ID:                  uuid.New(),
				ApplicationReviewID: s.createTestApplicationReview(), // New application for rejection test
				RequestType:         enums.RequestTypeDecline,
				State:               enums.RequestStatePending,
				RequestData: func() null.JSON {
					reasons := []types.DeclineReason{
						createTestDeclineReason(
							"TRLR001",
							"Test Reviewer Level Reject",
							"TRLR001-01",
							"Rejection level",
							"Testing reviewer level extraction on rejection for " + tc.name,
						),
					}
					targetPrice := 20000.0
					noQuoteExpl := "Test rejection case for reviewer level extraction"
					payload := types.DeclineRequestPayload{
						DeclineReasons:     &reasons,
						TargetQuotePrice:   &targetPrice,
						NoQuoteExplanation: &noQuoteExpl,
					}
					jsonBytes, _ := json.Marshal(payload)
					return null.JSONFrom(jsonBytes)
				}(),
				RequesterID: requesterID,
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
				SubmittedAt: null.TimeFrom(time.Now()),
			}

			createdRejectRequest, err := s.dbWrapper.CreateRequest(s.ctx, rejectRequest)
			s.Require().NoError(err)

			// Test rejection
			err = s.manager.ProcessRequest(
				ctxWithReviewer,
				createdRejectRequest.ID,
				authority_enums.ReviewActionReject,
				"Rejected for testing reviewer level extraction",
			)
			s.Require().NoError(err)

			// Verify rejection ReviewData also contains correct reviewer level
			updatedRejectRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRejectRequest.ID)
			s.Require().NoError(err)
			s.Assert().Equal(enums.RequestStateDeclined, updatedRejectRequest.State)
			s.Assert().True(updatedRejectRequest.ReviewData.Valid)

			var rejectReviewData types.ReviewData
			err = json.Unmarshal(updatedRejectRequest.ReviewData.JSON, &rejectReviewData)
			s.Require().NoError(err)

			// Verify reviewer level is correctly extracted for rejection as well
			s.Assert().Equal(tc.expectedLevel, rejectReviewData.ReviewerLevel,
				"Expected reviewer level %d for rejection test case: %s", tc.expectedLevel, tc.description)
			s.Assert().Equal(authority_enums.ReviewActionReject, rejectReviewData.Decision)
			s.Assert().Equal(reviewerID, rejectReviewData.ReviewedBy)
			s.Assert().Nil(rejectReviewData.ExecutedAt) // No execution time for rejections
		})
	}
}

// createReviewerContext creates a context with a user that has specific underwriter roles
func (s *AuthorityManagerTestSuite) createReviewerContext(userID uuid.UUID, roles ...authz.RoleGroupEnum) context.Context {
	userRoles := make([]authz.Role, len(roles))
	for i, role := range roles {
		userRoles[i] = authz.Role{
			Group:  role,
			Domain: "*",
		}
	}
	// Always add SuperuserRole to ensure authorization succeeds
	userRoles = append(userRoles, authz.Role{Group: authz.SuperuserRole, Domain: "*"})

	return authz.WithUser(s.ctx, authz.User{
		UserInfo: authz.UserInfo{
			ID:    userID,
			Email: "<EMAIL>",
		},
		Roles: userRoles,
	})
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_NonManagerCannotReview() {
	// Test that only managers can review/approve authority requests
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	reviewerID := uuid.New()

	// Create a pending request
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"TNM001",
					"Test Non Manager",
					"TNM001-01",
					"Permission check",
					"Testing non-manager review permissions",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Test case to verify only managers can review"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Test with regular underwriter (non-manager)
	ctxWithUnderwriter := s.createUnderwriterContext(reviewerID)

	// Attempt to approve the request - should fail
	err = s.manager.ProcessRequest(
		ctxWithUnderwriter,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Should not be allowed to approve",
	)
	s.Require().Error(err, "Non-manager should not be able to approve requests")
	s.Assert().Contains(err.Error(), "user does not have underwriter manager access")

	// Attempt to reject the request - should also fail
	err = s.manager.ProcessRequest(
		ctxWithUnderwriter,
		createdRequest.ID,
		authority_enums.ReviewActionReject,
		"Should not be allowed to reject",
	)
	s.Require().Error(err, "Non-manager should not be able to reject requests")
	s.Assert().Contains(err.Error(), "user does not have underwriter manager access")

	// Verify request state is still pending (unchanged)
	unchangedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStatePending, unchangedRequest.State)
	s.Assert().False(unchangedRequest.LastReviewedAt.Valid)
	s.Assert().False(unchangedRequest.ReviewData.Valid)

	// Now verify that a manager CAN approve the same request
	ctxWithManager := s.createManagerContext(uuid.New())
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Manager approval should succeed",
	)
	s.Require().NoError(err, "Manager should be able to approve requests")

	// Verify the request was approved and executed
	finalRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateApproved, finalRequest.State)
}

func (s *AuthorityManagerTestSuite) TestProcessRequest_DeclinedToApproved() {
	// Test the flow: Pending → Declined (with manager note) → Approved (execute decline)
	applicationReviewID := s.createTestApplicationReview()
	requesterID := uuid.New()
	managerID := uuid.New()

	// Create a pending request directly via DB wrapper
	request := authorities_wrapper.AuthorityRequest{
		ID:                  uuid.New(),
		ApplicationReviewID: applicationReviewID,
		RequestType:         enums.RequestTypeDecline,
		State:               enums.RequestStatePending,
		RequestData: func() null.JSON {
			reasons := []types.DeclineReason{
				createTestDeclineReason(
					"LH001",
					"Loss History",
					"LH001-01",
					"High frequency of claims",
					"Business has high loss history",
				),
			}
			targetPrice := 15000.0
			noQuoteExpl := "Risk exceeds acceptable parameters"
			payload := types.DeclineRequestPayload{
				DeclineReasons:     &reasons,
				TargetQuotePrice:   &targetPrice,
				NoQuoteExplanation: &noQuoteExpl,
			}
			jsonBytes, _ := json.Marshal(payload)
			return null.JSONFrom(jsonBytes)
		}(),
		RequesterID: requesterID,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		SubmittedAt: null.TimeFrom(time.Now()),
	}

	createdRequest, err := s.dbWrapper.CreateRequest(s.ctx, request)
	s.Require().NoError(err)

	// Step 1: Manager declines with feedback note
	ctxWithManager := s.createManagerContext(managerID)
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionReject,
		"Please provide additional documentation for loss history",
	)
	s.Require().NoError(err, "Manager should be able to decline with feedback")

	// Verify request is in declined state with manager note
	declinedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateDeclined, declinedRequest.State)
	s.Assert().True(declinedRequest.ReviewData.Valid)
	
	// Verify manager note is present
	var reviewData types.ReviewData
	err = json.Unmarshal(declinedRequest.ReviewData.JSON, &reviewData)
	s.Require().NoError(err)
	s.Assert().Equal("Please provide additional documentation for loss history", reviewData.ManagerNote)

	// Step 2: Manager approves the declined request (executes the decline)
	err = s.manager.ProcessRequest(
		ctxWithManager,
		createdRequest.ID,
		authority_enums.ReviewActionApprove,
		"Documentation reviewed, proceeding with decline",
	)
	s.Require().NoError(err, "Manager should be able to approve from declined state")

	// Verify request is now approved and executed
	approvedRequest, err := s.dbWrapper.GetRequest(s.ctx, createdRequest.ID)
	s.Require().NoError(err)
	s.Assert().Equal(enums.RequestStateApproved, approvedRequest.State)
	s.Assert().True(approvedRequest.ExecutedAt.Valid, "Request should be executed")
}
