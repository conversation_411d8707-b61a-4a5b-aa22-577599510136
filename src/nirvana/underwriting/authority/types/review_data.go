package types

import (
	"time"

	"github.com/google/uuid"

	authority_enums "nirvanatech.com/nirvana/underwriting/authority/enums"
)

// AuthorityInfo contains extracted authority information from application review
type AuthorityInfo struct {
	// MinLevelRequired is the minimum authority level required for the action
	MinLevelRequired float64 `json:"min_level_required"`

	// MinLevelPerRiskType contains risk-specific authority level requirements
	MinLevelPerRiskType map[string]float64 `json:"min_level_per_risk_type"`

	// RiskFactors contains the list of risk factors that triggered authority requirements
	RiskFactors []string `json:"risk_factors"`
}

// ReviewData represents structured review data for authority requests
// This replaces the map[string]interface{} pattern with a strongly-typed struct
type ReviewData struct {
	// Decision made during review (approve/reject)
	Decision authority_enums.ReviewAction `json:"decision" validate:"required"`

	// Comments provided during review
	Comments string `json:"comments" validate:"required,min=1"`

	// ID of the user who made the review decision
	ReviewedBy uuid.UUID `json:"reviewed_by" validate:"required"`

	// Timestamp when the review decision was made
	ReviewedAt time.Time `json:"reviewed_at" validate:"required"`

	// ExecutedAt timestamp when the approved action was executed (only for approved requests)
	// This is nullable since rejected requests won't have an execution time
	ExecutedAt *time.Time `json:"executed_at,omitempty"`

	// MinLevelRequired is the minimum authority level required for this action
	// This is extracted from the authority fact for the specific action (decline/approve)
	MinLevelRequired float64 `json:"min_level_required" validate:"min=0"`

	// MinLevelPerRiskType contains risk-specific authority level requirements
	// Key is the risk type (e.g., "ScheduleMod"), value is the minimum level
	MinLevelPerRiskType map[string]float64 `json:"min_level_per_risk_type,omitempty"`

	// ReviewerLevel is the actual authority level of the reviewer
	ReviewerLevel int32 `json:"reviewer_level" validate:"min=0"`

	// RiskFactors contains the list of risk factors that triggered authority requirements
	// These help understand why a specific authority level was required
	RiskFactors []string `json:"risk_factors,omitempty"`

	// ManagerNote contains feedback from the underwriting manager when declining a request
	// This helps the underwriter understand what needs to be addressed
	ManagerNote string `json:"manager_note,omitempty"`
}

// NewReviewData creates a new ReviewData instance with required fields
func NewReviewData(
	decision authority_enums.ReviewAction,
	comments string,
	reviewedBy uuid.UUID,
	reviewerLevel int32,
	minLevelRequired float64,
) *ReviewData {
	return &ReviewData{
		Decision:            decision,
		Comments:            comments,
		ReviewedBy:          reviewedBy,
		ReviewedAt:          time.Now(),
		ReviewerLevel:       reviewerLevel,
		MinLevelRequired:    minLevelRequired,
		MinLevelPerRiskType: make(map[string]float64),
		RiskFactors:         make([]string, 0),
	}
}

// SetExecutedAt marks the review as executed (for approved requests)
func (r *ReviewData) SetExecutedAt(executedAt time.Time) {
	r.ExecutedAt = &executedAt
}

// AddRiskTypeLevelRequirement adds a risk-specific authority level requirement
func (r *ReviewData) AddRiskTypeLevelRequirement(riskType string, minLevel float64) {
	if r.MinLevelPerRiskType == nil {
		r.MinLevelPerRiskType = make(map[string]float64)
	}
	r.MinLevelPerRiskType[riskType] = minLevel
}

// AddRiskFactor adds a risk factor that contributed to the authority requirement
func (r *ReviewData) AddRiskFactor(riskFactor string) {
	if r.RiskFactors == nil {
		r.RiskFactors = make([]string, 0)
	}
	r.RiskFactors = append(r.RiskFactors, riskFactor)
}
