load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "express_lane",
    srcs = [
        "deps.go",
        "fx.go",
        "manager.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/express_lane",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/application/experiments/non_fleet/express_lane",
        "//nirvana/common-go/log",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/jobber",
        "//nirvana/jobber/jtypes",
        "//nirvana/nirvanaapp/jobs/indication-job",
        "//nirvana/nonfleet/quoting-jobs",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cockroachdb_errors//:errors",
        "@org_uber_go_fx//:fx",
    ],
)
