package express_lane

import (
	"github.com/benb<PERSON><PERSON><PERSON>/clock"
	"go.uber.org/fx"
	"nirvanatech.com/nirvana/api-server/quoting_jobber"
	non_fleet_experiment "nirvanatech.com/nirvana/application/experiments/non_fleet"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
)

type ExpressLanesManagerDeps struct {
	fx.In

	Jobber                         quoting_jobber.Client
	NFApplicationExperimentManager *non_fleet_experiment.Manager
	AdmittedAppWrapper             nf_app.Wrapper[*admitted.AdmittedApp]
	Clock                          clock.Clock
}
