package express_lane

import (
	"context"

	"github.com/cockroachdb/errors"
	"nirvanatech.com/nirvana/application/experiments/non_fleet/express_lane"
	"nirvanatech.com/nirvana/common-go/log"
	"nirvanatech.com/nirvana/common-go/pointer_utils"
	nf_app "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application"
	admitted "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/application/admitted_app"
	nf_enums "nirvanatech.com/nirvana/db-api/db_wrappers/nonfleet/enums"
	enums2 "nirvanatech.com/nirvana/db-api/db_wrappers/policy/enums"
	"nirvanatech.com/nirvana/jobber"
	"nirvanatech.com/nirvana/jobber/jtypes"
	indication_job "nirvanatech.com/nirvana/nirvanaapp/jobs/indication-job"
	quoting_jobs "nirvanatech.com/nirvana/nonfleet/quoting-jobs"
)

type ExpressLaneManager struct {
	Deps ExpressLanesManagerDeps
}

func newExpressLaneManager(deps ExpressLanesManagerDeps) *ExpressLaneManager {
	return &ExpressLaneManager{
		Deps: deps,
	}
}

func (e ExpressLaneManager) TriggerJobsOnTelematicsSuccess(ctx context.Context, app nf_app.Application[*admitted.AdmittedApp]) error {
	// Skip if the program type is not non-fleet admitted.
	if app.ProgramType != enums2.ProgramTypeNonFleetAdmitted {
		return nil
	}
	// First, check if the Express Lane experiment is assigned to the app
	isApplied, err := e.Deps.NFApplicationExperimentManager.IsExperimentAppliedToSubject(ctx, express_lane.ExpressLaneV1ExperimentId, app.ID)
	if err != nil {
		return errors.Wrapf(err, "failed to check if experiment is applicable to app %s", app.ID)
	}

	if isApplied == nil || !*isApplied {
		return nil
	}

	// Skip if the app is express lanes are not enabled/already terminated.
	if app.ExpressLaneMetadata == nil {
		log.Info(ctx, "skipping Express Lane automation for app as it has no express lane metadata",
			log.Stringer("app_id", app.ID))
		return nil
	}
	if app.ExpressLaneMetadata.State != nf_enums.ExpressLaneStateWaitingForTelematics {
		log.Info(ctx, "Skipping Express Lane automation for app as it is not in the correct state",
			log.Stringer("app_id", app.ID),
			log.Stringer("express_app_state", app.ExpressLaneMetadata.State))
		return nil
	}

	// Step 1- Move the app to the automation in progress state.
	if err := e.updateExpressLaneState(ctx, app, nf_enums.ExpressLaneStateAutomationInProgress); err != nil {
		return errors.Wrap(err, "failed to update express lane state")
	}

	// Step 2 - Trigger the Express Lane Auto Underwriting job.
	expressLaneJobRunId, err := quoting_jobs.TriggerExpressLaneAutoUnderwritingJob(ctx, e.Deps.Jobber, app.ID, enums2.ProgramTypeNonFleetAdmitted)
	if err != nil {
		return errors.Wrapf(err, "failed to trigger ExpressLaneAutoUnderwriting job for app %s", app.ID)
	}
	if expressLaneJobRunId == nil {
		return errors.New("failed to trigger ExpressLaneAutoUnderwriting - job run id is nil")
	}

	log.Info(ctx, "Triggered ExpressLaneAutoUnderwriting job", log.Stringer("JobRunId", expressLaneJobRunId))

	if err = e.Deps.Jobber.WaitForJobRunCompletion(ctx, *expressLaneJobRunId); err != nil {
		return errors.Wrapf(err, " unable to wait for job run completion for job run id %s", expressLaneJobRunId)
	}

	log.Info(ctx, "ExpressLaneAutoUnderwriting job completed", log.Stringer("JobRunId", expressLaneJobRunId))

	// STEP 3 - Trigger the indication job, refresh the pricing for the application.
	indicationJobRunId, err := e.Deps.Jobber.AddJobRun(ctx,
		jobber.NewAddJobRunParams(
			indication_job.RunIndicationJobTaskID,
			&indication_job.IndicationJobArgs{
				ApplicationID: app.ID,
				ProgramType:   enums2.ProgramTypeNonFleetAdmitted,
			},
			jtypes.NewMetadata(jtypes.Immediate),
		),
	)
	if err != nil {
		return errors.Wrapf(err, "failed to add indication job run for app %s", app.ID)
	}

	log.Info(ctx, "Triggered Indication job", log.Stringer("JobRunId", indicationJobRunId))

	// STEP 3 - Wait for the indication job to complete.
	if err = e.Deps.Jobber.WaitForJobRunCompletion(ctx, indicationJobRunId); err != nil {
		return errors.Wrapf(err, " unable to wait for job run completion for job run id %s", indicationJobRunId)
	}

	log.Info(ctx, "Indication job completed", log.Stringer("JobRunId", indicationJobRunId))

	// STEP 4 - Mark the express lane state as automation completed.
	if err := e.updateExpressLaneState(ctx, app, nf_enums.ExpressLaneStateAutomationCompleted); err != nil {
		return errors.Wrapf(err, "failed to update express lane state for app %s ", app.ID)
	}

	jobRunIds := []jtypes.JobRunId{*expressLaneJobRunId}
	if app.ExpressLaneMetadata != nil {
		jobRunIds = append(jobRunIds, app.ExpressLaneMetadata.JobRunIds...)
	}

	err = e.Deps.AdmittedAppWrapper.UpdateApp(ctx, app.ID,
		func(appObj nf_app.Application[*admitted.AdmittedApp]) (nf_app.Application[*admitted.AdmittedApp], error) {
			expressLaneMetadata := nf_app.ExpressLaneMetadata{
				State:                    nf_enums.ExpressLaneStateAutomationCompleted,
				LastStateUpdateTimeStamp: e.Deps.Clock.Now(),
				JobRunIds:                jobRunIds,
			}
			appObj.ExpressLaneMetadata = pointer_utils.ToPointer(expressLaneMetadata)
			return appObj, nil
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update express lane status for app %s", app.ID)
	}

	payload := express_lane.ExpressLaneStateChangeEvent{
		FromState: pointer_utils.ToPointer(nf_enums.ExpressLaneStateAutomationInProgress.String()),
		ToState:   pointer_utils.ToPointer(nf_enums.ExpressLaneStateAutomationCompleted.String()),
	}

	err = e.Deps.NFApplicationExperimentManager.RecordExperimentStateChangeEvent(ctx, express_lane.ExpressLaneV1ExperimentId, app.ID, payload)
	if err != nil {
		// Just log and move, since this is non-breaking, and if it fails, it won't harm a lot.
		log.Error(ctx, "failed to record experiment state change event for express lane",
			log.Stringer("app_id", app.ID),
			log.Stringer("experiment_id", express_lane.ExpressLaneV1ExperimentId),
			log.Stringer("old_status", app.ExpressLaneMetadata.State),
			log.Stringer("new_status", nf_enums.ExpressLaneStateAutomationCompleted),
			log.Err(err),
		)
	}
	return nil
}

func (e ExpressLaneManager) updateExpressLaneState(ctx context.Context, app nf_app.Application[*admitted.AdmittedApp], state nf_enums.ExpressLaneState) error {
	expressLaneMetadata := &nf_app.ExpressLaneMetadata{
		State:                    state,
		LastStateUpdateTimeStamp: e.Deps.Clock.Now(),
	}
	if app.ExpressLaneMetadata != nil {
		expressLaneMetadata.JobRunIds = app.ExpressLaneMetadata.JobRunIds
	}

	// Update the application with the new express lane metadata.
	err := e.Deps.AdmittedAppWrapper.UpdateApp(ctx, app.ID,
		func(appObj nf_app.Application[*admitted.AdmittedApp]) (nf_app.Application[*admitted.AdmittedApp], error) {
			appObj.ExpressLaneMetadata = expressLaneMetadata
			return appObj, nil
		})
	if err != nil {
		return errors.Wrapf(err, "failed to update express lane status for app id %s", app.ID)
	}

	// Record the state change event for the express lane.
	// This is used to track the state changes in the express lane automation.
	if app.ExpressLaneMetadata == nil {
		payload := express_lane.ExpressLaneStateChangeEvent{
			FromState: pointer_utils.ToPointer(app.ExpressLaneMetadata.State.String()),
			ToState:   pointer_utils.ToPointer(state.String()),
		}

		err = e.Deps.NFApplicationExperimentManager.RecordExperimentStateChangeEvent(ctx, express_lane.ExpressLaneV1ExperimentId, app.ID, payload)
		if err != nil {
			// Just log and move, since this is non-breaking, and if it fails, it won't harm a lot.
			log.Error(ctx, "failed to record experiment state change event for express lane",
				log.Stringer("app_id", app.ID),
				log.Stringer("experiment_id", express_lane.ExpressLaneV1ExperimentId),
				log.Stringer("old_status", app.ExpressLaneMetadata.State),
				log.Stringer("new_status", state),
				log.Err(err),
			)
		}
	}

	return nil
}
