load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "impl",
    srcs = [
        "add_jobs.go",
        "backfill_app_review_widgets.go",
        "backfill_widgets.go",
        "cancellation_score_snowflake_postgres_sync.go",
        "fx.go",
        "generate_app_review_widget.go",
        "generate_app_review_widget_data.go",
        "generate_appetite_factors.go",
        "generate_appetite_factors_nf.go",
        "generate_experiments.go",
        "generate_mst_referral_rules.go",
        "generate_panel_notes_job.go",
        "generate_widgets_for_app_review.go",
        "queries.go",
        "refresh_fleet_underwriting_usecases_cron.go",
        "repull_app_review_widget.go",
        "update_appetite_factors_cron_nf.go",
        "update_application_pre_telematics_status_nf.go",
        "utils.go",
    ],
    importpath = "nirvanatech.com/nirvana/underwriting/jobs/impl",
    visibility = ["//visibility:public"],
    deps = [
        "//nirvana/api-server/quoting_jobber",
        "//nirvana/api-server/rule-engine",
        "//nirvana/application/experiments/non_fleet",
        "//nirvana/application/experiments/non_fleet/pre_telematics_quote",
        "//nirvana/common-go/application-util/appetite_factors/recommended_action_rubric",
        "//nirvana/common-go/errgroup",
        "//nirvana/common-go/feature_flag_lib",
        "//nirvana/common-go/log",
        "//nirvana/common-go/logic_utils",
        "//nirvana/common-go/pointer_utils",
        "//nirvana/common-go/postgres_utils",
        "//nirvana/common-go/rule_engine",
        "//nirvana/common-go/slice_utils",
        "//nirvana/db-api",
        "//nirvana/db-api/db_models",
        "//nirvana/db-api/db_wrappers/app_review_widget",
        "//nirvana/db-api/db_wrappers/application",
        "//nirvana/db-api/db_wrappers/application/enums",
        "//nirvana/db-api/db_wrappers/auth",
        "//nirvana/db-api/db_wrappers/fmcsa",
        "//nirvana/db-api/db_wrappers/forms",
        "//nirvana/db-api/db_wrappers/nonfleet/application",
        "//nirvana/db-api/db_wrappers/nonfleet/application/admitted_app",
        "//nirvana/db-api/db_wrappers/nonfleet/application_review",
        "//nirvana/db-api/db_wrappers/nonfleet/enums",
        "//nirvana/db-api/db_wrappers/policy/enums",
        "//nirvana/db-api/db_wrappers/uw",
        "//nirvana/db-api/db_wrappers/uw/appetite_factors",
        "//nirvana/events",
        "//nirvana/experiments",
        "//nirvana/external_client/salesforce/jobs",
        "//nirvana/external_client/salesforce/jobs/enums",
        "//nirvana/external_client/salesforce/wrapper",
        "//nirvana/external_data_management/data_fetching",
        "//nirvana/external_data_management/data_processing",
        "//nirvana/external_data_management/interceptors_management/read_from_store_interceptor",
        "//nirvana/external_data_management/interceptors_management/write_to_store_interceptor",
        "//nirvana/external_data_management/store_management",
        "//nirvana/feature_store",
        "//nirvana/fmcsa/safety",
        "//nirvana/infra/config",
        "//nirvana/infra/fx/fxregistry",
        "//nirvana/insurance-core/monitoring",
        "//nirvana/jobber",
        "//nirvana/jobber/event",
        "//nirvana/jobber/job_utils",
        "//nirvana/jobber/jtypes",
        "//nirvana/jobber/registry",
        "//nirvana/nonfleet/underwriting_panels",
        "//nirvana/openapi-specs/components/underwriting",
        "//nirvana/quoting/jobs/impl",
        "//nirvana/quoting/utils",
        "//nirvana/safety/common",
        "//nirvana/servers/telematicsv2",
        "//nirvana/servers/vehicles:proto",
        "//nirvana/telematics",
        "//nirvana/telematics/connections",
        "//nirvana/underwriting/app_review",
        "//nirvana/underwriting/app_review/vin_visibility",
        "//nirvana/underwriting/app_review/widgets/global",
        "//nirvana/underwriting/app_review_widget_manager",
        "//nirvana/underwriting/appetite_factors/appetite_score_rubric",
        "//nirvana/underwriting/appetite_factors/input",
        "//nirvana/underwriting/appetite_guidelines/guidelines",
        "//nirvana/underwriting/clients/uw_ai",
        "//nirvana/underwriting/clients/uw_ai/pb:uw_ai",
        "//nirvana/underwriting/common",
        "//nirvana/underwriting/jobs",
        "//nirvana/underwriting/jobs/impl/appetite_factors",
        "//nirvana/underwriting/jobs/impl/appetite_factors/fleet",
        "//nirvana/underwriting/jobs/impl/appetite_factors/non_fleet",
        "//nirvana/underwriting/jobs/impl/widgets",
        "//nirvana/underwriting/jobs/impl/widgets/panels",
        "//nirvana/underwriting/jobs/impl/widgets/panels/drivers/drivers_list",
        "//nirvana/underwriting/jobs/impl/widgets/panels/losses/large_loss",
        "//nirvana/underwriting/jobs/impl/widgets/panels/losses/loss_summary",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/commodities",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/projected_information",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/radius_of_operation",
        "//nirvana/underwriting/jobs/impl/widgets/panels/operations/terminal_locations",
        "//nirvana/underwriting/mst_referral",
        "//nirvana/underwriting/risk_factors",
        "//nirvana/underwriting/rule-engine/appetite_factors/appetite_factor",
        "//nirvana/underwriting/rule-engine/appetite_factors/nonfleet",
        "//nirvana/underwriting/rule-engine/appetite_factors/nonfleet/fact",
        "//nirvana/underwriting/rule-engine/authorities",
        "//nirvana/underwriting/rule-engine/experiments",
        "//nirvana/underwriting/rule-engine/experiments/fact",
        "//nirvana/underwriting/rule-engine/experiments/recommendation",
        "@com_github_benbjohnson_clock//:clock",
        "@com_github_cactus_go_statsd_client_v5//statsd",
        "@com_github_cockroachdb_errors//:errors",
        "@com_github_google_uuid//:uuid",
        "@com_github_hyperjumptech_grule_rule_engine//ast",
        "@com_github_hyperjumptech_grule_rule_engine//engine",
        "@com_github_oapi_codegen_runtime//types",
        "@com_github_volatiletech_null_v8//:null",
        "@org_uber_go_fx//:fx",
        "@org_uber_go_multierr//:multierr",
    ],
)

go_test(
    name = "impl_test",
    srcs = ["utils_test.go"],
    embed = [":impl"],
    deps = ["//nirvana/underwriting/rule-engine/experiments/recommendation"],
)
