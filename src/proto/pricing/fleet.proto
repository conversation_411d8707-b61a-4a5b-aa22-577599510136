syntax = "proto3";
package pricing;

option go_package = "nirvanatech.com/nirvana/rating/pricing/api/ptypes";

import "google/protobuf/timestamp.proto";
import "common/time.proto";
import "pricing/common.proto";
import "pricing/configs.proto";
import "pricing/sub_coverages.proto";
import "pricing/policies.proto";
import "fleet/model/program_data.proto";

// For Fleet sub-coverages, these are some preliminary thoughts:
//
// The CombinedDeductibles field inside the CoveragesSpec message will map to what is now called:
// "ModelInput.CoverageInfo.CoveragesWithCombinedDeductibles.CombinedCoveragesList".
// We will have to create a map/set with two keys: APD and MTC (see this: https://github.com/nirvanatech/nirvana/blob/46695deeabd3a6e3236389b6f14cd9b8d48fb184/src/nirvana/quoting/app_state_machine/app_logic/coverages.go#L76).
//
// This is the list of sub-coverages that we are currently pricing for (i.e. handled inside adaptors):
// 1. UM (no deductible)
//    - Limit: confirm type of limit
// 2. UIM (no deductible)
//    - Limit: confirm type of limit
// 3. UMUIM (no deductible)
//    - Limit: confirm type of limit
// 4. TI (no deductible)
//    - Limit: confirm type of limit
// 5. MedPay (no deductible)
//    - Limit: confirm type of limit
// 6. EnhancedPackageTowingLimit (no deductible)
//    - Limit: confirm type of limit
// 7. ReeferWithHumanError (no limit or deductible)
// 8. ReeferWithoutHumanError (no limit or deductible)
// 9. PIP (no deductible)
//    - Limit: confirm type of limit (note: we only do this for MI)
// 10. GuestPIP (no limit or deductible)
// 11. PIPExcessAttendantCare (no limit or deductible)
// 12. PPI (no limit or deductible)
// 13. UMPD (no deductible)
//    - Limit: confirm type of limit
//    - Note: this is used instead of UM in some states (GA, MI, NM, TN)
//    - Note: the fields set on RateML are still called UmLimit and HasUMCoverage, but there is no UMBI
//    - Note: in this states we still use UIM and UMUIM (without making a distinction between their BI/PD subcomponents, we only make the distinction for UM)
//    - Note: in newer models (MST), UMPD is used by itself, not as a replacement for UM
//      (in other words, the fields UmpdLimit and HasUMPDCoverage are set from the UMPD coverage).
//      We only use the limit from the coverage. All 27 states do this (i.e. AL, AZ, CA, CO, GA, IA, IL, IN, KS, KY, MI, MN, MO, NC, NE, NM, NV, OH, OK, OR, PA, SC, TN, TX, UT, WA, WI).
// 14. UMBI (no deductible)
//    - Limit: confirm type of limit
//    - Note: this is used instead of UM in some states (IL, KY, SC, UT, WA)
//    - Note: the fields set on RateML are still called UmLimit and HasUMCoverage, but there is no UMPD
//    - Note: in these states there is no PD component to UM.
// 15. UIMBI (no deductible)
//    - Limit: confirm type of limit
//    - Note: this is used instead of UIM in some states (IL, KY, SC, UT)
//    - Note: in these states there is no PD component to UIM.
//    - Note: the fields set on RateML are still called UimLimit and HasUIMCoverage, but there is no UIMPD
//    - Note: this is surprisingly not used in WA, despite the fact UMBI is used in that state.
// 16. UMUIMBI (no deductible)
//    - Limit: confirm type of limit
//    - Note: this is used instead of UIM in some states (IL)
//    - Note: in these states there is no PD component to UMUIM.
//    - Note: the fields set on RateML are still called UmUimLimit and HasUMUIMCoverage, but there is no UMUIMPD
//    - Note: this is surprisingly not used in KY, SC and UT, despite the fact
//      that UMBI and UIMBI are used in those states.
//    - Note: this is surprisingly not used in WA, despite the fact UMBI is used in that state.
// 17. PIPBasic (no deductible)
//    - Limit: confirm type of limit (probably the same as PIP)
//    - Note: only used in WA
//    - Note: in older adaptors we didn't pass a limit as input, but hardcoded in the adaptor.
//      We shouldn't do this. We should receive the limit as input.
// 18. PIPIncreased (no deductible)
//    - Limit: confirm type of limit (probably the same as PIP)
//    - Note: only used in WA
//    - Note: in older adaptors we didn't pass a limit as input, but hardcoded in the adaptor.
//      We shouldn't do this. We should receive the limit as input.
// 19. APD (no limit)
//    - Deductible
//    - Note: represented as a group of COLL + COMP
// 20. AL (no limit)
//    - Deductible
//    - Note: represented as a group of BI + PD
// 21. MTC
//    - Deductible
//    - Limit: confirm type of limit
//    - Note: represented as Cargo
// 22. GL (no limit)
//    - Deductible
//    - Note: represented as GL (sub-coverage of the same name)
// 23. CoverageStopGap (no limit or deductible)
//    - Note: only used in OH and WA
//
// For each one of these we need to create an instance of CoverageDetails, with the following fields:
// - CoverageType
// - Deductible (nullable)
// - Limit (nullable)
// Also, note that some of these sub-coverages are not really sub-coverages, and we might
// remove, unify, or split some of them after the coverages refactor. For example: MedPay,
// UM, UIM, UMUIM, EnhancedPackageTowingLimit, PIP, GuestPIP, PIPExcessAttendantCare,
// PIPBasic, PIPIncreased, etc.
//
// For schedule mods, these are some preliminary thoughts:
//
// We don't want the new API nor adaptors to be aware of the
// concept of indication. This means that the new API will not
// receive any inputs that are coupled to the concept of indication.
// More concretely, from the new API we will pass the following
// fields fixed to either nil, false, true or zero:
//   - ModelRunConfig.Hyperparams.SmartIndicationDiscount -> nil
//   - ModelRunConfig.Hyperparams.IndExperimentDiscount -> nil
//   - ModelRunConfig.RateMLFlags.IndicationPricingExperiment -> false
//   - ModelRunConfig.RateMLConfig.Flags.IsDefaultSafetyDiscountDisabled -> true
//   - ModelRunConfig.IsIndicationRun -> false
//
// The above means that the following fields received by the new
// API should already include the following adjustments:
//   - AL discount: should adjust for SmartIndicationDiscount, IsIndicationRun,
//     IndicationPricingExperiment and IndExperimentDiscount.
//   - APD discount: should adjust for SmartIndicationDiscount and IsIndicationRun.
//   - MTC discount: should adjust for SmartIndicationDiscount and IsIndicationRun.
// For more details about these adjustments, see the code in:
//   - sentry_v02.CreateUnderwriting
//   - sentry_v04.CreateUnderwriting
//
// Also note that currently we only five schedule mods for AL, APD and MTC.
// These will be modeled as grouped of sub-coverages, according to the this
// mapping:
// - AL: BI + PD
// - APD: COLL + COMP
// - MTC: Cargo
message Fleet {
  message BundleChunkSpecData {
    // Empty as there are no bundle-level fields
    // for Fleet (as of now). We will probably have
    // to add combined deductibles, just like we did
    // in NF.
  }

  message PolicyChunkSpecData {
    CommoditiesInfo commoditiesInfo = 1;

    Company company = 2;

    UnderwriterInput underwriterInput = 3;

    // This maps to what is now called "ModelInput.DriverInfo.Drivers"
    repeated Driver drivers = 5;

    // This maps to what is now called "ModelInput.EquipmentInfo.EquipmentList.Info"
    repeated Vehicle vehicles = 6;

    // This maps to what is now called "ModelInput.LossInfo.LossRunSummary"
    // (which is a slice of LossRunSummaryPerCoverage)
    // Note: records need be sorted by period dates before being passed to adaptors.
    repeated LossSummary lossSummaries = 7;

    optional LargeLossesInfo largeLossesInfo = 8;

    // This maps to ModelInput.UnderwriterInput.NegotiatedRates (with some adjustments)
    optional NegotiatedPremiumsInfo negotiatedPremiumsInfo = 9;

    UnclassifiedData unclassifiedData = 10;

    pricing.ArtifactConfig artifactConfig = 11;
  }

  message CommoditiesInfo {
    fleet_model.CommodityCategory primaryCommodityCategory = 1;

    repeated CommodityRecord records = 2;

    int64 otherCommoditiesPercentage = 3;
  }

  message CommodityRecord {
    fleet_model.CommodityCategory commodityCategory = 1;

    int64 percentageOfHauls = 2;

    int64 averageDollarValuePerHaul = 3;

    optional fleet_model.CommodityName commodityName = 4;
  }

  message Company {
    string dotNumber = 1;

    // Currently data comes from FMCSA.
    double averageMiles = 2;

    // Currently data comes from FMCSA.
    double averageCombinedGrossVehicleWeight = 3;

    // Currently data comes from FMCSA.
    double crashFrequency = 4;

    // Currently data comes from FMCSA.
    double vehicleInspectionRatio = 5;

    // Currently data comes from FMCSA.
    double maintenanceViolationsRatio = 6;

    // Currently data comes from FMCSA.
    double unsafeViolationRatio = 7;

    // Currently data comes from FMCSA.
    int64 yearsInBusiness = 8;

    // Currently data comes from FMCSA.
    // Note: we are passing this field as a string, but
    // the value needs to match with an enum defined in RateML.
    // We should add an enum for this in the pricing request,
    // to make it more robust.
    string inspectionIndicator = 9;

    // Currently data comes from FMCSA.
    bool largeMachineryIndicator = 10;

    // Currently data comes from FMCSA.
    int64 powerUnitCount = 11;

    string usState = 12;

    int64 totalDriversLastYear = 13;
    int64 driversHiredLastYear = 14;

    repeated RadiusOfOperationRecord radiusOfOperationRecords = 15;

    fleet_model.OperationClass primaryOperationClass = 16;

    // Note: we are passing this field as a string, but
    // the value needs to match with an enum defined in RateML.
    // We should add an enum for this in the pricing request,
    // to make it more robust.
    // The field is optional because it's only required by
    // newer models.
    optional string objectiveGrade = 17;

    repeated TaxRecord taxRecords = 18;

    // Field is optional because it's only required by newer models
    // (after MTC revamp project).
    optional int64 nirvanaYearsRetained = 19;

    // Field is optional because it's only required by newer models
    // (after MTC revamp project).
    optional int64 priorCarrierYearsRetained = 20;

    repeated Terminal terminals = 21;

    // Only required in newer models (after MTC revamp project, and only in the latest versions).
    optional double unsafeDrivingScore = 22;

    // This maps to "ModelInput.UnderwriterInput.RatingAddress.ZipCodeString"
    // It's the terminal location selected for rating purposes.
    string zipCode = 1000;

    // Maps to ModelInput.CompanyInfo.ProjectedMilage
    // We use this field to set the value in ModelOutput,
    // so we can then use it to calculate the per-mile rate
    // that will be stored inside some of the charges with
    // rate-based billing details.
    int32 projectedMiles = 1002;

    // This maps to "ModelInput.EquipmentInfo.OperatingClassDistribution"
    repeated OperationClassRecord operationClassRecords = 1004;

    // THis maps to "ModelInput.UnderwriterInput.VehicleZoneDistribution"
    repeated VehicleZoneRecord vehicleZoneRecords = 1006;

    // This maps to "ModelInput.AdditionalInfoExtraMetadata.PercentageOfSubhaul"
    // Note: not used in older adaptors/models.
    optional float percentageOfSubHaul = 1008;
  }

  message RadiusOfOperationRecord {
    fleet_model.RadiusOfOperationRange radiusOfOperationRange = 1;
    int32 percentage = 2;
  }

  // This maps to what is now called "PremiumTaxRecord"
  // Only used by KY adaptors/models at the moment.
  message TaxRecord {
    string jurisdictionType = 1;
    string jurisdictionName = 2;
    string taxCode = 3;
    string taxValue = 4;

    // Assuming the new operating model, where we call RateML once per
    // policy, not once for the entire bundle, we shouldn't need this
    // field. This is because tax records are already inside a policy
    // chunk spec (so we already know which policy they belong to).
    //
    // However, in the old/current operating model, we call RateML once
    // per bundle and the RateML models have policy-specific fields to
    // pass certain inputs (e.g. LGPTCitySurchargeAuto). Therefore, we
    // need a way to know to which policy does each tax record belong to.
    //
    // After migrating fleet, we should remove this field, and instead
    // pass down to the entity creator/modifiers the policy name within
    // the Input struct. In this world, the consumer would pass the tax
    // records for each policy separately (inside each policy chunk spec).
    pricing.PolicyName policyName = 5;
  }

  message Terminal {
    string id = 1;

    repeated pricing.LimitSpec limitSpecs = 2;

    fleet_model.TerminalConstructionClass constructionClass = 3;
    fleet_model.TerminalPublicProtectionClass publicProtectionClass = 4;
    fleet_model.TerminalPrivateTheftProtectionSystem privateTheftProtectionSystem = 5;
    fleet_model.TerminalPrivateFireProtectionSystem privateFireProtectionSystem = 6;
  }

  message OperationClassRecord {
    fleet_model.OperationClass operationClass = 1;
    int32 percentage = 2;
  }

  message VehicleZoneRecord {
    int32 startZone = 1;
    int32 endZone = 2;
    int32 percentage = 3;
  }

  message UnderwriterInput {
    repeated pricing.ScheduleModification scheduleMods = 2;
  }

  message Driver {
    string id = 1;

    google.protobuf.Timestamp dateHired = 2;

    // Note that I'm assuming here that violation count and attract score
    // are going to be calculated outside pricing, and just passed as inputs.
    //
    // These also means we don't need to pass to the pricing API the flag
    // ModelRunConfig.RateMLConfig.Flags.IsUncountedMVCEnabled (which can't
    // be removed yet as there are active policies with it set to false).
    //
    // If we don't do this refactor, then we would need to pass other driver
    // fields. Namely:
    //  - License number
    //  - State
    //  - First name
    //  - Last name
    //  - Date of birth
    // As well as the company's US state.
    int32 movingViolationCount = 3;
    int32 attractScore = 4;
  }

  // This maps to what is now called "EquipmentListRecord"
  message Vehicle {
    string vin = 1;
    int32 statedValue = 2;
    int32 modelYear = 3;
    fleet_model.VehicleType type = 4;
    fleet_model.VehicleWeightClass weightClass = 5;
  }

  // This maps to a combination of things:
  //   - "LossRunSummaryPerCoverage"
  //   - "LossRunSummaryPerCoverage.Summary"
  //   - "LossRunSummaryPerCoverage.Summary.LossRunSummaryRecord"
  // In the new API we are re-arranging how this data is communicated.
  //
  // Note: we need to validate in the engine that the dates associated to
  // every sub-coverage group are all the same. For example, if AL has
  // dates 3 summaries with dates A, B and C, then APD should also have
  // 3 summaries with dates A, B and C.
  message LossSummary {
    // This maps to LossRunSummaryRecord.PolicyPeriodStartDate/PolicyPeriodEndDate
    common.Interval dates = 1;

    // Currently, adaptors only care about three coverages: APD, AL and MTC.
    // Given that the Pricing API doesn't know about coverages, this
    // information needs to be passed for a group of sub-coverages.
    // The following mapping is defined:
    // APD: COLL + COMP
    // AL: BI + PD
    // MTC: Cargo
    repeated pricing.SubCoverageType subCoverages = 2;

    // Note: this is only used for records within the "AL" LossSummary.
    int32 puCount = 3;

    int32 claimsCount = 4;

    // Note: this is only used for records within the "AL" or "APD" LossSummary.
    int32 lossIncurred = 5;
  }

  message LargeLossesInfo {
    bool areLossesProxied = 1;

    // This maps to either:
    // - ModelInput.UnderwriterInput.LargeLosses
    // - ModelRunConfig.Hyperparams.ProxiedLargeLosses
    // Depending on the value of the boolean field areLossesProxied.
    //
    // Note that we currently only look at large losses for APD and AL.
    repeated LargeLoss largeLosses = 2;
  }

  message LargeLoss {
    google.protobuf.Timestamp date = 1;

    // Here we apply the same mapping between coverages of the old API
    // and sub-coverages of the new API. In other words:
    // - AL: BI + PD
    // - APD: COLL + COMP
    repeated pricing.SubCoverageType subCoverages = 2;

    int32 lossIncurred = 3;
  }

  // Maps to ModelInput.UnderwriterInput.NegotiatedRates
  message NegotiatedPremiumsInfo {
    // Maps to ModelInput.UnderwriterInput.NegotiatedRates.IsNegotiatedRatesApplied
    bool shouldUseNegotiatedRates = 1;

    // Maps to ModelInput.UnderwriterInput.NegotiatedRates.Details.AlNegotiatedRate/ApdNegotiatedRate
    // Note that we currently only do negotiation for APD and AL.
    // Also note that if NegotiatedPremiumsInfo is not nil,
    // then the negotiated premium for AL is required, but for APD is optional.
    repeated NegotiatedPremium negotiatedPremiums = 2;
  }

  message NegotiatedPremium {
    // Here we apply the same mapping between coverages of the old API
    // and sub-coverages of the new API. In other words:
    // - AL: BI + PD
    // - APD: COLL + COMP
    repeated pricing.SubCoverageType subCoverages = 1;
    int64 amount = 2;
  }

  message UnclassifiedData {
    // This maps to "ModelRunConfig.Hyperparams.RatingTierRecordDates"
    RatingTierDates ratingTierDates = 1;

    // This maps to "ModelRunConfig.PackageType".
    // It's optional because it's only used for backwards compatibility
    // of old RateML models. Eventually we will remove this field.
    // Note that this field is within StaticData, which means it shouldn't
    // change between requests. Additionally, it means that it's the same
    // for all chunks in the request.
    optional string packageType = 2;

    // Maps to ModelInput.UnderwriterInput.TransientSafetyCredit.
    //
    // Based on the comment on top of the ScheduleModification
    // message, this field should already be adjusted according
    // to the logic described in sentry_v02.CreateUnderwriting.
    // Basically, it should consider:
    // - IsDefaultSafetyDiscountDisabled
    // - IsIndicationRun
    // - MinSafetyDiscount
    //
    // We keep passing down this value to adaptors just for temporary
    // visibility (it gets logged in the RateML artifact). But this
    // field wil not be used for rating purposes, as from the new API
    // we will pass IsIndicationRun=false always.
    double allSubCoveragesSafetyScheduleMod = 3;
  }

  // This maps to "rating_tier.RecordDates"
  message RatingTierDates {
    google.protobuf.Timestamp dumpDate = 1;
    google.protobuf.Timestamp recordDate = 2;
  }

  message ChunkOutputMetadata {
    int32 numberOfPowerUnits = 1;

    Rule15Metadata rule15Metadata = 2;

    // Models currently only expose records for:
    // - BIPD
    // - Broadened Pollution
    // - Collision
    // - Cargo
    // - Reefer (with and without human error)
    //
    // Note that not all models will return all of these records,
    // and not all policy chunks will have all of these records
    // (e.g. chunks on the Motor Carrier policy will not have the Cargo record).
    repeated SubCoverageGroupPremiumRecord unmodifiedPremiumRecords = 3;

    // Models currently only expose records for:
    // - BIPD
    // - Coll + Comp (as a unit)
    //
    // Note that this can or cannot be the same premium from which the
    // rate stored in the corresponding charge was obtained. The rate can
    // be obtained from the traditional (a.k.a. non-negotiated) premium,
    // or from the negotiated premium (which is received as an input from
    // the consumer).
    repeated SubCoverageGroupPremiumRecord traditionalPremiumRecords = 4;
  }

  message Rule15Metadata {
    // To calculate if the rule 15 applied you need to check
    // if premium > threshold.
    double exemptionThreshold = 1;
    double exemptionPremium = 2;
  }

  message SubCoverageGroupPremiumRecord {
    double premium = 1;
    pricing.SubCoverageGroup subCoverageGroup = 2;
  }
}
